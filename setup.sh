#!/bin/bash

# Exolog Project Automated Setup Script
# This script sets up the complete Exolog development environment

set -e  # Exit on any error

echo "🚀 Starting Exolog Project Setup..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root"
   exit 1
fi

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check Git
    if ! command -v git &> /dev/null; then
        print_error "Git is not installed. Please install Git first."
        exit 1
    fi
    
    print_success "All prerequisites are installed"
}

# Create directory structure
create_directories() {
    print_status "Creating directory structure..."
    
    # Create main directories
    sudo mkdir -p /home/<USER>/{app,mysql_data,mail,caddy_config,plausible,nginx,supervisor,gitlab-runner}
    sudo mkdir -p /home/<USER>/plausible/{db,event-data,event-logs,clickhouse}
    sudo mkdir -p /home/<USER>/mail/{data,certs}
    
    # Set permissions
    sudo chown -R $USER:$USER /home/<USER>
    sudo chmod -R 755 /home/<USER>
    
    print_success "Directory structure created"
}

# Copy project files
copy_project_files() {
    print_status "Copying project files..."
    
    # Copy current directory to docker app directory
    sudo cp -r . /home/<USER>/app/
    sudo chown -R $USER:$USER /home/<USER>/app
    
    print_success "Project files copied"
}

# Create configuration files
create_config_files() {
    print_status "Creating configuration files..."
    
    # Create Nginx configuration for dealer
    cat > /home/<USER>/nginx/dealer_vhost.conf << 'EOF'
server {
    listen 80;
    server_name _;
    root /app/exologadmin.webkracht.nl/htdocs/dealer;
    index index.php index.html;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
EOF

    # Create Nginx configuration for main app
    cat > /home/<USER>/nginx/vhost.conf << 'EOF'
server {
    listen 80;
    server_name _;
    root /app/homepages.webkracht.nl/htdocs;
    index index.php index.html;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
EOF

    # Create Supervisor configuration
    cat > /home/<USER>/supervisor/laravel-worker.conf << 'EOF'
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /app/artisan queue:work --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=application
numprocs=8
redirect_stderr=true
stdout_logfile=/app/storage/logs/worker.log
stopwaitsecs=3600
EOF

    # Create Plausible ClickHouse configurations
    mkdir -p /home/<USER>/plausible/clickhouse
    
    cat > /home/<USER>/plausible/clickhouse/logs.xml << 'EOF'
<clickhouse>
    <logger>
        <level>warning</level>
        <console>true</console>
    </logger>
    <query_thread_log remove="remove"/>
    <query_log remove="remove"/>
    <text_log remove="remove"/>
    <trace_log remove="remove"/>
    <metric_log remove="remove"/>
    <asynchronous_metric_log remove="remove"/>
    <session_log remove="remove"/>
    <part_log remove="remove"/>
</clickhouse>
EOF

    cat > /home/<USER>/plausible/clickhouse/ipv4-only.xml << 'EOF'
<clickhouse>
    <listen_host>0.0.0.0</listen_host>
</clickhouse>
EOF

    cat > /home/<USER>/plausible/clickhouse/low-resources.xml << 'EOF'
<clickhouse>
    <max_server_memory_usage_to_ram_ratio>0.3</max_server_memory_usage_to_ram_ratio>
    <max_concurrent_queries>50</max_concurrent_queries>
    <background_pool_size>2</background_pool_size>
    <background_merges_mutations_concurrency_ratio>2</background_merges_mutations_concurrency_ratio>
</clickhouse>
EOF

    print_success "Configuration files created"
}

# Create environment file
create_env_file() {
    print_status "Creating environment file..."
    
    # Generate random passwords
    MYSQL_ROOT_PASSWORD=$(openssl rand -base64 32)
    DB_PASSWORD=$(openssl rand -base64 32)
    POSTGRES_PASSWORD=$(openssl rand -base64 32)
    SECRET_KEY_BASE=$(openssl rand -base64 64)
    TOTP_VAULT_KEY=$(openssl rand -base64 32)
    
    cat > /home/<USER>/app/.env.docker << EOF
# Database Configuration
MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
DB_DATABASE=exolog
DB_USERNAME=exolog_user
DB_PASSWORD=${DB_PASSWORD}

# Server Configuration
EXOLOG_SERVER_DOMAIN=localhost

# Plausible Analytics
PLAUSIBLE_BASE_URL=http://localhost:8882
PLAUSIBLE_SECRET_KEY_BASE=${SECRET_KEY_BASE}
POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
PLAUSIBLE_MAILER_EMAIL=admin@localhost

# SMTP Configuration
SMTP_HOST_ADDR=mailserver
SMTP_HOST_PORT=587
SMTP_USER_NAME=admin@localhost
SMTP_USER_PWD=admin123
SMTP_HOST_SSL_ENABLED=false

# Security
TOTP_VAULT_KEY=${TOTP_VAULT_KEY}

# Optional - Add your own values
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
EOF

    print_success "Environment file created with random passwords"
    print_warning "Please review /home/<USER>/app/.env.docker and update as needed"
}

# Start Docker services
start_services() {
    print_status "Starting Docker services..."
    
    cd /home/<USER>/app/docker
    
    # Export environment variables
    export $(cat /home/<USER>/app/.env.docker | xargs)
    
    # Start services
    docker-compose up -d
    
    print_success "Docker services started"
}

# Wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    # Wait for MySQL
    print_status "Waiting for MySQL..."
    while ! docker-compose exec -T mysql mysqladmin ping -h"localhost" --silent; do
        sleep 2
    done
    
    # Wait for Redis
    print_status "Waiting for Redis..."
    while ! docker-compose exec -T redis redis-cli ping; do
        sleep 2
    done
    
    print_success "All services are ready"
}

# Initialize application
initialize_app() {
    print_status "Initializing application..."
    
    cd /home/<USER>/app/docker
    
    # Run composer install and Laravel setup
    docker-compose exec -T composer-npm sh -c "
        cd /app &&
        composer install --no-dev --optimize-autoloader &&
        php artisan key:generate --force &&
        php artisan config:cache &&
        php artisan route:cache &&
        php artisan view:cache &&
        php artisan migrate --force &&
        php artisan queue:restart
    "
    
    print_success "Application initialized"
}

# Build frontend applications
build_frontend() {
    print_status "Building frontend applications..."
    
    cd /home/<USER>/app/docker
    
    # Build all frontend apps
    docker-compose exec -T composer-npm sh -c "
        cd /app/resources/js/exo_admin && npm install && npm run deploy &&
        cd /app/resources/js/exo_editor && npm install && npm run deploy &&
        cd /app/resources/js/dealer && npm install && npm run deploy
    "
    
    print_success "Frontend applications built"
}

# Display final information
display_info() {
    print_success "🎉 Exolog setup completed successfully!"
    echo ""
    echo "📋 Access Information:"
    echo "  • Main Application: http://localhost:82"
    echo "  • Dealer Interface: http://localhost:81"
    echo "  • Mail Server: http://localhost:85"
    echo "  • Plausible Analytics: http://localhost:8882"
    echo ""
    echo "🔧 Useful Commands:"
    echo "  • View logs: cd /home/<USER>/app/docker && docker-compose logs -f"
    echo "  • Restart services: cd /home/<USER>/app/docker && docker-compose restart"
    echo "  • Stop services: cd /home/<USER>/app/docker && docker-compose down"
    echo ""
    echo "📁 Important Files:"
    echo "  • Environment: /home/<USER>/app/.env.docker"
    echo "  • Application: /home/<USER>/app/"
    echo "  • Logs: /home/<USER>/app/storage/logs/"
    echo ""
    print_warning "Please review the environment file and update credentials as needed!"
}

# Main execution
main() {
    check_prerequisites
    create_directories
    copy_project_files
    create_config_files
    create_env_file
    start_services
    wait_for_services
    initialize_app
    build_frontend
    display_info
}

# Run main function
main "$@"
