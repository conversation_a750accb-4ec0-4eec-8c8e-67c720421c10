## What is the productivity problem to solve?

<!--
Please describe the productivity problem that needs to be solved
-->

### Problem identification checklist

- [ ] The root cause of the problem is identified.
- [ ] The surface of the problem is as small as possible.

## What are the potential solutions?

<!--
Please provide potential solutions here. Example solutions could be:

- Dogfood a feature.
- Refactor/improve some workflow code.
- Throw more money at the problem.

Please provide pros/cons and a weight estimate for each solution.
-->

- [ ] All potential solutions are listed.
- [ ] A solution has been chosen for the first iteration: `PUT THE CHOSEN SOLUTION HERE`

## Verify that the solution has improved the situation

<!--
Ideally, looking at the charts from the first part, we should see an improvement
after the implementation is merged/deployed/released.
-->

- [ ] The solution improved the situation.
  - If yes, check this box and close the issue. Well done! :tada:
  - Otherwise, create a new "Productivity Improvement" issue. You can re-use the description from this issue, but obviously another solution should be chosen this time.

/label ~"Engineering Productivity"
