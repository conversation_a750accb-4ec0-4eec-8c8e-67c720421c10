<!---
Please read this!

Before opening a new issue, make sure to search for keywords in the issues
filtered by the "bug" label:

- https://gitlab.fidela.nl/fidela/exolog-reactJson/-/issues?label_name%5B%5D=bug

and verify the issue you're about to submit isn't a duplicate.
--->

### Summary

(Summarize the bug encountered concisely)

### Steps to reproduce

(How one can reproduce the issue - this is very important)

### Environment info

<!---
Please provide related server and site info!
plese check https://gitlab.fidela.nl/fidela/exolog-reactJson/-/environments

and verify that assigned developer have access to reproduce bug.
--->
* Server name (WXX or dealer URL)
* Related site URL

### What is the current *bug* behavior?

(What actually happens)

### What is the expected *correct* behavior?

(What you should see instead)

### Relevant logs and/or screenshots

(Paste any relevant logs - please use code blocks (```) to format console output,
logs, and code as it's tough to read otherwise.)

### Possible fixes

(If you can, link to the line of code that might be responsible for the problem)

/label ~bug
