## Summary

<!--
Please briefly describe what part of the code base needs to be refactored.
-->

## Improvements

<!--
Explain the benefits of refactoring this code.
See also https://about.gitlab.com/handbook/values/index.html#say-why-not-just-what
-->

## Risks

<!--
Please list features that can break because of this refactoring and how you intend to solve that.
-->

## Involved components

<!--
List files or directories that will be changed by the refactoring.
-->

## Optional: Intended side effects

<!--
If the refactoring involves changes apart from the main improvements (such as a better UI), list them here.
It may be a good idea to create separate issues and link them here.
-->


## Optional: Missing test coverage

<!--
If you are aware of tests that need to be written or adjusted apart from unit tests for the changed components,
please list them here.
-->

/label ~backstage
