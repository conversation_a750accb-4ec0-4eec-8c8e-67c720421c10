<!---
The first four sections: "Problem to solve", "Intended users", "User experience goal", and "Proposal", are strongly recommended, while the rest of the sections can be filled out during the problem validation or breakdown phase. However, keep in mind that providing complete and relevant information early helps our product team validate the problem and start working on a solution. 
--->

### Problem to solve

<!---
What problem do we solve? Try to define the who/what/why of the opportunity as a user story. For example, "As a (who), I want (what), so I can (why/value)." 
--->

### User experience goal

<!-- What is the single user experience workflow this problem addresses? 
For example, "The user should be able to use the UI/API/.gitlab-ci.yml with GitLab to <perform a specific task>"
--> 

### Proposal

<!-- How are we going to solve the problem? Try to include the user journey! -->

### Further details

<!-- Include use cases, benefits, goals, or any other details that will help us understand the problem better. -->

### Documentation

<!-- Add all known Documentation Requirements in this section.-->

### What does success look like, and how can we measure that?

<!-- Define both the success metrics and acceptance criteria. Note that success metrics indicate the desired business outcomes, while acceptance criteria indicate when the solution is working correctly. If there is no way to measure success, link to an issue that will implement a way to measure this. -->


### Links / references

/label ~feature
