import Vue from 'vue'
import Vuex from 'vuex'
import {
    ADMIN_CREATE,
    ADMIN_DELETE,
    ADMIN_DISABLE_2FA,
    ADMIN_GET_ALL,
    ADMIN_GET_ALL_NOT_USER,
    ADMIN_REQUEST,
    ADMIN_REQUEST_ME,
    ADMIN_UPDATE,
    AUTH_2FA_REQUEST,
    AUTH_CHANGE_PASSWORD,
    AUTH_ERROR,
    AUTH_FORGOT_PASSWORD_LINK,
    AUTH_FORGOT_PASSWORD_LINK_SUCCESS,
    AUTH_LOGOUT,
    AUTH_REQUEST,
    AUTH_SUCCESS,
    DOMAIN_RESET_SSL_STATUS,
    SAVE_ROUTE_BEFORE_AUTH,
    SERVER_CONFIG,
    SITE_ADD_USER,
    SITE_CREATE,
    SITE_DELETE,
    SITE_GET_ALL,
    SITE_REQUEST,
    SITE_SOFT_DELETE,
    SITE_SOFT_UNDELETE,
    SITE_UPDATE,
} from "./actions";

import Api from "../services/Api";
import getErrorMessage from '../shared/getErrorMessage'

Vue.use(Vuex);

export default new Vuex.Store({
    state: {
        auth: {
            from_path: '/',
            status: '',
            error: ''
        },
        admin: JSON.parse(localStorage.getItem('admin')) || {},
        server: {
            server_name: '',
            options: {
                USE_SSL_FOR_SUBDOMAIN: false
            }
        },
    },
    getters: {
        isAdmin: state => {
            return state.admin.a_id > 0
        },
    },
    actions: {
        [SAVE_ROUTE_BEFORE_AUTH]: ({commit, dispatch}, path) => {
            commit(SAVE_ROUTE_BEFORE_AUTH, path)
        },

        [AUTH_REQUEST]: ({commit, dispatch}, admin) => {
            return new Promise((resolve, reject) => { // The Promise used for router redirect in login
                commit(AUTH_REQUEST);
                let payload = Object.assign({}, admin);
                Api.post('/auth/login', payload)
                    .then(result => {
                        /** @namespace resp.data.is_2fa_enabled */
                        if (result.data.is_2fa_enabled) commit(AUTH_2FA_REQUEST);
                        else {
                            commit(AUTH_SUCCESS, result.data.admin)
                            localStorage.setItem('admin', JSON.stringify(result.data.admin))
                            dispatch(SERVER_CONFIG);
                        }
                        resolve(result)
                    })
                    .catch(err => {
                        commit(AUTH_ERROR, getErrorMessage(err));
                        reject(err)
                    })
            })
        },

        [AUTH_FORGOT_PASSWORD_LINK]: ({commit, dispatch}, a_email) => {
            return new Promise((resolve, reject) => { // The Promise used for router redirect in login
                commit(AUTH_FORGOT_PASSWORD_LINK);

                Api.post('/auth/sendPasswordLink', a_email)
                    .then(result => {
                        commit(AUTH_FORGOT_PASSWORD_LINK_SUCCESS);
                        resolve(result)
                    })
                    .catch(err => {
                        commit(AUTH_ERROR, getErrorMessage(err));
                        reject(message)
                    })
            })
        },

        [AUTH_CHANGE_PASSWORD]: ({commit, dispatch}, params) => {
            let payload = Object.assign({}, params);
            return Api.post('/auth/changePassword', payload)
        },

        [AUTH_LOGOUT]: ({commit, dispatch, getters}) => {
            return new Promise((resolve, reject) => {
                if (getters['isAdmin']) {
                    commit(AUTH_LOGOUT);
                    Api.post('/auth/logout')
                    localStorage.removeItem('admin')
                }
                resolve()
            })
        },

        [ADMIN_REQUEST_ME]: ({commit, dispatch, state}) => {
            return new Promise((resolve, reject) => {
                commit(ADMIN_REQUEST_ME);
                Api.get('/admin/me').then(result => {
                        commit(AUTH_SUCCESS, result.data.admin)
                        localStorage.setItem('admin', JSON.stringify(result.data.admin))
                        dispatch(SERVER_CONFIG)
                        resolve(result)
                    }
                ).catch(error => {
                    commit(AUTH_LOGOUT);
                    reject(error);
                })
            })
        },

        [ADMIN_REQUEST]: ({commit, dispatch, state}, a_id) => {
            return Api.get('/admin/get', {params: {a_id}}).then(result => {
                    return result['data']
                }
            )
        },

        [ADMIN_GET_ALL]: () => {
            return new Promise((resolve, reject) => {
                Api.get('/admin/all').then(result => {
                    result['data'].map(admin => {
                        admin['a_id'] = +admin['a_id'];
                        return admin;
                    })
                    resolve(result['data']);
                }).catch(error => {
                    reject(error)
                })
            })
        },

        [ADMIN_GET_ALL_NOT_USER]: ({}, {site_id}) => {
            return new Promise((resolve, reject) => {
                Api.get('/site/getAdminsNotUser', {params: {site_id}}).then(result => {
                    result['data'].map(admin => {
                        admin['a_id'] = +admin['a_id'];
                        return admin;
                    })
                    resolve(result['data']);
                }).catch(error => {
                    reject(error)
                })
            })
        },

        [ADMIN_CREATE]: ({commit, dispatch, state}, admin) => {
            return new Promise((resolve, reject) => {
                Api.post('/admin/create', {admin}).then(result => {
                        resolve(result)
                    }
                ).catch(error => {
                    reject(error)
                })
            })
        },

        [ADMIN_UPDATE]: ({commit, dispatch, state}, admin) => {
            return new Promise((resolve, reject) => {
                Api.post('/admin/update', {admin}).then(result => {
                        resolve(result)
                    }
                ).catch(error => {
                    reject(error)
                })
            })
        },

        [ADMIN_DELETE]: ({commit, dispatch, state}, a_id) => {
            return Api.post('/admin/delete', {a_id})
        },

        [ADMIN_DISABLE_2FA]: ({commit, dispatch, state}, a_id) => {
            return new Promise((resolve, reject) => {
                Api.post('/auth/disable2fa', {a_id}).then(result => {
                        resolve(result)
                    }
                ).catch(error => {
                    reject(error)
                })
            })
        },

        [SITE_GET_ALL]: ({}, {trashed}) => {
            return new Promise((resolve, reject) => {
                Api.get('/site/all', {
                    progress: true,
                    params: {
                        trashed: trashed
                    }
                }).then(result => {
                    result['data'].map(site => {
                        site['site_id'] = +site['site_id'];
                        return site;
                    })
                    resolve(result['data']);
                }).catch(error => {
                    reject(error)
                })
            })
        },

        [SITE_REQUEST]: ({commit, dispatch, state}, site_id) => {
            return new Promise((resolve, reject) => {
                Api.get('/site/get', {
                    params: {site_id},
                    progress: true
                }).then(result => {
                        let site = result['data']
                        if (Array.isArray(site.domains)) {
                            site.domains.sort(function (a, b) {
                                return parseInt(b.is_readonly) - parseInt(a.is_readonly)
                            })
                        }
                        resolve(site)
                    }
                ).catch(error => {
                    reject(error)
                })
            })
        },

        [SITE_CREATE]: ({commit, dispatch, state}, site) => {
            return new Promise((resolve, reject) => {
                Api.post('/site/create', {site}, {progress: true}).then(result => {
                        resolve(result['data'])
                    }
                ).catch(error => {
                    reject(error)
                })
            })
        },

        [SITE_UPDATE]: ({commit, dispatch, state}, site) => {
            return new Promise((resolve, reject) => {
                Api.post('/site/update', {site}, {progress: true}).then(result => {
                        resolve(result['data'])
                    }
                ).catch(error => {
                    reject(error)
                })
            })
        },

        [SITE_SOFT_DELETE]: ({commit, dispatch, state}, site_id) => {
            return new Promise((resolve, reject) => {
                Api.post('/site/softDelete', {site_id}).then(result => {
                        resolve(result['data'])
                    }
                ).catch(error => {
                    reject(error)
                })
            })
        },
        [SITE_SOFT_UNDELETE]: ({commit, dispatch, state}, site_id) => {
            return new Promise((resolve, reject) => {
                Api.post('/site/softUndelete', {site_id}).then(result => {
                        resolve(result['data'])
                    }
                ).catch(error => {
                    reject(error)
                })
            })
        },
        [SITE_DELETE]: ({commit, dispatch, state}, site_id) => {
            return new Promise((resolve, reject) => {
                Api.post('/site/delete', {site_id}).then(result => {
                        resolve(result['data'])
                    }
                ).catch(error => {
                    reject(error)
                })
            })
        },

        [SITE_ADD_USER]: ({commit, dispatch, state}, {site_id, admin_id}) => {
            return new Promise((resolve, reject) => {
                Api.post('/site/addUser', {site_id, admin_id}).then(result => {
                        resolve(result['data'])
                    }
                ).catch(error => {
                    reject(error)
                })
            })
        },

        [SERVER_CONFIG]: ({commit, dispatch, state}) => {
            return new Promise((resolve, reject) => {
                Api.get('/server/config').then(result => {
                        commit(SERVER_CONFIG, result['data'])
                        resolve(result['data'])
                    }
                ).catch(error => {
                    reject(error)
                })
            })
        },

        [DOMAIN_RESET_SSL_STATUS]: ({commit, dispatch, state}, domain_id) => {
            return new Promise((resolve, reject) => {
                Api.post('/domain/resetSllStatus', {domain_id}).then(result => {
                        resolve(result['data'])
                    }
                ).catch(error => {
                    reject(error)
                })
            })
        },
    },
    mutations: {
        [SAVE_ROUTE_BEFORE_AUTH]: (state, path) => {
            state.auth.from_path = path
        },

        [ADMIN_REQUEST_ME]: (state) => {
            state.auth.status = 'admin_request';
            state.auth.error = ''
        },

        [AUTH_FORGOT_PASSWORD_LINK]: (state) => {
            state.auth.status = 'loading';
            state.auth.error = ''
        },

        [AUTH_FORGOT_PASSWORD_LINK_SUCCESS]: (state) => {
            state.auth.status = '';
            state.auth.error = ''
        },

        [AUTH_REQUEST]: (state) => {
            state.auth.status = 'loading';
            state.auth.error = ''
        },

        [AUTH_2FA_REQUEST]: (state) => {
            state.auth.status = 'need_2fa'
        },

        [AUTH_SUCCESS]: (state, admin) => {
            state.auth.status = 'success';
            state.auth.error = '';
            Vue.set(state, 'admin', admin)
        },

        [AUTH_ERROR]: (state, msg) => {
            state.auth.status = 'error';
            state.auth.error = msg
        },

        [AUTH_LOGOUT]: (state) => {
            state.auth.status = '';
            Vue.set(state, 'admin', {})
        },

        [SERVER_CONFIG]: (state, config) => {
            Vue.set(state, 'server', config)
        },
    }
});
