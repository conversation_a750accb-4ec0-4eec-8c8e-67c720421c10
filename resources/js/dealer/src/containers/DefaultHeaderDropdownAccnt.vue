<template>
    <AppHeaderDropdown right no-caret>
        <template slot="header">
            {{admin_email}}
        </template>
        <template slot="dropdown">
            <b-dropdown-header tag="div" class="text-center"><strong>Account</strong></b-dropdown-header>
            <b-dropdown-item @click="profile"><i class="fa fa-user"></i> Profile</b-dropdown-item>
            <b-dropdown-item @click="logout"><i class="fa fa-sign-out"></i> Logout</b-dropdown-item>
        </template>
    </AppHeaderDropdown>
</template>

<script>
    import {HeaderDropdown as AppHeaderDropdown} from '@coreui/vue'
    import {AUTH_LOGOUT} from "../store/actions";

    export default {
        name: 'DefaultHeaderDropdownAccnt',
        components: {
            AppHeaderDropdown
        },
        data: () => {
            return {}
        },
        computed: {
            admin_email() {
                return this.$store.state.admin.a_email
            }
        },
        methods: {
            logout() {
                this.$store.dispatch(AUTH_LOGOUT)
                    .then(() => {
                        this.$router.push({name: 'Login'})
                    })
            },
            profile() {
                this.$router.push({name: 'Edit admin', params: {a_id: 'me'}})
            }
        }
    }
</script>
