<template>
    <div class="app">
        <AppHeader fixed>
            <SidebarToggler class="d-lg-none" display="md" mobile/>
            <b-link class="navbar-brand" to="#">
                <img class="navbar-brand-full" src="/app/img/brand/logo.png" height="30"  alt="ExologCMS - Dealer admin">
                <img class="navbar-brand-minimized" src="/app/img/brand/logo_mini.png"  height="30" alt="ExologCMS - Dealer admin">
            </b-link>
            <SidebarToggler class="d-md-down-none" display="lg"/>
            <h4 class="mx-auto">
                <b-badge pill  variant="danger" v-text="this.$store.state.server.server_name"></b-badge>
            </h4>
            <b-navbar-nav class="mr-4">
                <DefaultHeaderDropdownAccnt/>
            </b-navbar-nav>
            <!--<AsideToggler class="d-lg-none" mobile />-->
        </AppHeader>
        <div class="app-body">
            <AppSidebar fixed>
                <SidebarHeader/>
                <SidebarForm/>
                <SidebarNav :navItems="nav"></SidebarNav>
                <SidebarFooter/>
                <SidebarMinimizer/>
            </AppSidebar>
            <main class="main">
                <Breadcrumb :list="list"/>
                <div class="container-fluid">
                    <router-view></router-view>
                </div>
            </main>
        </div>
    </div>
</template>

<script>
    import nav from '@/_nav'
    import {
        Header as AppHeader,
        SidebarToggler,
        Sidebar as AppSidebar,
        SidebarFooter,
        SidebarForm,
        SidebarHeader,
        SidebarMinimizer,
        SidebarNav,
        Aside as AppAside,
        AsideToggler,
        Footer as TheFooter,
        Breadcrumb
    } from '@coreui/vue'
    //import DefaultAside from './DefaultAside'
    import DefaultHeaderDropdownAccnt from './DefaultHeaderDropdownAccnt'

    export default {
        name: 'full',
        components: {
            AsideToggler,
            AppHeader,
            AppSidebar,
            //AppAside,
            TheFooter,
            Breadcrumb,
            //DefaultAside,
            DefaultHeaderDropdownAccnt,
            SidebarForm,
            SidebarFooter,
            SidebarToggler,
            SidebarHeader,
            SidebarNav,
            SidebarMinimizer
        },
        data() {
            return {
                nav: nav.items
            }
        },
        computed: {
            name() {
                return this.$route.name
            },
            list() {
                return this.$route.matched.filter((route) => route.name || route.meta.label)
            }
        }
    }
</script>
