<template>
    <b-modal
        id="editDomain"
        size="lg"
        centered
        v-model="modalShow"
        @ok="onSave"
        @hidden="$emit('close')">
        <form>
            <b-tabs class="edit-domain" v-model="tabIndex">
                <b-tab title="Domain">
                    <ValidationObserver ref="observer" v-slot="{ invalid, errors }">
                        <b-form-group horizontal
                                      :label-cols="2"
                                      label="Name"
                                      label-for="input_name">
                            <div v-if="domainInt.domain_id">
                                <b-form-input id="inputdomainInt_name"
                                              required
                                              type="text"
                                              :disabled="true"
                                              v-model="domainInt.domain_name">
                                </b-form-input>
                            </div>
                            <template v-else>
                                <div class="subdomain-name">
                                    <div>
                                        <ValidationProvider :rules="ruleObject"
                                                            name="subdomain"
                                                            :debounce="200"
                                                            v-slot="{ valid, errors }">
                                            <b-input v-model="domainInt.subdomainValue"
                                                     :disabled="isDisabledValue"
                                                     class="subdomain-name__value"
                                                     :state="errors[0] ? false : null"
                                            />
                                        </ValidationProvider>
                                    </div>
                                    <span class="px-1">.</span>
                                    <ValidationProvider rules="required"
                                                        class="subdomain-name__parent"
                                                        name="parentdomain"
                                                        :debounce="200"
                                                        v-slot="{ valid, errors }">
                                        <x-select v-model="domainInt.subdomainParent"
                                                  :clearable="false"
                                                  :multiple="false"
                                                  :options="parentDomains"
                                                  :searchable="false"
                                                  valueFormat="object"
                                                  id-key="idInt"
                                                  label-key="domain_name"/>
                                    </ValidationProvider>
                                    <b-button class="ml-1" variant="outline-primary" @click="onAddParentDomain"><i
                                        class="fa fa-plus"></i></b-button>
                                </div>
                                <div class="mt-2">
                                    <p v-for="(error,index) in errors" :key="index" class="text-danger">
                                        <template v-for="(message,index) in error">
                                        <span :key="index">
                                        {{ message }}
                                    </span><br>
                                        </template>
                                    </p>
                                </div>
                            </template>
                        </b-form-group>
                        <!-- Use for email checkbox hidden as requested -->
                        <!-- <b-form-group horizontal
                                      :label-cols="2"
                                      label="">
                            <b-form-checkbox id="ismail"
                                             value="1"
                                             v-model="domainInt.domain_ismail"
                                             unchecked-value="0">
                                Use for email
                            </b-form-checkbox>
                        </b-form-group> -->
                    </ValidationObserver>
                </b-tab>
                <b-tab title="SSL Certificate">
                    <template v-if="canSetupSSL">
                        <b-form-group label-sr-only>
                            <b-form-checkbox id="usehttps"
                                             value="1"
                                             v-model="domainInt.domain_isssl"
                                             unchecked-value="0">
                                Use HTTPS
                            </b-form-checkbox>
                        </b-form-group>
                        <b-form-group v-show="domainInt.domain_isssl==1"
                                      label-sr-only>
                            <b-form-checkbox id="usele"
                                             value="1"
                                             v-model="domainInt.domain_isletsencrypt"
                                             unchecked-value="0">
                                Use Let’s Encrypt
                            </b-form-checkbox>
                        </b-form-group>
                        <b-form-group
                            v-show="domainInt.domain_isssl==1">
                            <slot name="label">
                                <label for="ssl_cert">
                                    <span>Certificate</span>
                                    <i v-show="domainInt.domain_isletsencrypt==1" class="text-success">
                                        - will be generated automatically
                                    </i>
                                </label>

                            </slot>
                            <b-form-textarea id="ssl_cert"
                                             :disabled="domainInt.domain_isletsencrypt==1"
                                             v-model="domainInt.ssl.cert"
                                             placeholder=""
                                             :state="domainInt.domain_isssl==1 && domainInt.domain_isletsencrypt!=1 && domainInt.ssl.cert.trim()==''?false:null"
                                             :rows="3"
                                             :max-rows="6">
                            </b-form-textarea>
                            <b-form-invalid-feedback id="inputLiveFeedback1">
                                This field is reqired
                            </b-form-invalid-feedback>
                        </b-form-group>
                        <b-form-group label="Private Key"
                                      v-show="domainInt.domain_isssl==1">
                            <b-form-textarea id="ssl_private_key"
                                             :disabled="domainInt.domain_isletsencrypt==1"
                                             v-model="domainInt.ssl.private_key"
                                             :state="domainInt.domain_isssl==1 && domainInt.domain_isletsencrypt!=1 && domainInt.ssl.private_key.trim()==''?'invalid':null"
                                             placeholder=""
                                             :rows="3"
                                             :max-rows="6">
                            </b-form-textarea>
                            <b-form-invalid-feedback id="inputLiveFeedback2">
                                This field is reqired
                            </b-form-invalid-feedback>
                        </b-form-group>
                        <b-form-group v-show="domainInt.domain_isssl==1">
                            <slot name="label">
                                <label for="ssl_CA">
                                    Certificate Authority / Intermediate <span class="text-muted">(Optional)</span>
                                </label>
                            </slot>
                            <b-form-textarea id="ssl_CA"
                                             :disabled="domainInt.domain_isletsencrypt==1"
                                             v-model="domainInt.ssl.CA"
                                             placeholder=""
                                             :rows="3"
                                             :max-rows="6">
                            </b-form-textarea>
                        </b-form-group>
                    </template>
                    <template v-else>
                        <p class="text-muted">According to server settings, SSL for subdomains is managed automatically
                            on the server.</p>
                    </template>
                </b-tab>
            </b-tabs>
        </form>
    </b-modal>
</template>

<script>
import Vue from 'vue'
import VueBootstrapTypeahead from 'vue-bootstrap-typeahead'
import genID from '@modules/mixins/genID'

export default {
    name: "EditDomain",
    components: {VueBootstrapTypeahead},
    mixins: [genID],
    model: {
        prop: 'domain',
        event: 'change'
    },
    props: {
        edit: Boolean,

    },
    data() {
        return {
            newDomains: [],
            tabIndex: 0,
            modalShow: false,
            domainInt: {
                subdomainValue: '',
                subdomainParent: null,
                domain_name: null,
                domain_ismain: 0,
                domain_isdefault: 0,
                domain_isssl: 1,
                domain_isletsencrypt: 1,
                domain_ismail: 0,
                is_readonly: 0,
                ssl: {
                    cert: '',
                    private_key: '',
                    CA: ''
                }
            },
        }
    },
    watch: {
        edit(newVal) {
            if (newVal) {


                //if (this.domainInt.domain_name) Api.post('/domain/isOpenProvider', {domain_name: this.domainInt.domain_name})
            }
        },
        domain: function (newVal) {
            //this.domainInt = Vue.util.extend( {}, newVal)
        }
    },
    computed: {
    },

    methods: {
    }
}
</script>

