<template>
    <b-row class="animated fadeIn">
        <b-col cols="12">
            <b-form @submit.prevent="onSave">
                <b-card :header="caption" show-footer footer-class="d-flex justify-content-end" no-body>
                    <b-tabs v-model="tabIndex">
                        <b-tab title="Common">
                            <b-form-group horizontal
                                          :label-cols="2"
                                          label="Name"
                                          label-for="input_name">
                                <b-form-input id="input_name"
                                              required
                                              type="text"
                                              v-model="site.site_name">
                                </b-form-input>
                            </b-form-group>
                            <b-form-group horizontal
                                          :label-cols="2"
                                          label="Alias"
                                          label-for="input_alias">
                                <b-form-input id="input_alias"
                                              required
                                              type="text"
                                              :state="validation.alias.valid"
                                              :readonly="action==='SITE_UPDATE'"
                                              v-model="site.site_alias">
                                </b-form-input>
                                <b-form-invalid-feedback id="inputLiveFeedback">
                                    {{ validation.alias.message }}
                                </b-form-invalid-feedback>
                            </b-form-group>
                            <b-form-group horizontal
                                          :label-cols="2"
                                          label="Hostname"
                                          label-for="input_hostname">
                                <b-form-input id="input_hostname"
                                              disabled
                                              :value="hostname"
                                              type="text"
                                              required>
                                </b-form-input>
                            </b-form-group>
                        </b-tab>
                        <b-tab title="Site vars">
                            <vars-editor v-model="site.vars"></vars-editor>
                        </b-tab>
                        <b-tab title="Domains">
                            <site-domains ref="siteDomains"
                                          v-model="site.domains"
                                          :action="action"
                                          :site-id="site.site_id"
                                          @domainResetSslStatus="onDomainResetSslStatus"
                                          @change="onDomainsChange"
                                          @refresh-domains="onRefreshDomains">
                            </site-domains>
                        </b-tab>
                    </b-tabs>
                    <template #footer>
                        <b-btn variant="success"
                               @click="onRefresh"
                               class="mr-auto">Refresh
                        </b-btn>
                        <b-btn variant="default"
                               @click="onCancel"
                               class="mx-1"
                               :disabled="disableControl">Cancel
                        </b-btn>
                        <b-btn variant="primary"
                               @click="onSaveOnly"
                               class="mx-1"
                               :disabled="disableControl || statusInProcess">Save
                        </b-btn>
                        <b-btn variant="success"
                               type="submit"
                               :disabled="disableControl || statusInProcess">Save and Exit
                        </b-btn>
                    </template>
                </b-card>
            </b-form>
        </b-col>
    </b-row>
</template>

<script>
import {DOMAIN_RESET_SSL_STATUS, SITE_CREATE, SITE_GET_ALL, SITE_REQUEST, SITE_UPDATE} from "@/store/actions"
import VarsEditor from "../../components/VarsEditor"
import SiteDomains from "./SiteDomains"
import Api from '@/services/Api'

export default {
    name: "EditSite",
    components: {VarsEditor, SiteDomains},
    data() {
        return {
            tabIndex: 0,
            action: SITE_CREATE,
            caption: this.$route.name + ' site',
            disableControl: false,
            statusInProcess: false,
            isFirstWarning: true,
            isInitialized: false,  // Flag to track if component is fully loaded
            validation: {alias: {valid: null, message: ''}},
            sites: [],
            site: {
                site_id: '',
                site_alias: '',
                site_name: '',
                vars: [],
                domains: [],
            }
        }
    },
    computed: {
        hostname: {
            get() {
                let server_name = this.$store.state.server.server_name;
                let alias = this.site.site_alias.replace(/\s+/g, '-').toLowerCase();
                if (!alias) return
                return alias + '.' + server_name;
            }
        },

    },
    watch: {
        'site.site_alias'(newVal) {
            this.validateAlias(newVal)
        },
        hostname(newVal) {
            if (!this.site.site_id) this.addReadOnlyDomain(newVal)
        }
    },

    created() {
        this.loadSite()
    },

    methods: {
        checkDomainInProcess() {
            // Only disable save button for critical SSL operations, not routine status checks
            // Look for domains that are actually being processed for SSL certificate operations
            const criticalProcessingDomains = this.site.domains.filter(domain => {
                const sslStatus = domain['domain_ssl_status'];
                // Only consider these as critical processing states that should disable save
                return sslStatus === 'PROCESSING_CERTIFICATE' ||
                       sslStatus === 'INSTALLING_CERTIFICATE' ||
                       sslStatus === 'VALIDATING_DOMAIN';
            });

            this.statusInProcess = criticalProcessingDomains.length > 0;

            // Debug logging
            console.log('🔧 DEBUG: checkDomainInProcess', {
                statusInProcess: this.statusInProcess,
                disableControl: this.disableControl,
                criticalProcessingDomains: criticalProcessingDomains.map(d => ({
                    name: d.domain_name,
                    ssl_status: d.domain_ssl_status
                })),
                allDomains: this.site.domains.map(d => ({
                    name: d.domain_name,
                    ssl_status: d.domain_ssl_status
                }))
            });

            if (this.isFirstWarning && this.statusInProcess) {
                this.isFirstWarning = false
                this.$awn.warning('You can\'t change site settings while domain SSL certificate is being processed. Please wait until the process is over.')
            }
        },
        loadSite() {
            this.isInitialized = false;  // Reset initialization flag
            this.action = SITE_CREATE
            if (this.$route.params['site_id']) {
                this.action = SITE_UPDATE
                this.$store.dispatch(SITE_REQUEST, this.$route.params['site_id'])
                    .then(site => {
                        console.log('🔄 loadSite: Received site data from API:', site);
                        console.log('🔄 loadSite: Domains in response:', site.domains?.map(d => d.domain_name));

                        this.site = site
                        this.checkDomainInProcess();
                        // Set initialized flag after site data is loaded
                        this.$nextTick(() => {
                            this.isInitialized = true;
                            console.log('🔄 loadSite: Site data updated, domains now:', this.site.domains?.map(d => d.domain_name));
                        });
                    }).catch((error) => {
                        console.error('🔄 loadSite: Error loading site data:', error);
                    }
                )
            }
            if (this.action === SITE_CREATE) {
                this.loadSites()
                this.isInitialized = true;  // For new sites, set immediately
            }
        },
        loadSites() {
            this.$store.dispatch(SITE_GET_ALL, this.site).then(sites => this.sites = sites)
        },
        async onSave() {
            if (this.validation.alias.valid === false) {
                this.tabIndex = 0;
                return
            }
            this.disableControl = true

            try {
                // Debug: Log the site data being sent to backend
                console.log('🔧 DEBUG: Site data being sent to backend:', this.site);
                console.log('🔧 DEBUG: Domains in site data:', this.site.domains);
                if (this.site.domains) {
                    this.site.domains.forEach((domain, index) => {
                        console.log(`🔧 DEBUG: Domain ${index}:`, {
                            domain_id: domain.domain_id,
                            domain_name: domain.domain_name,
                            domain_ismail: domain.domain_ismail,
                            all_keys: Object.keys(domain)
                        });
                    });
                }

                const updatedSiteData = await this.$store.dispatch(this.action, this.site);

                // Update site data with the response from API (includes updated domains)
                if (updatedSiteData && updatedSiteData.domains) {
                    console.log('🔄 Updating site with latest data from API:', updatedSiteData);
                    console.log('📋 Updated domains from API:', updatedSiteData.domains);
                    console.log('📋 Current site.domains before update:', this.site.domains);

                    // Directly update site.domains with fresh API data
                    this.site.domains = updatedSiteData.domains.map(domain => {
                        // Ensure domain_name is a string, not an array or object
                        let domainName = domain.domain_name;
                        console.log('🔍 Raw domain name from API:', domainName, typeof domainName);

                        if (Array.isArray(domainName)) {
                            domainName = domainName[0]; // Take first element if it's an array
                            console.log('🔧 Extracted from array:', domainName);
                        } else if (typeof domainName === 'object' && domainName !== null) {
                            domainName = domainName.domain_name || domainName.name || String(domainName);
                            console.log('🔧 Extracted from object:', domainName);
                        }

                        // Ensure it's a string
                        domainName = String(domainName);
                        console.log('🔧 Final processed domain name:', domainName);

                        return {
                            domain_id: domain.domain_id,
                            domain_name: domainName,
                            domain_ismail: parseInt(domain.domain_ismail) || 0,
                            domain_isssl: parseInt(domain.domain_isssl) || 0,
                            domain_isletsencrypt: parseInt(domain.domain_isletsencrypt) || 0,
                            domain_ismain: parseInt(domain.domain_ismain) || 0,
                            domain_isdefault: parseInt(domain.domain_isdefault) || 0,
                            is_readonly: parseInt(domain.is_readonly) || 0,
                            is_new: false,
                            ...domain,
                            domain_name: domainName // Override with cleaned domain name
                        };
                    });

                    console.log('📋 Site.domains after direct update:', this.site.domains);
                }

                // Process DNS updates for domains with auto-management enabled
                if (this.$refs.siteDomains) {
                    await this.$refs.siteDomains.processDnsUpdates();
                    this.$refs.siteDomains.showPendingMailNotification();
                    this.$refs.siteDomains.showPendingSslNotification();
                }

                this.$router.push({name: 'All'})
            } catch (error) {
                this.disableControl = false
                if (error.message) {
                    this.$awn.alert(error.message)
                } else {
                    console.error(error)
                    this.$awn.alert('Unknown exception. See console log.')
                }
            }
        },

        async onSaveOnly() {
            if (this.validation.alias.valid === false) {
                this.tabIndex = 0;
                return
            }
            this.disableControl = true

            try {
                // Debug: Log the site data being sent to backend
                console.log('🔧 DEBUG: Site data being sent to backend (Save Only):', this.site);
                console.log('🔧 DEBUG: Domains in site data (Save Only):', this.site.domains);
                if (this.site.domains) {
                    this.site.domains.forEach((domain, index) => {
                        console.log(`🔧 DEBUG: Domain ${index}:`, {
                            domain_id: domain.domain_id,
                            domain_name: domain.domain_name,
                            domain_ismail: domain.domain_ismail,
                            all_keys: Object.keys(domain)
                        });
                    });
                }

                const updatedSiteData = await this.$store.dispatch(this.action, this.site);

                // Update site data with the response from API (includes updated domains)
                if (updatedSiteData && updatedSiteData.domains) {
                    console.log('🔄 Updating site with latest data from API:', updatedSiteData);
                    console.log('📋 Updated domains from API:', updatedSiteData.domains);
                    console.log('📋 Current site.domains before update:', this.site.domains);

                    // Directly update site.domains with fresh API data
                    this.site.domains = updatedSiteData.domains.map(domain => {
                        // Ensure domain_name is a string, not an array or object
                        let domainName = domain.domain_name;
                        console.log('🔍 Raw domain name from API:', domainName, typeof domainName);

                        if (Array.isArray(domainName)) {
                            domainName = domainName[0]; // Take first element if it's an array
                            console.log('🔧 Extracted from array:', domainName);
                        } else if (typeof domainName === 'object' && domainName !== null) {
                            domainName = domainName.domain_name || domainName.name || String(domainName);
                            console.log('🔧 Extracted from object:', domainName);
                        }

                        // Ensure it's a string
                        domainName = String(domainName);
                        console.log('🔧 Final processed domain name:', domainName);

                        return {
                            domain_id: domain.domain_id,
                            domain_name: domainName,
                            domain_ismail: parseInt(domain.domain_ismail) || 0,
                            domain_isssl: parseInt(domain.domain_isssl) || 0,
                            domain_isletsencrypt: parseInt(domain.domain_isletsencrypt) || 0,
                            domain_ismain: parseInt(domain.domain_ismain) || 0,
                            domain_isdefault: parseInt(domain.domain_isdefault) || 0,
                            is_readonly: parseInt(domain.is_readonly) || 0,
                            is_new: false,
                            ...domain,
                            domain_name: domainName // Override with cleaned domain name
                        };
                    });

                    console.log('📋 Site.domains after direct update:', this.site.domains);
                }

                // Process DNS updates for domains with auto-management enabled
                if (this.$refs.siteDomains) {
                    await this.$refs.siteDomains.processDnsUpdates();
                    this.$refs.siteDomains.showPendingMailNotification();
                    this.$refs.siteDomains.showPendingSslNotification();
                }

                // Show success message and stay on page
                this.$bvToast.toast('Site saved successfully', {
                    title: 'Success',
                    variant: 'success',
                    solid: true
                });

                this.disableControl = false;
            } catch (error) {
                this.disableControl = false
                if (error.message) {
                    this.$awn.alert(error.message)
                } else {
                    console.error(error)
                    this.$awn.alert('Unknown exception. See console log.')
                }
            }
        },
        onCancel() {
            this.$router.push({name: 'All'})
        },
        onRefresh() {
            this.loadSite()
        },
        addReadOnlyDomain(sitehost) {
            //only for new site
            if (!this.site.site_id) {

                let i = -1;
                i = this.site.domains.findIndex(item => {
                    return (item.idInt === 99999)
                })

                if (i < 0) {
                    this.site.domains.map(item => {
                        item['domain_ismain'] = 0
                    })

                    this.site.domains.push({
                        idInt: 99999,
                        domain_id: null,
                        domain_name: sitehost,
                        domain_ismain: 1,
                        domain_isdefault: 1,
                        domain_isssl: 1,
                        domain_isletsencrypt: 1,
                        is_readonly: 1,
                        ssl: {
                            cert: '',
                            private_key: '',
                            CA: ''
                        }
                    })
                } else {
                    this.site.domains[i].domain_name = sitehost;
                }
            }
        },
        onDomainResetSslStatus(domain) {
            this.$store.dispatch(DOMAIN_RESET_SSL_STATUS, domain.domain_id).then(() => this.loadSite())
        },
        onDomainsChange(domains) {
            // Update local domains only - no auto-save
            this.site.domains = domains;
            // Check if any domains are in processing state to update save button
            this.checkDomainInProcess();
        },
        onRefreshDomains() {
            // Refresh the entire site to get updated domains from database
            console.log('🔄 onRefreshDomains: Starting domain refresh after database save');
            console.log('🔄 Current domains before refresh:', this.site.domains?.map(d => d.domain_name));

            // Add a small delay to ensure database transaction is committed
            setTimeout(() => {
                console.log('🔄 Calling loadSite() to refresh from API...');
                this.loadSite();
            }, 500);
        },
        validateAlias(alias) {
            if (this.site.site_id) return
            Api.get('/site/checkAliasUnique', {params: {alias}}).then(data => {
                this.validation.alias = Object.assign({}, data.data)
            })
        }
    }
}
</script>

<style scoped>
.tab-pane {
    min-height: 320px;
}
</style>
