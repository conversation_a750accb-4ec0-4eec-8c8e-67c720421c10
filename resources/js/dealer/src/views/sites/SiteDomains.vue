<template>
    <div>
        <b-row>
            <b-col>
                <b-alert variant="warning" :show="showMailWarning">
                    Pay attention that removing a domain with mail or
                    turning off mail also removes all mailboxes belonging to it.
                </b-alert>
            </b-col>
        </b-row>
        <b-row>
            <b-col>
                <b-button-toolbar aria-label="Toolbar with button to manage domains">
                    <b-button-group class="mx-1">
                        <b-btn variant="primary" @click.prevent="onAddDomain">
                            <i class="fa fa-plus mr-1"
                               aria-hidden="true"></i>Add domain
                        </b-btn>
                    </b-button-group>
                    <b-button-group class="mx-1">
                        <b-btn variant="outline-secondary"
                               @click.prevent="refreshAllDnsStatus"
                               :disabled="refreshingDnsStatus">
                            <i class="fa fa-refresh mr-1"
                               :class="{ 'fa-spin': refreshingDnsStatus }"
                               aria-hidden="true"></i>
                            {{ refreshingDnsStatus ? 'Refreshing...' : 'Refresh DNS Status' }}
                        </b-btn>
                    </b-button-group>
                    <b-button-group class="mx-1">
                        <b-btn variant="outline-info"
                               @click.prevent="refreshAllSslStatus"
                               :disabled="refreshingSslStatus">
                            <i class="fa fa-shield mr-1"
                               :class="{ 'fa-spin': refreshingSslStatus }"
                               aria-hidden="true"></i>
                            {{ refreshingSslStatus ? 'Refreshing...' : 'Refresh SSL Status' }}
                        </b-btn>
                    </b-button-group>
                </b-button-toolbar>
            </b-col>
        </b-row>
        <b-row class="mt-3">
            <b-col>
                <v-client-table :name="'Domains'"
                                :data="processedDomains"
                                :columns="columns"
                                class="domain-table"
                                :options="tableOptions"
                                :key="tableKey">
                    <template #domain_name="props">
                        <span :class="{ 'text-muted': props.row.is_readonly==1 }"
                              class="mr-2">{{ props.row.domain_name }}</span>
                        <span v-if="props.row.domain_ismain===1" class="badge badge-danger">main</span>
                        <!--
                                                <span v-if="props.row.domain_isdefault==1" class="badge badge-secondary">default</span>
                        -->
                    </template>
                    <template #domain_edition="props">
                        {{ props.row.domain_edition_id }}
                        <span v-if="props.row.domain_isdefault===1" class="badge badge-info">default</span>
                    </template>

                    <template #domain_ismail="props">
                        <div class="d-flex align-items-center">
                            <!-- Always show mail toggle for main level domains -->
                            <template v-if="isMainLevelDomain(props.row.domain_name)">
                                <c-switch
                                    class="mx-1"
                                    :class="getMailSwitchClass(props.row.domain_id)"
                                    :color="getMailSwitchColor(props.row.domain_id)"
                                    :model-checked="+props.row.domain_ismail"
                                    @change="onSwitchMail(props.row, $event)"
                                    :disabled="!canToggleMail(props.row.domain_id)"
                                    label
                                    size="sm"
                                    dataOn="yes"
                                    dataOff="no"/>
                            </template>
                            <template v-else>
                                <span class="text-muted">N/A</span>
                            </template>
                        </div>
                    </template>

                    <template #ssl="props">
                        <div class="d-flex align-items-center flex-wrap">
                            <!-- SSL Status Display - Similar to DNS Status -->
                            <b-btn
                                :disabled="ssl_loading[props.row.domain_id]"
                                size="sm"
                                :variant="getSslStatusVariant(ssl_status[props.row.domain_id])"
                                @click.prevent="checkSslStatus(props.row, false)"
                                class="mr-2 mb-1"
                                :title="getSslStatusTitle(props.row.domain_id)">
                                <template v-if="ssl_loading[props.row.domain_id]">
                                    <i class="fa fa-spinner fa-spin"></i>
                                </template>
                                <template v-else-if="ssl_status[props.row.domain_id] === 'Valid'">
                                    <i class="fa fa-check-circle"></i>
                                </template>
                                <template v-else-if="ssl_status[props.row.domain_id] === 'Expiring Soon'">
                                    <i class="fa fa-exclamation-triangle"></i>
                                </template>
                                <template v-else-if="ssl_status[props.row.domain_id] === 'Expired'">
                                    <i class="fa fa-times-circle"></i>
                                </template>
                                <template v-else-if="ssl_status[props.row.domain_id] === 'Invalid'">
                                    <i class="fa fa-times-circle"></i>
                                </template>
                                <template v-else-if="ssl_status[props.row.domain_id] === 'No SSL'">
                                    <i class="fa fa-minus-circle"></i>
                                </template>
                                <template v-else-if="ssl_status[props.row.domain_id] === 'Error'">
                                    <i class="fa fa-exclamation-circle"></i>
                                </template>
                                <template v-else-if="!+props.row.domain_isssl">
                                    <i class="fa fa-minus-circle"></i>
                                </template>
                                <template v-else>
                                    <i class="fa fa-question-circle"></i>
                                </template>
                            </b-btn>

                            <!-- SSL Toggle Switch - Show when SSL has errors or is disabled -->
                            <div v-if="shouldShowSslToggle(props.row)" class="ml-2">
                                <c-switch
                                    class="mx-1"
                                    color="success"
                                    :model-checked="+props.row.domain_isssl"
                                    @change="onToggleSsl(props.row, $event)"
                                    :disabled="ssl_loading[props.row.domain_id]"
                                    label
                                    size="sm"
                                    dataOn="yes"
                                    dataOff="no"
                                />
                            </div>
                        </div>
                    </template>



                    <template #status="props">
                        <div class="d-flex align-items-center flex-wrap">
                            <!-- DNS Status Display - Icon Only - FIRST -->
                            <b-btn
                                :disabled="dns_loading[props.row.domain_id]"
                                size="sm"
                                :variant="getDnsStatusVariant(dns_status[props.row.domain_id])"
                                @click.prevent="checkDnsStatus(props.row, false)"
                                class="mr-2 mb-1"
                                :title="getDnsStatusTitle(props.row.domain_id)">
                                <template v-if="dns_loading[props.row.domain_id]">
                                    <i class="fa fa-spinner fa-spin"></i>
                                </template>
                                <template v-else-if="!dns_status[props.row.domain_id]">
                                    <i class="fa fa-question-circle"></i>
                                </template>
                                <template v-else-if="dns_status[props.row.domain_id] === 'OK'">
                                    <i class="fa fa-check-circle"></i>
                                </template>
                                <template v-else-if="dns_status[props.row.domain_id] === 'Update DNS'">
                                    <i class="fa fa-exclamation-triangle"></i>
                                </template>
                                <template v-else-if="dns_status[props.row.domain_id] === 'Error'">
                                    <i class="fa fa-times-circle"></i>
                                </template>
                                <template v-else-if="dns_status[props.row.domain_id] === 'Check DNS'">
                                    <i class="fa fa-question-circle"></i>
                                </template>
                                <template v-else>
                                    <i class="fa fa-question-circle"></i>
                                </template>
                            </b-btn>

                            <!-- DNS Management Toggle (show for any DNS error status that can be managed) - SECOND -->
                            <template v-if="isDnsStatusError(props.row.domain_id) && dns_status[props.row.domain_id] !== 'Error'">
                                <div class="d-flex align-items-center mr-2 mb-1">
                                    <c-switch
                                        class="mx-1"
                                        color="success"
                                        :model-checked="getDnsManagementStatus(props.row.domain_id)"
                                        @change="onToggleDnsManagement(props.row, $event)"
                                        :disabled="dns_updating[props.row.domain_id]"
                                        label
                                        size="sm"
                                        dataOn="yes"
                                        dataOff="no"
                                    />
                                </div>
                            </template>
                        </div>
                    </template>

                    <template #edit="props">
                        <b-btn-group v-if="props.row.domain_id" size="sm">
                            <b-btn @click="onEditDomain(props.row)"
                                   variant="light"
                                   :disabled="isDnsStatusError(props.row.domain_id)">
                                <i class="icons font-1xl cui-pencil"></i>
                            </b-btn>
                            <b-dropdown id="ddown-right" right text="" variant="light" size="sm">
                                <b-dropdown-item href="#" @click="onSetMainDomain(props.row)"
                                                 :disabled="props.row.domain_ismain===1">
                                    <i class="fa fa-globe" aria-hidden="true"></i>
                                    Set as main domain
                                </b-dropdown-item>
                                <!--<b-dropdown-item href="#" @click="onSetDefaultDomain(props.row)"
                                                 :disabled="true">
                                    <i class="fa fa-globe" aria-hidden="true"></i>
                                    Set as default domain
                                </b-dropdown-item>-->
                                <b-dropdown-item href="#" @click="onDeleteDomain(props.row)"
                                                 variant="danger"
                                                 :disabled="props.row.is_readonly===1 || props.row.domain_ismain===1">
                                    <i class="fa fa-trash-o" aria-hidden="true"></i>Delete
                                </b-dropdown-item>

                                <b-dropdown-item @click="onResetSllStatus(props.row)"
                                                 variant="danger">
                                    <i class="fa fa-repeat" aria-hidden="true"></i>Reset SSL status
                                </b-dropdown-item>
                            </b-dropdown>
                            <!--<b-btn @click="onDeleteDomain(props.row)" variant="outline-danger"><i
                                class="fa fa-trash-o" aria-hidden="true"></i></b-btn>-->
                        </b-btn-group>

                    </template>
                </v-client-table>
            </b-col>
        </b-row>
    </div>
</template>

<script>
import {Switch as cSwitch} from '@coreui/vue'
import Api from "@/services/Api";
import DomainEditDialog from "@/components/dialogs/DomainEditDialog.vue";

export default {
    name: "Domains",
    components: {cSwitch},
    model: {
        prop: 'domains',
        event: 'change'
    },
    props: {
        action: String,
        domains: {
            type: Array,
            default: () => [],
            required: true,
        },
        siteId: {
            type: [String, Number],
            required: true
        }
    },
    data() {
        return {
            dns_status: {},
            dns_loading: {}, // Individual loading state for each domain
            dns_management: {}, // Track which domains have auto DNS management enabled
            dns_updating: {}, // Track DNS update operations
            updating_dns: {},
            mx_info: {}, // Store MX record information for each domain
            ssl_status: {}, // Store SSL status for each domain
            ssl_loading: {}, // Individual loading state for SSL checks
            showMailWarning: false,
            pendingMailNotification: null,
            pendingSslNotification: null,
            refreshingDnsStatus: false,
            refreshingSslStatus: false,
            isTableUpdating: false, // Flag to prevent notifications during table operations
            tableKey: 0, // Force table re-render when domains change
            id: 0,
            columns: [
                'domain_id',
                'domain_edition',
                'domain_name',
                'status',
                'domain_ismail',
                'ssl',
                'edit'
            ],
            options: {
                sortable: [
                    'domain_id',
                    'domain_edition',
                    'domain_name'
                ],
                filterable: [
                    'domain_id',
                    'domain_name'
                ],
                skin: "table table-striped table-bordered table-hover",
                uniqueKey: 'domain_id',
                headings: {
                    'domain_id': 'ID',
                    'domain_edition': 'Edition',
                    'domain_name': 'Domain Name',
                    'status': 'DNS Status',
                    'domain_ismail': 'Mail Status',
                    'ssl': 'SSL Status',
                    'edit': ''
                },
                rowClassCallback: function(row) {
                    const dnsStatus = this.dns_status[row.domain_id];
                    return dnsStatus && dnsStatus !== 'OK' && dnsStatus !== '...' ? 'dns-status-error' : '';
                }.bind(this)
            }
        }
    },

    computed: {
        processedDomains() {
            console.log('🔍 === PROCESSED DOMAINS COMPUTED PROPERTY ===');
            console.log('🔍 Raw domains array:', this.domains);
            console.log('🔍 Raw domains count:', this.domains.length);

            const processed = this.domains.map((domain, index) => {
                console.log(`🔍 Processing domain ${index}:`, domain);
                console.log(`🔍 Domain ${index} domain_name:`, domain.domain_name, typeof domain.domain_name);
                console.log(`🔍 Domain ${index} domain_id:`, domain.domain_id);
                console.log(`🔍 Domain ${index} _temp_id:`, domain._temp_id);

                const processed = {
                    ...domain,
                    _unique_key: domain.domain_id || domain._temp_id || `temp_${Math.random()}`
                };

                console.log(`🔍 Processed domain ${index}:`, processed);
                console.log(`🔍 Processed domain ${index} domain_name:`, processed.domain_name, typeof processed.domain_name);

                return processed;
            });

            console.log('🔍 Final processed domains:', processed);
            console.log('🔍 Final processed domains count:', processed.length);

            return processed;
        },

        tableOptions() {
            return {
                ...this.options,
                uniqueKey: '_unique_key', // Use our computed unique key
                rowClassCallback: (row) => {
                    const dnsStatus = this.dns_status[row.domain_id];
                    return dnsStatus && dnsStatus !== 'OK' && dnsStatus !== '...' ? 'dns-status-error' : '';
                }
            }
        }
    },

    watch: {
        domains: {
            handler(newDomains) {
                // Skip status checks if table is being updated (e.g., during deletion)
                if (this.isTableUpdating) {
                    return;
                }

                // Set flag to prevent notifications during table updates
                this.isTableUpdating = true;

                if (newDomains && newDomains.length > 0) {
                    this.$nextTick(() => {
                        this.checkAllDnsStatuses();
                        this.checkAllSslStatuses();
                        // Clear the flag after table operations are complete
                        setTimeout(() => {
                            this.isTableUpdating = false;
                        }, 100);
                    })
                } else {
                    // Clear the flag if no domains
                    setTimeout(() => {
                        this.isTableUpdating = false;
                    }, 100);
                }
            },
            immediate: true
        }
    },

    created() {
        // Ensure loading states are cleared on component initialization
        this.clearMailToggleLoading();
        // Initialize table updating flag
        this.isTableUpdating = false;
    },

    methods: {
        /**
         * Check if domain is a main level domain (not a subdomain)
         * @param {string} domainName
         * @returns {boolean}
         */
        isMainLevelDomain(domainName) {
            if (!domainName) return false;

            // List of known multi-level TLDs that should be treated as main domains
            const multiLevelTlds = [
                '.co.uk', '.co.nl', '.co.za', '.co.jp', '.co.kr', '.co.in', '.co.au',
                '.com.au', '.com.br', '.com.mx', '.com.ar', '.com.co', '.com.pe',
                '.net.au', '.org.uk', '.org.au', '.gov.uk', '.gov.au', '.edu.au',
                '.ac.uk', '.ac.za', '.ac.in', '.ac.jp', '.ac.kr', '.ac.th',
                '.or.jp', '.or.kr', '.or.th', '.ne.jp', '.ne.kr',
                '.web.za', '.web.tr', '.nom.es', '.nom.fr'
            ];

            // Check if domain ends with any multi-level TLD
            const lowerDomain = domainName.toLowerCase();
            for (const tld of multiLevelTlds) {
                if (lowerDomain.endsWith(tld)) {
                    // For multi-level TLD, check if there's only one part before the TLD
                    // e.g., "onlinedrogist.co.nl" should be main, "sub.onlinedrogist.co.nl" should be subdomain
                    const beforeTld = lowerDomain.substring(0, lowerDomain.length - tld.length);
                    const dotCount = (beforeTld.match(/\./g) || []).length;
                    return dotCount === 0; // No dots before the multi-level TLD = main domain
                }
            }

            // For regular TLDs, count dots
            // Main level domains have 1 dot (e.g., example.com)
            // Subdomains have 2 or more dots (e.g., sub.example.com, www.example.com)
            const dotCount = (domainName.match(/\./g) || []).length;
            return dotCount === 1;
        },

        /**
         * Check if domain has DNS status error (not OK)
         * @param {number} domainId
         * @returns {boolean}
         */
        isDnsStatusError(domainId) {
            const dnsStatus = this.dns_status[domainId];
            return dnsStatus && dnsStatus !== 'OK' && dnsStatus !== '...';
        },

        /**
         * Get DNS management status for domain
         * @param {number} domainId
         * @returns {boolean}
         */
        getDnsManagementStatus(domainId) {
            return this.dns_management[domainId] || false;
        },

        /**
         * Toggle DNS management for domain
         * @param {object} row
         * @param {boolean} enabled
         */
        onToggleDnsManagement(row, enabled) {
            this.$set(this.dns_management, row.domain_id, enabled);

            // Show notification about what will happen on save

        },

        /**
         * Check if SSL toggle should be shown for this domain
         * @param {object} row
         * @returns {boolean}
         */
        shouldShowSslToggle(row) {
            const sslStatus = this.ssl_status[row.domain_id];

            // Show toggle if:
            // 1. SSL is disabled (!+row.domain_isssl)
            // 2. SSL has errors (Error, Invalid, Expired, No SSL)
            // 3. SSL is not currently loading
            return !this.ssl_loading[row.domain_id] && (
                !+row.domain_isssl ||
                sslStatus === 'Error' ||
                sslStatus === 'Invalid' ||
                sslStatus === 'Expired' ||
                sslStatus === 'No SSL'
            );
        },

        /**
         * Toggle SSL for domain (reuses existing modal functionality)
         * @param {object} row
         * @param {boolean} enabled
         */
        onToggleSsl(row, enabled) {
            // Prevent execution during table operations (sorting, etc.)
            if (!row || !row.domain_id || enabled === undefined || enabled === null) {
                return;
            }

            // Skip if table is being updated programmatically
            if (this.isTableUpdating) {
                return;
            }

            // Find the domain in the domains array
            const domainIndex = this.findDomain(row);
            if (domainIndex === -1) return;

            // Check if this is the current value (no actual change) - prevents firing during table re-renders
            const currentSslValue = enabled ? 1 : 0;
            if (this.domains[domainIndex].domain_isssl === currentSslValue) {
                return; // No actual change, likely a table re-render
            }

            // Update the domain object directly (same as mail toggle pattern)
            this.domains[domainIndex].domain_isssl = currentSslValue;

            // If enabling SSL, also enable Let's Encrypt by default (same as modal)
            if (enabled) {
                this.domains[domainIndex].domain_isletsencrypt = 1;
            }

            // Store pending notification (same pattern as mail toggle)
            if (this.$parent && this.$parent.isInitialized && row && row.domain_name) {
                const action = enabled ? 'enabled' : 'disabled';
                this.pendingSslNotification = {
                    domain: row.domain_name,
                    action: action,
                    userTriggered: true,
                    timestamp: Date.now()
                };
            }

            // Do NOT emit change immediately to prevent infinite loops
            // Changes will be saved when Save button is clicked
        },

        async onAddDomain() {
            let response = await this.$dialogs.show(DomainEditDialog, {
                title: 'Add domain',
                domain: {},
                domains: this.domains,
                siteId: this.siteId
            })

            // 🔍 Console log the complete response for debugging
            console.log('🔍 onAddDomain response:', response);
            console.log('🔍 response.trigger:', response.trigger);
            console.log('🔍 response.value:', response.value);
            console.log('🔍 response.value.domainAlreadySaved:', response.value?.domainAlreadySaved);

            if (response.trigger !== 'ok') return

            // Check if domain was already saved to database via setupDomainComplete
            if (response.value && response.value.domainAlreadySaved) {
                console.log('Domain was already saved to database, refreshing domains list');

                // Clear DNS cache for the new domain to force fresh status check
              //  if (response.value.domainData && response.value.domainData.domain_name) {
                    this.clearDnsCacheForDomain(response.value.domainData.domain_name);
                    console.log('🔧 Cleared DNS cache for new domain:', response.value.domainData.domain_name);
               // }

                // Domain was already saved, just refresh the parent to reload domains
                this.$emit('refresh-domains');
                return;
            }

            // Traditional flow: add domain to local array (will be saved when site is saved)
            if (response.value) {
                // Clear DNS cache for the new domain to force fresh status check
                if (response.value.domain_name) {
                    this.clearDnsCacheForDomain(response.value.domain_name);
                    console.log('🔧 Cleared DNS cache for new domain:', response.value.domain_name);
                }

                this.$emit('change', [...this.domains, response.value])
            } else {
                console.warn('No domain data returned from dialog');
            }

        },
        async onEditDomain(domain) {
            // Check if DNS status allows editing
            const dnsStatus = this.dns_status[domain.domain_id];
            if (dnsStatus && dnsStatus !== 'OK' && dnsStatus !== '...') {
                this.$bvToast.toast('Please fix DNS issues before editing this domain', {
                    title: 'DNS Error',
                    variant: 'warning',
                    solid: true
                });
                return;
            }

            let response = await this.$dialogs.show(DomainEditDialog, {
                title: 'Edit domain',
                domain: domain,
                domains: this.domains
            })
            if (response.trigger !== 'ok') return
            const domains = [...this.domains];
            this.$set(domains,
                domains.findIndex(item => item.domain_id === response.value.domain_id),
                response.value)
            this.$emit('change', domains)
        },
        onDeleteDomain(domain) {
            console.log('🗑️ === DOMAIN DELETION STARTED ===');
            console.log('🗑️ Domain to delete:', domain);
            console.log('🗑️ Current domains array:', this.domains);

            // Set flag to prevent status checks during deletion
            this.isTableUpdating = true;

            let i = this.findDomain(domain)
            console.log('🗑️ Domain index found:', i);

            const updatedDomains = [...this.domains];
            updatedDomains.splice(i, 1);

            console.log('🗑️ Updated domains array after deletion:', updatedDomains);
            console.log('🗑️ Emitting change event with updated domains');

            this.$emit('change', updatedDomains);
            this.showMailWarning = true;

            // Clear the flag after deletion is complete (shorter timeout)
            this.$nextTick(() => {
                setTimeout(() => {
                    this.isTableUpdating = false;
                }, 100);
            });
        },
        findDomain(domain) {
            return this.domains.findIndex(item => {
                return (item.domain_id === domain.domain_id && domain.domain_id !== null) || (item.idInt === domain.idInt && domain.idInt)
            })
        },
        onSetMainDomain(domain) {
            const updatedDomains = this.domains.map(d => ({
                ...d,
                domain_ismain: 0
            }));
            let i = this.findDomain(domain)
            updatedDomains[i].domain_ismain = 1;
            this.$emit('change', updatedDomains);
        },
        onSetDefaultDomain(domain) {
            this.domains.map(domain => {
                domain.domain_isdefault = 0
            })
            let i = this.findDomain(domain)
            this.domains[i].domain_isdefault = 1;
        },
        async onSwitchMail(domainValue, value) {
            // Prevent execution during table operations (sorting, etc.)
            // Only allow actual user interactions
            if (!domainValue || !domainValue.domain_id || value === undefined || value === null) {
                return;
            }

            // Convert boolean to integer (true -> 1, false -> 0)
            const numericValue = value ? 1 : 0;

            // Skip if table is being updated programmatically
            if (this.isTableUpdating) {
                return;
            }

            // Check if this is the current value (no actual change) - prevents firing during table re-renders
            let i = this.findDomain(domainValue);
            if (i !== -1 && this.domains[i].domain_ismail === numericValue) {
                return; // No actual change, likely a table re-render
            }

            // Only check DNS status if there's an actual change being made
            // Check if DNS status allows mail changes
            const dnsStatus = this.dns_status[domainValue.domain_id];
            if (dnsStatus && dnsStatus !== 'OK' && dnsStatus !== '...') {
                this.$bvToast.toast('Please fix DNS issues before changing mail settings for this domain', {
                    title: 'DNS Error',
                    variant: 'warning',
                    solid: true
                });
                return;
            }

            // If disabling mail, show confirmation dialog
            if (this.domains[i].domain_ismail === 1 && numericValue === 0) {
                let response = await this.$dialogs.confirmDelete({
                    okTitle: 'CONFIRM',
                    title: 'Disable Domain Mail',
                    message: `Are you sure you want to disable mail for ${domainValue.domain_name}? This will remove all mailboxes belonging to this domain and cannot be undone. Click Save to apply changes.`
                });

                if (response.trigger !== 'ok') {
                    // User cancelled, keep mail enabled
                    return;
                }

                this.showMailWarning = true;
            }

            // Update local state only - no auto-save
            this.domains[i].domain_ismail = numericValue;

            // Store the domain info for notification after manual save
            // Only set notification for actual user interactions (not table sorting/operations)
            if (this.$parent && this.$parent.isInitialized && domainValue && domainValue.domain_name) {
                const action = numericValue ? 'enabled' : 'disabled';
                this.pendingMailNotification = {
                    domain: domainValue.domain_name,
                    action: action,
                    userTriggered: true,  // Flag to indicate this was triggered by user action
                    timestamp: Date.now() // Add timestamp to track when this was set
                };
            }

            // Do NOT emit change to prevent auto-save - changes will be saved when Save button is clicked
        },
        onResetSllStatus(domain) {
            this.$emit('domainResetSslStatus', domain)
        },

        showPendingMailNotification() {
            // Only show notification if it was explicitly triggered by user action (not sorting/table operations)
            if (this.pendingMailNotification &&
                this.pendingMailNotification.userTriggered &&
                this.pendingMailNotification.domain) {
                this.$awn.success(`Mail ${this.pendingMailNotification.action} for ${this.pendingMailNotification.domain}. Changes saved successfully.`);
                this.pendingMailNotification = null;
            }
            // Clear all mail toggle loading states
        },

        showPendingSslNotification() {
            // Only show notification if it was explicitly triggered by user action (not sorting/table operations)
            if (this.pendingSslNotification &&
                this.pendingSslNotification.userTriggered &&
                this.pendingSslNotification.domain) {
                this.$awn.success(`HTTPS ${this.pendingSslNotification.action} for ${this.pendingSslNotification.domain}. Changes saved successfully.`);
                this.pendingSslNotification = null;
            }
            this.clearMailToggleLoading();
        },
        clearMailToggleLoading() {
            // Clear loading state for all domains
            this.mailToggleLoading = {};
        },
        loadDnsStatusFromCache(row) {
            const domainId = row.domain_id;
            const cacheKey = `dns_${domainId}`;

            // Load from cache immediately if available and valid
            if (this.enableDnsCache && this.dns_status_cache[cacheKey]) {
                const cached = this.dns_status_cache[cacheKey];
                const now = Date.now();

                // If cache is still valid (within timeout)
                if (now - cached.timestamp < this.dnsCacheTimeout) {
                    this.$set(this.dns_status, domainId, cached.status);
                    return true; // Cache hit
                }
            }
            return false; // No cache or expired
        },

        getDnsCache() {
            if (!this.enableDnsCache) return {};

            try {
                const cached = localStorage.getItem(this.cacheStorageKey);
                return cached ? JSON.parse(cached) : {};
            } catch (e) {
                console.warn('Failed to load DNS cache from localStorage:', e);
                return {};
            }
        },

        setDnsCache(cache) {
            if (!this.enableDnsCache) return;

            try {
                localStorage.setItem(this.cacheStorageKey, JSON.stringify(cache));
            } catch (e) {
                console.warn('Failed to save DNS cache to localStorage:', e);
            }
        },

        clearDnsCache() {
            try {
                localStorage.removeItem(this.cacheStorageKey);
            } catch (e) {
                console.warn('Failed to clear DNS cache from localStorage:', e);
            }
        },

        clearDnsCacheForDomain(domainName) {
            try {
                // Clear from memory cache
                Object.keys(this.dns_status_cache).forEach(key => {
                    if (key.includes(domainName) || this.dns_status_cache[key].domain_name === domainName) {
                        delete this.dns_status_cache[key];
                    }
                });

                // Clear from localStorage cache
                const cached = localStorage.getItem(this.cacheStorageKey);
                if (cached) {
                    const cacheData = JSON.parse(cached);
                    Object.keys(cacheData).forEach(key => {
                        if (key.includes(domainName) || cacheData[key].domain_name === domainName) {
                            delete cacheData[key];
                        }
                    });
                    localStorage.setItem(this.cacheStorageKey, JSON.stringify(cacheData));
                }

                console.log('🔧 Cleared DNS cache for domain:', domainName);
            } catch (e) {
                console.warn('Failed to clear DNS cache for domain:', domainName, e);
            }
        },

        async checkDnsStatus(row, forceRefresh = false) {
            const domainId = row.domain_id;

            // Skip DNS check for new domains that don't have an ID yet
            if (!domainId) {
                console.log('Skipping DNS check for new domain:', row.domain_name);
                return;
            }

            // Set individual loading state
            this.$set(this.dns_loading, domainId, true);

            try {
                let res = await Api.post('/domain/checkDnsStatus', {
                    domain_name: row.domain_name,
                    force_refresh: forceRefresh
                })
                const status = res.data.status;
                this.$set(this.dns_status, domainId, status);

                // Store MX record information if available
                if (res.data.mx_info) {
                    this.$set(this.mx_info, domainId, res.data.mx_info);
                }
            } catch (err) {
                this.$set(this.dns_status, domainId, 'Error')
                console.error('DNS status check failed:', err)
            } finally {
                // Clear individual loading state
                this.$set(this.dns_loading, domainId, false);
            }
        },
        getDnsStatusVariant(status) {
            switch (status) {
                case 'OK':
                    return 'success'
                case 'Check DNS':
                    return 'warning'
                case 'Update DNS':
                    return 'warning'
                case 'Error':
                    return 'danger'
                default:
                    return 'light'
            }
        },

        getDnsStatusTitle(domainId) {
            const status = this.dns_status[domainId];
            switch (status) {
                case 'OK':
                    return 'DNS configuration is correct - click to recheck'
                case 'Update DNS':
                    return 'DNS needs to be updated - click to refresh'
                case 'Error':
                    return 'DNS Error - Check domain configuration'
                case 'Check DNS':
                    return 'DNS status unknown - click to check'
                default:
                    return 'Click to check DNS status'
            }
        },
        getDnsActionTitle(status) {
            switch (status) {
                case 'OK':
                    return 'Recheck DNS status'
                case 'Check DNS':
                    return 'Recheck DNS status'
                case 'Update DNS':
                    return 'Update DNS records in Cloudflare'
                case 'Error':
                    return 'Recheck DNS status'
                default:
                    return 'Check DNS status'
            }
        },
        async handleDnsAction(row) {
            const currentStatus = this.dns_status[row.domain_id]

            if (currentStatus === 'Update DNS') {
                // Attempt to update DNS records
                await this.updateDnsRecords(row)
            } else {
                // Just recheck status for other cases
                await this.checkDnsStatus(row)
            }
        },
        async updateDnsRecords(row) {
            this.$set(this.updating_dns, row.domain_id, true)
            try {
                let res = await Api.post('/domain/updateDnsRecords', {
                    domain_name: row.domain_name
                })

                if (res.success) {
                    this.$awn.success(`DNS records updated for ${row.domain_name}`)
                    // Recheck status after update
                    await this.checkDnsStatus(row)
                } else {
                    this.$awn.alert(res.message || 'Failed to update DNS records')
                }
            } catch (err) {
                console.error('DNS update failed:', err)
                this.$awn.alert('Failed to update DNS records: ' + (err.message || 'Unknown error'))
            } finally {
                this.$set(this.updating_dns, row.domain_id, false)
            }
        },

        /**
         * Refresh domains table with updated data from site save API response
         */
        refreshDomainsFromSiteData(updatedDomains) {
            console.log('🔄 Refreshing domains table with updated data:', updatedDomains);
            console.log('📋 Current domains before refresh:', this.domains);

            if (!Array.isArray(updatedDomains)) {
                console.warn('⚠️ Updated domains is not an array:', updatedDomains);
                return;
            }

            // Clear any temporary new domains and replace with fresh data from API
            this.newDomains = [];

            // Ensure all domains have proper structure and mark as saved
            const refreshedDomains = updatedDomains.map(domain => {
                console.log('🔍 Processing domain:', domain);

                // Debug: Log the full domain structure
                console.log('🔍 Full domain object from API:', JSON.stringify(domain, null, 2));

                // Ensure domain_name is a string, not an array or object
                let domainName = domain.domain_name;
                console.log('🔍 Raw domain name from API:', domainName, typeof domainName);
                console.log('🔍 Raw domain name JSON:', JSON.stringify(domainName));

                if (Array.isArray(domainName)) {
                    domainName = domainName[0]; // Take first element if it's an array
                    console.log('🔧 Extracted from array:', domainName);
                } else if (typeof domainName === 'object' && domainName !== null) {
                    // Try different extraction methods
                    if (domainName.domain_name) {
                        domainName = domainName.domain_name;
                    } else if (domainName.name) {
                        domainName = domainName.name;
                    } else if (domainName.value) {
                        domainName = domainName.value;
                    } else {
                        // Last resort - convert to string and try to extract
                        const str = String(domainName);
                        console.log('🔧 Object as string:', str);
                        domainName = str;
                    }
                    console.log('🔧 Extracted from object:', domainName);
                }

                // Ensure it's a string and clean it
                domainName = String(domainName).trim();

                // Final validation - ensure domain name is a clean string
                if (typeof domainName !== 'string' || domainName.includes('{') || domainName.includes('[')) {
                    console.error('❌ Domain name from API is still not a clean string:', domainName);
                    // Try to extract from the original domain object
                    domainName = domain.domain_name?.domain_name || domain.domain_name?.name || 'invalid-domain-from-api';
                    console.log('🔧 Using fallback domain name from API:', domainName);
                }

                console.log('🔧 Final processed domain name from API:', domainName);

                return {
                    domain_id: domain.domain_id,
                    domain_name: domainName,
                    domain_ismail: parseInt(domain.domain_ismail) || 0,
                    domain_isssl: parseInt(domain.domain_isssl) || 0,
                    domain_isletsencrypt: parseInt(domain.domain_isletsencrypt) || 0,
                    domain_ismain: parseInt(domain.domain_ismain) || 0,
                    domain_isdefault: parseInt(domain.domain_isdefault) || 0,
                    is_readonly: parseInt(domain.is_readonly) || 0,
                    is_new: false, // Mark all domains as saved (not new anymore)
                    ...domain, // Spread any additional properties
                    domain_name: domainName // Override with cleaned domain name
                };
            });

            console.log('✅ Domains table refreshed with', refreshedDomains.length, 'domains');
            console.log('📋 Refreshed domains data:', refreshedDomains);

            // Emit change event to update parent component's site.domains
            this.$emit('change', refreshedDomains);

            // Trigger status checks for all domains after the data is updated
            this.$nextTick(() => {
                this.checkAllDnsStatuses();
                this.checkAllSslStatuses();
            });
        },

        async checkAllDnsStatuses(forceRefresh = false) {
            if (!this.domains || this.domains.length === 0) return;

            // Send all AJAX requests in parallel for domains with IDs (skip new domains)
            // Backend cache will handle returning cached data or fresh data
            const promises = this.domains
                .filter(domain => domain.domain_id && !domain.is_new)
                .map(domain => this.checkDnsStatus(domain, forceRefresh));

            await Promise.all(promises);
        },

        async checkSslStatus(row, forceRefresh = false) {
            const domainId = row.domain_id;

            // Skip SSL check for new domains that don't have an ID yet
            if (!domainId) {
                console.log('Skipping SSL check for new domain:', row.domain_name);
                return;
            }

            // Set individual loading state
            this.$set(this.ssl_loading, domainId, true);

            try {
                let res = await Api.post('/domain/checkSslStatus', {
                    domain_name: row.domain_name,
                    force_refresh: forceRefresh
                })
                const status = res.data.status;
                this.$set(this.ssl_status, domainId, status);
            } catch (err) {
                this.$set(this.ssl_status, domainId, 'Error')
                console.error('SSL status check failed:', err)
            } finally {
                // Clear individual loading state
                this.$set(this.ssl_loading, domainId, false);
            }
        },

        async checkAllSslStatuses(forceRefresh = false) {
            if (!this.domains || this.domains.length === 0) return;

            // Send all AJAX requests in parallel for domains with IDs (skip new domains)
            const promises = this.domains
                .filter(domain => domain.domain_id && !domain.is_new)
                .map(domain => this.checkSslStatus(domain, forceRefresh));

            await Promise.all(promises);
        },

        async refreshAllDnsStatus() {
            this.refreshingDnsStatus = true;
            try {
                // Clear individual loading states and force refresh from backend (bypass cache)
                this.dns_loading = {};

                await this.checkAllDnsStatuses(true); // force_refresh = true (bypasses backend cache)

                this.$bvToast.toast('DNS status refreshed for all domains (fresh data)', {
                    title: 'DNS Status Updated',
                    variant: 'success',
                    solid: true
                });
            } catch (err) {
                console.error('Failed to refresh DNS status:', err);
                this.$bvToast.toast('Failed to refresh DNS status', {
                    title: 'Error',
                    variant: 'danger',
                    solid: true
                });
            } finally {
                this.refreshingDnsStatus = false;
            }
        },

        async refreshAllSslStatus() {
            this.refreshingSslStatus = true;
            try {
                // Clear individual loading states and force refresh from backend (bypass 10-minute cache)
                this.ssl_loading = {};

                await this.checkAllSslStatuses(true); // force_refresh = true (bypasses backend cache)

                this.$bvToast.toast('SSL status refreshed for all domains (fresh data)', {
                    title: 'SSL Status Updated',
                    variant: 'success',
                    solid: true
                });
            } catch (err) {
                console.error('Failed to refresh SSL status:', err);
                this.$bvToast.toast('Failed to refresh SSL status', {
                    title: 'Error',
                    variant: 'danger',
                    solid: true
                });
            } finally {
                this.refreshingSslStatus = false;
            }
        },

        /**
         * Process DNS updates for domains with auto-management enabled
         */
        async processDnsUpdates() {
            const domainsToUpdate = [];

            // Find domains that have DNS auto-management enabled and need updates
            for (const domain of this.domains) {
                if (domain.domain_id &&
                    this.getDnsManagementStatus(domain.domain_id) &&
                    this.dns_status[domain.domain_id] === 'Update DNS') {
                    domainsToUpdate.push(domain);
                }
            }

            if (domainsToUpdate.length === 0) {
                return; // No DNS updates needed
            }

            // Show progress notification
            this.$bvToast.toast(`Updating DNS for ${domainsToUpdate.length} domain(s)...`, {
                title: 'DNS Update in Progress',
                variant: 'info',
                solid: true,
                noAutoHide: true,
                id: 'dns-update-progress'
            });

            let successCount = 0;
            let errorCount = 0;

            // Process DNS updates in parallel
            const updatePromises = domainsToUpdate.map(async (domain) => {
                this.$set(this.dns_updating, domain.domain_id, true);

                try {
                    const response = await Api.post('/domain/updateDnsRecords', {
                        domain_name: domain.domain_name
                    });

                    if (response.data.success) {
                        successCount++;
                        // Refresh DNS status to show updated state
                        await this.checkDnsStatus(domain, true);
                    } else {
                        errorCount++;
                        console.error('DNS update failed:', response.data.message);
                    }
                } catch (error) {
                    errorCount++;
                    console.error('DNS update error:', error);
                } finally {
                    this.$set(this.dns_updating, domain.domain_id, false);
                }
            });

            await Promise.all(updatePromises);

            // Hide progress notification
            this.$bvToast.hide('dns-update-progress');

            // Show final result
            if (successCount > 0 && errorCount === 0) {
                this.$bvToast.toast(`Successfully updated DNS for ${successCount} domain(s)`, {
                    title: 'DNS Update Complete',
                    variant: 'success',
                    solid: true
                });
            } else if (successCount > 0 && errorCount > 0) {
                this.$bvToast.toast(`Updated ${successCount} domain(s), ${errorCount} failed`, {
                    title: 'DNS Update Partial',
                    variant: 'warning',
                    solid: true
                });
            } else if (errorCount > 0) {
                this.$bvToast.toast(`Failed to update DNS for ${errorCount} domain(s)`, {
                    title: 'DNS Update Failed',
                    variant: 'danger',
                    solid: true
                });
            }
        },

        /**
         * Check if domain should show mail toggle based on MX records
         * @param {number} domainId
         * @returns {boolean}
         */
        shouldShowMailToggle(domainId) {
            const mxInfo = this.mx_info[domainId];
            return mxInfo && mxInfo.show_mail_toggle === true;
        },

        /**
         * Check if domain has MX records
         * @param {number} domainId
         * @returns {boolean}
         */
        hasMxRecords(domainId) {
            const mxInfo = this.mx_info[domainId];
            return mxInfo && mxInfo.has_mx_records === true;
        },

        /**
         * Get mail switch color based on MX configuration
         * @param {number} domainId
         * @returns {string}
         */
        getMailSwitchColor(domainId) {
            const mxInfo = this.mx_info[domainId];

            if (!mxInfo || !mxInfo.has_mx_records) {
                // No MX records - use default color (blanco/white)
                return 'secondary';
            }

            if (mxInfo.mx_points_to_our_ip) {
                // MX points to our server - green
                return 'success';
            }

            // MX points to different IP - green but will be blurred
            return 'success';
        },

        /**
         * Get mail switch CSS class for styling
         * @param {number} domainId
         * @returns {string}
         */
        getMailSwitchClass(domainId) {
            const mxInfo = this.mx_info[domainId];

            if (mxInfo && mxInfo.has_mx_records && !mxInfo.mx_points_to_our_ip) {
                // MX points to different IP - add blur effect
                return 'mail-switch-blurred';
            }

            return '';
        },

        /**
         * Check if mail can be toggled based on MX configuration
         * @param {number} domainId
         * @returns {boolean}
         */
        canToggleMail(domainId) {
            const mxInfo = this.mx_info[domainId];

            // Allow toggling if:
            // 1. No MX records exist (user can enable mail)
            // 2. MX records point to our server (already configured correctly)
            // Disable only if MX records point elsewhere (conflict)
            if (!mxInfo) {
                return true; // No MX info yet, allow toggling
            }

            if (!mxInfo.has_mx_records) {
                return true; // No MX records, user can enable mail
            }

            // Has MX records - only allow if they point to our server
            return mxInfo.mx_points_to_our_ip === true;
        },



        /**
         * Get SSL status button variant (color)
         * @param {string} status
         * @returns {string}
         */
        getSslStatusVariant(status) {
            switch (status) {
                case 'Valid':
                    return 'success';
                case 'Expiring Soon':
                    return 'warning';
                case 'Expired':
                case 'Invalid':
                    return 'danger';
                case 'No SSL':
                    return 'secondary';
                case 'Error':
                    return 'danger';
                default:
                    return 'outline-secondary';
            }
        },

        /**
         * Get SSL status button title (tooltip)
         * @param {number} domainId
         * @returns {string}
         */
        getSslStatusTitle(domainId) {
            const status = this.ssl_status[domainId];
            if (!status) {
                return 'Click to check SSL status';
            }

            switch (status) {
                case 'Valid':
                    return 'SSL certificate is valid';
                case 'Expiring Soon':
                    return 'SSL certificate expires soon';
                case 'Expired':
                    return 'SSL certificate has expired';
                case 'Invalid':
                    return 'SSL certificate is invalid';
                case 'No SSL':
                    return 'No SSL certificate found';
                case 'Error':
                    return 'Error checking SSL certificate';
                default:
                    return 'Click to check SSL status';
            }
        }
    },

}
</script>

<style scoped lang="scss">
.domain-table {
    .table-sm th, .table-sm td {
        vertical-align: middle;

        .switch {
            vertical-align: middle;
        }
    }

    // Minimal styling for DNS status errors - no row blurring
    ::v-deep .dns-status-error {
        // Only disable the edit button when DNS has errors
        .btn:has(.cui-pencil) {
            opacity: 0.5;
            pointer-events: none;
            cursor: not-allowed;
        }
    }
}

// Mail switch styling
.mail-switch-blurred {
    filter: blur(1px);
    opacity: 0.7;
}
</style>
