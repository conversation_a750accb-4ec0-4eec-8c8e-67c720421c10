<template>
    <b-row class="animated fadeIn">
        <b-col cols="12" xl="8">
            <b-form @submit.prevent="save">
                <b-card header="Edit" show-footer footer-class="d-flex justify-content-end">
                    <b-form-group label="Command">
                        <x-select
                            v-model="schedule.command"
                            :options="commandsOptions"
                            id-key="name"
                            label-key="description"
                            db-field="command"
                            @select="onSelectCommand">
                            <template
                                #option-label="{ node, labelClassName }">
                                <label :class="labelClassName">
                                    <span class="font-weight-bold">{{ node.id }}</span> - {{ node.label }}
                                </label>
                            </template>
                            <template #value-label="{ node }">
                                <span class="font-weight-bold">{{ node.id }}</span> - {{ node.label }}
                            </template>
                        </x-select>
                    </b-form-group>

                    <b-form-group
                        v-if="commandObject && commandObject.arguments && commandObject.arguments.length"
                        label="Arguments">
                        <command-arguments
                            :command-object="commandObject"
                            v-model="schedule.params"/>
                    </b-form-group>

                    <b-form-group
                        v-if="commandObject && commandObject.options && commandObject.options.withValue.length"
                        label="Options with Value">
                        <command-options
                            :command-object="commandObject"
                            v-model="schedule.options"/>
                    </b-form-group>

                    <b-form-group
                        v-if="commandObject && commandObject.options && commandObject.options.withoutValue.length"
                        label="Options">
                        <command-options-no-value
                            :command-object="commandObject"
                            v-model="schedule.options"/>
                    </b-form-group>


                    <b-form-group label="Cron Expression">
                        <b-form-input
                            v-model="schedule.expression"
                            db-field="expression">
                            <template #description>
                                <a href="https://crontab.cronhub.io/" target="_blank">If necessary click here and
                                    use a
                                    tool to facilitate the creation of the cron expression</a>
                            </template>
                        </b-form-input>
                    </b-form-group>

                    <b-form-group label="Log filename">
                        <b-form-input
                            v-model="schedule.log_filename"
                            db-field="log_filename"
                            description="If log file is set, the log messages from this cron are written to storage/logs/<log filename>.log">
                        </b-form-input>
                    </b-form-group>

                    <b-form-group label="URL Before">
                        <b-form-input
                            v-model="schedule.webhook_before"
                            db-field="webhook_before">
                        </b-form-input>
                    </b-form-group>

                    <b-form-group label="URL After">
                        <b-form-input
                            v-model="schedule.webhook_after"
                            db-field="webhook_after">
                        </b-form-input>
                    </b-form-group>

                    <b-form-group label="Email for sending output">
                        <b-form-input
                            v-model="schedule.email_output"
                            db-field="email_output">
                        </b-form-input>
                    </b-form-group>

                    <b-form-group label="">
                        <x-switch
                            label-position="right"
                            v-model="schedule.sendmail_success"
                            db-field="sendmail_success">
                            Send email in case of success to execute the command
                        </x-switch>
                    </b-form-group>

                    <b-form-group label="">
                        <x-switch
                            label-position="right"
                            v-model="schedule.sendmail_error"
                            db-field="sendmail_error">
                            Send email in case of failure to execute the command
                        </x-switch>
                    </b-form-group>

                    <b-form-group label="">
                        <x-switch

                            label-position="right"
                            v-model="schedule.log_success"
                            db-field="log_success">
                            Write command output into history table in case of success to execute the command
                        </x-switch>
                    </b-form-group>

                    <b-form-group label="">
                        <x-switch
                            label-position="right"
                            v-model="schedule.log_error"
                            db-field="log_error">
                            Write command output into history table in case of failure to execute the command
                        </x-switch>
                    </b-form-group>

                    <b-form-group label="">
                        <x-switch
                            label-position="right"
                            v-model="schedule.even_in_maintenance_mode"
                            db-field="even_in_maintenance_mode">
                            Even in maintenance mode
                        </x-switch>
                    </b-form-group>

                    <b-form-group label="">
                        <x-switch
                            label-position="right"
                            v-model="schedule.without_overlapping"
                            db-field="without_overlapping">
                            Without overlapping
                        </x-switch>
                    </b-form-group>

                    <b-form-group label="">
                        <x-switch
                            label-position="right"
                            v-model="schedule.on_one_server"
                            db-field="on_one_server">
                            Execute scheduling only on one server
                        </x-switch>
                    </b-form-group>

                    <b-form-group label="">
                        <x-switch
                            label-position="right"
                            v-model="schedule.run_in_background"
                            db-field="run_in_background">
                            Run in background
                        </x-switch>
                    </b-form-group>

                    <div slot="footer">
                        <b-btn variant="default" @click="cancel" class="mx-1">Cancel</b-btn>
                        <b-btn variant="primary" type="submit">Save</b-btn>
                    </div>

                </b-card>
            </b-form>
        </b-col>
    </b-row>
</template>

<script>

import Api from "@/services/Api";
import XSwitch from "@/components/XSwitch.vue";
import CommandOptionsNoValue from "@modules/components/schedule-command/CommandOptionsNoValue.vue";
import CommandOptions from "@modules/components/schedule-command/CommandOptions.vue";
import CommandArguments from "@modules/components/schedule-command/CommandArguments.vue";

export default {
    name: 'Schedule',
    components: {CommandOptionsNoValue, CommandOptions, CommandArguments, XSwitch},
    props: {
        schedule_id: {
            required: true,
        },
    },
    data() {
        return {
            schedule: {
                id: null,
                command: null,
                command_custom: '',
                params: [],
                options: {},
                expression: '* * * * *',
                webhook_before: '',
                webhook_after: '',
                email_output: '',
                status: '1',
                log_filename: '',
                groups: '',
                environments: '',
                sendmail_error: false,
                sendmail_success: false,
                log_success: false,
                log_error: false,
                even_in_maintenance_mode: false,
                without_overlapping: false,
                on_one_server: false,
                run_in_background: false,
            },
            commands: []
        }
    },
    computed: {
        commandObject() {
            return this.commands[this.schedule.command]
        },
        commandsOptions() {
            return Object.values(this.commands)
        }
    },
    watch: {
        schedule_id() {
            this.load()
        }
    },

    mounted() {
        this.loadCommands()
        this.load()
    },

    methods: {
        cancel() {
            this.$router.push({name: 'All schedules'})
        },

        async load() {
            if (!this.schedule_id) {
                this.$awn.alert('Prop "schedule_id" is not defined!');
                return
            }

            if (this.schedule_id === 'new') {
                return
            }
            this.schedule = await Api.get('/schedule/get', {params: {id: this.schedule_id}}).then(data => data.data)

        },
        async save() {
            try {
                let result = await Api.post('/schedule/update', {...this.schedule}).then(data => data.data)
                if (this.schedule_id === 'new') {
                    await this.$router.replace({
                        name: this.$route.name,
                        params: {
                            schedule_id: result.id,
                        },
                        query: this.$route.query
                    })
                } else {
                    await this.load()
                }
            } catch (e) {
                //debugger
                if (_.get(e, 'response.status') === 422) {
                    const response = e.response
                    const errors = response.data.errors
                    //this.$refs.observer.setErrors(errors)
                } else {
                    throw e
                }

            }
        },
        async loadCommands() {
            this.commands = await Api.get('schedule/commands').then(data => data.data)
        },
        onSelectCommand(commandObject) {
            this.setDefaultParams(commandObject)
            this.setDefaultOptionsValue(commandObject)
        },
        setDefaultParams(commandObject) {
            const defaults = {}
            if (commandObject && commandObject.arguments) {
                const args = commandObject.arguments
                args.forEach(argument => {
                    defaults[argument.name] = {
                        type: 'string',
                        value: argument.default
                    }
                })
            }
            this.schedule.params = defaults
        },
        setDefaultOptionsValue(commandObject) {
            const defaults = {}
            if (commandObject && commandObject.options && commandObject.options.withValue) {
                const opts = commandObject.options.withValue
                opts.forEach(option => {
                    defaults[option.name] = {
                        type: 'string',
                        value: option.default
                    }
                })
            }
            this.schedule.options = defaults
        }
    },

}
</script>

