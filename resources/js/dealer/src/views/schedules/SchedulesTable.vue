<template>
    <div class="animated fadeIn">
        <b-card-group deck>
            <b-card>
                <b-row>
                    <b-col>
                        <b-button-toolbar aria-label="Toolbar with button groups and dropdown menu">
                            <b-button-group class="mx-1">
                                <b-btn variant="primary" @click.prevent="onActionNew">
                                    <i class="fa fa-plus mr-1"
                                       aria-hidden="true"></i>Add task
                                </b-btn>
                                <b-btn variant="primary" @click.prevent="load">
                                    <i class="fa fa-refresh mr-1"
                                       aria-hidden="true"></i>Refresh
                                </b-btn>
                            </b-button-group>
                        </b-button-toolbar>
                    </b-col>
                </b-row>
                <div class="admins-wrapper mt-3">
                    <v-server-table :name="'Schedules'"
                                    ref="table"
                                    table-type="server"
                                    :columns="columns"
                                    :options="options"
                                    class="schedule-table">
                        <!--Actions-->
                        <template #col-action="props">

                            <b-btn-group size="sm">
                                <b-btn @click="onActionEdit([props.row])" variant="outline-primary">
                                    <i class="icons font-1xl cui-pencil"></i>
                                </b-btn>

                                <b-dropdown id="down-right" right text="" variant="primary" size="sm">
                                    <b-dropdown-item v-if="+props.row.status===1"
                                                     @click="onActionInactivate([props.row])">
                                        <i class="fa fa-pause" aria-hidden="true"></i>Inactivate
                                    </b-dropdown-item>

                                    <b-dropdown-item v-else
                                                     @click="onActionActivate([props.row])">
                                        <i class="fa fa-play" aria-hidden="true"></i>Activate
                                    </b-dropdown-item>

                                    <b-dropdown-item href="#" @click="onActionDelete([props.row])" variant="danger"><i
                                        class="fa fa-trash-o" aria-hidden="true"></i>Delete
                                    </b-dropdown-item>

                                    <b-dropdown-item href="#" @click="onActionHistory(props.row)"><i
                                        class="fa fa-history" aria-hidden="true"></i>History
                                    </b-dropdown-item>
                                </b-dropdown>
                            </b-btn-group>

                        </template>

                        <!--Columns-->
                        <template #command="{row}">
                            <code>{{ row.command }}</code>
                        </template>
                        <template #status="{row}">
                            <span v-if="row.status === '1'"
                                  class="badge badge-success">Active
                            </span>
                            <span v-else
                                  class="badge badge-secondary">Inactive
                            </span>
                        </template>
                        <template #params="{row}">
                            <div class="text-truncate">{{ formatParams(row.params) }}</div>
                        </template>
                        <template #options="{row}">
                            <div class="text-truncate">{{ formatOptions(row.options) }}</div>
                        </template>
                        <template #created_at="{row}">
                            {{ formatDate(row.created_at) }}
                        </template>
                        <template #updated_at="{row}">
                            {{ formatDate(row.updated_at) }}
                        </template>
                    </v-server-table>
                </div>
            </b-card>
        </b-card-group>
    </div>
</template>

<script>

import Api from "@/services/Api";
import dayjs from "dayjs";
import _ from 'lodash'

export default {
    name: 'SchedulesTable',
    components: {},
    props: {},
    data() {
        return {
            loading:false,

            columns: [

                'id',
                'site_id',
                'command',
                'params',
                'options',
                'expression',
                'status',
                'created_at',
                'updated_at',

                'col-action'
            ],
            options: {
                requestFunction: this.requestFunction,
                filterable: false,
                filterByColumn: true,
                sortable: [
                    'id',
                    'site_id',
                    'command',
                    'expression',
                    'status',
                    'created_at',
                    'updated_at',
                ],
                uniqueKey: 'id',
                headings: {
                    'id': 'ID',
                    'command': 'Command',
                    'params': 'Arguments',
                    'options': 'Options',
                    'expression': 'Cron Expression',
                    'created_at': 'Created At',
                    'updated_at': 'Updated At',
                    'status': 'Status',
                    'col-action': '',
                },
            },
        }
    },
    computed: {},
    mounted() {

    },
    activated() {
        this.load()
    },
    methods: {
        async requestFunction(params) {
            this.loading = true;
            let data = await Api.get('/schedule/all', {params: params})
            this.loading = false;
            return data.data
        },
        load() {
            this.$refs.table.refresh()
        },

        onActionNew() {
            this.$router.push({name: 'Edit schedule', params: {schedule_id: 'new'}})
        },

        async onActionInactivate(rows) {
            await Api.post('/schedule/inactivate', {id: rows.map(row => row['id'])})
            await this.load()
        },

        async onActionActivate(rows) {
            await Api.post('/schedule/activate', {id: rows.map(row => row['id'])})
            await this.load()
        },

        onActionEdit(rows) {
            this.$router.push({name: 'Edit schedule', params: {schedule_id: rows[0]['id']}})
        },

        async onActionDelete(rows) {
            let message
            if (rows.length === 1) {
                message = `Are you sure want to delete schedule <b>${rows[0]['command']}</b>?`
            } else {
                message = `Are you sure want to delete "${rows.length}" schedules?`
            }
            if ((await this.$dialogs.confirmDelete({title: 'Delete schedule', message})).trigger !== 'ok') return
            await Api.post('/schedule/delete', {id: rows.map(row => row['id'])})
            await this.load()

        },

        formatDate(str) {
            return dayjs(str).utc().format('YYYY-MM-DD HH:mm')
        },
        formatParams(params) {
            return Object.keys(params).reduce((acc, key) => {
                acc.push(`${key}=${params[key].value}`)
                return acc
            }, []).join(', ')
        },

        formatOptions(params) {
            return Object.keys(params).reduce((acc, key) => {
                if (_.isObject(params[key])) {
                    acc.push(`--${key}=${params[key].value}`)
                } else {
                    acc.push(`--${key}`)
                }
                return acc
            }, []).join(' ')
        },
        onActionHistory(row){
           this.$awn.alert('Not implemented!')
        }
    },

}
</script>

<style lang="scss">
.schedule-table {
    .col-id {
        width: 100px;
    }

    .col-status {
        width: 100px;
    }

    .col-params, .col-options {
        max-width: 180px;
    }

    .col-created_at, .col-updated_at {
        width: 130px;
    }
}
</style>
