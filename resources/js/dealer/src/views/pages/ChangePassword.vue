<template>
    <div class="app app__splash flex-row align-items-center">
        <div class="container">
            <b-row class="justify-content-center">
                <b-col md="4">
                    <b-card-group>
                        <b-card
                            no-body class="p-3 animated fadeIn login-card"
                            img-src="/app/img/brand/logo.png"
                            img-alt="ExologCMS">
                            <b-card-body>
                                <b-form>
                                    <h4 class="text-primary mb-3 login-card__title">Dealer admin panel</h4>
                                    <b-form-group v-show="state==='INPUT-PASSWORD'">
                                        <p>Enter new password.</p>
                                        <b-input-group class="mb-3">
                                            <b-input-group-prepend>
                                                <b-input-group-text><i class="icon-lock"></i></b-input-group-text>
                                            </b-input-group-prepend>
                                            <b-form-input type="password"
                                                          class="form-control"
                                                          ref="passwordEl"
                                                          placeholder="Password"
                                                          :state="passwordError?false:null"
                                                          v-model="password">
                                            </b-form-input>
                                            <b-form-invalid-feedback id="inputPasswordFeedback">
                                                <!-- This will only be shown if the preceeding input has an invalid state -->
                                                {{ passwordError }}
                                            </b-form-invalid-feedback>
                                        </b-input-group>
                                        <b-input-group class="mb-3">
                                            <b-input-group-prepend>
                                                <b-input-group-text><i class="icon-lock"></i></b-input-group-text>
                                            </b-input-group-prepend>
                                            <b-form-input type="password"
                                                          class="form-control"
                                                          placeholder="Confirm password"
                                                          :state="passwordConfirmError?false:null"
                                                          v-model="passwordConfirm"
                                                          @keyup.native.enter="changePassword">
                                            </b-form-input>
                                            <b-form-invalid-feedback id="inputLiveFeedback">
                                                <!-- This will only be shown if the preceeding input has an invalid state -->
                                                {{ passwordConfirmError }}
                                            </b-form-invalid-feedback>
                                        </b-input-group>
                                    </b-form-group>
                                    <b-row>
                                        <b-col cols="auto">
                                            <b-button variant="primary"
                                                      class="px-4"
                                                      :disabled="!canChangePassword"
                                                      @click="changePassword">Change password
                                            </b-button>
                                        </b-col>
                                        <b-col cols="auto" class="ml-auto text-right">
                                            <b-button variant="link" class="px-0"
                                                      @click="goLogin">
                                                Login
                                            </b-button>
                                        </b-col>
                                    </b-row>
                                </b-form>
                            </b-card-body>
                        </b-card>
                    </b-card-group>
                </b-col>
            </b-row>
        </div>
    </div>

</template>

<script>
import {AUTH_CHANGE_PASSWORD, AUTH_REQUEST} from "../../store/actions";
import InputTwoFactorAuthCode from "../../components/InputTwoFactorAuthCode"

export default {
    name: 'ChangePassword',
    components: {InputTwoFactorAuthCode},
    data() {
        return {
            state: 'INPUT-PASSWORD',
            password: '',
            passwordConfirm: '',
            error2FA: '',
            a_email: ''
        }
    },
    computed: {
        passwordError() {
            if (this.password.length === 0) {
                return ''
            }
            if (this.password.length < 6) {
                return 'The minimum password length is 6 characters.'
            }
            return ''

        },
        passwordConfirmError() {
            if (this.password !== this.passwordConfirm && this.passwordConfirm.length > 0) {
                return 'Passwords do not match.'
            }
            return ''
        },
        canChangePassword() {
            return this.passwordError === '' && this.passwordConfirmError === '' && this.password.length > 0 && this.passwordConfirm.length > 0
        }
    },
    mounted() {
        this.$refs.passwordEl.focus()
        this.a_email = decodeURI(this.$route.query.email);

    },
    methods: {
        changePassword() {
            if (!this.canChangePassword) return

            let me = this;
            let hash = me.$route.query.hash;
            if (!hash) {
                me.$awn.alert('Incorrect password change link!')
                return;
            }

            if (!me.a_email) {
                me.$awn.alert('Incorrect password change link!')
                return;
            }
            const {password, a_email} = this
            this.$store.dispatch(AUTH_CHANGE_PASSWORD, {password, hash, a_email})
                .then(_ => {
                    me.$awn.success('The password has been changed successfully. Please login.')
                    me.$router.push({name: 'Login'});
                })
        },

        goLogin() {
            this.$router.push({name: 'Login'})
        }
    }

}
</script>
<style lang="scss" scoped>
.login-card {
    > img {
        max-width: 158px;
        margin: 1rem auto 1rem auto;
    }

    &__code {
        margin-top: 1.9rem;
        margin-bottom: 2rem;
    }

    &__title {
        text-align: center;
        font-weight: lighter;
    }
}
</style>
