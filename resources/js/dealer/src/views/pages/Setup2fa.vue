<template>
    <div class="app app__splash flex-row align-items-center setup-2fa">
        <div class="container">
            <b-row class="justify-content-center">
                <b-col md="6">
                    <b-card-group>
                        <b-card no-body class="animated fadeIn"
                                header-tag="header">

                            <h2 slot="header" class="text-primary mb-0">Set up Authenticator</h2>
                            <b-card-body v-show="step===1">
                                <div v-if="!showCode">
                                    <ol class="text-dark">
                                        <li>
                                            Get the Authenticator App from the Play Store.
                                            <div class="d-flex flex-row align-items-center">
                                                <div class="exo-2FA-steps__link-android">
                                                    <a href='https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2&pcampaignid=MKT-Other-global-all-co-prtnr-py-PartBadge-Mar2515-1'>
                                                        <img
                                                            style="display:inline-block;width:150px;"
                                                            alt='Get it on Google Play'
                                                            src='https://play.google.com/intl/en_us/badges/images/generic/en_badge_web_generic.png'/>
                                                    </a>
                                                </div>
                                                <a href="https://itunes.apple.com/us/app/google-authenticator/id388497605?mt=8"
                                                   style="display:inline-block;overflow:hidden;background:url('https://linkmaker.itunes.apple.com/assets/shared/badges/en-us/appstore-lrg.svg') no-repeat;width:135px;height:40px;background-size:contain;">
                                                </a>
                                            </div>

                                        </li>
                                        <li> In the App select Set up account.
                                        </li>
                                        <li> Choose Scan a barcode.
                                        </li>
                                    </ol>
                                    <div class="text-center mb-4 mt-4">
                                        <img :src="barcodeSrc" alt="barcode">
                                    </div>
                                    <div class="text-center">
                                        <a href="#" @click="showCode=true">Can't scan it? or want backup code?</a>
                                    </div>
                                </div>

                                <div v-else>
                                    <ol class="text-dark">
                                        <li>Tap Menu, then Set up account.</li>
                                        <li>Tap Enter provided key.</li>
                                        <li>Enter your email address and this key:
                                            <b-alert show variant="dark" class="mt-3">{{ secretCode }}</b-alert>
                                        </li>
                                        <li>Make sure Time based is turned on, and tap Add to finish.</li>
                                    </ol>
                                    <div class="text-center">
                                        <a href="#" @click="showCode=false">Show QR code</a>
                                    </div>
                                </div>

                            </b-card-body>

                            <b-card-body v-show="step===2">
                                <div>
                                    <b-input-group class="mb-3">
                                        <b-input-group-prepend>
                                            <b-input-group-text>6-digit code</b-input-group-text>
                                        </b-input-group-prepend>
                                        <b-form-input type="text" class="form-control"
                                                      placeholder="Enter code that you see in the app"
                                                      :state="gaCodeState && !verifyError"
                                                      @input="verifyError=''"
                                                      v-model="gaCode"></b-form-input>
                                        <b-form-invalid-feedback id="inputLiveFeedback">
                                            <!-- This will only be shown if the preceeding input has an invalid state -->
                                            {{ verifyError }}
                                        </b-form-invalid-feedback>
                                    </b-input-group>
                                </div>

                            </b-card-body>

                            <b-card-body v-show="step===3">

                                <b-alert variant="success" show><h4 class="text-center">2-Step Verification turned
                                    on</h4></b-alert>

                            </b-card-body>

                            <b-card-footer>

                                <b-button variant="secondary" v-show="step>1 && step<3"
                                          class="px-4 mx-1"
                                          @click="step--">Back
                                </b-button>
                                <b-button variant="primary" v-show="step<2"
                                          class="px-4 mx-1"
                                          @click="step++">Next
                                </b-button>
                                <b-button variant="primary" v-show="step===2"
                                          class="px-4 mx-1"
                                          @click="verify"
                                          :disabled=!gaCodeState>Verify
                                </b-button>
                                <b-button variant="primary" v-show="step===3"
                                          class="px-4 mx-1"
                                          to="/">Close
                                </b-button>
                            </b-card-footer>
                        </b-card>
                    </b-card-group>
                </b-col>
            </b-row>
        </div>
    </div>
</template>

<script>
import Api from "../../services/Api";

export default {
    name: 'Setup2fa',
    data() {
        return {
            step: 1,
            barcodeSrc: '',
            secretCode: '',
            showCode: false,
            gaCode: '',
            verifyError: ''
        }
    },
    computed: {
        gaCodeState() {
            return this.gaCode.length === 6 ? true : this.gaCode.length === 0 ? null : false
        },
        error() {
            return this.$store.state.auth.error
        }
    },
    methods: {
        verify() {
            this.verifyError = ''
            Api.post('/auth/enable2FA', {
                "gaCode": this.gaCode
            }).then(result => {
                this.step++
            })
                .catch(error => {
                    if (error.data && error.data.error) {
                        this.verifyError = error.data.error
                    } else {
                        throw error;
                    }
                })
        }
    },
    mounted() {
        Api.post('/auth/create2FA').then(result => {
            this.barcodeSrc = 'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=' + encodeURIComponent(result.data.barcode)
            this.secretCode = result.data.code
        }).catch(error => {
            this.$router.push('/')
        })
    }

}
</script>
