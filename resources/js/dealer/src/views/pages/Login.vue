<template>
    <div class="app app__splash flex-row align-items-center">
        <div class="container">
            <b-row class="justify-content-center">
                <b-col md="4">
                    <b-card-group class="login-group block-loader" :class="{'block-loader--active': isLoading }">
                        <b-card v-show="state==='LOGIN'"
                                no-body class="p-3 animated fadeIn login-card"
                                img-src="/app/img/brand/logo.png"
                                img-alt="ExologCMS">
                            <b-card-body>
                                <b-form>
                                    <h4 class="text-primary mb-3 login-card__title">Dealer admin panel</h4>
                                    <b-input-group class="mb-3">
                                        <b-input-group-prepend>
                                            <b-input-group-text><i class="icon-user"></i></b-input-group-text>
                                        </b-input-group-prepend>
                                        <b-form-input type="text" class="form-control"
                                                      placeholder="Username"
                                                      ref="a_emailEl"
                                                      autocomplete="username email"
                                                      @keyup.native.enter="login"
                                                      :state="error?false:null"
                                                      v-model="a_email"></b-form-input>
                                    </b-input-group>
                                    <b-input-group class="mb-4">
                                        <b-input-group-prepend>
                                            <b-input-group-text><i class="icon-lock"></i></b-input-group-text>
                                        </b-input-group-prepend>
                                        <b-form-input type="password"
                                                      class="form-control"
                                                      placeholder="Password"
                                                      autocomplete="current-password"
                                                      :state="error?false:null"
                                                      v-model="password"
                                                      @keyup.native.enter="login">
                                        </b-form-input>
                                        <b-form-invalid-feedback id="inputLiveFeedback">
                                            <!-- This will only be shown if the preceeding input has an invalid state -->
                                            {{ error }}
                                        </b-form-invalid-feedback>
                                    </b-input-group>
                                    <b-row>
                                        <b-col cols="6">
                                            <b-button variant="primary"
                                                      class="px-4"
                                                      v-on:click="login">Login
                                            </b-button>
                                        </b-col>
                                        <b-col cols="6" class="text-right">
                                            <b-button variant="link" class="px-0" @click="state='FORGOT_PASSWORD'">
                                                Forgot password?
                                            </b-button>
                                        </b-col>
                                    </b-row>
                                </b-form>
                            </b-card-body>
                        </b-card>
                        <!---->
                        <b-card v-show="state==='2FA'"
                                no-body class="p-3 login-card"
                                img-src="/app/img/brand/logo.png"
                                img-alt="ExologCMS">
                            <b-card-body>
                                <b-form @submit.prevent="">
                                    <h4 class="text-primary mb-3 login-card__title">Dealer admin panel</h4>
                                    <input-two-factor-auth-code ref="inputCode" v-model="gaCode" :error="error"
                                                                @enter="login">

                                    </input-two-factor-auth-code>
                                    <b-row>
                                        <b-col cols="6">
                                            <b-button variant="primary"
                                                      class="px-4"
                                                      @click="login">Login
                                            </b-button>
                                        </b-col>
                                        <b-col cols="6" class="text-right">
                                            <b-button variant="link" class="px-0"
                                                      @click="lostCode">
                                                Can't enter code?
                                            </b-button>
                                        </b-col>
                                    </b-row>
                                </b-form>
                            </b-card-body>
                        </b-card>
                        <!---->
                        <b-card v-show="state==='FORGOT_PASSWORD'"
                                no-body class="p-3 animated fadeIn login-card"
                                img-src="/app/img/brand/logo.png"
                                img-alt="ExologCMS">
                            <b-card-body>
                                <b-form>
                                    <h4>Forgot your password?</h4>
                                    <p class="text-muted">Enter your email address and we will send you instructions on
                                        how to reset your password.</p>
                                    <b-input-group class="mb-3">
                                        <b-input-group-prepend>
                                            <b-input-group-text><i class="icon-user"></i></b-input-group-text>
                                        </b-input-group-prepend>
                                        <b-form-input type="text" class="form-control"
                                                      placeholder="Username"
                                                      autocomplete="username email"
                                                      v-model="a_email"></b-form-input>
                                    </b-input-group>

                                    <b-row>
                                        <b-col cols="auto">
                                            <b-button variant="primary"
                                                      class="px-4"
                                                      :disabled="a_email===''"
                                                      @click="resetPassword">Reset password
                                            </b-button>
                                        </b-col>
                                        <b-col cols="auto" class="text-right ml-auto">
                                            <b-button variant="link" class="px-0" @click="state='LOGIN'">Login page
                                            </b-button>
                                        </b-col>
                                    </b-row>
                                </b-form>
                            </b-card-body>
                        </b-card>
                    </b-card-group>
                </b-col>
            </b-row>
        </div>
    </div>

</template>

<script>
import {AUTH_ERROR, AUTH_FORGOT_PASSWORD_LINK, AUTH_REQUEST} from "../../store/actions";
import InputTwoFactorAuthCode from "../../components/InputTwoFactorAuthCode"

export default {
    name: 'Login',
    components: {InputTwoFactorAuthCode},
    data() {
        return {
            state: 'LOGIN',
            gaCode: '',
            a_email: process.env.VUE_APP_LOGIN || '',
            password: process.env.VUE_APP_PASSWORD || ''
        }
    },
    computed: {
        error() {
            return this.$store.state.auth.error
        },
        isLoading() {
            return this.$store.state.auth.status === 'loading'
        }
    },
    mounted() {
        this.$refs.a_emailEl.focus()
    },
    methods: {
        login() {

            const me = this;
            const {a_email, password, gaCode} = this

            if (me.state === '2FA' && !gaCode) {
                this.$store.commit(AUTH_ERROR, 'code is empty')
                return;
            }
            this.$store.dispatch(AUTH_REQUEST, {a_email, password, gaCode})
                .then(_ => {
                    if (me.$store.state.auth.status === 'need_2fa') {
                        me.state = '2FA'
                        this.$nextTick(() => {
                            me.$refs.inputCode.focus()
                        })
                        return
                    }
                    if (me.$store.state.auth.status === 'success' &&
                        me.$store.state.admin.is2fa !== true /*&& process.env.NODE_ENV !== 'development'*/) {
                        return me.$router.push({name: 'Setup2fa'})
                    }
                    if (me.$store.state.auth.status === 'success') {
                        //me.$router.push({name: 'Dashboard'})
                        return me.$router.push(me.$store.state.auth.from_path)
                    }
                })
        },
        resetPassword() {

            const {a_email} = this
            this.$store.dispatch(AUTH_FORGOT_PASSWORD_LINK, {a_email})
                .then((result) => {
                    this.state = 'LOGIN'
                    this.$awn.success(`The instruction has been sent to your email. Please check.`)
                })
        },

        lostCode() {
            this.$awn.info('Please contact with administrator. <a href="mailto:<EMAIL>?subject=Dealer two step verification"><EMAIL></a>')
        }
    }

}
</script>
<style lang="scss" scoped>
.login-group {
    box-shadow: 0 0 20px #adb5bd;
}

.login-card {
    > img {
        max-width: 158px;
        margin: 1rem auto 1rem auto;
    }

    &__code {
        margin-top: 1.9rem;
        margin-bottom: 2rem;
    }

    &__title {
        text-align: center;
        font-weight: lighter;
    }
}
</style>
