<template>
    <div class="animated fadeIn">
        <b-card-group deck>
            <b-card>
                <b-row>
                    <b-col md="1">
                        <b-nav pills vertical>
                            <b-nav-item>Server</b-nav-item>
                            <b-nav-item active>Exolog</b-nav-item>
                            <b-nav-item>Routes</b-nav-item>
                        </b-nav>
                    </b-col>
                    <b-col md="11">
                        <codemirror
                            :options="cmOptions"></codemirror>
                    </b-col>
                </b-row>

            </b-card>
        </b-card-group>

    </div>
</template>

<script>
    // require component
    import { codemirror } from 'vue-codemirror'

    // require styles
    import 'codemirror/lib/codemirror.css'

    // require more codemirror resource...


    export default {
        name: "Configuration",
        components: {
            codemirror
        },
        data(){
            return {
                cmOptions: {
                    // codemirror options
                    tabSize: 4,
                    mode: 'text/javascript',
                    theme: 'base16-dark',
                    lineNumbers: true,
                    line: true,
                    // more codemirror options, 更多 codemirror 的高级配置...
                }
            }
        }
    }
</script>

<style scoped>

</style>