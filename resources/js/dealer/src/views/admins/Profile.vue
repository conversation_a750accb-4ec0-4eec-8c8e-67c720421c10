<template>
    <b-row class="animated fadeIn">
        <b-col cols="12" xl="8">
            <b-form @submit.prevent="onSave">
                <b-card :header="caption" show-footer footer-class="d-flex justify-content-end">
                    <b-form-group id="exampleInputGroup1"
                                  label="Email address:"
                                  label-for="inputAdminEmail">
                        <b-form-input id="inputAdminEmail"
                                      type="email"
                                      v-model="admin.a_email"
                                      required
                                      placeholder="Enter email">
                        </b-form-input>
                    </b-form-group>
                    <b-form-group id="exampleInputGroup2"
                                  label="Name:"
                                  label-for="a_email">
                        <b-form-input id="a_email"
                                      type="text"
                                      v-model="admin.a_name"
                                      required
                                      placeholder="Enter name">
                        </b-form-input>
                    </b-form-group>
                    <b-form-group id="exampleInputGroup3"
                                  label="Password:"
                                  label-for="inputAdminPassword">
                        <b-input-group>
                            <b-form-input :type="isGenerated?'text':'password'"
                                          id="inputAdminPassword"
                                          class="form-control"
                                          placeholder="Password"
                                          :required="!admin.a_id"
                                          @keyup.native="isGenerated=false"
                                          v-model="admin.a_password">
                            </b-form-input>
                            <b-input-group-append>
                                <b-btn variant="outline-success" @click.prevent="generatePass">Generate</b-btn>
                            </b-input-group-append>
                        </b-input-group>
                    </b-form-group>
                    <b-form-group v-show="canUserChange"
                                  label="">
                        <b-form-checkbox id="checkboxIsSuper"
                                         v-model="admin.a_issuper"
                                         :value="true"
                                         :unchecked-value="false">
                            Super user
                        </b-form-checkbox>
                    </b-form-group>
                    <div slot="footer">
                        <b-btn variant="default" @click="onCancel" class="mx-1">Cancel</b-btn>
                        <b-btn variant="primary" type="submit">Save</b-btn>
                    </div>
                </b-card>
            </b-form>
        </b-col>
    </b-row>
</template>

<script>

    import {Switch as cSwitch} from '@coreui/vue'
    import {ADMIN_CREATE, ADMIN_REQUEST, ADMIN_UPDATE} from "../../store/actions"

    export default {
        name: "Profile",
        components: {
            cSwitch
        },
        data() {
            return {
                action: ADMIN_CREATE,
                caption: this.$route.name,
                isGenerated: false,
                admin: {
                    a_id: '',
                    a_name: '',
                    a_email: '',
                    a_password: '',
                    a_issuper: true,
                    is2fa: false
                }
            }
        },
        created() {
            this.loadAdmin()
        },
        computed: {
            canUserChange() {
                return this.$store.state.admin.a_issuper
            }
        },
        watch: {
            '$route'(to, from) {
                this.loadAdmin()
            }
        },
        methods: {
            loadAdmin() {
                this.action = ADMIN_CREATE
                let admin_id = this.$route.params['a_id']

                if (admin_id) {
                    this.action = ADMIN_UPDATE


                    if (admin_id === 'me') {
                        admin_id = this.$store.state.admin.a_id;
                    }
                    this.$store.dispatch(ADMIN_REQUEST, admin_id)
                        .then(admin => {
                            this.admin = admin
                        })


                }
            },
            onSave() {
                this.$store.dispatch(this.action, this.admin)
                    .then(() => {
                        this.$router.go(-1)
                    })
                    .catch(error => {
                        this.$awn.alert(error.message)
                    })
            },
            onCancel() {
                this.$router.go(-1)
            },

            generatePass() {
                const range = 'abcdefghjkmnpqrstuvwxyz23456789'
                const length = 7

                let newpass = ''

                for (let i = 0; i < length; i++) {
                    newpass += range.charAt(Math.floor(Math.random() * range.length));
                }
                this.isGenerated = true
                this.admin.a_password = newpass;
            }
        }
    }
</script>

<style scoped>

</style>
