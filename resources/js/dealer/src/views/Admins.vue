<template>
    <div class="animated fadeIn">
        <b-card-group deck>
            <b-card>
                <b-row>
                    <b-col>
                        <b-button-toolbar aria-label="Toolbar with button groups and dropdown menu">
                            <b-button-group class="mx-1">
                                <b-btn variant="primary" @click.prevent="onCreateAdmin"><i class="fa fa-user-plus mr-1"
                                                                                           aria-hidden="true"></i>Add
                                    admin
                                </b-btn>
                            </b-button-group>
                        </b-button-toolbar>
                    </b-col>
                </b-row>
                <div class="admins-wrapper mt-3">
                    <v-client-table :name="'Admins'" :data="admins" :columns="columns"
                                    :options="options">
                        <template slot="a_issuper" slot-scope="props">
                            <strong>{{ props.row.a_issuper ? 'Yes' : '' }}</strong>
                            {{ !props.row.a_issuper ? 'No' : '' }}
                        </template>
                        <template slot="edit" slot-scope="props">
                            <b-btn-group size="sm" v-show="canUserChange(props.row)">
                                <b-btn @click="onEditAdmin(props.row)" variant="outline-primary">
                                    <i class="icons font-1xl cui-pencil"></i>
                                </b-btn>
                                <b-dropdown id="ddown-right" right text="" variant="primary" size="sm">
                                    <b-dropdown-item href="#" @click="onDeleteAdmin(props.row)" variant="danger"><i
                                        class="fa fa-trash-o" aria-hidden="true"></i>Delete
                                    </b-dropdown-item>
                                </b-dropdown>
                            </b-btn-group>
                        </template>

                        <template slot="is2fa" slot-scope="props">
                            <c-switch class="mx-1" color="primary"
                                      :model-checked="props.row.is2fa"
                                      @change="onSwitch2FA(props.row, $event)"
                                      :disabled="!props.row.is2fa || !canUserChange(props.row)"
                                      label
                                      size="sm"
                                      dataOn="yes" dataOff="no"/>
                        </template>
                    </v-client-table>
                </div>
            </b-card>
        </b-card-group>
        <confirm-modal title="Delete admin"
                       :open="modalConfirmDelete"
                       @confirm="onConfirmDelete"
                       @cancel="modalConfirmDelete=false">
            Are you sure want delete user?
        </confirm-modal>
    </div>
</template>
<script>

import {Switch as cSwitch} from '@coreui/vue'
import Api from '../services/Api'
import Profile from "./admins/Profile"
import ConfirmModal from "../components/ConfirmModal"
import {ADMIN_DELETE, ADMIN_DISABLE_2FA} from "@/store/actions"

export default {
    name: 'admins',
    components: {Profile, ConfirmModal, cSwitch},
    data() {
        return {
            modalEditAdmin: false,
            modalConfirmDelete: false,
            modalEditAdminTitle: 'Add new admin',
            currentAdmin: {
                a_id: '',
                a_name: '',
                a_email: '',
                a_password: '',
                a_issuper: false,
                'is2fa': false
            },
            columns: [
                'a_id',
                'a_name',
                'a_email',
                'a_issuper',
                'a_lastlogin',
                'is2fa',
                'edit'
            ],
            admins: [],
            options: {
                uniqueKey: 'a_id',
                headings: {
                    'a_id': 'ID',
                    'a_name': 'Full Name',
                    'a_email': 'E-Mail',
                    'a_issuper': 'Super user',
                    'a_lastlogin': 'Admin last login time',
                    'is2fa': '2 Step Auth',
                    'edit': ''
                }
            }
        }
    },
    mounted() {
        this.loadAdmins()
    },
    computed: {},
    methods: {
        loadAdmins() {
            Api.get('/admin/all').then(result => {
                result.data.map(admin => {
                    admin['a_id'] = +admin['a_id'];
                    return admin;
                })
                this.admins = result.data;
            })
        },

        onSwitch2FA(adminValue, value) {
            if (value) return
            let admin = this.admins.find(item => item.a_id === adminValue.a_id)
            if (!admin.is2fa) return

            admin.is2fa = false;

            this.$store.dispatch(ADMIN_DISABLE_2FA, admin.a_id).then(result => {
            }).catch(
                error => {
                    admin.is2fa = true;
                    this.$awn.alert(error.message)
                }
            )
        },

        onCreateAdmin() {
            this.$router.push({name: 'Create admin'})
        },

        onEditAdmin(admin) {
            Object.assign(this.currentAdmin, admin);
            this.$router.push({name: 'Edit admin', params: {a_id: admin.a_id}})
        },

        onDeleteAdmin(admin) {
            Object.assign(this.currentAdmin, admin);
            this.modalConfirmDelete = true;

        },
        async onConfirmDelete() {
            let me = this;
            this.modalConfirmDelete = false;
            try {
                await this.$store.dispatch(ADMIN_DELETE, me.currentAdmin.a_id)
                me.loadAdmins()
            } catch (error) {
                this.$awn.alert(error.message)
            }
        },
        canUserChange(admin) {
            return this.$store.state.admin.a_issuper || (+admin.a_id === +this.$store.state.admin.a_id)
        }
    }
}
</script>
