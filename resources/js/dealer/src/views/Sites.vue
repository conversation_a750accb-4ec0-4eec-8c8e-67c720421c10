<template>
    <div class="animated fadeIn sites">
        <b-card-group deck>
            <b-card>
                <b-row>
                    <b-col>
                        <b-button-toolbar aria-label="Toolbar with button to manage sites">
                            <b-button-group class="mx-1">
                                <b-btn variant="primary" @click.prevent="onCreateSite"><i class="fa fa-plus mr-1"
                                                                                          aria-hidden="true"></i>Create
                                    site
                                </b-btn>
                            </b-button-group>
                        </b-button-toolbar>
                    </b-col>
                </b-row>
                <div class="sites-wrapper mt-3">
                    <v-client-table :name="'Sites'" :data="sites" :columns="columns"
                                    :options="options">
                        <template #afterFilterWrapper>
                            <div class="pull-left">
                                <b-dropdown id="dropdown-1" class="ml-2">
                                    <template v-slot:button-content>
                                        <i class="fa fa-filter"></i>
                                    </template>
                                    <b-dropdown-item :active="showDeleted" @click="setFilter">
                                        Show deleted sites
                                    </b-dropdown-item>
                                    <b-dropdown-item :active="!showDeleted" @click="setFilter">
                                        Show active sites
                                    </b-dropdown-item>
                                </b-dropdown>
                            </div>
                        </template>
                        <template #main_domain_name="props">
                            <div class="d-flex justify-content-between align-content-center">
                                <div>
                                    <a :href="'https://'+props.row.main_domain_name"
                                       target="_blank">{{ props.row.main_domain_name }}</a>
                                </div>
                                <div v-if="!showDeleted">
                                    <template v-if="!props.row.checks.domains.validAliasDomain.valid">
                                        <b-button
                                            @click="onFixSite(props.row.checks.domains.validAliasDomain, props.row)"
                                            v-b-tooltip.hover title="Site have inccorect domain 'alias'.'server_name'"
                                            variant="outline-warning" size="sm"><i class="fa fa-warning"></i></b-button>
                                    </template>

                                    <template v-if="!props.row.checks.domains.haveAliasDomain.valid">
                                        <b-button
                                            @click="onFixSite(props.row.checks.domains.haveAliasDomain, props.row)"
                                            v-b-tooltip.hover title="Site do not have default domain with alias name"
                                            variant="outline-warning" size="sm"><i class="fa fa-warning"></i></b-button>
                                    </template>

                                    <template v-if="!props.row.checks.site.hasDefaultEdition.valid">
                                        <b-button
                                            @click="onFixSite(props.row.checks.site.hasDefaultEdition, props.row)"
                                            v-b-tooltip.hover title="Site do not have default edition"
                                            variant="outline-warning" size="sm"><i class="fa fa-warning"></i>
                                        </b-button>
                                    </template>
                                </div>
                            </div>
                        </template>
                        <template #edit="props">
                            <b-btn-group size="sm">
                                <b-btn @click="onEditSite(props.row)" variant="outline-primary">
                                    <i class="icons font-1xl cui-pencil"></i>
                                </b-btn>
                                <b-dropdown id="ddown-right" right text="" variant="primary" size="sm">
                                    <template v-if="!showDeleted">
                                        <b-dropdown-item @click="onAddSiteUser(props.row)"><i
                                            class="fa fa-user-plus" aria-hidden="true"></i>Assign other developers for
                                            this site
                                        </b-dropdown-item>
                                        <b-dropdown-item @click="openAdminPanel(props.row)"><i
                                            class="fa fa-external-link" aria-hidden="true"></i>Go to admin panel
                                        </b-dropdown-item>
                                        <b-dropdown-item @click="buildSkeleton(props.row)"><i
                                            class="fa fa-recycle" aria-hidden="true"></i>Build skeleton
                                        </b-dropdown-item>
                                        <b-dropdown-item @click="forceSSL(props.row)"><i
                                            class="fa fa-certificate" aria-hidden="true"></i>Force obtain SSL
                                        </b-dropdown-item>
                                        <b-dropdown-item @click="onSoftDelete(props.row)"
                                                         variant="danger"><i
                                            class="fa fa-trash-o" aria-hidden="true"></i>Delete
                                        </b-dropdown-item>
                                    </template>
                                    <template v-else>
                                        <b-dropdown-item @click="onDelete(props.row)"
                                                         variant="danger"><i
                                            class="fa fa-trash-o" aria-hidden="true"></i>Delete completely
                                        </b-dropdown-item>
                                        <b-dropdown-item @click="onSoftUndelete(props.row)"><i
                                            class="fa fa-retweet" aria-hidden="true"></i>Restore site
                                        </b-dropdown-item>
                                    </template>
                                </b-dropdown>
                            </b-btn-group>
                        </template>
                    </v-client-table>
                </div>
            </b-card>
        </b-card-group>
        <confirm-modal title="Delete site"
                       :open="modalConfirmDelete"
                       @confirm="onConfirmSoftDelete"
                       @cancel="modalConfirmDelete=false">
            <h5>Are you sure want delete <strong>"{{ currentSite.site_name }}"</strong> site?</h5>
            <b-alert show class="mt-3">To remove domains from DNS, perform manual domain deletions before deleting the
                site and wait for the operation to be completed.
            </b-alert>
        </confirm-modal>
        <b-modal centered id="modalAddSiteUser" v-model="modalAddSiteUser" ok-title="Add" @ok="onOkModalAddSiteUser"
                 title="Assign developers for this site">
            <multiselect
                v-model="selectedAdmins"
                :options="userSelectOptions"
                :multiple="true"
                :close-on-select="true"
                :clear-on-select="false"
                :hide-selected="true"
                :preserve-search="true"
                placeholder="Pick someone"
                label="a_email"
                track-by="a_id">
            </multiselect>
        </b-modal>
    </div>
</template>
<script>

import Multiselect from 'vue-multiselect'

import Api from '@/services/Api'
import ConfirmModal from "@/components/ConfirmModal"
import {
    ADMIN_GET_ALL_NOT_USER,
    SITE_ADD_USER,
    SITE_DELETE,
    SITE_GET_ALL,
    SITE_SOFT_DELETE,
    SITE_SOFT_UNDELETE
} from "@/store/actions"
import SelectDialog from '@/components/dialogs/SelectDialog'

export default {
    name: 'sites',
    components: {ConfirmModal, Multiselect},
    data() {
        return {
            showDeleted: false,
            modalAddSiteUser: false,
            selectedAdmins: '',
            userSelectOptions: [],
            modalConfirmDelete: false,
            currentSite: {},
            columns: [
                'site_id',
                'site_alias',
                'site_name',
                'main_domain_name',
                'edit'
            ],
            sites: [],
            options: {
                columnsClasses: {
                    'site_id': 'site_id',
                    'site_alias': 'site_alias',
                    'site_name': 'site_name',
                    'main_domain_name': 'main_domain_name',
                    'edit': 'edit'
                },
                headings: {
                    'site_id': 'ID',
                    'site_alias': 'Alias',
                    'site_name': 'Name',
                    'main_domain_name': 'Host',
                    'edit': ''
                },
                filterAlgorithm: {
                    main_domain_name(row, query) {
                        return (row.domains.some(domain => domain.domain_name.match(query)));
                    }
                }
            }
        }
    },
    mounted() {
        this.loadSites()
    },
    methods: {
        loadSites() {
            this.$store.dispatch(SITE_GET_ALL, {trashed: +this.showDeleted}).then(sites => this.sites = sites)
        },

        onCreateSite() {
            this.$router.push({name: 'Create'})
        },

        onEditSite(site) {
            this.$router.push({name: 'Edit', params: {site_id: site.site_id}})
        },

        onSoftDelete(site) {
            Object.assign(this.currentSite, site);
            this.modalConfirmDelete = true;

        },

        onConfirmSoftDelete() {
            this.modalConfirmDelete = false;
            this.$store.dispatch(SITE_SOFT_DELETE, this.currentSite.site_id).then(() => {
                    this.loadSites()
                }
            )
        },

        onSoftUndelete(site) {
            this.$store.dispatch(SITE_SOFT_UNDELETE, site.site_id).then(() => {
                    this.loadSites()
                }
            )
        },

        onDelete(site) {
            this.$bvModal.msgBoxConfirm('Please confirm that you want to delete everything.', {
                title: 'Please Confirm',
                //size: 'sm',
                //buttonSize: 'sm',
                okVariant: 'danger',
                okTitle: 'Confirm',
                cancelTitle: 'Cancel',
                footerClass: 'p-2',
                hideHeaderClose: false,
                centered: true
            })
                .then(value => {
                    if (!value) return
                    this.$store.dispatch(SITE_DELETE, site.site_id).then(() => {
                            this.loadSites()
                        }
                    )
                })
        },

        buildSkeleton(site) {
            this.$bvModal.msgBoxConfirm('Please confirm that you want to run skeleton builder.', {
                title: 'Please Confirm',
                //size: 'sm',
                //buttonSize: 'sm',
                okVariant: 'warning',
                okTitle: 'Build',
                cancelTitle: 'Cancel',
                footerClass: 'p-2',
                hideHeaderClose: false,
                centered: true
            })
                .then(value => {
                    if (!value) return

                    Api.post('/site/buildSkeleton', {site_id: site.site_id})
                        .then(result => {
                            this.$awn.success('Skeleton built successfully')
                        })
                        .catch(error => {
                            this.$awn.alert(error.message)
                        })
                })
        },

        onAddSiteUser(site) {
            this.$store.dispatch(ADMIN_GET_ALL_NOT_USER, {site_id: site.site_id}).then(admins => {
                    if (admins.length === 0) {
                        this.$awn.info('There are no available users to add. It looks like all admins are already Developers on this site')
                        return
                    }
                    this.selectedAdmins = []
                    Object.assign(this.currentSite, site);
                    this.userSelectOptions = admins
                    this.modalAddSiteUser = true
                }
            )
        },

        onOkModalAddSiteUser() {
            this.modalAddSiteUser = false;
            this.selectedAdmins.forEach(admin => {
                this.$store.dispatch(SITE_ADD_USER, {
                    site_id: this.currentSite.site_id,
                    admin_id: admin.a_id
                }).then(() => {
                    this.$awn.success(`User ${admin.a_email} have been added`)
                }).catch(error => {
                    this.$awn.alert(error.message)
                })
            })
        },

        openAdminPanel(site) {
            let win = window.open(`/api/site-exo-admin/${site.site_id}`, '_blank')
            win.focus();
        },

        setFilter() {
            this.showDeleted = !this.showDeleted
            this.loadSites()
        },

        onFixSite(check, row) {
            this.$bvModal.msgBoxConfirm('Please confirm that you want to fix this issue.', {
                title: 'Please Confirm',
                //size: 'sm',
                //buttonSize: 'sm',
                okVariant: 'warning',
                okTitle: 'Confirm',
                cancelTitle: 'Cancel',
                footerClass: 'p-2',
                hideHeaderClose: false,
                centered: true
            })
                .then(value => {
                    if (!value) return
                    Api.post(check.api, {site_id: row.site_id}).then(data => {
                        this.$awn.success(data.data.message)
                        this.loadSites()
                    }).catch(error => {
                        this.$awn.alert(error.message)
                        this.loadSites()
                    })
                })

        },

        async forceSSL(row) {
            let site = await Api.get('/site/get', {params: {site_id: row.site_id}})
                .then(response => response.data)
            let response = await this.$dialogs.show(SelectDialog, {
                title: 'Select a domain',
                tree: site.domains,
                //default: this.presetForms[0]['name'],
                treeProps: {
                    idKey: 'domain_id',
                    labelKey: 'domain_name',
                }
            })
            if (response.trigger !== 'ok') return

            let res = await Api.post('/domain/forceSSL', {
                site_id: row.site_id,
                domain_id: response.value
            }).then(response => response.data)

            this.$awn.success(res.message)
            await this.loadSites()
        },
    }
}
</script>

<style lang="scss">
.sites {
    .VueTables__limit-field {
        display: flex;
    }
}
</style>
