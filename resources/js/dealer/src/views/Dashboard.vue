<template>
    <div class="animated fadeIn">
        <b-row>
            <b-col sm="6" md="2">
                <b-card>
                    <div class="mb-4 d-flex justify-content-between">
                        <p v-if="!verInfo.version.COMMIT_REF_NAME" class="mb-0 mr-3 font-weight-bold"></p>
                        <p v-else-if="isLastVersion" class="mb-0 mr-3 text-info font-weight-bold">This is last
                            version.</p>
                        <p v-else-if="isVersionUnknown" class="mb-0 mr-3 text-danger font-weight-bold">
                            Failed to get update information.
                        </p>
                        <p v-else class="mb-0 mr-3  text-warning font-weight-bold">A new version is available.</p>

                        <div class="h1 mb-0 text-muted">
                            <i class="icon-info"></i>
                        </div>
                    </div>
                    <div class="h4 mb-0">{{verInfo.version.COMMIT_REF_NAME}} - {{verInfo.version.COMMIT_SHORT_SHA}}
                    </div>
                    <small class="text-muted text-uppercase font-weight-bold">Version</small>
                    <b-progress height={} class="progress-xs mt-3 mb-0" variant="info" :value="0"></b-progress>
                </b-card>
            </b-col>
            <b-col sm="6" md="2">
                <b-card>
                    <div class="h1 text-muted text-right mb-4">
                        <i class="icon-layers"></i>
                    </div>
                    <div class="h4 mb-0">{{sitesCount}}</div>
                    <small class="text-muted text-uppercase font-weight-bold">Sites</small>
                    <b-progress height={} class="progress-xs mt-3 mb-0" variant="info" :value="25"></b-progress>
                </b-card>
            </b-col>
            <b-col sm="6" md="2">
                <b-card>
                    <div class="h1 text-muted text-right mb-4">
                        <i class="icon-people"></i>
                    </div>
                    <div class="h4 mb-0">{{usersCount}}</div>
                    <small class="text-muted text-uppercase font-weight-bold">Admin</small>
                    <b-progress height={} class="progress-xs mt-3 mb-0" variant="success" :value="25"></b-progress>
                </b-card>
            </b-col>
        </b-row>
    </div>
</template>

<script>

import Api from '../services/Api'
import isEmpty from '@modules/isEmpty'

export default {
    name: 'dashboard',
    components: {},
    data: function () {
        return {
            sitesCount: '-',
            usersCount: '-',
            verInfo: {
                last_version: {
                    COMMIT_SHORT_SHA: ''
                },
                version: {
                    COMMIT_SHORT_SHA: ''
                }
            },
        }
    },
    computed: {
        isLastVersion() {
            return this.verInfo.version.COMMIT_SHORT_SHA === this.verInfo.last_version.COMMIT_SHORT_SHA
        },
        isVersionUnknown() {
            return isEmpty(this.verInfo.last_version.COMMIT_SHORT_SHA)
        }
    },
    methods: {
        getInfo() {
            let me = this;
            Api.get('/admin/all').then(result => {
                me.usersCount = result.data.length;
            })
            Api.get('/site/all').then(result => {
                me.sitesCount = result.data.length;
            })
            Api.get('/server/version').then(result => {
                me.verInfo = result.data
            })
        }
    },
    mounted() {
        this.getInfo()
    }


}
</script>

<style>
/* IE fix */
#card-chart-01, #card-chart-02 {
    width: 100% !important;
}
</style>
