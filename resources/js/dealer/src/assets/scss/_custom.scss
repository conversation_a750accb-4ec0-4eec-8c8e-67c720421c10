// Here you can add other styles
.setup-2fa {
    .card-body {
        min-height: 400px;
    }
}

.table-responsive {
    overflow-x: unset;
}

.VueTables__table {
    overflow: auto !important;

    &__search-field, &__limit-field {
        display: flex;

        label {
            margin-right: 0.5rem;
        }
    }

    &__sortable {
        .fa.pull-right {
            margin-top: .3em;
            color: $gray-500;
        }
    }
}

.VueTables {
    .site_id {
        width: 4%;
    }

    .site_name {
        width: 35%;
    }

    .site_alias {
        width: 20%;
    }

    .edit {
        width: 110px;
    }
}

.card > .tabs {
    > div > .nav.nav-tabs {
        padding: $card-spacer-y $card-spacer-x 0 $card-spacer-y;
    }

    > .tab-content {
        margin-top: 0;
        border: 0;

        > .tab-pane {
            padding: ($card-spacer-x);
        }
    }
}
