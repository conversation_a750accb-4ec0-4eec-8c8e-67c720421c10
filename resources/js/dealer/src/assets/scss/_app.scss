.app {
    &__splash {
        background-image: url("/app/img/lina-loos-1116698-unsplash.jpg");
        background-size: cover;
    }
}

.block-loader {
    position: relative;

    &.block-loader--active {
        &:before {
            position: absolute;
            top: 0;
            bottom: 0;
            right: 0;
            left: 0;
            //background:rgba(0,0,0,.4);
            background: rgba(253, 253, 253, 0.80);
            pointer-events: all;
            content: "";
            z-index: 10000;
        }

        &:after {
            content: "\f110";
            display: inline-block;
            color: $primary;
            font: normal normal normal 14px/1 FontAwesome;
            text-rendering: auto;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            font-size: 3em;
            animation: fa-spin 2s infinite linear;
            position: absolute;
            top: 50%;
            left: 50%;
            margin-top: -5%;
            margin-left: -5%;
            z-index: 10001;
            text-shadow: 1px 1px 1px #000;
        }
    }
}

