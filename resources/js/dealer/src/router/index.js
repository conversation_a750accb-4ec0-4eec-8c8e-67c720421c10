import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

// Containers
const DefaultContainer = () => import('@/containers/DefaultContainer')

// Views
const Dashboard = () => import('@/views/Dashboard')
const Admins = () => import('@/views/Admins')
const Profile = () => import('@/views/admins/Profile')
const Sites = () => import('@/views/Sites')
const Configuration = () => import('@/views/Configuration')
const EditSite = () => import('@/views/sites/EditSite')

// Views - Pages
const Page404 = () => import('@/views/pages/Page404')
const Page500 = () => import('@/views/pages/Page500')
const Login = () => import('@/views/pages/Login')
const Register = () => import('@/views/pages/Register')
const Setup2fa = () => import('@/views/pages/Setup2fa')
const ChangePassword = () => import('@/views/pages/ChangePassword')

import store from '../store'
import {ADMIN_REQUEST_ME, SAVE_ROUTE_BEFORE_AUTH} from "@/store/actions"
import SchedulesTable from "@/views/schedules/SchedulesTable.vue";
import Schedule from "@/views/schedules/Schedule.vue";


const ifNotAuthenticated = (to, from, next) => {
    if (!store.getters.isAdmin) {
        next()
        return
    }
    next('/')
}

const ifAuthenticated = (to, from, next) => {
    if (store.state.auth.status === '') {
        store.dispatch(ADMIN_REQUEST_ME).then(() => {
            //In case lost local storage
            next(to.path)
        })
    }

    if (store.getters.isAdmin) {
        next()
        return
    }
    store.dispatch(SAVE_ROUTE_BEFORE_AUTH, to.path)
    next('/pages/login')
}


export default new Router({
    mode: 'history',
    base: '/app/',
    linkActiveClass: 'open active',
    scrollBehavior: () => ({y: 0}),
    routes: [
        {
            path: '/',
            redirect: '/dashboard',
            name: 'Home',

            component: DefaultContainer,
            beforeEnter: ifAuthenticated,
            children: [
                {
                    path: 'dashboard',
                    name: 'Dashboard',
                    beforeEnter: ifAuthenticated,
                    component: Dashboard
                },
                {
                    path: 'configuration',
                    name: 'Configuration',
                    beforeEnter: ifAuthenticated,
                    component: Configuration
                },
                {
                    path: 'admins',
                    name: 'Admins',
                    redirect: '/admins',
                    component: {
                        render(c) {
                            return c('router-view')
                        }
                    },
                    children: [
                        {
                            path: '',
                            name: 'All admins',
                            beforeEnter: ifAuthenticated,
                            component: Admins
                        },
                        {
                            path: 'create',
                            name: 'Create admin',
                            beforeEnter: ifAuthenticated,
                            component: Profile
                        },
                        {
                            path: 'edit/:a_id',
                            name: 'Edit admin',
                            beforeEnter: ifAuthenticated,
                            component: Profile
                        }
                    ]
                }, {
                    path: 'sites',
                    name: 'Sites',
                    redirect: '/sites',
                    component: {
                        render(c) {
                            return c('router-view')
                        }
                    },
                    children: [
                        {
                            path: '',
                            name: 'All',
                            beforeEnter: ifAuthenticated,
                            component: Sites
                        },
                        {
                            path: 'create',
                            name: 'Create',
                            beforeEnter: ifAuthenticated,
                            component: EditSite
                        },
                        {
                            path: 'edit/:site_id',
                            name: 'Edit',
                            beforeEnter: ifAuthenticated,
                            component: EditSite
                        }
                    ]
                },
                {
                    path: 'schedules',
                    name: 'Schedules',
                    redirect: '/schedules',
                    component: {
                        render(c) {
                            return c('router-view')
                        }
                    },
                    children: [
                        {
                            path: '',
                            name: 'All schedules',
                            beforeEnter: ifAuthenticated,
                            component: SchedulesTable
                        },
                        {
                            path: 'edit/:schedule_id',
                            props: true,
                            name: 'Edit schedule',
                            beforeEnter: ifAuthenticated,
                            component: Schedule
                        }
                    ]
                },

            ]

        },
        {
            path: '/pages',
            redirect: '/pages/404',
            name: 'Pages',
            component: {
                render(c) {
                    return c('router-view')
                }
            },
            children: [
                {
                    path: '404',
                    name: 'Page404',
                    component: Page404
                },
                {
                    path: '500',
                    name: 'Page500',
                    component: Page500
                },
                {
                    path: 'register',
                    name: 'Register',
                    component: Register
                },
                {
                    path: 'login',
                    name: 'Login',
                    component: Login,
                    beforeEnter: ifNotAuthenticated,
                },
                {
                    path: '2fa',
                    name: 'Setup2fa',
                    component: Setup2fa,
                    beforeEnter: ifAuthenticated
                },
                {
                    path: 'change-password',
                    name: 'ChangePassword',
                    component: ChangePassword
                },
            ]
        }
    ]
})
