/* eslint-disable no-unused-vars */
import axios from 'axios'
import {AUTH_LOGOUT} from "@/store/actions";
import store from '@/store'
import router from '@/router'
import App from "@/main"
import getErrorMessage from '../shared/getErrorMessage'

class Api {
    constructor() {

        this.progress = false;
        this.timer = 0;
        let service = axios.create({
            baseURL: '/api/',
            withCredentials: false,
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });
        service.interceptors.request.use(this.handleRequest.bind(this));
        service.interceptors.response.use(this.handleSuccess.bind(this), this.handleError.bind(this));
        this.service = service;

    }

    handleRequest(config) {
        this.progress = true
        App.$Progress.start()
        this.checkProgress()
        return config;
    }

    handleSuccess(response) {
        if (!response.data.success) {
            if (this.progress) {
                this.progress = false;
                if (this.timer) {
                    clearTimeout(this.timer);
                    this.timer = 0;
                }
                App.$Progress.fail()
            }
            //normalize
            response.message = response.data.error
            return Promise.reject(response)
        }
        if (this.progress) {
            this.progress = false;
            if (this.timer) {
                clearTimeout(this.timer);
                this.timer = 0;
            }
            App.$Progress.finish()
        }
        if (response.data && response.data.info) {
            if (Array.isArray(response.data.info)) {
                response.data.info.forEach((info) => {
                    App.$awn.info(info);
                })
            } else {
                App.$awn.info(response.data.info);
            }
        }
        return response.data;
    }

    handleError = (error) => {
        let me = this
        return new Promise(function (resolve, reject) {
            if (me.progress) {
                me.progress = false;
                if (me.timer) {
                    clearTimeout(me.timer);
                    me.timer = 0;
                }
                App.$Progress.fail()
            }
            if (error.response.status === 401 && error.config && !error.config.__isRetryRequest) {
                //if you ever get an unauthorized, logout the admin
                store.dispatch(AUTH_LOGOUT)
                // you can also redirect to /login if needed !
                router.push({name: 'Login'});

            }
            let message = getErrorMessage(error)
            App.$awn.alert(message);

            throw error;
        });
    }

    checkProgress() {
        let me = this
        me.timer = setTimeout(function () {
            if (me.progress) {
                App.$Progress.set(10)
                me.checkProgress()
            }
        }, 6000)
    }

}

export default new Api().service;
