import Api from "@/services/Api";

/**
 * Domain Validation Service
 * Provides comprehensive domain validation with visual feedback states
 */
export default class DomainValidationService {
    constructor() {
        // No caching - always fetch fresh data for accurate domain validation
    }

    /**
     * Validation states enum
     */
    static STATES = {
        IDLE: 'idle',
        CHECKING: 'checking',
        VALID: 'valid',
        INVALID: 'invalid',
        WARNING: 'warning',
        ERROR: 'error',
        DNS_MISMATCH: 'dns_mismatch',
        DNS_FIXABLE: 'dns_fixable',
        WWW_DETECTED: 'www_detected'
    };

    /**
     * Validate domain with comprehensive checks
     * @param {string} domainName - Full domain name to validate
     * @param {Array} existingDomains - Array of existing domains to check against
     * @returns {Promise<Object>} Validation result with state and message
     */
    async validateDomain(domainName, existingDomains = []) {
        if (!domainName || domainName.trim() === '') {
            return {
                state: DomainValidationService.STATES.IDLE,
                message: '',
                canCreate: false
            };
        }

        const trimmedDomain = domainName.trim().toLowerCase();

        // Always perform fresh validation - no caching
        return await this._performValidation(trimmedDomain, existingDomains);
    }

    /**
     * Perform comprehensive domain validation
     * @private
     */
    async _performValidation(domainName, existingDomains) {
        try {
            // 1. Basic format validation
            const formatCheck = this._validateFormat(domainName);
            if (!formatCheck.valid) {
                return {
                    state: DomainValidationService.STATES.INVALID,
                    message: formatCheck.message,
                    canCreate: false
                };
            }

            // 2. Check if www subdomain was entered and normalize domain
            const wwwCheck = this._checkWwwSubdomain(domainName);
            let normalizedDomain = domainName;
            if (wwwCheck.isWww) {
                // Use the root domain for all subsequent checks
                normalizedDomain = wwwCheck.rootDomain;
            }

            // 3. Check for duplicates in existing domains (using normalized domain)
            console.log('🔍 About to check duplicates for normalized domain:', normalizedDomain);
            const duplicateCheck = this._checkDuplicates(normalizedDomain, existingDomains);
            console.log('🔍 Duplicate check result:', duplicateCheck);

            if (!duplicateCheck.valid) {
                console.log('❌ DUPLICATE FOUND - STOPPING VALIDATION');
                return {
                    state: DomainValidationService.STATES.INVALID,
                    message: duplicateCheck.message,
                    canCreate: false
                };
            }

            console.log('✅ No duplicate found - continuing validation');

            // Continue validation with the normalized domain
            domainName = normalizedDomain;

            // 4. Check if domain exists globally in database
            const globalExistenceCheck = await this._checkGlobalDomainExistence(domainName);
            if (!globalExistenceCheck.valid) {
                return {
                    state: DomainValidationService.STATES.INVALID,
                    message: globalExistenceCheck.message,
                    canCreate: false
                };
            }

            // 5. Check DNS provider availability (Cloudflare)
            const dnsCheck = await this._checkDnsProvider(domainName);
            if (dnsCheck.state === DomainValidationService.STATES.ERROR) {
                return dnsCheck;
            }

            // If domain not found in Cloudflare - Case 2
            if (!dnsCheck.foundInCloudflare && !dnsCheck.parentInCloudflare) {
                return {
                    state: DomainValidationService.STATES.INVALID,
                    message: 'This domain was not found in Cloudflare. Please try another or verify Cloudflare account before connecting.',
                    canCreate: false,
                    actionType: 'cloudflare_not_found'
                };
            }

            // 6. Check DNS IP status
            const dnsIpCheck = await this._checkDnsIpStatus(domainName);

            // Handle DNS IP check errors - but domain is still in Cloudflare
            if (dnsIpCheck.state === DomainValidationService.STATES.ERROR) {
                // If domain is in Cloudflare but DNS check fails (permissions, etc.)
                // Allow user to proceed with force add option
                return {
                    state: DomainValidationService.STATES.DNS_FIXABLE,
                    message: 'Domain is in Cloudflare but DNS check failed due to permissions.',
                    canCreate: true,
                    actionType: 'force_add_permissions',
                    actionText: 'Add domain anyway (DNS check failed)',
                    details: {
                        cloudflare: '✅ Domain is registered in Cloudflare',
                        dnsIp: '❌ DNS check failed: ' + (dnsIpCheck.message || 'Permission error'),
                        action: '✅ Option to add domain anyway'
                    }
                };
            }

            // Case 3a: DNS IP Mismatch (Fix Available)
            if (dnsIpCheck.state === DomainValidationService.STATES.DNS_MISMATCH) {
                const result = {
                    state: DomainValidationService.STATES.DNS_MISMATCH,
                    message: 'DNS IP does not match expected server IP.',
                    canCreate: true,
                    actionType: 'fix_dns_ip',
                    actionText: 'Self-correct IP for this domain',
                    wwwRecordExists: dnsCheck.wwwRecordExists || false,
                    details: {
                        cloudflare: '✅ Domain is registered in Cloudflare',
                        dnsIp: '❌ DNS IP does not match expected server IP',
                        action: '✅ Option to self-correct IP available'
                    }
                };

                // Add WWW Page Rule status to details
                if (dnsCheck.wwwRecordExists) {
                    result.details.www = '✅ WWW Page Rule or DNS record already exists';
                } else {
                    result.details.www = '⚪ WWW Page Rule not found (can be created)';
                }

                return result;
            }

            // Case 3b: DNS IP Fixable (Add new IP)
            if (dnsIpCheck.state === DomainValidationService.STATES.DNS_FIXABLE) {
                const result = {
                    state: DomainValidationService.STATES.DNS_FIXABLE,
                    message: 'DNS configuration needs to be created.',
                    canCreate: true,
                    actionType: 'create_dns_record',
                    actionText: 'Create an A DNS file for server',
                    wwwRecordExists: dnsCheck.wwwRecordExists || false,
                    details: {
                        cloudflare: '✅ Domain is in Cloudflare',
                        dnsIp: '❌ IP is incorrect',
                        action: '✅ Option to create A DNS record'
                    }
                };

                // Add WWW Page Rule status to details
                if (dnsCheck.wwwRecordExists) {
                    result.details.www = '✅ WWW Page Rule or DNS record already exists';
                } else {
                    result.details.www = '⚪ WWW Page Rule not found (can be created)';
                }

                return result;
            }

            // Case 4: DNS OK
            if (dnsIpCheck.state === DomainValidationService.STATES.VALID) {
                const result = {
                    state: DomainValidationService.STATES.VALID,
                    message: 'Domain is ready to be added',
                    canCreate: true,
                    actionType: 'dns_ok',
                    wwwRecordExists: dnsCheck.wwwRecordExists || false, // Pass WWW record status
                    details: {
                        cloudflare: '✅ Domain is in Cloudflare',
                        dnsIp: '✅ DNS IP matches our server',
                        optional: '✅ Optional: Create a CNAME for subdomain'
                    }
                };

                // Add WWW Page Rule status to details
                if (dnsCheck.wwwRecordExists) {
                    result.details.www = '✅ WWW Page Rule or DNS record already exists';
                } else {
                    result.details.www = '⚪ WWW Page Rule not found (can be created)';
                }

                // Case 5: WWW Subdomain Detected
                if (wwwCheck.isWww) {
                    result.state = DomainValidationService.STATES.WWW_DETECTED;
                    result.message = 'WWW subdomain detected - no extra step needed if DNS is correct';
                    result.details.www = '✅ WWW subdomain - no extra configuration needed';
                }

                return result;
            }

            // Fallback
            return {
                state: DomainValidationService.STATES.ERROR,
                message: 'Unable to validate domain. Please try again.',
                canCreate: false
            };

        } catch (error) {
            console.error('Domain validation error:', error);
            return {
                state: DomainValidationService.STATES.ERROR,
                message: 'Validation failed due to network error. Please try again.',
                canCreate: false
            };
        }
    }

    /**
     * Validate domain name format
     * @private
     */
    _validateFormat(domainName) {
        // Basic length check
        if (domainName.length > 253) {
            return { valid: false, message: 'Domain name is too long (max 253 characters)' };
        }

        // Check for valid characters and structure
        const domainRegex = /^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?(\.[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/;
        if (!domainRegex.test(domainName)) {
            return { valid: false, message: 'Invalid domain format. Use only letters, numbers, and hyphens.' };
        }

        // Check each label (part between dots)
        const labels = domainName.split('.');
        for (const label of labels) {
            if (label.length === 0) {
                return { valid: false, message: 'Domain cannot have empty parts' };
            }
            if (label.length > 63) {
                return { valid: false, message: 'Domain part is too long (max 63 characters per part)' };
            }
            if (label.startsWith('-') || label.endsWith('-')) {
                return { valid: false, message: 'Domain parts cannot start or end with hyphens' };
            }
        }

        // Must have at least one dot for valid domain
        if (labels.length < 2) {
            return { valid: false, message: 'Please enter a valid domain name (e.g., example.com)' };
        }

        return { valid: true, message: 'Valid domain format' };
    }

    /**
     * Check for duplicate domains
     * @private
     */
    _checkDuplicates(domainName, existingDomains) {
        console.log('🔍 Checking duplicates for:', domainName);
        console.log('🔍 Existing domains:', existingDomains);
        console.log('🔍 Existing domain objects structure:', existingDomains.map(d => ({
            domain_name: d.domain_name,
            keys: Object.keys(d)
        })));

        const duplicate = existingDomains.find(domain => {
            const domainNameToCheck = domain.domain_name || domain.name || domain.domainName;
            console.log('🔍 Comparing:', domainNameToCheck, 'with', domainName);
            return domainNameToCheck && domainNameToCheck.toLowerCase() === domainName.toLowerCase();
        });

        console.log('🔍 Duplicate found:', duplicate);

        if (duplicate) {
            return {
                valid: false,
                message: 'This domain is already added to this site'
            };
        }

        return { valid: true, message: 'No duplicate found' };
    }

    /**
     * Check if www subdomain was entered
     * @private
     */
    _checkWwwSubdomain(domainName) {
        const isWww = domainName.toLowerCase().startsWith('www.');
        const rootDomain = isWww ? domainName.substring(4) : domainName;

        return {
            isWww,
            rootDomain
        };
    }

    /**
     * Check DNS provider (Cloudflare) availability
     * @private
     */
    async _checkDnsProvider(domainName) {
        try {
            const response = await Api.post('/domain/checkDnsProvider', {
                domain_name: domainName
            });

            const data = response.data;

            return {
                state: DomainValidationService.STATES.VALID,
                foundInCloudflare: data.found_in_cloudflare,
                parentInCloudflare: data.parent_in_cloudflare,
                parentDomain: data.parent_domain,
                wwwRecordExists: data.www_record_exists || false,
                message: data.message
            };
        } catch (error) {
            // Check if this is a backend error response with a message
            if (error.response && error.response.data && error.response.data.message) {
                const backendMessage = error.response.data.message;

                // Handle specific error cases
                if (backendMessage.includes('Access denied') ||
                    backendMessage.includes('10005') ||
                    backendMessage.includes('can_update: false')) {
                    return {
                        state: DomainValidationService.STATES.ERROR,
                        foundInCloudflare: false,
                        parentInCloudflare: false,
                        message: 'Access denied: Unable to check domain in Cloudflare. Please verify your account permissions.'
                    };
                }

                // If the backend message indicates domain not found in Cloudflare,
                // don't treat it as an error - let the validation logic handle it
                if (backendMessage.includes('Failed to check DNS provider') ||
                    backendMessage.includes('Domain not found')) {
                    return {
                        state: DomainValidationService.STATES.VALID,
                        foundInCloudflare: false,
                        parentInCloudflare: false,
                        message: backendMessage
                    };
                }

                // Return the specific backend error message
                return {
                    state: DomainValidationService.STATES.ERROR,
                    foundInCloudflare: false,
                    parentInCloudflare: false,
                    message: backendMessage
                };
            }

            return {
                state: DomainValidationService.STATES.ERROR,
                foundInCloudflare: false,
                parentInCloudflare: false,
                message: 'Unable to check DNS provider. Please try again.'
            };
        }
    }

    /**
     * Check DNS IP status against expected server IP
     * @private
     */
    async _checkDnsIpStatus(domainName) {
        try {
            // Always force refresh for domain validation - don't use cached results
            const response = await Api.post('/domain/checkDnsStatus', {
                domain_name: domainName,
                force_refresh: true
            });

            const data = response.data;

            if (data.status === 'OK') {
                return {
                    state: DomainValidationService.STATES.VALID,
                    message: 'DNS IP matches server IP'
                };
            } else if (data.status === 'Update DNS' && data.can_update) {
                return {
                    state: DomainValidationService.STATES.DNS_MISMATCH,
                    message: 'DNS IP mismatch - can be fixed'
                };
            } else if (data.status === 'Check DNS') {
                return {
                    state: DomainValidationService.STATES.DNS_FIXABLE,
                    message: 'DNS record needs to be created'
                };
            } else {
                return {
                    state: DomainValidationService.STATES.ERROR,
                    message: data.message || 'DNS status check failed'
                };
            }
        } catch (error) {
            // Handle different types of errors
            if (error.response && error.response.data) {
                const errorData = error.response.data;

                // Check for specific error messages from backend
                if (errorData.message) {
                    if (errorData.message.includes('Access denied') ||
                        errorData.message.includes('10005') ||
                        errorData.message.includes('can_update: false')) {
                        return {
                            state: DomainValidationService.STATES.ERROR,
                            message: 'Access denied: Unable to manage DNS for this domain. Please check your Cloudflare permissions.'
                        };
                    }

                    return {
                        state: DomainValidationService.STATES.ERROR,
                        message: errorData.message
                    };
                }
            }

            return {
                state: DomainValidationService.STATES.ERROR,
                message: 'Unable to check DNS status. Please try again.'
            };
        }
    }

    /**
     * Check if domain exists globally in database
     * @private
     */
    async _checkGlobalDomainExistence(domainName) {
        try {
            const response = await Api.post('/domain/checkDomainExists', {
                domain_name: domainName
            });

            if (response.data.exists) {
                return {
                    valid: false,
                    message: 'This domain already exists in the system'
                };
            }

            return {
                valid: true,
                message: 'Domain is available'
            };

        } catch (error) {
            console.error('Global domain existence check failed:', error);
            // If check fails, allow to continue (don't block on API error)
            return {
                valid: true,
                message: 'Could not verify domain availability'
            };
        }
    }

    /**
     * Check domain ownership/availability
     * @private
     */
    async _checkOwnership(domainName) {
        try {
            const response = await Api.post('/domain/checkOwnership', {
                domain_name: domainName
            });

            const data = response.data;
            
            if (data.can_manage) {
                return {
                    state: DomainValidationService.STATES.VALID,
                    message: 'Domain ownership verified'
                };
            } else {
                return {
                    state: DomainValidationService.STATES.WARNING,
                    message: data.message || 'Domain ownership could not be verified'
                };
            }
        } catch (error) {
            // If ownership check fails, we'll allow creation with warning
            return {
                state: DomainValidationService.STATES.WARNING,
                message: 'Could not verify domain ownership. Proceed with caution.'
            };
        }
    }

    /**
     * Clear validation cache
     */
    clearCache() {
        this.validationCache.clear();
    }

    /**
     * Get validation state icon
     */
    static getStateIcon(state) {
        switch (state) {
            case DomainValidationService.STATES.CHECKING:
                return 'fa-spinner fa-spin';
            case DomainValidationService.STATES.VALID:
                return 'fa-check-circle';
            case DomainValidationService.STATES.INVALID:
                return 'fa-times-circle';
            case DomainValidationService.STATES.WARNING:
                return 'fa-exclamation-triangle';
            case DomainValidationService.STATES.ERROR:
                return 'fa-exclamation-circle';
            case DomainValidationService.STATES.DNS_MISMATCH:
                return 'fa-wrench';
            case DomainValidationService.STATES.DNS_FIXABLE:
                return 'fa-plus-circle';
            case DomainValidationService.STATES.WWW_DETECTED:
                return 'fa-check-circle';
            default:
                return '';
        }
    }

    /**
     * Get validation state color
     */
    static getStateColor(state) {
        switch (state) {
            case DomainValidationService.STATES.CHECKING:
                return 'text-info';
            case DomainValidationService.STATES.VALID:
                return 'text-success';
            case DomainValidationService.STATES.INVALID:
                return 'text-danger';
            case DomainValidationService.STATES.WARNING:
                return 'text-warning';
            case DomainValidationService.STATES.ERROR:
                return 'text-danger';
            case DomainValidationService.STATES.DNS_MISMATCH:
                return 'text-warning';
            case DomainValidationService.STATES.DNS_FIXABLE:
                return 'text-info';
            case DomainValidationService.STATES.WWW_DETECTED:
                return 'text-success';
            default:
                return 'text-muted';
        }
    }
}
