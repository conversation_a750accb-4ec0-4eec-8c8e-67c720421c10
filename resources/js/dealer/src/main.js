import './bootstrap'
import Vue from 'vue'
import App from './App'
import router from './router'
import store from './store'

import {BootstrapVue} from 'bootstrap-vue'
Vue.use(BootstrapVue)

import VueTruncate  from 'vue-truncate-filter'
Vue.use(VueTruncate)

import VueAW<PERSON> from "vue-awesome-notifications"
Vue.use(VueAWN)

import VueProgressBar from 'vue-progressbar'
Vue.use(VueProgressBar, {
    color: '#41af67',
    failedColor: '#f86c6b',
    thickness: '3px',
    transition: {speed: '0.4s', opacity: '0.6s', termination: 500}
})

import {ClientTable, ServerTable} from 'vue-tables-2'
let vueTableConfig = {
    theme: 'bootstrap4',
    perPage: 50,
    saveState: true,
    sortIcon: {
        base: 'fa',
        up:
            'fa-sort-asc',
        down:
            'fa-sort-desc',
        is:
            'fa-sort'
    },
    texts:{
        filter:''
    }
}
Vue.use(ClientTable, vueTableConfig)
Vue.use(ServerTable, vueTableConfig)

//Application components registration
import Dialogs from '@modules/dialogs'
Vue.use(Dialogs);

/*import Components from '@@/components'
Vue.use(Components);*/
import XSelect from '@modules/XSelect'
Vue.component('XSelect', XSelect)

//VeeValidate with custom rules
import veeValidate from '@modules/veeValidate'
Vue.use(veeValidate);

/* eslint-disable no-new */
export default new Vue({
    el: '#app',
    router,
    store,
    template: '<App/>',
    components: {
        App
    },
    created: function () {
        //this.$store.dispatch(ADMIN_REQUEST_ME).then(() => {
        //    if (store.getters.authStatus === 'success') {
        //        //me.$router.push(me.$store.state.auth.from_path)
        //    }
        //})
    }
})
