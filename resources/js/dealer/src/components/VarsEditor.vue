<template>
    <b-list-group>
        <b-list-group-item v-for="customvar in varsIntUndeleted" :key="customvar.int_id">
            <key-value :id="customvar.int_id"
                       :name="customvar.var_name"
                       :value="customvar.var_value"
                       @delete="onDelete"
                       @update="onUpdate"></key-value>
        </b-list-group-item>
        <b-list-group-item>
            <b-btn variant="outline-success" @click="onAdd">Add</b-btn>
        </b-list-group-item>
    </b-list-group>
</template>

<script>

import KeyValue from "./KeyValue"

export default {
    name: "VarsEditor",
    model: {
        prop: 'vars',
        event: 'change'
    },
    components: {KeyValue},
    props: {
        vars: {
            type: Array,
            default: () => [],
            required: true,
        }
    },
    data() {
        return {
            id: 0,
            varsInt: []
        }
    },
    watch: {
        vars(newval) {
            this.varsInt = newval.map(item => {
                item.int_id = item.int_id ? item.int_id : item.var_id
                return item
            })
        }
    },
    computed: {
        varsIntUndeleted() {
            return this.varsInt.filter(item => item.$_deleted !== 1)
        }
    },
    methods: {
        genId() {
            return --this.id
        },
        onDelete(id) {
            let idx = this.varsInt.findIndex(item => item.int_id === id);
            if (this.varsInt[idx].int_id < 0) {
                this.varsInt.splice(idx, 1);
            } else {
                this.varsInt[idx].$_deleted = 1
            }
            this.$emit('change', this.varsInt)

        },
        onUpdate(id, name, value) {
            let idx = this.varsInt.findIndex(item => item.int_id === id)
            this.varsInt[idx].var_name = name
            this.varsInt[idx].var_value = value
            this.$emit('change', this.varsInt)
        },
        onAdd() {
            this.varsInt.push(
                {
                    int_id: this.genId(),
                    var_id: 'new',
                    var_name: '',
                    var_value: ''
                })
        }
    }
}
</script>
