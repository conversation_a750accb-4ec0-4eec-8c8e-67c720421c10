<template>
    <b-row no-gutters>
        <b-col cols="4" class="pr-1">
            <b-form-input type="text" placeholder="Enter var name"
                          :value="name"
                          @input="onNameInput"></b-form-input>
        </b-col>
        <b-col cols="7" class="pr-1">
            <b-form-input type="text" placeholder="Enter var value"
                          :value="value"
                          @input="onValueInput"></b-form-input>
        </b-col>
        <b-col cols="1">
            <b-btn @click="onDelete" variant="outline-danger"><i class="fa fa-trash-o" aria-hidden="true"></i></b-btn>
        </b-col>
    </b-row>
</template>

<script>
    export default {
        name: "KeyValue",
        props: {
            id: '',
            name: '',
            value: ''
        },
        methods: {
            onDelete() {
                this.$emit('delete', this.id)
            },
            onValueInput(value) {
                this.$emit('update', this.id, this.name, value)
            },
            onNameInput(name) {
                this.$emit('update', this.id, name, this.value)
            },
        }
    }
</script>

<style scoped>

</style>