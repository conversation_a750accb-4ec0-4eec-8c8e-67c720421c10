<template>
    <b-modal class="modal-danger" :title="title" hide-header-close
             centered
             @ok="onOk"
             @cancel="onCancel"
             v-model="isOpen"
             ok-title="Delete"
             ok-variant="danger">
        <slot>
            Are you sure?
        </slot>
    </b-modal>
</template>

<script>
    export default {
        name: "ConfirmModal",
        props: {
            title: {default:'Exolog'},
            open: false
        },
        data() {
            return {isOpen: false}
        },
        computed: {},
        watch: {
            open: function (newVal) {
                if (newVal) {
                    this.isOpen = true;
                }
            }
        },
        methods: {
            onOk() {
                this.$emit('confirm')
            }
            ,
            onCancel() {
                this.$emit('cancel')
            }
        }
    }
</script>

<style scoped>

</style>
