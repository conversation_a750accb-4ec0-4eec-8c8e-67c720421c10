<template>
    <dialog-modal
        v-bind="$dialogsProps"
        size="lg"
        @hide="onModalHide"
        @hidden="onModalHidden"
        ref="modal">
        <form>
            <b-tabs class="edit-domain" v-model="tabIndex">
                <b-tab title="Domain">
                    <ValidationObserver ref="observer" v-slot="{ invalid, errors }">
                        <b-form-group horizontal
                                      :label-cols="2"
                                      label="Name"
                                      label-for="input_name">
                            <div v-if="localValue.domain_id">
                                <b-form-input id="inputlocalValue_name"
                                              required
                                              type="text"
                                              :disabled="true"
                                              v-model="localValue.domain_name">
                                </b-form-input>
                            </div>
                            <template v-else>
                                <div class="subdomain-name">
                                    <div>
                                        <ValidationProvider :rules="ruleObject"
                                                            name="subdomain"
                                                            :debounce="200"
                                                            v-slot="{ valid, errors }">
                                            <b-input v-model="subdomainValue"
                                                     :disabled="isDisabledValue"
                                                     class="subdomain-name__value"
                                                     :state="errors[0] ? false : null"
                                            />
                                        </ValidationProvider>
                                    </div>
                                    <span class="px-1">.</span>
                                    <ValidationProvider rules="required"
                                                        class="subdomain-name__parent"
                                                        name="parentdomain"
                                                        :debounce="200"
                                                        v-slot="{ valid, errors }">
                                        <x-select v-model="subdomainParent"
                                                  :clearable="false"
                                                  :multiple="false"
                                                  :options="parentDomains"
                                                  :searchable="false"
                                                  valueFormat="object"
                                                  id-key="domain_name"
                                                  label-key="domain_name"/>
                                    </ValidationProvider>
                                    <b-button class="ml-1" variant="outline-primary" @click="onAddParentDomain"><i
                                        class="fa fa-plus"></i></b-button>
                                </div>



                                <div class="mt-2">
                                    <p v-for="(error,index) in errors" :key="index" class="text-danger">
                                        <template v-for="(message,index) in error">
                                        <span :key="index">
                                        {{ message }}
                                    </span><br>
                                        </template>
                                    </p>
                                </div>
                            </template>
                        </b-form-group>
                        <!-- Use for email checkbox hidden as requested -->
                        <!-- <b-form-group horizontal
                                      :label-cols="2"
                                      label="">
                            <b-form-checkbox id="ismail"
                                             value="1"
                                             v-model="localValue.domain_ismail"
                                             unchecked-value="0">
                                Use for email
                            </b-form-checkbox>
                        </b-form-group> -->
                    </ValidationObserver>
                </b-tab>
                <b-tab title="SSL Certificate">
                    <template v-if="canSetupSSL">
                        <b-form-group label-sr-only>
                            <b-form-checkbox id="usehttps"
                                             value="1"
                                             v-model="localValue.domain_isssl"
                                             unchecked-value="0">
                                Use HTTPS
                            </b-form-checkbox>
                        </b-form-group>
                        <b-form-group v-show="localValue.domain_isssl==1"
                                      label-sr-only>
                            <b-form-checkbox id="usele"
                                             value="1"
                                             v-model="localValue.domain_isletsencrypt"
                                             unchecked-value="0">
                                Use Let’s Encrypt
                            </b-form-checkbox>
                        </b-form-group>
                        <b-form-group
                            v-show="localValue.domain_isssl==1">
                            <slot name="label">
                                <label for="ssl_cert">
                                    <span>Certificate</span>
                                    <i v-show="localValue.domain_isletsencrypt==1" class="text-success">
                                        - will be generated automatically
                                    </i>
                                </label>

                            </slot>
                            <b-form-textarea id="ssl_cert"
                                             :disabled="localValue.domain_isletsencrypt==1"
                                             v-model="localValue.ssl.cert"
                                             placeholder=""
                                             :state="localValue.domain_isssl==1 && localValue.domain_isletsencrypt!=1 && localValue.ssl.cert.trim()==''?false:null"
                                             :rows="3"
                                             :max-rows="6">
                            </b-form-textarea>
                            <b-form-invalid-feedback id="inputLiveFeedback1">
                                This field is reqired
                            </b-form-invalid-feedback>
                        </b-form-group>
                        <b-form-group label="Private Key"
                                      v-show="localValue.domain_isssl==1">
                            <b-form-textarea id="ssl_private_key"
                                             :disabled="localValue.domain_isletsencrypt==1"
                                             v-model="localValue.ssl.private_key"
                                             :state="localValue.domain_isssl==1 && localValue.domain_isletsencrypt!=1 && localValue.ssl.private_key.trim()==''?'invalid':null"
                                             placeholder=""
                                             :rows="3"
                                             :max-rows="6">
                            </b-form-textarea>
                            <b-form-invalid-feedback id="inputLiveFeedback2">
                                This field is reqired
                            </b-form-invalid-feedback>
                        </b-form-group>
                        <b-form-group v-show="localValue.domain_isssl==1">
                            <slot name="label">
                                <label for="ssl_CA">
                                    Certificate Authority / Intermediate <span class="text-muted">(Optional)</span>
                                </label>
                            </slot>
                            <b-form-textarea id="ssl_CA"
                                             :disabled="localValue.domain_isletsencrypt==1"
                                             v-model="localValue.ssl.CA"
                                             placeholder=""
                                             :rows="3"
                                             :max-rows="6">
                            </b-form-textarea>
                        </b-form-group>
                    </template>
                    <template v-else>
                        <p class="text-muted">According to server settings, SSL for subdomains is managed automatically
                            on the server.</p>
                    </template>
                </b-tab>
            </b-tabs>
        </form>
        <template #modal-footer>
            <b-button variant="outline-secondary" @click="cancel">Cancel</b-button>
            <b-button
                variant="primary"
                @click="onSave"
                :disabled="!canSaveDomain">
                <i v-if="validationState === 'checking'" class="fa fa-spinner fa-spin mr-1"></i>
                OK
            </b-button>
        </template>
    </dialog-modal>
</template>

<script>

import withBModal from "@modules/dialogs/mixins/withBModal";
import DomainValidationDialog from "./DomainValidationDialog.vue";
import Api from "@/services/Api";

export default {
    name: "DomainEditDialog",
    mixins: [withBModal],
    components: {
        DomainValidationDialog
    },
    props: {
        domains: {
            type: Array,
            required: true
        },
        domain: {
            type: Object,
            required: true
        },
        siteId: {
            type: [String, Number],
            required: true
        }
    },
    data() {
        return {
            newDomains: [],
            tabIndex: 0,
            subdomainValue: '',
            subdomainParent: undefined,
        }
    },
    computed: {
        isInvalidCert() {
            return (
                +this.localValue.domain_isssl === 1 &&
                +this.localValue.domain_isletsencrypt !== 1 &&
                this.localValue.ssl.private_key.trim() === ''
            ) && (
                +this.localValue.domain_isssl === 1 &&
                +this.localValue.domain_isletsencrypt !== 1 &&
                this.localValue.ssl.cert.trim() === ''
            )
        },
        isSubDomain() {
            //subdomain of exolog Server
            const domain_name = this.localValue.domain_name || ''
            const server_name = this.$store.state.server.server_name || ''
            if (domain_name.toLowerCase().endsWith(server_name.toLowerCase())) {
                return true
            }

            let parent = this.domains.find(domain => {
                return domain_name.toLowerCase().endsWith(domain.domain_name.toLowerCase())
            })
            return !!parent;
        },
        canSetupSSL() {
            if (this.$store.state.server.options.USE_SSL_FOR_SUBDOMAIN) return true

            return !this.isSubDomain;
        },
        parentDomains() {
            return [...this.newDomains, ...this.domains]
        },
        isDisabledValue() {
            return !this.subdomainParent || (this.subdomainParent && this.subdomainParent.is_new === true)
        },
        ruleObject() {
            return {
                required: !this.isDisabledValue,
                regex: /^[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?$/,
            }
        },

        fullDomainName() {
            if (!this.subdomainParent || !this.subdomainParent.domain_name) {
                return '';
            }

            const subdomain = this.subdomainValue ? this.subdomainValue.trim() : '';
            return subdomain ? `${subdomain}.${this.subdomainParent.domain_name}` : this.subdomainParent.domain_name;
        },



        canSaveDomain() {
            // For existing domains (editing), always allow save
            if (this.localValue.domain_id) {
                return true;
            }

            // For new domains, just require parent domain selection
            return this.subdomainParent && this.subdomainParent.domain_name;
        }
    },
    created() {
        this.tabIndex = 0;
        this.$set(this, 'localValue', Object.assign({
                domain_id: null,
                domain_name: null,
                domain_ismain: 0,
                domain_isdefault: 0,
                domain_isssl: 1,
                domain_isletsencrypt: 1,
                domain_ismail: 0,
                is_readonly: 0,
                ssl: {
                    cert: '',
                    private_key: '',
                    CA: ''
                }
            }, this.domain)
        )
    },
    methods: {

        isValidDomainName(value) {
            if ((value === '') || (value == null) || (value.length == null)) return false;
            if (value.length > 63) return false;
            const reg = /^([A-Za-z0-9])+[A-Za-z0-9-]*$/
            const domain_array = value.split('.')
            for (let i = 0; i < domain_array.length; i++) {
                if (reg.test(domain_array[i]) === false) return false;
            }
            return true;
        },

        extractDomain(url) {
            return url.replace(/^(?:https?:\/\/)?(?:[^\/]+\.)?([^.\/]+\.[^.\/]+).*$/, "$1");
        },







        async onSave() {
            const isValid = await this.$refs.observer.validate()
            if (!isValid) {
                this.tabIndex = 0;
                return
            }

            if (this.localValue.domain_id === null) {
                this.localValue.domain_name = this.calcDomainName()
            }

            if (!this.isValidDomainName(this.localValue.domain_name)) {
                this.tabIndex = 0;
                this.$refs.observer.setErrors({
                    subdomain: ['Not valid']
                });
                return
            }

            if (this.canSetupSSL && this.isInvalidCert) {
                this.tabIndex = 1;
                return
            }



            // DNS setup is now handled above for subdomains
            // Return the domain data to the parent component
            console.log('🔧 === SUBDOMAIN EDIT DIALOG: PREPARING RESPONSE ===');
            console.log('🔧 DomainEditDialog: this.localValue:', this.localValue);
            console.log('🔧 DomainEditDialog: Domain name calculated:', this.localValue.domain_name);

            const responseData = {
                domainData: this.localValue,
                value: this.localValue.domain_name
            };

            console.log('🔧 DomainEditDialog: Final response object:', responseData);

            this.ok(responseData);
        },



        async onAddParentDomain() {
            console.log('🔍 DEBUG: Opening DomainValidationDialog with domains:', this.domains);
            console.log('🔍 DEBUG: Domain names in array:', this.domains.map(d => d.domain_name));

            const response = await this.$dialogs.show(DomainValidationDialog, {
                message: 'Enter domain name:',
                existingDomains: this.domains,
                title: 'Add Domain',
                siteId: this.siteId
            })

            // 🔍 DEBUG: Log the complete response from DomainValidationDialog
            console.log('🔍 DEBUG: DomainValidationDialog response:', response);
            console.log('🔍 DEBUG: response.trigger:', response.trigger);
            console.log('🔍 DEBUG: response.closeAllModals:', response.closeAllModals);
            console.log('🔍 DEBUG: response.dnsSetupComplete:', response.dnsSetupComplete);
            console.log('🔍 DEBUG: response.domainAlreadySaved:', response.domainAlreadySaved);

            if (response.trigger !== 'ok') return

            // Use the complete domain data from validation dialog if available
            let domain = response.domainData || {
                domain_id: null,
                domain_name: response.value,
                domain_ismail: 0,
                domain_isssl: 1,
                domain_isletsencrypt: 1,
                domain_ismain: 0,
                domain_isdefault: 0,
                domain_edition_id: 1,
                is_readonly: 0,
                is_new: true,
                forceAdd: response.forceAdd || false,
                createWwwRule: response.createWwwRule || false,
                validationState: response.validationState,
                dnsSetupComplete: response.dnsSetupComplete || false
            }
            this.subdomainValue = '';
            this.newDomains.push(domain)
            this.subdomainParent = domain;

            // If closeAllModals flag is set, DNS is already set up, just close
            if (response.closeAllModals) {
                console.log('Closing subdomain dialog after parent domain added');

                if (response.dnsSetupComplete) {
                    // DNS setup and database save were already completed in validation modal
                    console.log('🔧 DNS setup and domain save already completed');
                    console.log('🔧 Response from validation dialog:', response);
                    console.log('🔧 DEBUG: response.setupResponse:', response.setupResponse);
                    console.log('🔧 DEBUG: response.setupResponse.domain_saved:', response.setupResponse?.domain_saved);

                    // Show success notification regardless of domain_saved flag
                    this.$bvToast.toast('Domain has been added to your site', {
                        title: 'Success',
                        variant: 'success',
                        solid: true
                    });

                    // Close modal - domain is already saved, return flag to indicate this
                    console.log('🚀 Closing dialog with domain data:', response.domainData);
                    console.log('🚀 DEBUG: About to call this.ok() with domainAlreadySaved: true');

                    // Preserve the original localValue structure and add our custom properties
                    this.localValue = {
                        ...this.localValue, // Keep existing structure (including ssl property)
                        trigger: 'ok',
                        domainAlreadySaved: true,
                        domainName: domain.domain_name,
                        domainData: response.domainData // Pass the complete domain data
                    };

                    console.log('🚀 DEBUG: localValue set to:', this.localValue);
                    this.ok(); // Call ok() without parameters - it will use localValue
                } else {
                    console.log('DNS setup not completed, this should not happen');
                }
            } else {
                console.log('closeAllModals flag not set:', response);
            }
        },

        calcDomainName() {
            return (this.subdomainValue ? this.subdomainValue + '.' : '') + this.subdomainParent.domain_name
        }
    },




}
</script>

<style lang="scss">

@import "../../assets/scss/vendors/variables";

.edit-domain {
    .tab-pane {
        min-height: 450px
    }
}

.subdomain-name {
    display: flex;
    align-items: flex-end;

    &__value {
        width: 140px;
    }

    &__parent {
        flex: 1 0 auto;
    }

    .x-select {
        flex: 1 0 auto;

        .vue-treeselect__control {
            border-color: $border-color;
        }
    }
}

.domain-validation {
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    background-color: #f8f9fa;
    transition: all 0.3s ease;

    .domain-display {
        font-size: 16px;
        font-weight: 600;
    }

    .validation-status {
        font-size: 14px;
        font-weight: 500;
    }

    .validation-message {
        font-weight: 500;
    }

    .validation-details {
        .detail-item {
            margin-bottom: 2px;
            font-size: 12px;
        }
    }

    .fa-spinner {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    // State-specific styling
    &.validation-checking {
        border-color: #17a2b8;
        background-color: #e7f3ff;
    }

    &.validation-valid {
        border-color: #28a745;
        background-color: #e8f5e8;
    }

    &.validation-warning {
        border-color: #ffc107;
        background-color: #fff8e1;
    }

    &.validation-invalid {
        border-color: #dc3545;
        background-color: #ffeaea;
    }

    &.validation-fixable {
        border-color: #17a2b8;
        background-color: #e7f3ff;
    }
}

</style>

