<template>
    <b-modal modal-class="modal-primary modal-min-btn"
             v-model="isOpen"
             :title="title"
             hide-header-close
             centered
             cancel-title="CANCEL"
             @cancel="onClick"
             cancel-variant="light"
             @ok="onClick"
             ok-title="OK"
             ok-variant="primary"
             ref="modal"
             :ok-disabled="!value">
        <slot>
            <x-select v-model="value" :multiple="false" :options="tree" v-bind="treePropsDefault"/>
        </slot>
    </b-modal>
</template>

<script>

export default {
    name: "SelectDialog",
    props: {
        modalProps: {},
        title: {default: 'Exolog'},
        open: false,
        tree: Array,
        multiple: false,
        default: null,
        treeProps: {
            default() {
                return {
                    clearable: false
                }
            }
        }
    },
    data() {
        return {
            isOpen: false,
            // define the default value
            value: null,
        }
    },

    computed: {
        treePropsDefault() {
            return {
                ...{
                    clearable: false
                },
                ...this.treeProps
            }
        }
    },
    watch: {
        open: function (newVal) {
            if (newVal) {
                this.isOpen = true;
            }
        }
    },
    methods: {
        show() {
            return new Promise((resolve) => {
                this.resolve = resolve;
                if (this.default) {
                    this.value = this.default
                } else {
                    this.value = null
                }
                this.$refs.modal.show();
            })
        },
        onClick(e) {
            if (this.resolve) {
                e.value = this.value
                this.resolve(e)
            }
        }
    }
}
</script>

<style scoped>

</style>
