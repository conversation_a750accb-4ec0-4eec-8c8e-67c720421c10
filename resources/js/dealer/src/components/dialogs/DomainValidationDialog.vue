<template>
    <dialog-modal
        ref="modal"
        v-bind="$dialogsProps"
        @hide="onModalHide"
        @hidden="onModalHidden"
        @ok="handleOk"
        :ok-disabled="!canSave || actionInProgress"
        :ok-title="actionInProgress ? 'Setting up...' : 'OK'"
        @shown="focusInput"
        title="Domain Setup"
        :no-close-on-backdrop="actionInProgress"
        :no-close-on-esc="actionInProgress">
        <div class="domain-setup-dialog">
            <div class="form-group">
                <label for="domainInput" class="form-label">Enter Domain name:</label>
                <b-form-input
                    id="domainInput"
                    v-model="domainName"
                    autocomplete="off"
                    @keypress.native="onKeypress"
                    @input="onDomainInput"
                    trim
                    ref="input"
                    :state="getInputState()"
                    placeholder="example.com"
                    class="domain-input">
                </b-form-input>
            </div>

            <!-- Validation Status List -->
            <div v-if="domainName && domainName.length > 0" class="validation-status-list">
                <!-- Domain Registration Status -->
                <div class="status-item">
                    <i :class="getStatusIcon('cloudflare')" class="status-icon"></i>
                    <span class="status-text">{{ getStatusText('cloudflare') }}</span>
                </div>

                <!-- DNS IP Status -->
                <div class="status-item" v-if="showDnsStatus">
                    <i :class="getStatusIcon('dns')" class="status-icon"></i>
                    <span class="status-text">{{ getStatusText('dns') }}</span>
                </div>

                <!-- Action Text -->
                <div v-if="validationActionText && !showForceAddOption" class="action-text">
                    {{ validationActionText }}
                </div>

                <!-- Force Add Option -->
                <div v-if="showForceAddOption" class="force-add-option">
                    <b-form-checkbox
                        v-model="forceAdd"
                        class="force-add-checkbox">
                        {{ getForceAddText() }}
                    </b-form-checkbox>
                </div>

                <!-- WWW Page Rule Option -->
                <div v-if="showWwwOption" class="www-option">
                    <b-form-checkbox
                        v-model="createWwwRule"
                        class="www-checkbox">
                        Create WWW Page Rule (301 redirect to non-WWW)
                    </b-form-checkbox>
                    <small class="text-muted d-block mt-1 ml-4">
                        Creates a Cloudflare Page Rule to redirect www.domain.com → domain.com
                    </small>
                </div>

                <!-- Action Button (for auto-fix cases) -->
                <div v-if="validationActionText && !showForceAddOption" class="mt-2">
                    <b-button
                        variant="primary"
                        size="sm"
                        @click="handleValidationAction"
                        :disabled="actionInProgress">
                        <i v-if="actionInProgress" class="fa fa-spinner fa-spin mr-1"></i>
                        {{ validationActionText }}
                    </b-button>
                </div>


            </div>
        </div>
    </dialog-modal>
</template>

<script>
import withBModal from "@modules/dialogs/mixins/withBModal";
import DomainValidationService from "@/services/DomainValidationService";
import Api from "@/services/Api";

export default {
    name: "DomainValidationDialog",
    mixins: [withBModal],
    props: {
        message: {
            type: String,
            default: 'Enter domain name:'
        },
        value: {
            type: String,
            default: ''
        },
        existingDomains: {
            type: Array,
            default: () => []
        },
        siteId: {
            type: [String, Number],
            required: true
        }
    },
    data() {
        return {
            domainName: this.value || '',
            // Domain validation state
            validationState: DomainValidationService.STATES.IDLE,
            validationMessage: '',
            validationDetails: null,
            validationActionType: null,
            validationActionText: null,
            validationService: new DomainValidationService(),
            validationTimeout: null,
            actionInProgress: false,
            // Force add option
            forceAdd: false,
            // WWW DNS rule option
            createWwwRule: false,
        }
    },
    computed: {
        canSave() {
            if (!this.domainName || this.domainName.trim() === '') {
                return false;
            }

            // Don't allow save while checking or during action
            if (this.validationState === DomainValidationService.STATES.CHECKING || this.actionInProgress) {
                return false;
            }

            // If force add option is shown, require checkbox to be checked
            if (this.showForceAddOption) {
                return this.forceAdd;
            }

            // Allow save for valid states
            return this.validationState === DomainValidationService.STATES.VALID ||
                   this.validationState === DomainValidationService.STATES.WARNING ||
                   this.validationState === DomainValidationService.STATES.DNS_MISMATCH ||
                   this.validationState === DomainValidationService.STATES.DNS_FIXABLE ||
                   this.validationState === DomainValidationService.STATES.WWW_DETECTED;
        },

        showForceAddOption() {
            // Show force add option for certain validation states, but NOT for invalid domains
            return this.validationState === DomainValidationService.STATES.DNS_MISMATCH ||
                   this.validationState === DomainValidationService.STATES.DNS_FIXABLE;
        },

        showDnsStatus() {
            // Show DNS status when we have validation results
            return this.validationState !== DomainValidationService.STATES.IDLE &&
                   this.validationState !== DomainValidationService.STATES.CHECKING;
        },

        showWwwOption() {
            // Show WWW option when domain is valid or can be auto-fixed and doesn't have www
            const isValidOrFixable = this.validationState === DomainValidationService.STATES.VALID ||
                                   this.validationState === DomainValidationService.STATES.DNS_FIXABLE ||
                                   this.validationState === DomainValidationService.STATES.DNS_MISMATCH;

            const hasNoWww = this.domainName && !this.domainName.toLowerCase().startsWith('www.');

            // Only show if no existing WWW record is found
            const noExistingWwwRecord = !this.validationDetails?.wwwRecordExists;

            console.log('🔍 WWW Option Check:', {
                isValidOrFixable,
                hasNoWww,
                noExistingWwwRecord,
                wwwRecordExists: this.validationDetails?.wwwRecordExists,
                validationDetails: this.validationDetails
            });

            return isValidOrFixable && hasNoWww && noExistingWwwRecord;
        },
        
        validationIconClass() {
            return DomainValidationService.getStateIcon(this.validationState);
        },
        
        validationColorClass() {
            return DomainValidationService.getStateColor(this.validationState);
        }
    },
    methods: {
        focusInput() {
            this.$nextTick(() => {
                if (this.$refs.input) {
                    this.$refs.input.focus();
                }
            });
        },
        
        onKeypress(event) {
            if (event.key === 'Enter' && this.canSave) {
                this.ok();
            }
        },
        
        getInputState() {
            if (this.validationState === DomainValidationService.STATES.VALID || 
                this.validationState === DomainValidationService.STATES.WWW_DETECTED) {
                return true;
            } else if (this.validationState === DomainValidationService.STATES.INVALID || 
                       this.validationState === DomainValidationService.STATES.ERROR) {
                return false;
            }
            return null;
        },
        
        onDomainInput() {
            // Clear previous timeout
            if (this.validationTimeout) {
                clearTimeout(this.validationTimeout);
            }

            // If no domain name, reset to idle
            if (!this.domainName || this.domainName.trim() === '') {
                this.validationState = DomainValidationService.STATES.IDLE;
                this.validationMessage = '';
                this.validationDetails = null;
                this.validationActionType = null;
                this.validationActionText = null;
                return;
            }

            const trimmedValue = this.domainName.trim();

            // Reset validation state when input changes
            this.validationState = DomainValidationService.STATES.IDLE;
            this.validationMessage = '';
            this.validationDetails = null;
            this.validationActionType = null;
            this.validationActionText = null;

            // Pre-validation with regex - only proceed if domain looks valid
            if (!this.isValidDomainFormat(trimmedValue)) {
                return; // Too early to validate
            }

            // Set checking state for valid-looking domains
            this.validationState = DomainValidationService.STATES.CHECKING;
            this.validationMessage = 'Validating domain...';

            // Determine debounce delay based on input state
            let delay = 600; // Default delay

            if (trimmedValue.endsWith('.')) {
                // User might still be typing TLD, wait longer
                delay = 1200;
            } else if (this.mightBeIncompleteMultiPartTLD(trimmedValue)) {
                // Might be typing multi-part TLD like .co.nl, .co.uk, .com.au
                delay = 900;
            } else if (trimmedValue.length < 6) {
                // Short domains, wait a bit longer
                delay = 750;
            }

            // Set new timeout for validation with smart timing
            this.validationTimeout = setTimeout(() => {
                this.validateDomain();
            }, delay);
        },

        isValidDomainFormat(domain) {
            // Enhanced regex to support multi-part TLDs like .co.nl, .co.uk, .com.au
            // Pattern: ^([a-z0-9-]+\.)+[a-z]{2,}(\.[a-z]{2,})*$
            // ✅ www.example.com → valid
            // ✅ example.com → valid
            // ✅ onlinedrogist.co.nl → valid
            // ✅ shop.example.co.uk → valid
            // ✅ site.domain.com.au → valid
            // ❌ www. → too early
            // ❌ example. → too early
            // ❌ example.c → too early (TLD too short)
            // ❌ d → definitely too early
            const domainRegex = /^([a-z0-9-]+\.)+[a-z]{2,}(\.[a-z]{2,})*$/i;
            return domainRegex.test(domain);
        },

        mightBeIncompleteMultiPartTLD(domain) {
            // Check if domain might be incomplete multi-part TLD
            // Examples that should wait longer:
            // - example.co (might become example.co.nl)
            // - site.com (might become site.com.au)
            // - shop.co (might become shop.co.uk)

            const commonMultiPartPrefixes = [
                '.co', '.com', '.gov', '.edu', '.org', '.net', '.ac', '.mil'
            ];

            return commonMultiPartPrefixes.some(prefix =>
                domain.toLowerCase().endsWith(prefix)
            );
        },
        
        async validateDomain() {
            if (!this.domainName || this.domainName.trim() === '') {
                this.validationState = DomainValidationService.STATES.IDLE;
                this.validationMessage = '';
                this.validationDetails = null;
                this.validationActionType = null;
                this.validationActionText = null;
                return;
            }

            console.log('🔍 DEBUG: Starting validation for domain:', this.domainName.trim());
            console.log('🔍 DEBUG: Existing domains received:', this.existingDomains);

            try {
                const result = await this.validationService.validateDomain(
                    this.domainName.trim(),
                    this.existingDomains
                );

                this.validationState = result.state;
                this.validationMessage = result.message;
                this.validationDetails = {
                    ...(result.details || {}),
                    wwwRecordExists: result.wwwRecordExists || false
                };
                this.validationActionType = result.actionType || null;
                this.validationActionText = result.actionText || null;

            } catch (error) {
                console.error('Domain validation failed:', error);
                this.validationState = DomainValidationService.STATES.ERROR;
                this.validationMessage = 'Validation failed. Please try again.';
                this.validationDetails = null;
                this.validationActionType = null;
                this.validationActionText = null;
            }
        },
        
        async handleValidationAction() {
            if (!this.validationActionType) return;

            this.actionInProgress = true;

            try {
                if (this.validationActionType === 'fix_dns_ip') {
                    await this.fixDnsIp();
                } else if (this.validationActionType === 'create_dns_record') {
                    await this.createDnsRecord();
                }
            } catch (error) {
                console.error('Action failed:', error);
                this.$bvToast.toast('Action failed. Please try again.', {
                    title: 'Error',
                    variant: 'danger',
                    solid: true
                });
            } finally {
                this.actionInProgress = false;
            }
        },

        async fixDnsIp() {
            try {
                const response = await Api.post('/domain/updateDnsRecords', {
                    domain_name: this.domainName.trim()
                });

                if (response.data.success) {
                    this.$bvToast.toast('DNS IP has been corrected successfully!', {
                        title: 'Success',
                        variant: 'success',
                        solid: true
                    });
                    // Re-validate after fixing
                    await this.validateDomain();
                } else {
                    throw new Error(response.data.message || 'Failed to fix DNS IP');
                }
            } catch (error) {
                throw error;
            }
        },

        async createDnsRecord() {
            try {
                const response = await Api.post('/domain/updateDnsRecords', {
                    domain_name: this.domainName.trim()
                });

                if (response.data.success) {
                    this.$bvToast.toast('DNS A record has been created successfully!', {
                        title: 'Success',
                        variant: 'success',
                        solid: true
                    });
                    // Re-validate after creating
                    await this.validateDomain();
                } else {
                    throw new Error(response.data.message || 'Failed to create DNS record');
                }
            } catch (error) {
                throw error;
            }
        },

        getStatusIcon(type) {
            if (type === 'cloudflare') {
                // Check if domain is registered in Cloudflare
                if (this.validationState === DomainValidationService.STATES.VALID ||
                    this.validationState === DomainValidationService.STATES.WARNING ||
                    this.validationState === DomainValidationService.STATES.DNS_MISMATCH ||
                    this.validationState === DomainValidationService.STATES.DNS_FIXABLE ||
                    this.validationState === DomainValidationService.STATES.WWW_DETECTED) {
                    return 'fa fa-check text-success';
                } else if (this.validationState === DomainValidationService.STATES.INVALID) {
                    return 'fa fa-times text-danger';
                }
                return 'fa fa-question text-muted';
            } else if (type === 'dns') {
                // Check DNS IP status
                if (this.validationState === DomainValidationService.STATES.VALID ||
                    this.validationState === DomainValidationService.STATES.WARNING ||
                    this.validationState === DomainValidationService.STATES.WWW_DETECTED) {
                    return 'fa fa-check text-success';
                } else if (this.validationState === DomainValidationService.STATES.DNS_MISMATCH ||
                           this.validationState === DomainValidationService.STATES.DNS_FIXABLE) {
                    return 'fa fa-times text-danger';
                }
                return 'fa fa-question text-muted';
            }
            return 'fa fa-question text-muted';
        },

        getStatusText(type) {
            if (type === 'cloudflare') {
                if (this.validationState === DomainValidationService.STATES.VALID ||
                    this.validationState === DomainValidationService.STATES.WARNING ||
                    this.validationState === DomainValidationService.STATES.DNS_MISMATCH ||
                    this.validationState === DomainValidationService.STATES.DNS_FIXABLE ||
                    this.validationState === DomainValidationService.STATES.WWW_DETECTED) {
                    return 'Domain is registered in Cloudflare';
                } else if (this.validationState === DomainValidationService.STATES.INVALID) {
                    return 'This domain was not found in Cloudflare. Please try another or verify Cloudflare account before connecting.';
                } else if (this.validationState === DomainValidationService.STATES.ERROR) {
                    // Show the actual error message from validation
                    return this.validationMessage || 'Error checking domain registration';
                } else if (this.validationState === DomainValidationService.STATES.CHECKING) {
                    return 'Checking domain registration...';
                }
                return 'Checking domain registration...';
            } else if (type === 'dns') {
                if (this.validationState === DomainValidationService.STATES.VALID ||
                    this.validationState === DomainValidationService.STATES.WARNING ||
                    this.validationState === DomainValidationService.STATES.WWW_DETECTED) {
                    return 'DNS IP is correct';
                } else if (this.validationState === DomainValidationService.STATES.DNS_MISMATCH ||
                           this.validationState === DomainValidationService.STATES.DNS_FIXABLE) {
                    // Check if this is a permission error vs normal DNS issue
                    if (this.validationMessage && this.validationMessage.includes('permissions')) {
                        return 'DNS check failed due to permissions';
                    }
                    return 'The DNS IP is incorrect';
                } else if (this.validationState === DomainValidationService.STATES.ERROR) {
                    // For DNS errors, show a generic message since the main error is shown in cloudflare section
                    return 'DNS check failed';
                } else if (this.validationState === DomainValidationService.STATES.CHECKING) {
                    return 'Checking DNS configuration...';
                }
                return 'Checking DNS configuration...';
            }
            return '';
        },

        getForceAddText() {
            if (this.validationState === DomainValidationService.STATES.DNS_MISMATCH ||
                this.validationState === DomainValidationService.STATES.DNS_FIXABLE) {
                return 'Set correct IP for this domain';
            }
            return 'Force add this domain';
        },



        async handleOk(bvModalEvt) {
            // Prevent default modal closing behavior
            bvModalEvt.preventDefault();

            // Call our custom ok method
            await this.ok();
        },

        async ok() {
            console.log('🔄 DomainValidationDialog OK clicked - setting up domain completely');

            if (!this.canSave) {
                console.log('❌ Cannot save - validation not complete');
                return;
            }

            // Remove www prefix from domain name before saving
            let cleanDomainName = this.domainName.trim();
            if (cleanDomainName.startsWith('www.')) {
                cleanDomainName = cleanDomainName.substring(4);
                console.log('🔧 Removed www prefix, saving as:', cleanDomainName);
            }

            // Show loading state - modal stays open with loader
            this.actionInProgress = true;

            try {
                console.log('🔧 Starting complete domain setup for:', cleanDomainName);

                // Single AJAX call to handle everything: DNS + WWW + save to database
                console.log('🔧 Sending setupDomainComplete request with:', {
                    domain_name: cleanDomainName,
                    create_www_rule: this.createWwwRule,
                    force_add: this.forceAdd,
                    site_id: this.siteId
                });

                const response = await Api.post('/domain/setupDomainComplete', {
                    domain_name: cleanDomainName,
                    create_www_rule: this.createWwwRule,
                    force_add: this.forceAdd,
                    site_id: this.siteId // Pass site ID from props
                });

                console.log('🔧 setupDomainComplete response:', response.data);

                if (!response.data.success) {
                    throw new Error(response.data.message || 'Failed to setup domain');
                }

                console.log('✅ Domain setup completed successfully:', response.data);

                // Show success notifications for what was done
                if (response.data.dns_updated) {
                    this.$bvToast.toast(`DNS A record updated for ${cleanDomainName}`, {
                        title: 'DNS Updated',
                        variant: 'success',
                        solid: true
                    });
                }

                if (response.data.www_created) {
                    this.$bvToast.toast(`WWW Page Rule created for ${cleanDomainName}`, {
                        title: 'WWW Page Rule Created',
                        variant: 'success',
                        solid: true
                    });
                }

                // Main success notification
                if (response.data.domain_saved) {
                    this.$bvToast.toast(`Domain ${cleanDomainName} added and saved successfully`, {
                        title: 'Domain Added',
                        variant: 'success',
                        solid: true
                    });
                } else {
                    this.$bvToast.toast(`Domain ${cleanDomainName} DNS configured successfully`, {
                        title: 'DNS Configured',
                        variant: 'success',
                        solid: true
                    });
                }

                // Success - resolve with domain data and close modal
                const resolveData = {
                    trigger: 'ok',
                    value: cleanDomainName,
                    forceAdd: this.forceAdd,
                    createWwwRule: this.createWwwRule,
                    validationState: this.validationState,
                    closeAllModals: true, // Flag to indicate both modals should close
                    dnsSetupComplete: true, // Flag to indicate DNS was set up successfully
                    domainAlreadySaved: true, // Flag to trigger refresh from database
                    setupResponse: response.data, // Include the full response
                    // Create domain object for immediate display (without domain_id since it's still being processed)
                    domainData: {
                        domain_id: null, // No ID yet since domain addition is still in progress
                        domain_name: cleanDomainName,
                        domain_ismail: 0,
                        domain_isssl: 1,
                        domain_isletsencrypt: 1,
                        domain_ismain: 0,
                        domain_isdefault: 0,
                        domain_edition_id: 1, // Default edition
                        is_readonly: 0,
                        is_new: true, // Mark as new since it's being added
                        dns_status: 'Processing...' // Show processing status
                    }
                };

                console.log('✅ Complete domain setup finished, closing modal');
                console.log('tesstts');
                console.log('🔄 11 Domain saved to database, will trigger refresh');
                console.log('🔍 DEBUG: About to resolve with data:', resolveData);

                // Manually close the modal and resolve
                this.$refs.modal.hide();
                console.log('🔍 DEBUG: Modal hidden, now calling resolve...');
                this.resolve(resolveData);
                console.log('🔍 DEBUG: Resolve called successfully');

            } catch (error) {
                console.error('❌ Domain setup failed:', error);
                this.$bvToast.toast(error.message || 'Failed to setup domain. Please try again.', {
                    title: 'Domain Setup Error',
                    variant: 'danger',
                    solid: true
                });
                // Don't close modal on error - let user try again
                // actionInProgress will be set to false in finally block
            } finally {
                this.actionInProgress = false;
            }
        },



        onModalHide() {
            // Handle modal hide
        },

        onModalHidden() {
            // Handle modal hidden
        }
    },
    
    beforeDestroy() {
        // Clean up timeout
        if (this.validationTimeout) {
            clearTimeout(this.validationTimeout);
        }
    }
}
</script>

<style lang="scss" scoped>
.domain-setup-dialog {
    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #333;
    }

    .domain-input {
        font-size: 1rem;
        padding: 0.75rem;
        border: 1px solid #ddd;
        border-radius: 4px;

        &:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
    }

    .validation-status-list {
        margin-top: 1rem;

        .status-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;

            .status-icon {
                width: 16px;
                margin-right: 0.5rem;
                font-size: 0.9rem;
            }

            .status-text {
                font-size: 0.9rem;
                line-height: 1.4;
                color: #333;
            }
        }

        .action-text {
            margin-top: 0.75rem;
            margin-bottom: 0.75rem;
            font-size: 0.9rem;
            color: #666;
            padding-left: 1.5rem; // Align with status text
        }

        .force-add-option {
            margin-top: 1rem;
            padding-left: 1.5rem; // Align with status text

            .force-add-checkbox {
                font-size: 0.9rem;

                .custom-control-label {
                    color: #333;
                    line-height: 1.4;
                }
            }
        }

        .www-option {
            margin-top: 0.75rem;
            padding-left: 1.5rem; // Align with status text

            .www-checkbox {
                font-size: 0.9rem;

                .custom-control-label {
                    color: #333;
                    line-height: 1.4;
                }
            }
        }
    }

    .fa-spinner {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
}
</style>
