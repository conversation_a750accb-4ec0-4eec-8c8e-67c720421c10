export default function getErrorMessage(err) {
    let message = 'Erorr';

    if (err && err.data && err.data.error) {
        message = err.data.error
    }
    if (err && err.response && err.response.data && err.response.data.error) {
        message = err.response.data.error
    }
    if (err && err.response && err.response.data && err.response.data.message) {
        message = err.response.data.message
    }
    return message
}
