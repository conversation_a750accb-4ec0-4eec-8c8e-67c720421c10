{"name": "dealer", "version": "1.0.0", "description": "Exolog dealer admin panel", "author": "<PERSON><PERSON><PERSON><PERSON>", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build --dest ./dist", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e", "lint": "vue-cli-service lint", "deploy": "npm run build && shx rm -rf ../../../exologadmin.webkracht.nl/htdocs/dealer/app/* && shx mkdir -p ../../../exologadmin.webkracht.nl/htdocs/dealer/app && shx cp -r ./dist/* ../../../exologadmin.webkracht.nl/htdocs/dealer/app", "build:direct": "vue-cli-service build", "build:watch": "vue-cli-service build --watch"}, "dependencies": {"@coreui/coreui": "^2.0.4", "@coreui/coreui-plugin-chartjs-custom-tooltips": "^1.2.0", "@coreui/icons": "0.3.0", "@coreui/vue": "^2.0.0", "@riophae/vue-treeselect": "^0.4.0", "axios": "^0.18.0", "bootstrap": "^4.6.1", "bootstrap-vue": "^2.13.0", "chart.js": "^2.7.2", "core-js": "^3.6.5", "css-vars-ponyfill": "^1.9.0", "flag-icon-css": "^3.0.0", "font-awesome": "^4.7.0", "perfect-scrollbar": "^1.4.0", "simple-line-icons": "^2.4.1", "vee-validate": "^3.3.0", "vue": "^2.5.17", "vue-awesome-notifications": "^2.2.9", "vue-bootstrap-typeahead": "^0.2.6", "vue-chartjs": "^3.4.0", "vue-codemirror": "^4.0.5", "vue-multiselect": "^2.1.0", "vue-perfect-scrollbar": "^0.1.0", "vue-progressbar": "^0.7.5", "vue-router": "^3.1.6", "vue-tables-2": "^2.0.21", "vue-truncate-filter": "^1.1.7", "vuex": "^3.2.0", "dayjs": "^1.8.28", "lodash": "^4.17.15"}, "devDependencies": {"@vue/cli": "^4.5.15", "@vue/cli-service": "^4.5.15", "@vue/cli-plugin-babel": "^4.5.15", "@vue/cli-plugin-e2e-nightwatch": "^4.5.15", "@vue/cli-plugin-eslint": "^4.5.15", "@vue/cli-plugin-unit-jest": "^4.5.15", "@vue/test-utils": "^1.0.0-beta.33", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^10.1.0", "babel-jest": "^23.4.2", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "less": "^3.10.3", "less-loader": "^5.0.0", "sass": "1.32.13", "sass-loader": "^10", "shx": "^0.3.2", "vue-template-compiler": "^2.5.17"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 9"], "engines": {"node": ">= 8.10.x", "npm": ">= 5.6.0"}, "license": "MIT"}