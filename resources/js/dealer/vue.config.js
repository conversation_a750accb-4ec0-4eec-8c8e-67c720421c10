const path = require('path');
const target = 'https://dealer.w28.exolog.net/'

module.exports = {
    lintOnSave: false,
    runtimeCompiler: true,
    publicPath: '/app',
    outputDir: '../../../exologadmin.webkracht.nl/htdocs/dealer/app',
    productionSourceMap: false,
    devServer: {
        port: 8085,
        proxy: {
            '/api/*': {
                target,
                changeOrigin: true,
            },
            '/redir/*': {
                target,
                changeOrigin: true,
            },
            '/utils/*': {
                target,
                changeOrigin: true,
            }
        }

    },
    configureWebpack: {
        resolve: {
            alias: {
                //share between projects (dealer|exo_admin)
                '@modules': path.resolve(__dirname, '../modules'),
            },
        },
    },
    css: {
        loaderOptions: {
            sass: {
                //additionalData: `@import "~@/variables.sass"`
            },

            scss: {
                sassOptions: {quietDeps: true},
                //additionalData: `@import "~@/variables.scss";`
            },
        }
    }
};
