@mixin action-button {
    color: $primary;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6px 6px;
    width: 35px;
    position: relative;
    text-align: center;
    cursor: pointer;
    background: none;
    border: none;
    height: 35px;

    .action-icon {
        font-size: 23px;
        line-height: 23px;
    }

    &.action-button--text {
        width: auto;
        text-decoration: none;

        .action-icon {
            margin-right: 0.4rem;
        }

        &:hover {
            text-decoration: none;
        }

        &:before {
            //border-radius: 5;
        }
    }

    &.action-button--split {
        width: 20px;

        &[aria-expanded="true"]:before {
            background-color: rgba(32, 33, 36, 0.1);
            border: none;
            box-shadow: none;
            opacity: 1;
            transform: scale(1);
        }
        &.action-button--sm {
            width: 15px;
        }
    }

    &--active,

    &:hover {
        text-decoration: none;
        opacity: 1;
        color: darken($primary, 7.5%);
    }

    &:focus {
        outline: none;
        text-decoration: none;
    }

    &:before {
        content: '';
        display: block;
        opacity: 0;
        position: absolute;
        transition-duration: .15s;
        transition-timing-function: cubic-bezier(0.4, 0.0, 0.2, 1);
        background: none;
        border-radius: $input-border-radius;
        box-sizing: border-box;
        transform: scale(0);
        transition-property: transform, opacity;
        bottom: 0;
        left: 0;
        right: 0;
        top: 0;
    }

    &--active::before,
    &:hover::before,
    &:focus::before {
        background-color: rgba(32, 33, 36, 0.1);
        border: none;
        box-shadow: none;
        opacity: 1;
        transform: scale(1);
    }

    &[disabled] {
        opacity: 0.35;
        cursor: auto;
        filter: grayscale(1);
        color: $primary;

        &:before {
            display: none;
        }
    }

    &--primary {

    }

    &--white {
        color: $white;

        &:hover {
            color: darken($white, 7.5%);
        }
    }

    &--secondary {
        color: $secondary;

        &:hover {
            color: darken($secondary, 7.5%);
        }
    }

    &--sm {
        font-size: 12px;
        width: 24px;
        height: 24px;
        .action-icon {
            font-size: 16px;
            line-height: 16px;
        }
    }
}
