.x-tabs {
    .nav-tabs {
        border-top: none; //1px solid $gray-300;
        border-bottom: none;
    }

    .nav-tabs .nav-item {
        margin-bottom: 0;
    }

    .nav-tabs .nav-link {
        font-weight: 600;
        border-bottom-width: 2px;
        border: none;
        padding: 0.5rem 0.75rem;
        font-size: 1rem;

    }

    .nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
        border-color: transparent;
    }

    .nav-tabs .nav-link.active {
        border: none;
        background: transparent;
        color: $primary;
        border-bottom: 2px solid $primary;
    }

    .nav-tabs .nav-link.disabled {
        color: $gray-400;
        background-color: transparent;
        border-color: transparent;
    }

    .tab-content {
        border: none;
        margin-top: 0;
        //TODO Remove from component
        min-height: calc(100vh - #{$app-header-height} - #{$action-toolbar-height} - #{$tab-toolbar-height} - (16px * 2));
    }


}
