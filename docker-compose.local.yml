services:
  # MySQL Database (ARM64 compatible)
  mysql:
    image: mysql:8.0
    container_name: exolog-mysql
    restart: unless-stopped
    platform: linux/amd64
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-root}
      MYSQL_DATABASE: ${DB_DATABASE:-exolog}
      MYSQL_USER: ${DB_USERNAME:-exolog}
      MYSQL_PASSWORD: ${DB_PASSWORD:-password}
    command: --default-authentication-plugin=mysql_native_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./db/schema:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    networks:
      - exolog

  # Redis Cache (ARM64 compatible)
  redis:
    image: redis:7-alpine
    container_name: exolog-redis
    restart: unless-stopped
    command: ["redis-server", "--bind", "0.0.0.0"]
    ports:
      - "6379:6379"
    networks:
      - exolog

  # PHP Application (Dealer) - ARM64 compatible
  dealer:
    image: php:8.1-fpm-alpine
    container_name: exolog-dealer
    restart: unless-stopped
    ports:
      - "8081:9000"
    environment:
      - PHP_DISPLAY_ERRORS=1
      - PHP_MEMORY_LIMIT=512M
      - PHP_MAX_EXECUTION_TIME=300
    volumes:
      - .:/var/www/html
    depends_on:
      - mysql
      - redis
    networks:
      - exolog

  # Nginx for Dealer
  dealer-nginx:
    image: nginx:alpine
    container_name: exolog-dealer-nginx
    restart: unless-stopped
    ports:
      - "8083:80"
    volumes:
      - .:/var/www/html
      - ./docker/nginx/dealer_nginx.conf:/etc/nginx/conf.d/default.conf:ro
    depends_on:
      - dealer
    networks:
      - exolog

  # PHP Application (Main) - ARM64 compatible
  exolog:
    image: php:8.1-fpm-alpine
    container_name: exolog-main
    restart: unless-stopped
    ports:
      - "8082:9000"
    environment:
      - PHP_DISPLAY_ERRORS=1
      - PHP_MEMORY_LIMIT=512M
      - PHP_MAX_EXECUTION_TIME=300
    volumes:
      - .:/var/www/html
    depends_on:
      - mysql
      - redis
    networks:
      - exolog

  # Nginx for Main App
  exolog-nginx:
    image: nginx:alpine
    container_name: exolog-main-nginx
    restart: unless-stopped
    ports:
      - "8084:80"
    volumes:
      - .:/var/www/html
      - ./docker/nginx/main_nginx.conf:/etc/nginx/conf.d/default.conf:ro
    depends_on:
      - exolog
    networks:
      - exolog

  # Mail Server (Optional - for testing)
  mailhog:
    image: mailhog/mailhog
    container_name: exolog-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - exolog

  # Adminer (Database Management)
  adminer:
    image: adminer
    container_name: exolog-adminer
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      ADMINER_DEFAULT_SERVER: mysql
    depends_on:
      - mysql
    networks:
      - exolog

  # Node.js Development Server (for frontend development)
  node-dev:
    image: node:16-alpine
    container_name: exolog-node-dev
    working_dir: /app
    volumes:
      - .:/app
      - node_modules_dealer:/app/resources/js/dealer/node_modules
      - node_modules_admin:/app/resources/js/exo_admin/node_modules
      - node_modules_editor:/app/resources/js/exo_editor/node_modules
    ports:
      - "3000:3000"  # Dealer dev server
      - "3001:3001"  # Admin dev server
      - "3002:3002"  # Editor dev server
    command: sh -c "echo 'Node.js development container ready. Use docker-compose exec node-dev sh to access.'"
    networks:
      - exolog

volumes:
  mysql_data:
  node_modules_dealer:
  node_modules_admin:
  node_modules_editor:

networks:
  exolog:
    driver: bridge
