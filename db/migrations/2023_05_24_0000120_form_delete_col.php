<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('form', static function(Blueprint $table) {
            $table->dropColumn(
                [
                    'form_allow_extranet_submit_owneronly',
                    'form_allow_extranet_viewdata',
                    'form_allow_extranet_submit',
                    'form_allow_global_viewdata',
                    'form_allow_global_submit',
                    'form_viewisextranet',
                    'form_isextranet',
                    'form_mailto_formfield',
                    'form_do_mailto_formfield',
                    'form_submittemplate',
                    'form_mailto_usergroup_template_url',
                    'form_mailto_usergroup_template',
                    'form_mailto_usergroup',
                    'form_mailto_question_checkquestion',
                    'form_mailto_question_template_url',
                    'form_mailto_question_template',
                    'form_mailto_question',
                    'form_mailto_email_template_url',
                    'form_mailto_email_template',
                    'form_mailto_email',
                    'form_do_mailto_usergroup',
                    'form_do_mailto_question',
                    'form_do_mailto_email',
                    'form_do_mailonupdate',
                    'form_usergroup',
                    'form_reactgroup',
                    'form_html',
                    'form_driver',
                    'form_redirtype',
                    'form_redir_page',
                ]
            );
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};