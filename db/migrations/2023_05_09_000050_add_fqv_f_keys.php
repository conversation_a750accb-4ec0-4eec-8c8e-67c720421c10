<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('formquestionvalue')
            ->whereNotExists(function($query) {
                $query->select(\DB::raw(1))
                    ->from('formquestion')
                    ->whereColumn('fq_id', 'formquestionvalue.fqv_question');
            })
            ->delete();

        Schema::table('formquestionvalue', static function(Blueprint $table) {
            $table->integer('fqv_question', false, false)->nullable(false)->change();
            $table->foreign('fqv_question')->references('fq_id')->on('formquestion')->cascadeOnDelete();
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};