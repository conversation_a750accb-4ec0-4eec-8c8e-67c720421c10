<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('site', static function(Blueprint $table) {
            $table->dropColumn(
                [
                    'site_show_news',
                    'site_showinsitelinks',
                    'site_show_mirrors',
                    'site_do_nodeliverypriceabove',
                    'site_show_emailsettings',
                    'site_nohostmatch',
                    'site_adminpassword',
                    'site_adminpath',
                    'site_deliverypricemethod',
                    'site_show_calendar',
                    'site_inherit_intropage',
                    'site_nbtemplatehtml',
                    'site_adminurl',
                    'site_show_files',
                    'site_newsitems',
                    'site_path',
                    'site_adminlogin',
                    'site_show_ocms',
                    'site_template',
                    'site_template_closed',
                    'site_base_delivery_price',
                    'site_admin',
                    'site_show_newsletter',
                    'site_show_users',
                    'site_nodeliverypriceabove',
                    'site_show_editors',
                    'site_delivery_price_currency',
                    'site_currency2',
                    'site_nbemail',
                    'site_forward_to',
                    'site_is_parked',
                    'site_nodeliverypriceabove_currency',
                    'site_opener_delay',
                    'site_show_editions',
                    'site_show_templates',
                    'site_metakeywords',
                    'site_calendaritems',
                    'site_has_extranet',
                    'site_faxnumber',
                    'site_url',
                    'site_show_opener',
                    'site_default_delivery_price',
                    'site_curedition',
                    'site_opener',
                    'site_nocaching',
                    'site_currency',
                    'site_show_search',
                    'site_inherit_from',
                    'site_nbitems',
                    'site_stylesheet',
                    'site_user',
                    'site_has_community',
                    'site_hostmatch',
                    'site_show_forum',
                    'site_ccordersto',
                    'site_nbtemplatetext',
                    'site_intropage',
                    'site_show_products',
                    'site_isclosed',
                    'site_metainfo',
                    'site_http',
                    'site_https',
                    'site_backend_http',
                    'site_backend_https',
                    'site_extjs_admin',
                    'site_notesconfirmemail',
                ]
            );
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};