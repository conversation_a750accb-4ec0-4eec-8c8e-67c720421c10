<?php

use Exolog\Module\Permalinks\Model\Permalink;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('permalinks', function (Blueprint $table) {
            // Add p_redirect column only if it doesn't exist
            if (!Schema::hasColumn('permalinks', 'p_redirect')) {
                $table->string('p_redirect', 500)->nullable()->after('p_context');
            }
        });

        DB::table('permalinks')
            ->where('p_type', Permalink::TYPE_HISTORY)
            ->update(['p_redirect' => Permalink::TYPE_PRIMARY]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropColumns('permalinks', ['p_redirect']);
    }
};
