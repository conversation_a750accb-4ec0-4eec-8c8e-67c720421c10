<?php

use Exolog\Module\Container\ContainerService;
use Exolog\Module\Support\Facades\Site;
use Exolog\Module\Support\Facades\Vars;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;


class MigrateFormContainer extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        foreach (\Exolog\Module\Site\Model\Site::all() as $site) {
            coreInit()->resolveSite($site['site_id']);
            dump('Site ID=' . Site::id());
            $containers = ContainerService::collectLegacyContainers();
            foreach ($containers as $container) {
                if ($container->getHolderType() === 'form') {
                    dump($container->getContainerId());
                    $prefix = str_replace('_table', '', $container->fieldNameTable);
                    dump($prefix);
                    $var = \Exolog\Module\Support\Facades\Vars::get('container_' . $prefix, 'form', $container->getHolderId());
                    $var->save();
                    $config = $container->getConfig();
                    $config['useOwnConfig'] = 1;
                    $var->getContainer()->setConfig($config)->save();
                    $var->getContainer()->setItems($container->getItemsTree())->save();
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
}