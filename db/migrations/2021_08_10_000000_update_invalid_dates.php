<?php

use Exolog\Module\Container\ContainerService;
use Exolog\Module\Support\Facades\Site;
use Exolog\Module\Support\Facades\Vars;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;


return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("    
UPDATE file SET file_date = '2000-01-01 00:00:00' WHERE CAST(file_date AS CHAR(20)) = '0000-00-00 00:00:00';");
        DB::statement("
ALTER TABLE file CHANGE `file_date` `file_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;");
        DB::statement("
UPDATE ref SET ref_date_added = '2000-01-01 00:00:00' WHERE CAST(ref_date_added AS CHAR(20)) = '0000-00-00 00:00:00';");
        DB::statement("
ALTER TABLE ref CHANGE `ref_date_added` `ref_date_added` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;");
        DB::statement("
UPDATE referrer SET ref_date = '2000-01-01 00:00:00' WHERE CAST(ref_date AS CHAR(20)) = '0000-00-00 00:00:00';");
        DB::statement("
ALTER TABLE referrer CHANGE `ref_date` `ref_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;");
        DB::statement("
UPDATE var SET var_date = '2000-01-01 00:00:00' WHERE CAST(var_date AS CHAR(20)) = '0000-00-00 00:00:00';");
        DB::statement("
ALTER TABLE var CHANGE `var_date` `var_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;");
        DB::statement("
UPDATE user SET u_date_added = '2000-01-01 00:00:00' WHERE CAST(u_date_added AS CHAR(20)) = '0000-00-00 00:00:00';");
        DB::statement("
UPDATE user SET u_date_changed = '2000-01-01 00:00:00' WHERE CAST(u_date_changed AS CHAR(20)) = '0000-00-00 00:00:00';");
        DB::statement("
UPDATE user SET u_lastlogin =  null WHERE CAST(u_lastlogin AS CHAR(20)) = '0000-00-00 00:00:00';");
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};