<?php

use Exolog\Module\Support\Facades\Site;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        foreach (\Exolog\Module\Site\Model\Site::all() as $site) {
            coreInit()->resolveSite($site['site_id']);
            dump('Site ID=' . Site::id());
            $form = \Exolog\Module\Forms\Model\Form::findByName('URL');
            if ($form) {
                $form->form_permalink_id = null;
                $form->save();
            }
            react()->form('URL')->get()->each(function (\Exolog\Core\Forms\React $react) {
                $permalink = $react['permalink'];
                if (empty($permalink)) {
                    return;
                }
                $react['react_form_permalink_id'] = null;
                $react->save();

                // Check if permalink already exists before creating (based on unique constraint: p_url + p_site_id)
                $existingPermalink = \Exolog\Module\Permalinks\Model\Permalink::where('p_url', $permalink)
                    ->where('p_site_id', Site::id())
                    ->first();

                if (!$existingPermalink) {
                    \Exolog\Module\Permalinks\Model\Permalink::create([
                        'p_react_id' => $react->id(),
                        'p_type' => \Exolog\Module\Permalinks\Model\Permalink::TYPE_MANUAL,
                        'p_url' => $permalink
                    ]);
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};
