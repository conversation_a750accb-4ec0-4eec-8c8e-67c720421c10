<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('form')->where('form_isdeleted', 1)->delete();
        DB::table('formquestion')->where('fq_isdeleted', 1)->delete();
        DB::table('formquestionvalue')->where('fqv_isdeleted', 1)->delete();

        Schema::dropColumns('form', ['form_isdeleted']);
        Schema::dropColumns('formquestion', ['fq_isdeleted']);
        Schema::dropColumns('formquestionvalue', ['fqv_isdeleted']);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};