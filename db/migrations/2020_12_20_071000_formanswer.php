<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class Formanswer extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("ALTER TABLE `formanswer`
                            CHANGE `fa_value` `fa_value` text COLLATE 'utf8_general_ci' NULL AFTER `fa_question`");

        DB::statement("ALTER TABLE `formanswerint`
                            CHANGE `fa_value` `fa_value` double(13,2) NULL DEFAULT '0.00' AFTER `fa_question`");

        DB::statement("ALTER TABLE `formanswerstr`
                            CHANGE `fa_value` `fa_value` varchar(255) COLLATE 'utf8_general_ci' NULL AFTER `fa_question`");

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement("ALTER TABLE `formanswer`
                            CHANGE `fa_value` `fa_value` text COLLATE 'utf8_general_ci' NOT NULL AFTER `fa_question`");

        DB::statement("ALTER TABLE `formanswerint`
                            CHANGE `fa_value` `fa_value` double(13,2) NOT NULL DEFAULT '0.00' AFTER `fa_question`");

        DB::statement("ALTER TABLE `formanswerstr`
                            CHANGE `fa_value` `fa_value` varchar(255) COLLATE 'utf8_general_ci' NOT NULL AFTER `fa_question`");

    }
}
