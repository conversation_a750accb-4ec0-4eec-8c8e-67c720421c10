<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Create schedules table only if it doesn't exist
        if (!Schema::hasTable('schedules')) {
            Schema::create('schedules', function(Blueprint $table) {

                $table->increments('id');
                $table->unsignedInteger('site_id')->nullable();

                $table->string('command');
                $table->string('command_custom')->nullable()->default(null);

                $table->text('params')->nullable();
                $table->string('expression');
                $table->string('environments')->nullable();
                $table->text('options')->nullable();
                $table->string('log_filename')->nullable();
                $table->boolean('even_in_maintenance_mode')->default(false);
                $table->boolean('without_overlapping')->default(false);
                $table->boolean('on_one_server')->default(false);

                $table->string('webhook_before')->nullable();
                $table->string('webhook_after')->nullable();
                $table->string('email_output')->nullable();
                $table->boolean('sendmail_error')->default(false);
                $table->boolean('sendmail_success')->default(false);
                $table->boolean('log_success')->default(true);
                $table->boolean('log_error')->default(true);

                $table->boolean('status')->default(true);
                $table->boolean('run_in_background')->default(false);
                $table->string('groups')->nullable();

                $table->timestamps();
                $table->softDeletes();

                $table->foreign('site_id')->references('site_id')
                    ->on('site')->cascadeOnDelete();

            });
        }

        // Create schedule_histories table only if it doesn't exist
        if (!Schema::hasTable('schedule_histories')) {
            Schema::create('schedule_histories', function(Blueprint $table) {
                $table->increments('id');
                $table->unsignedInteger('schedule_id');
                $table->string('command');
                $table->text('params')->nullable();
                $table->text('output');
                $table->text('options')->nullable();

                $table->timestamps();
                $table->foreign('schedule_id')->references('id')->on(Config::get('database-schedule.table.schedules',
                    'schedules'));
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('schedules');
        Schema::dropIfExists('schedule_histories');
    }
};
