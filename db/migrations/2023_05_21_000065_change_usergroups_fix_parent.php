<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        Schema::table('usergroup', static function(Blueprint $table) {
            $table->integer('ug_parent', false, false)->nullable(true)->change();
        });


        DB::table('usergroup')
            ->where('ug_parent', 0)
            ->update(['ug_parent' => null]);

        DB::table('usergroup')
            ->whereNotNull('ug_parent')
            ->whereNotExists(function($query) {
                $query->select(DB::raw(1))
                    ->from('usergroup as ug')
                    ->whereColumn('ug.ug_id', 'usergroup.ug_parent');
            })
            ->get()
            ->each(function($ug) {
                DB::table('usergroup')->where('ug_id', $ug['ug_id'])->delete();
                Log::channel('discord')->debug("Deleted usergroup {$ug['ug_name']}");
            });

        Schema::table('usergroup', static function(Blueprint $table) {
            $table->foreign('ug_parent')->references('ug_id')->on('usergroup')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};