<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::insert(
            'insert into formquestiontype (fqt_id, fqt_name, fqt_displayname, fqt_info) values (?, ? , ? ,?)',
            [24, 'mailable', 'mailable', 'Allows send this react like mail']);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::delete(
            'delete from formquestiontype where fqt_id = ?',
            [24]);
    }
};
