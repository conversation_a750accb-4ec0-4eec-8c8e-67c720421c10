<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('react', static function(Blueprint $table) {
            $table->dropColumn([
                'react_agent',
                'react_body',
                'react_checked',
                'react_comments',
                'react_count_reads',
                'react_entry',
                'react_from',
                'react_from_email',
                'react_from_id',
                'react_group',
                'react_host',
                'react_order',
                'react_page',
                'react_parent',
                'react_parent_order',
                'react_section',
                'react_sub',
                'react_to',
                'react_to_email',
                'react_to_id',
                'react_type',
                'react_user_target',
            ]);
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};