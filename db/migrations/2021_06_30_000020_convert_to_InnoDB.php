<?php

use Exolog\Module\Container\ContainerService;
use Exolog\Module\Support\Facades\Site;
use Exolog\Module\Support\Facades\Vars;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;


return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement('
        
ALTER TABLE admin ENGINE=InnoDB;
ALTER TABLE brand ENGINE=InnoDB;
ALTER TABLE cache ENGINE=InnoDB;
ALTER TABLE crontask ENGINE=InnoDB;
ALTER TABLE crontask_log ENGINE=InnoDB;
ALTER TABLE currency ENGINE=InnoDB;
ALTER TABLE debug ENGINE=InnoDB;
ALTER TABLE deliverymethod ENGINE=InnoDB;
ALTER TABLE deliverypricemethod ENGINE=InnoDB;
ALTER TABLE deliveryrange ENGINE=InnoDB;
ALTER TABLE discount ENGINE=InnoDB;
ALTER TABLE discount_coupons ENGINE=InnoDB;
ALTER TABLE discount_rules ENGINE=InnoDB;
ALTER TABLE domain ENGINE=InnoDB;
ALTER TABLE domaingroup ENGINE=InnoDB;
ALTER TABLE domaingroup_domain ENGINE=InnoDB;
ALTER TABLE edition ENGINE=InnoDB;
ALTER TABLE failed_jobs ENGINE=InnoDB;
ALTER TABLE file ENGINE=InnoDB;
ALTER TABLE file_user ENGINE=InnoDB;
ALTER TABLE filegroup ENGINE=InnoDB;
ALTER TABLE filegroup_file ENGINE=InnoDB;
ALTER TABLE filegroup_usergroup ENGINE=InnoDB;
ALTER TABLE form ENGINE=InnoDB;
ALTER TABLE form_form ENGINE=InnoDB;
ALTER TABLE form_permalink ENGINE=InnoDB;
ALTER TABLE form_site ENGINE=InnoDB;
ALTER TABLE formanswer ENGINE=InnoDB;
ALTER TABLE formanswerint ENGINE=InnoDB;
ALTER TABLE formanswerstr ENGINE=InnoDB;
ALTER TABLE formquestion ENGINE=InnoDB;
ALTER TABLE formquestiontype ENGINE=InnoDB;
ALTER TABLE formquestionvalue ENGINE=InnoDB;
ALTER TABLE `groups` ENGINE=InnoDB;
ALTER TABLE migrations ENGINE=InnoDB;
ALTER TABLE note ENGINE=InnoDB;
ALTER TABLE ord ENGINE=InnoDB;
ALTER TABLE ord_product ENGINE=InnoDB;
ALTER TABLE ord_product_option ENGINE=InnoDB;
ALTER TABLE ord_reminder ENGINE=InnoDB;
ALTER TABLE order_product ENGINE=InnoDB;
ALTER TABLE orderstate ENGINE=InnoDB;
ALTER TABLE page ENGINE=InnoDB;
ALTER TABLE page_date ENGINE=InnoDB;
ALTER TABLE page_page ENGINE=InnoDB;
ALTER TABLE page_product ENGINE=InnoDB;
ALTER TABLE page_productgroup ENGINE=InnoDB;
ALTER TABLE page_ref ENGINE=InnoDB;
ALTER TABLE page_user ENGINE=InnoDB;
ALTER TABLE page_usergroup ENGINE=InnoDB;
ALTER TABLE pagegroup ENGINE=InnoDB;
ALTER TABLE pagegroup_page ENGINE=InnoDB;
ALTER TABLE pagegroup_usergroup ENGINE=InnoDB;
ALTER TABLE pagereads ENGINE=InnoDB;
ALTER TABLE password_resets ENGINE=InnoDB;
ALTER TABLE paymentmethod ENGINE=InnoDB;
ALTER TABLE product ENGINE=InnoDB;
ALTER TABLE product_product ENGINE=InnoDB;
ALTER TABLE product_user ENGINE=InnoDB;
ALTER TABLE productgroup ENGINE=InnoDB;
ALTER TABLE productgroup_product ENGINE=InnoDB;
ALTER TABLE productoption ENGINE=InnoDB;
ALTER TABLE productoption_notify ENGINE=InnoDB;
ALTER TABLE productoptionvalue ENGINE=InnoDB;
ALTER TABLE productoptionvalue_notify ENGINE=InnoDB;
ALTER TABLE rate ENGINE=InnoDB;
ALTER TABLE react ENGINE=InnoDB;
ALTER TABLE react_relations ENGINE=InnoDB;
ALTER TABLE react_relations_value ENGINE=InnoDB;
ALTER TABLE react_site ENGINE=InnoDB;
ALTER TABLE reactgroup ENGINE=InnoDB;
ALTER TABLE read_event ENGINE=InnoDB;
ALTER TABLE read_event_bot ENGINE=InnoDB;
ALTER TABLE read_event_event ENGINE=InnoDB;
ALTER TABLE read_event_referrer ENGINE=InnoDB;
ALTER TABLE read_event_session ENGINE=InnoDB;
ALTER TABLE read_event_user ENGINE=InnoDB;
ALTER TABLE read_newsletter ENGINE=InnoDB;
ALTER TABLE read_newsletter_link ENGINE=InnoDB;
ALTER TABLE read_referrer ENGINE=InnoDB;
ALTER TABLE ref ENGINE=InnoDB;
ALTER TABLE referrer ENGINE=InnoDB;
ALTER TABLE relations ENGINE=InnoDB;
ALTER TABLE relationtype ENGINE=InnoDB;
ALTER TABLE searchlog ENGINE=InnoDB;
ALTER TABLE session_product ENGINE=InnoDB;
ALTER TABLE shared_forms ENGINE=InnoDB;
ALTER TABLE shared_templates ENGINE=InnoDB;
ALTER TABLE site_site ENGINE=InnoDB;
ALTER TABLE site_template ENGINE=InnoDB;
ALTER TABLE site_templategroup ENGINE=InnoDB;
ALTER TABLE stock ENGINE=InnoDB;
ALTER TABLE stock_option ENGINE=InnoDB;
ALTER TABLE template ENGINE=InnoDB;
ALTER TABLE template_content ENGINE=InnoDB;
ALTER TABLE template_type ENGINE=InnoDB;
ALTER TABLE templategroup ENGINE=InnoDB;
ALTER TABLE templategroup_template ENGINE=InnoDB;
ALTER TABLE type ENGINE=InnoDB;
ALTER TABLE user ENGINE=InnoDB;
ALTER TABLE user_authy ENGINE=InnoDB;
ALTER TABLE user_pagegroup ENGINE=InnoDB;
ALTER TABLE user_setting ENGINE=InnoDB;
ALTER TABLE user_usergroup ENGINE=InnoDB;
ALTER TABLE usergroup ENGINE=InnoDB;
ALTER TABLE var ENGINE=InnoDB;
ALTER TABLE var_value ENGINE=InnoDB;
ALTER TABLE vargroup ENGINE=InnoDB;
ALTER TABLE vargroup_edition ENGINE=InnoDB;
ALTER TABLE vargroup_type ENGINE=InnoDB;
ALTER TABLE vargroup_var ENGINE=InnoDB;

        
        ');
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement('
        
ALTER TABLE admin ENGINE=MyISAM;
ALTER TABLE brand ENGINE=MyISAM;
ALTER TABLE cache ENGINE=MyISAM;
ALTER TABLE crontask ENGINE=MyISAM;
ALTER TABLE crontask_log ENGINE=MyISAM;
ALTER TABLE currency ENGINE=MyISAM;
ALTER TABLE debug ENGINE=MyISAM;
ALTER TABLE deliverymethod ENGINE=MyISAM;
ALTER TABLE deliverypricemethod ENGINE=MyISAM;
ALTER TABLE deliveryrange ENGINE=MyISAM;
ALTER TABLE discount ENGINE=MyISAM;
ALTER TABLE discount_coupons ENGINE=MyISAM;
ALTER TABLE discount_rules ENGINE=MyISAM;
ALTER TABLE domain ENGINE=MyISAM;
ALTER TABLE domaingroup ENGINE=MyISAM;
ALTER TABLE domaingroup_domain ENGINE=MyISAM;
ALTER TABLE edition ENGINE=MyISAM;
ALTER TABLE failed_jobs ENGINE=MyISAM;
ALTER TABLE file ENGINE=MyISAM;
ALTER TABLE file_user ENGINE=MyISAM;
ALTER TABLE filegroup ENGINE=MyISAM;
ALTER TABLE filegroup_file ENGINE=MyISAM;
ALTER TABLE filegroup_usergroup ENGINE=MyISAM;
ALTER TABLE form ENGINE=MyISAM;
ALTER TABLE form_form ENGINE=MyISAM;
ALTER TABLE form_permalink ENGINE=MyISAM;
ALTER TABLE form_site ENGINE=MyISAM;
ALTER TABLE formanswer ENGINE=MyISAM;
ALTER TABLE formanswerint ENGINE=MyISAM;
ALTER TABLE formanswerstr ENGINE=MyISAM;
ALTER TABLE formquestion ENGINE=MyISAM;
ALTER TABLE formquestiontype ENGINE=MyISAM;
ALTER TABLE formquestionvalue ENGINE=MyISAM;
ALTER TABLE `groups` ENGINE=MyISAM;
ALTER TABLE migrations ENGINE=MyISAM;
ALTER TABLE note ENGINE=MyISAM;
ALTER TABLE ord ENGINE=MyISAM;
ALTER TABLE ord_product ENGINE=MyISAM;
ALTER TABLE ord_product_option ENGINE=MyISAM;
ALTER TABLE ord_reminder ENGINE=MyISAM;
ALTER TABLE order_product ENGINE=MyISAM;
ALTER TABLE orderstate ENGINE=MyISAM;
ALTER TABLE page ENGINE=MyISAM;
ALTER TABLE page_date ENGINE=MyISAM;
ALTER TABLE page_page ENGINE=MyISAM;
ALTER TABLE page_product ENGINE=MyISAM;
ALTER TABLE page_productgroup ENGINE=MyISAM;
ALTER TABLE page_ref ENGINE=MyISAM;
ALTER TABLE page_user ENGINE=MyISAM;
ALTER TABLE page_usergroup ENGINE=MyISAM;
ALTER TABLE pagegroup ENGINE=MyISAM;
ALTER TABLE pagegroup_page ENGINE=MyISAM;
ALTER TABLE pagegroup_usergroup ENGINE=MyISAM;
ALTER TABLE pagereads ENGINE=MyISAM;
ALTER TABLE password_resets ENGINE=MyISAM;
ALTER TABLE paymentmethod ENGINE=MyISAM;
ALTER TABLE product ENGINE=MyISAM;
ALTER TABLE product_product ENGINE=MyISAM;
ALTER TABLE product_user ENGINE=MyISAM;
ALTER TABLE productgroup ENGINE=MyISAM;
ALTER TABLE productgroup_product ENGINE=MyISAM;
ALTER TABLE productoption ENGINE=MyISAM;
ALTER TABLE productoption_notify ENGINE=MyISAM;
ALTER TABLE productoptionvalue ENGINE=MyISAM;
ALTER TABLE productoptionvalue_notify ENGINE=MyISAM;
ALTER TABLE rate ENGINE=MyISAM;
ALTER TABLE react ENGINE=MyISAM;
ALTER TABLE react_relations ENGINE=MyISAM;
ALTER TABLE react_relations_value ENGINE=MyISAM;
ALTER TABLE react_site ENGINE=MyISAM;
ALTER TABLE reactgroup ENGINE=MyISAM;
ALTER TABLE read_event ENGINE=MyISAM;
ALTER TABLE read_event_bot ENGINE=MyISAM;
ALTER TABLE read_event_event ENGINE=MyISAM;
ALTER TABLE read_event_referrer ENGINE=MyISAM;
ALTER TABLE read_event_session ENGINE=MyISAM;
ALTER TABLE read_event_user ENGINE=MyISAM;
ALTER TABLE read_newsletter ENGINE=MyISAM;
ALTER TABLE read_newsletter_link ENGINE=MyISAM;
ALTER TABLE read_referrer ENGINE=MyISAM;
ALTER TABLE ref ENGINE=MyISAM;
ALTER TABLE referrer ENGINE=MyISAM;
ALTER TABLE relations ENGINE=MyISAM;
ALTER TABLE relationtype ENGINE=MyISAM;
ALTER TABLE searchlog ENGINE=MyISAM;
ALTER TABLE session_product ENGINE=MyISAM;
ALTER TABLE shared_forms ENGINE=MyISAM;
ALTER TABLE shared_templates ENGINE=MyISAM;
ALTER TABLE site_site ENGINE=MyISAM;
ALTER TABLE site_template ENGINE=MyISAM;
ALTER TABLE site_templategroup ENGINE=MyISAM;
ALTER TABLE stock ENGINE=MyISAM;
ALTER TABLE stock_option ENGINE=MyISAM;
ALTER TABLE template ENGINE=MyISAM;
ALTER TABLE template_content ENGINE=MyISAM;
ALTER TABLE template_type ENGINE=MyISAM;
ALTER TABLE templategroup ENGINE=MyISAM;
ALTER TABLE templategroup_template ENGINE=MyISAM;
ALTER TABLE type ENGINE=MyISAM;
ALTER TABLE user ENGINE=MyISAM;
ALTER TABLE user_authy ENGINE=MyISAM;
ALTER TABLE user_pagegroup ENGINE=MyISAM;
ALTER TABLE user_setting ENGINE=MyISAM;
ALTER TABLE user_usergroup ENGINE=MyISAM;
ALTER TABLE usergroup ENGINE=MyISAM;
ALTER TABLE var ENGINE=MyISAM;
ALTER TABLE var_value ENGINE=MyISAM;
ALTER TABLE vargroup ENGINE=MyISAM;
ALTER TABLE vargroup_edition ENGINE=MyISAM;
ALTER TABLE vargroup_type ENGINE=MyISAM;
ALTER TABLE vargroup_var ENGINE=MyISAM;
        ');
    }
};