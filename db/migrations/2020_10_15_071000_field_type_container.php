<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class FieldTypeContainer extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::insert(
            'insert into formquestiontype (fqt_id, fqt_name, fqt_displayname, fqt_info) values (?, ? , ? ,?)',
            [21, 'container', 'Container', 'Allow create references to other reacts']);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::delete(
            'delete from formquestiontype where fqt_id = ?',
            [21]);
    }
}
