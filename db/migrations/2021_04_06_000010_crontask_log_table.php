<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CrontaskLogTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('crontask_log', function (Blueprint $table) {
            $table->id('cl_id');
            $table->unsignedBigInteger('cl_cron_id')->nullable(false);
            $table->string('cl_run_id')->nullable(false);
            $table->text('cl_message');
            $table->dateTime('cl_date')->nullable(false)->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->foreign('cl_cron_id')->references('cron_id')->on('crontask')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('crontask_log');
    }
}