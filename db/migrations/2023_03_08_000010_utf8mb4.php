<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement('alter table react collate = utf8mb4_unicode_ci;');
        DB::statement('alter table var collate = utf8mb4_unicode_ci;');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement('alter table react collate = utf8_general_ci;');
        DB::statement('alter table var collate = utf8_general_ci;');

    }
};