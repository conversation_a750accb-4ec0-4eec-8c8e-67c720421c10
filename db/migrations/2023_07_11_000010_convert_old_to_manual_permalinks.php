<?php

use Exolog\Core\Forms\React;
use Exolog\Module\Permalinks\Model\Permalink;
use Exolog\Module\Site\Model\Site;
use Illuminate\Database\Migrations\Migration;
use Symfony\Component\VarDumper\VarDumper;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Site::all()->each(function (Site $site) {
            coreInit()->resolveSite($site->site_id);
            $reacts = React::query()->whereNotNull('old_permalinks')->get();
            $reacts->each(function (React $react) {
                try {
                    $links = json_decode($react['old_permalinks']);
                    if (empty($links)) {
                        return;
                    }
                    VarDumper::dump('react_id: ' . $react->id());

                    foreach ($links as $link) {
                        $permalink = Permalink::query()
                            ->where('p_react_id', $react->id())
                            ->where('p_url', $link)->first();
                        if ($permalink === null) {
                            Permalink::create([
                                'p_react_id' => $react->id(),
                                'p_type' => Permalink::TYPE_MANUAL,
                                'p_url' => $link,
                                'p_redirect' => Permalink::TYPE_PRIMARY,
                                'p_context' => ['old_permalinks' => true]
                            ]);
                            VarDumper::dump('Created: ' . $link);
                        } else {
                            VarDumper::dump('exists: ' . $link);
                        }
                    }
                } catch (\Exception $e) {
                    VarDumper::dump($e->getMessage());
                }
            });
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};
