<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class FormPermalink extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //only for test
        //Schema::dropIfExists('form_permalink');

        Schema::create('form_permalink', function (Blueprint $table) {
            $table->id('fp_id');
            $table->unsignedBigInteger('fp_form_id');
            $table->string('fp_uri')->nullable(false);
            $table->boolean('fp_sitemap_active')->default(false);
            $table->string('fp_sitemap_name')->nullable(true);


            $table->foreign('fp_form_id')->references('form_id')->on('form')->onDelete('cascade');
            $table->unique(['fp_form_id', 'fp_uri']);
            $table->unique(['fp_form_id', 'fp_sitemap_name']);

        });

        Schema::table('form', function (Blueprint $table) {
            $table->unsignedBigInteger('form_permalink_id')->nullable(true);
            $table->foreign('form_permalink_id')->references('fp_id')->on('form_permalink')->restrictOnDelete();
        });

        Schema::table('react', function (Blueprint $table) {
            $table->unsignedBigInteger('react_form_permalink_id')->nullable(true);
            $table->foreign('react_form_permalink_id')->references('fp_id')->on('form_permalink')->restrictOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('form_permalink');
        Schema::dropColumns('form', ['form_permalink_id']);
        Schema::dropColumns('react', ['react_form_permalink_id']);
    }
}