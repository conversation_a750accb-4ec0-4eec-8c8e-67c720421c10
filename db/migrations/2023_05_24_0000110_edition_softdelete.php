<?php

use Exolog\Module\Editions\Model\Edition;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('edition', static function(Blueprint $table) {
            // Add deleted_at column only if it doesn't exist
            if (!Schema::hasColumn('edition', Edition::DELETED_AT)) {
                $table->softDeletes(Edition::DELETED_AT);
            }
        });

        // Only update if e_isdeleted column exists
        if (Schema::hasColumn('edition', 'e_isdeleted')) {
            DB::table('edition')
                ->where('e_isdeleted', 1)
                ->update([Edition::DELETED_AT => now()]);

            Schema::table('edition', static function(Blueprint $table) {
                $table->dropColumn(['e_isdeleted']);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};