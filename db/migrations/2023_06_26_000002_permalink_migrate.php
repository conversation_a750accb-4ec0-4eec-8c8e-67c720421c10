<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('formquestion')
            ->where('fq_name', 'permalink')
            ->update(['fq_type' => 26]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('formquestion')
            ->where('fq_name', 'permalink')
            ->update(['fq_type' => 1]);
    }
};
