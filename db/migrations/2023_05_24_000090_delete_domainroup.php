<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists('domaingroup');
        Schema::dropIfExists('domaingroup_domain');
        Schema::dropIfExists('form_form');
        Schema::dropIfExists('paymentmethod');
        Schema::dropIfExists('site_site');
        Schema::dropIfExists('user_setting');

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};