<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Only rename column if domain_relation exists and domain_edition_id doesn't exist
        if (Schema::hasColumn('domain', 'domain_relation') && !Schema::hasColumn('domain', 'domain_edition_id')) {
            Schema::table('domain', static function(Blueprint $table) {
                $table->renameColumn('domain_relation', 'domain_edition_id');
            });
        }

        // Only proceed if domain_edition_id column exists
        if (Schema::hasColumn('domain', 'domain_edition_id')) {
            DB::statement('alter table domain alter column domain_edition_id drop default');

            DB::statement('update domain set domain_edition_id=null where domain_edition_id not in (select e_id from edition)');

            Schema::table('domain', static function(Blueprint $table) {
                $table->integer('domain_edition_id', false, false)->nullable(true)->change();
                $table->foreign('domain_edition_id')->references('e_id')->on('edition');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};