<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement('delete from user_usergroup where uug_user not in (select u_id from user)');

        Schema::table('user_usergroup', static function(Blueprint $table) {
            $table->integer('uug_user', false, true)->nullable(false)->change();
            $table->foreign('uug_user')->references('u_id')->on('user')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};