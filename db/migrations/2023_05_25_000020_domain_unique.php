<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('domain')
            ->whereNull('domain_name')
            ->delete();

        DB::statement('delete from domain where domain_site not in (select site_id from site where site_deleted = 0)');

        Schema::table('domain', static function(Blueprint $table) {
            $table->string('domain_name', 255)->nullable(false)->change();
        });

        DB::statement('alter table domain drop key `unique`');
        DB::statement('create unique index u_domain_domain_name on domain (domain_name)');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};