<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('react', function(Blueprint $table) {
            // Add react_layout_scope column only if it doesn't exist
            if (!Schema::hasColumn('react', 'react_layout_scope')) {
                $table->string('react_layout_scope', 50)
                    ->default(\Exolog\Module\ReactLayout\ReactLayout::SCOPE_FORM_PERMALINK)
                    ->nullable(false);
            }
        });

        Schema::table('react', function(Blueprint $table) {
            // Add react_layout column only if it doesn't exist
            if (!Schema::hasColumn('react', 'react_layout')) {
                $table->json('react_layout')->nullable();
            }
        });

        Schema::table('form_permalink', function(Blueprint $table) {
            // Add fp_react_layout column only if it doesn't exist
            if (!Schema::hasColumn('form_permalink', 'fp_react_layout')) {
                $table->json('fp_react_layout')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropColumns('react', ['react_layout', 'react_layout_scope']);
        Schema::dropColumns('form_permalink', ['fp_react_layout']);
    }
};