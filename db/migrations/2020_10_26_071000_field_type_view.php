<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class FieldTypeView extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::insert(
            'insert into formquestiontype (fqt_id, fqt_name, fqt_displayname, fqt_info) values (?, ? , ? ,?)',
            [22, 'view', 'View', 'Allow create references to blade view']);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::delete(
            'delete from formquestiontype where fqt_id = ?',
            [22]);
    }
}
