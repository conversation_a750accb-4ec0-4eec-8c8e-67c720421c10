<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add columns only if they don't exist
        Schema::table('user', static function(Blueprint $table) {
            if (!Schema::hasColumn('user', 'u_two_factor_secret')) {
                $table->text('u_two_factor_secret')
                    ->after('u_password')
                    ->nullable();
            }
            if (!Schema::hasColumn('user', 'remember_token')) {
                $table->rememberToken();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropColumns('user', ['u_two_factor_secret', 'remember_token']);

    }
};