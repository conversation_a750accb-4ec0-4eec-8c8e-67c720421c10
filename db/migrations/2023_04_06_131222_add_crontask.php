<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('crontask')
            ->where('cron_site', 0)
            ->where('cron_type', 'php')
            ->where('cron_value', '{path_base}/exologadmin.webkracht.nl/sub/renewDomainsSSL.php')
            ->delete();

        DB::table('crontask')->insert([
            'cron_site' => 0,
            'cron_type' => 'command',
            'cron_value' => 'dealer:renew-ssl',
            'cron_status' => 'new',
            'cron_date_added' => now(),
            'cron_date' => now(),
            'cron_data' => null,
            'cron_every' => 1440,
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('crontask')
            ->where('cron_site', 0)
            ->where('cron_type', 'command')
            ->where('cron_value', 'dealer:renew-ssl')
            ->delete();
    }
};