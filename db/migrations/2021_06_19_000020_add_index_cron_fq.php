<?php

use Exolog\Module\Container\ContainerService;
use Exolog\Module\Support\Facades\Site;
use Exolog\Module\Support\Facades\Vars;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;


return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Create crontask index only if it doesn't exist
        $result = DB::select("SHOW INDEX FROM crontask WHERE Key_name = 'crontask_cron_status_cron_date_index'");
        if (empty($result)) {
            DB::statement('create index crontask_cron_status_cron_date_index
                                        on crontask (cron_status, cron_date)');
        }

        // Create formquestion index only if it doesn't exist
        $result = DB::select("SHOW INDEX FROM formquestion WHERE Key_name = 'formquestion_fq_form_fq_name_fq_isdeleted_index'");
        if (empty($result)) {
            DB::statement('create index formquestion_fq_form_fq_name_fq_isdeleted_index
                                        on formquestion (fq_form, fq_name, fq_isdeleted)');
        }
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};