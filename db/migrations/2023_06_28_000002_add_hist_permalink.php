<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('form_permalink', function (Blueprint $table) {
            // Add fp_history_active column only if it doesn't exist
            if (!Schema::hasColumn('form_permalink', 'fp_history_active')) {
                $table->boolean('fp_history_active')->default(false);
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropColumns('form_permalink', ['fp_history_active']);
    }
};
