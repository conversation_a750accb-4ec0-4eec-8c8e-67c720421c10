<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('react', function (Blueprint $table) {
            // Add react_edition_id column only if it doesn't exist
            if (!Schema::hasColumn('react', 'react_edition_id')) {
                $table->unsignedBigInteger('react_edition_id')->nullable(true);
                $table->foreign('react_edition_id')->references('e_id')->on('edition')->restrictOnDelete();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropColumns('react', ['react_edition_id']);
    }
};