<?php

use Exolog\Module\Site\Model\Site;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('site', static function(Blueprint $table) {
            // Add deleted_at column only if it doesn't exist
            if (!Schema::hasColumn('site', Site::DELETED_AT)) {
                $table->softDeletes(Site::DELETED_AT);
            }
        });

        // Only update if site_deleted column exists
        if (Schema::hasColumn('site', 'site_deleted')) {
            DB::table('site')
                ->where('site_deleted', 1)
                ->update([Site::DELETED_AT => now()]);

            Schema::table('site', static function(Blueprint $table) {
                $table->dropColumn(['site_deleted']);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};