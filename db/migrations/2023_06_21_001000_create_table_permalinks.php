<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        // Drop table if exists to ensure clean state
        Schema::dropIfExists('permalinks');

        // Create permalinks table only if it doesn't exist (extra safety)
        if (!Schema::hasTable('permalinks')) {
            Schema::create('permalinks', function(Blueprint $table) {
                $table->id('p_id');
                $table->unsignedInteger('p_site_id');
                $table->integer('p_react_id');
                $table->enum('p_type', ['PRIMARY', 'HISTORY', 'ALTERNATE', 'MANUAL']);
                $table->string('p_url', 500);
                $table->unsignedBigInteger('p_fp_id')->nullable();
                $table->json('p_context')->nullable();
                $table->timestamps();
                $table->unique(['p_url', 'p_site_id']);

                $table->foreign('p_site_id')->references('site_id')->on('site')->cascadeOnDelete();
                $table->foreign('p_react_id')->references('react_id')->on('react')->cascadeOnDelete();
                $table->foreign('p_fp_id')->references('fp_id')->on('form_permalink')->restrictOnDelete();
            });
        }
    }

    public function down()
    {
        Schema::dropIfExists('permalinks');
    }
};
