<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class Crontask extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("alter table crontask modify cron_type varchar(100) charset utf8 null;");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement("
        alter table crontask modify cron_type enum('tmpl', 'url', 'sendMail', 'orderReminder', 'siteDump', 'checkOrderStatus', 'export', 'convertVideo', 'copySite', 'importUsers', 'php') charset utf8 default 'url' null;
        ");
    }
}
