<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        DB::statement('update react set react_site = (select form_site_id from form where form_id = react.react_form)');

        DB::statement('delete from react where react_site not in (select site_id from site)');


        Schema::table('react', static function(Blueprint $table) {
            $table->unsignedInteger('react_site')->default(null)->nullable(false)->change();
            $table->foreign('react_site')->references('site_id')->on('site')->cascadeOnDelete();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};