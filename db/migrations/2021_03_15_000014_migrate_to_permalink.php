<?php

use Exolog\Module\Container\ContainerService;
use Exolog\Module\Support\Facades\Site;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;


class MigrateToPermalink extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $forms = DB::select("
                            select s.site_id,
                                   f.*
                            from form f,
                                 form_site fs,
                                 site s
                            where fs.fs_site_id = s.site_id
                              and s.site_deleted = 0
                              and f.form_id = fs.fs_form_id
                              and f.form_react_url > ''
                            order by s.site_name");

        foreach ($forms as $form) {
            $permalink_id = DB::table('form_permalink')->select('*')
                ->where('fp_form_id', $form['form_id'])
                ->where('fp_uri', $form['form_react_url'])
                ->value('fp_id');
            if (empty($permalink_id)) {
                $permalink_id = DB::table('form_permalink')->insertGetId(
                    [
                        'fp_form_id' => $form['form_id'],
                        'fp_uri' => $form['form_react_url'],
                        'fp_sitemap_active' => $form['form_sitemap_isenable'] ?? 0,
                        'fp_sitemap_name' => $form['form_sitemap_index_name']
                    ]
                );
            }
            dump(sprintf('%s: %s=>%s', $form['form_id'], $form['form_react_url'], $permalink_id));

            DB::table('form')->where('form_id', $form['form_id'])->update(['form_permalink_id' => $permalink_id]);
        }

        foreach (\Exolog\Module\Site\Model\Site::all() as $site) {
            coreInit()->resolveSite($site['site_id']);
            dump('Site ID=' . Site::id());
            $this->migrateCustomURL(ContainerService::collcetContainers());
            dump('Migrate legacy containers');
            $this->migrateCustomURL(ContainerService::collectLegacyContainers());
        }
    }

    private function migrateCustomURL(array $containers)
    {
        foreach ($containers as $container) {
            dump($container->getContainerId());
            $config = $container->getConfig();
            foreach ($config['forms'] as $key => $form) {
                if (empty($config['forms'][$key]['custom_react_url'])) continue;
                if (is_numeric($config['forms'][$key]['custom_react_url'])) continue;

                $permalink_id = DB::table('form_permalink')->select('*')
                    ->where('fp_form_id', $form['form_id'])
                    ->where('fp_uri', $form['custom_react_url'])
                    ->value('fp_id');
                if (empty($permalink_id)) {
                    $permalink_id = DB::table('form_permalink')
                        ->insertGetId(
                            [
                                'fp_form_id' => $form['form_id'],
                                'fp_uri' => $form['custom_react_url'],
                            ]
                        );
                }

                dump(sprintf('%s: %s=%s', $form['form_name'], $config['forms'][$key]['custom_react_url'], $permalink_id));
                $config['forms'][$key]['form_permalink_id'] = $permalink_id;
            };
            $container->setConfig($config)->save();
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('form_permalink')->truncate();
    }
}