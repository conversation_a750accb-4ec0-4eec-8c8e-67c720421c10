<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class Cleanup extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $tables = DB::select("select * from  information_schema.TABLES t 
        where t.TABLE_SCHEMA = 'exolog' and t.TABLE_NAME like '%_shift'");
        foreach ($tables as $table) {
            $this->drop($table['TABLE_NAME']);
        }

        $tables = DB::select("select * from  information_schema.TABLES t 
        where t.TABLE_SCHEMA = 'exolog' and t.TABLE_NAME like 'stats_archive_%'");
        foreach ($tables as $table) {
            $this->drop($table['TABLE_NAME']);
        }

        $this->drop('admin_stylesheet');
        $this->drop('admin_template');
        $this->drop('adminuser');
        $this->drop('db_config');
        $this->drop('departments');
        $this->drop('employees');
        $this->drop('exit');
        $this->drop('exittype');
        $this->drop('formquestionvalue_temp_ids');
        $this->drop('forumboard');
        $this->drop('forummessage');
        $this->drop('languages');
        $this->drop('languages_orig');
        $this->drop('log');
        $this->drop('log_cpu_usage');
        $this->drop('ocms2_Ogol_t');
        $this->drop('page_back');
        $this->drop('page_backup');
        $this->drop('react_sync');
        $this->drop('res_react_user');
        $this->drop('search');
        $this->drop('search_log');
        $this->drop('searchgroup');

        $this->drop('stat_page');
        $this->drop('stat_page_daily');

        $this->drop('stat_page');
        $this->drop('stat_page_daily');
        $this->drop('stat_react');
        $this->drop('stat_react_daily');
        $this->drop('stat_site');
        $this->drop('stats_access');
        $this->drop('stats_goal');
        $this->drop('stats_log_action');
        $this->drop('stats_log_conversion');
        $this->drop('stats_log_conversion_item');
        $this->drop('stats_log_link_visit_action');
        $this->drop('stats_log_profiling');
        $this->drop('stats_log_visit');
        $this->drop('stats_logger_message');
        $this->drop('stats_option');
        $this->drop('stats_report');
        $this->drop('stats_segment');
        $this->drop('stats_sequence');
        $this->drop('stats_session');
        $this->drop('stats_site');
        $this->drop('stats_site_setting');
        $this->drop('stats_site_url');
        $this->drop('stats_user');
        $this->drop('stats_user_dashboard');
        $this->drop('stats_user_language');

        $this->drop('tmp_react_json');
        $this->drop('user_chat');
        $this->drop('visit');

    }

    protected function drop($table_name)
    {
        dump(sprintf('Drop table "%s"', $table_name));
        Schema::dropIfExists($table_name);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

    }
}
