<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        DB::table('domain')
            ->where('domain_isdeleted', 1)
            ->delete();

        Schema::table('domain', static function(Blueprint $table) {
            $table->dropColumn(
                [
                    'domain_isdeleted',

                    'domain_register',
                    'domain_expiration_date',
                    'domain_ns4',
                    'domain_parent',
                    'domain_issub',
                    'domain_ns3',
                    'domain_ns1',
                    'domain_type',
                    'domain_creation_date',
                    'domain_ns2',
                    'domain_status',
                    'domain_pk_id',
                    'domain_hidden',
                    'domain_iscloudflare',

                ]
            );
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};