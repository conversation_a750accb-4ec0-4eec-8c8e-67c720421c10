<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Check if column exists before dropping to avoid errors
        if (Schema::hasColumn('react', 'react_vr_permalink')) {
            try {
                // First try to drop any indexes that might be using this column
                $indexes = DB::select("SHOW INDEX FROM react WHERE Column_name = 'react_vr_permalink'");
                foreach ($indexes as $index) {
                    if ($index->Key_name !== 'PRIMARY') {
                        try {
                            DB::statement("DROP INDEX `{$index->Key_name}` ON react");
                        } catch (\Exception $e) {
                            // Index might not exist or already dropped, continue
                        }
                    }
                }

                // Try to drop the column using direct SQL
                DB::statement('ALTER TABLE react DROP COLUMN react_vr_permalink');

            } catch (\Exception $e) {
                // If dropping fails due to row size, try to set the column to NULL first
                if (strpos($e->getMessage(), 'Row size too large') !== false) {
                    try {
                        // First set the column to NULL to reduce storage
                        DB::statement('UPDATE react SET react_vr_permalink = NULL');
                        // Then try to drop it again
                        DB::statement('ALTER TABLE react DROP COLUMN react_vr_permalink');
                    } catch (\Exception $e2) {
                        // If still failing, log the issue but don't fail the migration
                        dump("Warning: Could not drop react_vr_permalink column due to row size limitations");
                        dump("Error: " . $e2->getMessage());
                        // The column will remain but be NULL, which is acceptable for migration continuity
                    }
                } else {
                    throw $e; // Re-throw if it's a different error
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};
