<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add form_sitemap_isenable column only if it doesn't exist
        if (!Schema::hasColumn('form', 'form_sitemap_isenable')) {
            DB::statement("alter table form add form_sitemap_isenable tinyint default 0 null;");
        }

        // Add form_sitemap_index_name column only if it doesn't exist
        if (!Schema::hasColumn('form', 'form_sitemap_index_name')) {
            DB::statement("alter table form add form_sitemap_index_name text null;");
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement(" alter table form drop column form_sitemap_isenable;");
        DB::statement("alter table form drop column form_sitemap_index_name;");
    }
};
