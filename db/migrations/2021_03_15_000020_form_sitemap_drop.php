<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class FormSitemapDrop extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("alter table form drop column form_sitemap_isenable;");
        DB::statement("alter table form drop column form_sitemap_index_name;");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement("alter table form add form_sitemap_isenable tinyint default 0 null;");
        DB::statement("alter table form add form_sitemap_index_name text null;");
    }
}
