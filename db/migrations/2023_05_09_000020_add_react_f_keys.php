<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        \DB::table('react')
            ->whereNotExists(function($query) {
                $query->select(\DB::raw(1))
                    ->from('form')
                    ->whereColumn('form_id', 'react.react_form');
            })
            ->delete();

        Schema::table('react', static function(Blueprint $table) {
            $table->integer('react_form', false, false)->nullable(false)->change();
            $table->foreign('react_form')->references('form_id')->on('form')->cascadeOnDelete();
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};