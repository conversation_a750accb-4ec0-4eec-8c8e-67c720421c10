<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        Schema::table('admin', function(Blueprint $table) {
            $table->json('a_options')->nullable();
        });


        $rows = DB::table('admin')->get();

        foreach ($rows as $row) {
            $settings = @unserialize($row['a_settings']);
            if ($settings) {
                DB::table('admin')
                    ->where('a_id', $row['a_id'])
                    ->update(['a_options' => $settings]);
            }
        }
        //Schema::dropColumns('admin', ['a_settings']);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropColumns('admin', ['a_options']);
    }
};