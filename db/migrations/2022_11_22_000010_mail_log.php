<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists('mail_log');
        Schema::create('mail_log', function(Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id('ml_id');
            $table->string('ml_to', 1000)->nullable(false);
            $table->string('ml_from', 1000)->nullable(false);
            $table->string('ml_subject', 1000);
            $table->integer('ml_site_id', false, true);
            $table->foreign('ml_site_id')->references('site_id')->on('site')->onDelete('cascade');
            $table->timestamp(\Exolog\Module\Mails\Model\MailLog::CREATED_AT)->nullable();
            $table->timestamp(\Exolog\Module\Mails\Model\MailLog::UPDATED_AT)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('mail_log');
    }
};