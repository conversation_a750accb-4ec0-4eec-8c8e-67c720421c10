<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $idsToDelete = DB::table('formquestion')
            ->select(DB::raw('MIN(fq_id) as fq_id'))
            ->groupBy('fq_form', 'fq_name')
            ->havingRaw('COUNT(1) > 1')
            ->pluck('fq_id');

        //dd($idsToDelete);
        foreach ($idsToDelete as $id) {
            //dump($id);
            DB::table('formquestion')
                ->where('fq_id', $id)
                ->delete();
        }

        DB::statement('
               drop index formquestion_fq_form_fq_name_fq_isdeleted_index on formquestion;');

        DB::statement('
        create unique index formquestion_fq_form_fq_name_fq_isdeleted_index
    on formquestion (fq_form, fq_name);');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

    }
};
