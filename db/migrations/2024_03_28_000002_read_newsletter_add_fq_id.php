<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('read_newsletter', function (Blueprint $table) {
            // Add columns only if they don't exist
            if (!Schema::hasColumn('read_newsletter', 'rn_fq_id')) {
                $table->integer('rn_fq_id')->nullable();
                $table->foreign('rn_fq_id')->references('fq_id')->on('formquestion')->cascadeOnDelete();
            }
            if (!Schema::hasColumn('read_newsletter', 'rn_extra')) {
                $table->json('rn_extra')->nullable();
            }

            // Modify existing column (this should be safe to run multiple times)
            $table->integer('rn_usergroup')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropColumns('read_newsletter', ['rn_fq_id', 'rn_extra']);
    }
};
