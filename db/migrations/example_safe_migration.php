<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Exolog\Core\Database\MigrationHelper;

/**
 * Example migration showing how to use MigrationHelper for safe migrations
 * This prevents "Table already exists" and "Column already exists" errors
 */
return new class extends Migration {
    
    public function up()
    {
        // Example 1: Safe table creation
        // MigrationHelper::safeCreateTable('example_table', function (Blueprint $table) {
        //     $table->id();
        //     $table->string('name');
        //     $table->text('description')->nullable();
        //     $table->timestamps();
        // });

        // Example 2: Safe column addition (single column)
        // MigrationHelper::safeAddColumn('users', 'profile_image', function (Blueprint $table) {
        //     $table->string('profile_image')->nullable()->after('email');
        // });

        // Example 3: Safe multiple column addition
        // $columnsToAdd = ['phone', 'address', 'city'];
        // MigrationHelper::safeAddColumns('users', $columnsToAdd, function (Blueprint $table) {
        //     $table->string('phone')->nullable();
        //     $table->text('address')->nullable();
        //     $table->string('city')->nullable();
        // });

        // Example 4: Safe index addition
        // MigrationHelper::safeAddIndex('users', 'email', 'users_email_unique');
        // MigrationHelper::safeAddIndex('users', ['city', 'phone'], 'users_city_phone_index');

        // Example 5: Safe foreign key addition
        // MigrationHelper::safeAddForeignKey('posts', 'user_id', 'users', 'id');

        // Example 6: Traditional approach with manual checks
        // if (!Schema::hasTable('manual_check_table')) {
        //     Schema::create('manual_check_table', function (Blueprint $table) {
        //         $table->id();
        //         $table->string('title');
        //         $table->timestamps();
        //     });
        // }

        // Example 7: Adding columns with manual checks
        // Schema::table('existing_table', function (Blueprint $table) {
        //     if (!Schema::hasColumn('existing_table', 'new_column')) {
        //         $table->string('new_column')->nullable();
        //     }
        //     if (!Schema::hasColumn('existing_table', 'another_column')) {
        //         $table->integer('another_column')->default(0);
        //     }
        // });
    }

    public function down()
    {
        // Safe table dropping
        // MigrationHelper::safeDropTable('example_table');
        // MigrationHelper::safeDropTable('manual_check_table');

        // Safe column dropping
        // MigrationHelper::safeDropColumns('users', ['profile_image', 'phone', 'address', 'city']);
        // MigrationHelper::safeDropColumns('existing_table', ['new_column', 'another_column']);
    }
};
