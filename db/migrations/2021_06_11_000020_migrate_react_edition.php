<?php

use Exolog\Module\Container\ContainerService;
use Exolog\Module\Support\Facades\Site;
use Exolog\Module\Support\Facades\Vars;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;


return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $rr = DB::table('react_relations')->where('rr_type', 'edition')->get();
        foreach ($rr as $item) {
            DB::table('react')
                ->where('react_id', $item['rr_react'])
                ->update(['react_edition_id' => $item['rr_obj']]);
            dump('Updated react=' . $item['rr_react']);
            //dump($item);
        }
        /*foreach (\Exolog\Module\Site\Model\Site::all() as $site) {
            coreInit()->initExolog($site['site_id']);
            dump('Site ID=' . Site::id());

            $formId = exolog()->formHandler->getFormIdByName(exolog()->ECO['define']['SYSTEM_URL_FORM']);
            if (!empty($formId)) {
                $parentReact = fd()->form(exolog()->ECO['define']['SYSTEM_URL_FORM'])
                    ->order(array('react_id' => 'ASC'))
                    ->fetch();
                if (!empty($parentReact)) {
                    $containerId = $formId . '|' . $parentReact['react_id'] . '|related_forms|related_table';
                    $container = exolog()->getContainer($containerId);

                    $var = \Exolog\Module\Support\Facades\Vars::get('container_custom_buttons', 'form', $formId);
                    dump($formId);
                    $var->save();
                    $config = $container->getConfig();
                    $config['useOwnConfig'] = 1;
                    $var->getContainer()->setConfig($config)->save();
                    $var->getContainer()->setItems($container->getItemsTree())->save();
                    dump('done.');
                }
            }
        }*/
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};