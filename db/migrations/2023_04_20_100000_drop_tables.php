<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists('cache');
        Schema::dropIfExists('brand');
        Schema::dropIfExists('crontask');
        Schema::dropIfExists('crontask_log');
        Schema::dropIfExists('currency');
        Schema::dropIfExists('debug');
        Schema::dropIfExists('deliverymethod');
        Schema::dropIfExists('deliverypricemethod');
        Schema::dropIfExists('deliveryrange');
        Schema::dropIfExists('discount');
        Schema::dropIfExists('discount_coupons');
        Schema::dropIfExists('discount_rules');
        Schema::dropIfExists('file');
        Schema::dropIfExists('file_user');
        Schema::dropIfExists('filegroup');
        Schema::dropIfExists('filegroup_file');
        Schema::dropIfExists('filegroup_usergroup');
        Schema::dropIfExists('formanswer');
        Schema::dropIfExists('formanswerint');
        Schema::dropIfExists('formanswerstr');
        Schema::dropIfExists('note');
        Schema::dropIfExists('options');
        Schema::dropIfExists('ord');
        Schema::dropIfExists('ord_product');
        Schema::dropIfExists('ord_product_option');
        Schema::dropIfExists('ord_reminder');
        Schema::dropIfExists('order_product');
        Schema::dropIfExists('orderstate');
        Schema::dropIfExists('page');
        Schema::dropIfExists('page_date');
        Schema::dropIfExists('page_page');
        Schema::dropIfExists('page_product');
        Schema::dropIfExists('page_productgroup');
        Schema::dropIfExists('page_ref');
        Schema::dropIfExists('page_user');
        Schema::dropIfExists('page_usergroup');
        Schema::dropIfExists('pagegroup');
        Schema::dropIfExists('pagegroup_page');
        Schema::dropIfExists('pagegroup_usergroup');
        Schema::dropIfExists('pagereads');
        Schema::dropIfExists('product');
        Schema::dropIfExists('product_product');
        Schema::dropIfExists('product_user');
        Schema::dropIfExists('productgroup');
        Schema::dropIfExists('productgroup_product');
        Schema::dropIfExists('productoption');
        Schema::dropIfExists('productoption_notify');
        Schema::dropIfExists('productoptionvalue');
        Schema::dropIfExists('productoptionvalue_notify');
        Schema::dropIfExists('rate');
        Schema::dropIfExists('read_event');
        Schema::dropIfExists('read_event_bot');
        Schema::dropIfExists('read_event_event');
        Schema::dropIfExists('read_event_referrer');
        Schema::dropIfExists('read_event_session');
        Schema::dropIfExists('read_event_user');
        Schema::dropIfExists('read_newsletter_link');
        Schema::dropIfExists('read_referrer');
        Schema::dropIfExists('ref');
        Schema::dropIfExists('referrer');
        Schema::dropIfExists('searchlog');
        Schema::dropIfExists('session_product');
        Schema::dropIfExists('sessions');
        Schema::dropIfExists('shared_forms');
        Schema::dropIfExists('shared_templates');
        Schema::dropIfExists('shortener');
        Schema::dropIfExists('site_template');
        Schema::dropIfExists('site_templategroup');
        Schema::dropIfExists('stock');
        Schema::dropIfExists('stock_option');
        Schema::dropIfExists('tag_relations');
        Schema::dropIfExists('tag');
        Schema::dropIfExists('template');
        Schema::dropIfExists('template_content');
        Schema::dropIfExists('template_type');
        Schema::dropIfExists('templategroup');
        Schema::dropIfExists('templategroup_template');
        Schema::dropIfExists('type');
        Schema::dropIfExists('user_authy');
        Schema::dropIfExists('user_pagegroup');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

    }
};
