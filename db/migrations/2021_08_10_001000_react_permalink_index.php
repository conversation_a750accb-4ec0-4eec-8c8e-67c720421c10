<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('react', 'react_vr_permalink')) {
            DB::statement("
        alter table react
            add react_vr_permalink varchar(500) GENERATED ALWAYS AS (`react_jdoc`->>'$.permalink');
        ");
        }
        if (!Schema::hasColumn('react', 'react_vr_publish')) {
            DB::statement("
        alter table react
            add react_vr_publish varchar(500) GENERATED ALWAYS AS (`react_jdoc`->>'$.publish');
        ");
        }
        // Create index only if it doesn't exist
        $result = DB::select("SHOW INDEX FROM react WHERE Key_name = 'react_react_vr_permalink_react_vr_pablish_react_edition_id_index'");
        if (empty($result)) {
            DB::statement("
            create index react_react_vr_permalink_react_vr_pablish_react_edition_id_index
                on react (react_vr_permalink, react_vr_publish, react_edition_id, react_isdeleted);
            ");
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement('
            alter table react drop column react_vr_permalink;
        ');
        DB::statement('
            alter table react drop column react_vr_publish;
        ');
        DB::statement('
           drop index react_react_vr_permalink_react_vr_pablish_react_edition_id_index on react;
        ');
    }
};
