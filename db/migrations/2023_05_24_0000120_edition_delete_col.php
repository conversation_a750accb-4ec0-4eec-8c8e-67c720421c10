<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('edition', static function(Blueprint $table) {
            $table->dropColumn(
                [
                    'e_template',
                    'e_stylesheet',
                    'e_intropage',
                    'e_subtitle',
                    'e_ishidden',
                    'e_date_end',
                    'e_date_start',
                    'e_template_group',
                    'e_stylesheet_apply_site',
                    'e_template_closed',
                    'e_hostname',
                    'e_metakeywords',
                    'e_metadescription',
                ]
            );
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};