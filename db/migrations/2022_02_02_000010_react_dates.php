<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement('alter table react modify react_date_changed datetime default CURRENT_TIMESTAMP not null;');
        DB::statement('alter table react modify react_date datetime default CURRENT_TIMESTAMP not null;');
        DB::statement("update react
                                set react_date_changed = react_date
                             where  CAST(react_date_changed AS CHAR(20)) = '0000-00-00 00:00:00';");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};