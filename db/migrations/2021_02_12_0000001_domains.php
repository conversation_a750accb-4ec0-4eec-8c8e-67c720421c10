<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class Domains extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('domain','domain_isreadonly')) {
            DB::statement("ALTER TABLE `domain` DROP `domain_isreadonly`;");
        }
        if (Schema::hasColumn('domain','domain_isdealer')) {
            DB::statement("ALTER TABLE `domain` DROP `domain_isdealer`;");
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (!Schema::hasColumn('domain','domain_isreadonly')) {
            DB::statement("alter table domain add domain_isreadonly int default 0 null;");
        }
        if (!Schema::hasColumn('domain','domain_isdealer')) {
            DB::statement("alter table domain add domain_isdealer int default 0 null;");
        }
    }
}
