<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('formquestion', static function(Blueprint $table) {
            // Add fq_parent_id column only if it doesn't exist
            if (!Schema::hasColumn('formquestion', 'fq_parent_id')) {
                $table->integer('fq_parent_id')->nullable();
                $table->foreign('fq_parent_id')->references('fq_id')->on('formquestion')->cascadeOnDelete();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('formquestion', static function(Blueprint $table) {
            $table->dropColumn('fq_parent_id');
        });
    }
};