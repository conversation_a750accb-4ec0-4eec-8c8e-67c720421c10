<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateExampleTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        /*Schema::create('example', function (Blueprint $table) {
            $table->id();
            $table->text('title');
            $table->text('content');
            $table->dateTime('published_at')->nullable();
            $table->boolean('show_on_home')->default(0);
            $table->enum('status',['draft','review','ready'])->default('draft');
            $table->foreignId('user_id')->constrained('users');
            $table->timestamps();
        });


        Schema::table('users', function (Blueprint $table) {
            $table->integer('votes');
        });
        */
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
       /* Schema::dropIfExists('example');*/
    }
}
