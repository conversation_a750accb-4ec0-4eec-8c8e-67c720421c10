<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists('vargroup');
        Schema::dropIfExists('vargroup_edition');
        Schema::dropIfExists('vargroup_type');
        Schema::dropIfExists('vargroup_var');
        Schema::dropIfExists('var_value');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};