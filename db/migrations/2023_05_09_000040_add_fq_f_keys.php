<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        \DB::table('formquestion')
            ->whereNotExists(function($query) {
                $query->select(\DB::raw(1))
                    ->from('form')
                    ->whereColumn('form_id', 'formquestion.fq_form');
            })
            ->delete();

        Schema::table('formquestion', static function(Blueprint $table) {
            $table->integer('fq_form', false, false)->nullable(false)->change();
            $table->foreign('fq_form')->references('form_id')->on('form')->cascadeOnDelete();
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};