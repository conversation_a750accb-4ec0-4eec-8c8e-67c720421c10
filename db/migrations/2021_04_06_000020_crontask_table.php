<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CrontaskTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement('
        alter table crontask modify cron_site int not null;
        alter table crontask modify cron_type varchar(100) charset utf8 not null;
        alter table crontask modify cron_date_added datetime default CURRENT_TIMESTAMP not null;
        alter table crontask modify cron_date datetime default CURRENT_TIMESTAMP not null;
        alter table crontask alter column cron_every set default 0;
        ');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

    }
}