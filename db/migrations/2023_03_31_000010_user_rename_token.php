<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropColumns('user', ['remember_token']);
        Schema::table('user', static function(Blueprint $table) {
             $table->string('u_remember_token', 100)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropColumns('user', ['u_remember_token']);
        Schema::table('user', static function(Blueprint $table) {
            $table->string('remember_token', 100)->nullable();
        });
    }
};