<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        Schema::table('read_newsletter', function(Blueprint $table) {
            $table->dateTime('rn_date_busy')->nullable()->change();
            $table->dateTime('rn_date_done')->nullable()->change();

            $table->dropColumn([
                'rn_user_ip',
                'rn_page',
                'rn_count_clicks',
                'rn_count_click_users',
                'rn_ispacked',
                'rn_repeat_period',
                'rn_bounces_ug',
            ]);

        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        /*Schema::table('read_newsletter', function(Blueprint $table) {
            $table->dateTime('rn_date_busy')->nullable(false)->change();
            $table->dateTime('rn_date_done')->nullable(false)->change();
        });*/
    }
};
