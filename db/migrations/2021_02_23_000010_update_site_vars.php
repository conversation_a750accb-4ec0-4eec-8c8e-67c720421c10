<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class UpdateSiteVars extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("
        UPDATE var v SET
            var_type = 'site',
            var_parent = var_site
        WHERE 
              var_type = '' or var_type is null
              or var_parent is null or var_parent = 0 or var_parent = ''
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

    }
}
