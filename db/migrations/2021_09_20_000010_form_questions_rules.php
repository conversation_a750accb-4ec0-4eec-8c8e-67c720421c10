<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('formquestion', function (Blueprint $table) {
            // Add fq_rules column only if it doesn't exist
            if (!Schema::hasColumn('formquestion', 'fq_rules')) {
                $table->string('fq_rules')->nullable(true);
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropColumns('formquestion', ['fq_rules']);
    }
};