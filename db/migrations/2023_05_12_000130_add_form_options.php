<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('form', static function(Blueprint $table) {
            // Add form_options column only if it doesn't exist
            if (!Schema::hasColumn('form', 'form_options')) {
                $table->json('form_options')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('form', static function(Blueprint $table) {
            $table->dropColumn('form_options');
        });
    }
};