<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists('form_site');
        Schema::dropIfExists('react_site');
        Schema::dropIfExists('reactgroup');
        Schema::dropIfExists('relationtype');
        Schema::dropIfExists('react_relations');
        Schema::dropIfExists('react_relations_value');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};