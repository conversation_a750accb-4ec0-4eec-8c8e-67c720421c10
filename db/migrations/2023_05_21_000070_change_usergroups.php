<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropColumns('usergroup', [
            'ug_deleted',
            'ug_ismoder',
            'ug_isbusinessclub',
            'ug_iseditors',
            'ug_allow_login',
            'ug_allow_page_assign',
            'ug_allow_page_unhide',
            'ug_allow_news',
            'ug_allow_calendar',
            'ug_allow_spam',
            'ug_allow_upload',
            'ug_allow_extranet',
            'ug_allow_all_extranet',
            'ug_allow_community',
            'ug_allow_forum',
            'ug_allow_forum_mail',
            'ug_allow_order_mail',
            'ug_allow_email',
            'ug_allow_ocms',
            'ug_allow_social_login',
            'ug_islimitedadmin',
            'ug_allow_shop',
            'ug_allow_tmpl',
            'ug_editable',
            'ug_allow_store_bounces',
            'ug_isnewslettersenders',
        ]);

        DB::table('user_usergroup')
            ->whereNotExists(function($query) {
                $query->select(DB::raw(1))
                    ->from('usergroup')
                    ->whereColumn('ug_id', 'user_usergroup.uug_usergroup');
            })
            ->delete();

        Schema::table('user_usergroup', static function(Blueprint $table) {
            $table->integer('uug_usergroup', false, false)->nullable(false)->change();
            $table->foreign('uug_usergroup')->references('ug_id')->on('usergroup')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};