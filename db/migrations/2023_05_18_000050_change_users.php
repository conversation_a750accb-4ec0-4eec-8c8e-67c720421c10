<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropColumns('user', [
            'u_allow_stats',
            'u_google_id',
            'u_fb_id',
            'u_tw_id',
            'u_vk_id',
            'u_authy_id',
            'u_chat_account',
            'u_editable',
            'u_api_key',
            'u_last_browser',
            'u_chat_options',
            'u_couponamount',
            'u_backend',
            'u_issuper',
            'u_webmaillogin',

            'u_newsletter_count_sent',
            'u_newsletter_type',
            'u_newsletter_send',
            'u_newsletter_status',
            'u_newsletter_count_bounced',
            'u_newsletter_newemail',
        ]);


        Schema::table('user', static function(Blueprint $table) {
            $table->string('u_alt_email', 100)->nullable(true)->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};