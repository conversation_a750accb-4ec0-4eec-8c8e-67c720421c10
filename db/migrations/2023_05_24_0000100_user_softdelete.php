<?php

use Exolog\Module\Users\Model\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user', static function(Blueprint $table) {
            // Add u_deleted_at column only if it doesn't exist
            if (!Schema::hasColumn('user', User::DELETED_AT)) {
                $table->softDeletes(User::DELETED_AT);
            }
        });

        // Only update if u_deleted column exists
        if (Schema::hasColumn('user', 'u_deleted')) {
            DB::table('user')
                ->where('u_deleted', 1)
                ->update(['u_deleted_at' => now()]);

            Schema::table('user', static function(Blueprint $table) {
                $table->dropColumn(['u_deleted']);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};