<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Create index only if it doesn't exist
        $result = DB::select("SHOW INDEX FROM react WHERE Key_name = 'react_react_user'");
        if (empty($result)) {
            DB::statement('create index react_react_user on react (react_user);');
        }

        // Drop and recreate index on user table
        try {
            DB::statement('drop index deleted on user;');
        } catch (\Exception $e) {
            // Index might not exist, continue
        }

        // Create new index only if it doesn't exist
        $result = DB::select("SHOW INDEX FROM user WHERE Key_name = 'deleted'");
        if (empty($result)) {
            DB::statement('create index deleted on user (u_site, u_deleted);');
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement('drop index react_react_user on react;');
        DB::statement('drop index deleted on user;');
        DB::statement('create index deleted on user (u_deleted);');
    }
};