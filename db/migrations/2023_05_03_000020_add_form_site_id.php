<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add form_site_id column only if it doesn't exist
        if (!Schema::hasColumn('form', 'form_site_id')) {
            Schema::table('form', static function(Blueprint $table) {
                $table->integer('form_site_id', false, true)->nullable(true);
            });
        }

        DB::table('form')
            ->whereNotExists(function($query) {
                $query->select(DB::raw(1))
                    ->from('form_site')
                    ->whereRaw('fs_form_id = form.form_id');
            })
            ->delete();

        DB::table('form')
            ->update(['form_site_id' => DB::raw('(select fs_site_id from form_site join site on site.site_id = form_site.fs_site_id where fs_form_id = form_id)')]);

        DB::table('form')
            ->whereNull('form_site_id')
            ->delete();

        Schema::table('form', static function(Blueprint $table) {
            $table->integer('form_site_id', false, true)->nullable(false)->change();
        });

        // Drop form_site column only if it exists
        if (Schema::hasColumn('form', 'form_site')) {
            Schema::dropColumns('form', ['form_site']);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

        Schema::dropColumns('form', ['form_site_id']);
    }
};