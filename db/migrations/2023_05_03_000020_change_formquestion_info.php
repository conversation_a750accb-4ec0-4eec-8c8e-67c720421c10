<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        Schema::table('formquestion', static function(Blueprint $table) {
            $table->text('fq_info')->nullable()->change();
            $table->text('fq_prefix')->nullable()->change();
            $table->text('fq_postfix')->nullable()->change();
            $table->text('fq_custom_params')->nullable()->change();
            $table->string('fq_displayname',255)->nullable()->change();
        });

        Schema::table('formquestionvalue', static function(Blueprint $table) {
            $table->text('fqv_custom_params')->nullable()->change();
            $table->text('fqv_displayvalue')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};