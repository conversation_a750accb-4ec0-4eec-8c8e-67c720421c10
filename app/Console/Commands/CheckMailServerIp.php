<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CheckMailServerIp extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:mail-server-ip';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check MAIL_SERVER_IP environment variable';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('=== Environment Variable Debug ===');
        
        // Check different ways to get the environment variable
        $envValue = env('MAIL_SERVER_IP');
        $getenvValue = getenv('MAIL_SERVER_IP');
        $serverValue = $_ENV['MAIL_SERVER_IP'] ?? 'NOT_SET';
        
        $this->info("env('MAIL_SERVER_IP'): " . ($envValue ?: 'NULL/EMPTY'));
        $this->info("getenv('MAIL_SERVER_IP'): " . ($getenvValue ?: 'NULL/EMPTY'));
        $this->info("\$_ENV['MAIL_SERVER_IP']: " . $serverValue);
        
        // Check if .env file exists and contains the variable
        $envFile = base_path('.env');
        $this->info("=== .env File Check ===");
        $this->info(".env file path: " . $envFile);
        $this->info(".env file exists: " . (file_exists($envFile) ? 'YES' : 'NO'));
        
        if (file_exists($envFile)) {
            $envContent = file_get_contents($envFile);
            $hasMailServerIp = strpos($envContent, 'MAIL_SERVER_IP') !== false;
            $this->info("Contains MAIL_SERVER_IP: " . ($hasMailServerIp ? 'YES' : 'NO'));
            
            if ($hasMailServerIp) {
                // Extract the line with MAIL_SERVER_IP
                $lines = explode("\n", $envContent);
                foreach ($lines as $line) {
                    if (strpos($line, 'MAIL_SERVER_IP') !== false) {
                        $this->info("Found line: " . trim($line));
                    }
                }
            }
        }
        
        // Test the actual function that's failing
        $this->info("=== Testing PosteDnsManager Logic ===");
        try {
            $mailServerIp = env('MAIL_SERVER_IP');
            
            if (empty($mailServerIp)) {
                $this->error('❌ MAIL_SERVER_IP is empty - this is the error condition');
                $this->info('💡 Possible solutions:');
                $this->info('1. Add MAIL_SERVER_IP=your.ip.address to .env file');
                $this->info('2. Clear config cache: php artisan config:clear');
                $this->info('3. Restart web server/php-fpm');
            } else {
                $this->info('✅ MAIL_SERVER_IP found: ' . $mailServerIp);
            }
        } catch (\Exception $e) {
            $this->error('Error testing: ' . $e->getMessage());
        }
        
        return 0;
    }
}
