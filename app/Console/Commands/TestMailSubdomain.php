<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Exolog\Module\Mail\PosteDnsManager;

class TestMailSubdomain extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:mail-subdomain {domain} {action=add}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test mail subdomain A record creation/removal';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $domain = $this->argument('domain');
        $action = $this->argument('action');

        $this->info("Testing mail subdomain for domain: {$domain}");
        $this->info("Action: {$action}");
        
        // Check configuration
        $configIp = config('mail.server_ip');
        $envIp = env('MAIL_SERVER_IP');
        $getenvIp = getenv('MAIL_SERVER_IP');

        $this->info("=== Configuration Check ===");
        $this->info("Config value: " . ($configIp ?: 'NULL'));
        $this->info("Env value: " . ($envIp ?: 'NULL'));
        $this->info("Getenv value: " . ($getenvIp ?: 'NULL'));

        if (empty($configIp)) {
            $this->error('MAIL_SERVER_IP not configured properly');
            $this->info('Please add MAIL_SERVER_IP=************** to your .env file');
            $this->info('Then run: php artisan config:clear');
            return 1;
        }

        $this->info("Expected A record: mail.{$domain} -> {$configIp}");

        try {
            $dnsManager = new PosteDnsManager();

            if ($action === 'add') {
                $this->info('Adding mail DNS records (including mail subdomain)...');
                $result = $dnsManager->addMailDnsRecords($domain);
                
                if ($result) {
                    $this->info('✅ Mail DNS records added successfully');
                    $this->info("✅ A record created: mail.{$domain} -> {$configIp}");
                    $this->info("✅ Webmail URL: https://mail.{$domain}/webmail/");
                } else {
                    $this->error('❌ Failed to add mail DNS records');
                }
            } elseif ($action === 'remove') {
                $this->info('Removing mail DNS records (including mail subdomain)...');
                $result = $dnsManager->removeMailDnsRecords($domain);
                
                if ($result) {
                    $this->info('✅ Mail DNS records removed successfully');
                    $this->info("✅ A record removed: mail.{$domain}");
                } else {
                    $this->error('❌ Failed to remove mail DNS records');
                }
            } else {
                $this->error('Invalid action. Use "add" or "remove"');
                return 1;
            }

        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
