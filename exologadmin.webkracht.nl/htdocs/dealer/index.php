<?php

error_reporting(E_ERROR);
ini_set("display_errors", "1");
define('LARAVEL_START', microtime(true));
define('APP_DEALER', true);

use Exolog\Module\Http\Kernel;


/*
|--------------------------------------------------------------------------
| Check If The Application Is Under Maintenance
|--------------------------------------------------------------------------
|
| If the application is in maintenance / demo mode via the "down" command
| we will load this file so that any pre-rendered content can be shown
| instead of starting the framework, which could cause an exception.
|
*/
if (file_exists($maintenance = __DIR__ . '/../../../storage/framework/maintenance.php')) {
    require $maintenance;
}

$app = require __DIR__ . '/../../../bootstrap/app.php';

$kernel = $app->make(Kernel::class);

$response = $kernel->handle(
    $request = Exolog\Module\Http\Request::capture()
)->send();

$kernel->terminate($request, $response);
