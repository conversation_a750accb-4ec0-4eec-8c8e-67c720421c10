#!/bin/bash

##########################################################################################
#                                                                                        #
#   Script to register new domain proxy in Caddy via API                                #
#   Special case: if target is "mailserver", proxy to /webmail/ on EXOLOG_MAIL_DOMAIN   #
#                                                                                        #
#   Author: Spider, 2025                                                                 #
#   Version: 2.2.0                                                                       #
#   Contact: <EMAIL>                                                        #
#                                                                                        #
##########################################################################################

########## EXIT CODES ##########
# 0 - Success
# 1 - Error
###############################

set -e
export LC_ALL=en_US;

########## PARAMETERS ##########
INPUT="$1"
DOMAIN="$2"
APP_DIR="/app"
CADDY_API="http://caddy:2019"
ENV_PATH="$APP_DIR/.env"

ALLOWED=("mailserver" "exolog" "dealer" "phpmyadmin" "filebrowser")

########## CHECK ARGUMENTS ##########
if [[ -z "$INPUT" || -z "$DOMAIN" ]]; then
  echo "Usage: $0 <target> <domain>"
  exit 1
fi

########## LOAD .env ##########
if [[ -f "$ENV_PATH" ]]; then
  export $(grep -v '^#' "$ENV_PATH" | xargs)
else
  echo "❌ .env file not found at $ENV_PATH"
  exit 1
fi

if [[ -z "$EXOLOG_MAIL_DOMAIN" ]]; then
  echo "❌ EXOLOG_MAIL_DOMAIN is not set in .env"
  exit 1
fi

########## CHECK jq ##########
if ! command -v jq &> /dev/null; then
  echo "⚠️ jq not found, attempting to download..."

  JQ_BIN_DIR="$APP_DIR"
  JQ_PATH="$JQ_BIN_DIR/jq"

  curl -sSL -o "$JQ_PATH" https://github.com/stedolan/jq/releases/latest/download/jq-linux64
  chmod +x "$JQ_PATH"

  if ! "$JQ_PATH" --version &> /dev/null; then
    echo "❌ Failed to install jq"
    exit 1
  else
    echo "✅ jq downloaded successfully"
  fi
else
  JQ_PATH=$(command -v jq)
fi

########## DETERMINE TARGET ##########
IS_ALLOWED=false
for val in "${ALLOWED[@]}"; do
  if [[ "$INPUT" == "$val" ]]; then
    IS_ALLOWED=true
    break
  fi
done

if $IS_ALLOWED && [[ "$INPUT" != "mailserver" ]]; then
  TARGET="${INPUT}:80"
else
  TARGET="exolog:80"
fi

########## FETCH EXISTING CONFIG ##########
curl -s "$CADDY_API/config/" > full_config.json

########## MODIFY CONFIG ##########
if [[ "$INPUT" == "mailserver" ]]; then
  echo "🔧 Adding special proxy config for mailserver..."
$JQ_PATH --arg domain "$DOMAIN" --arg backend "$EXOLOG_MAIL_DOMAIN" '
  .apps.http.servers.srv0.routes += [
    {
      "match": [{"host": [$domain]}],
      "handle": [
        {
          "handler": "subroute",
          "routes": [
            {
              "handle": [
                {
                  "handler": "reverse_proxy",
                  "upstreams": [{"dial": ($backend + ":443")}],
                  "transport": {
                    "protocol": "http",
                    "tls": {
                      "insecure_skip_verify": true
                    }
                  },
                  "headers": {
                    "request": {
                      "set": {
                        "Host": [$backend]
                      }
                    }
                  }
                }
              ]
            }
          ]
        }
      ]
    }
  ]
' full_config.json > new_config.json

else
  echo "🔧 Adding standard reverse proxy for $DOMAIN → $TARGET"
  $JQ_PATH --arg domain "$DOMAIN" --arg target "$TARGET" '
    .apps.http.servers.srv0.routes += [
      {
        "match": [{"host": [$domain]}],
        "handle": [
          {
            "handler": "reverse_proxy",
            "upstreams": [{"dial": $target}]
          }
        ]
      }
    ]
  ' full_config.json > new_config.json
fi

########## APPLY NEW CONFIG ##########
curl -s -X POST "$CADDY_API/load" \
  -H "Content-Type: application/json" \
  --data-binary @new_config.json

EXIT_CODE=$?

if [[ "$EXIT_CODE" == "0" ]]; then
  echo "✅ Domain $DOMAIN added successfully!"
else
  echo "❌ Failed to apply config. Exit code: $EXIT_CODE"
fi

########## CLEANUP (optional) ##########
# rm -f full_config.json new_config.json

exit $EXIT_CODE
