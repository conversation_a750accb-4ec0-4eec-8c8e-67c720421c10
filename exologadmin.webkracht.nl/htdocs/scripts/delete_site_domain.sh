#!/bin/bash

##########################################################################################
#                                                                                        #
#      Script that work with Caddy                                                       #
#      by Spider 2025                                                                    #
#      version 2.0.0                                                                     #
#      <EMAIL>                                                              #
#                                                                                        #
##########################################################################################

########   EXIT CODES #########
#  0 - OK                     #
#  1 - ERROR                  #
###############################

export LC_ALL=en_US;
########   Parameters on start  $host    ########################

DOMAIN="$2"
CADDY_API="http://caddy:2019"
if ! command -v jq &> /dev/null; then
  echo "jq not found trying to download ..."

  JQ_BIN_DIR="/app"
  JQ_PATH="$JQ_BIN_DIR/jq"

  curl -L -o "$JQ_PATH" https://github.com/stedolan/jq/releases/latest/download/jq-linux64

  chmod +x "$JQ_PATH"

  if ! "$JQ_PATH" --version &> /dev/null; then
    echo "Can't download jq 😢"
    exit 1
  else
    echo "jq Downloaded ✅"
  fi
else
  JQ_PATH=$(command -v jq)
fi

if [[ -z "$DOMAIN" ]]; then
  echo "Usage: $0 <domain>"
  exit 1
fi

# Getting current config
curl -s "$CADDY_API/config/" > full_config.json

# Delete rourte with selected domain
$JQ_PATH --arg domain "$DOMAIN" '
  .apps.http.servers.srv0.routes |= map(select(.match[0].host[0] != $domain))
' full_config.json > new_config.json

# Send new config
curl -s -X POST "$CADDY_API/load" \
  -H "Content-Type: application/json" \
  --data-binary @new_config.json
EXIT_CODE=$(echo $?)
echo "🗑️  Domain $DOMAIN deleted from proxy"
#rm full_config.json new_config.json
echo ${EXIT_CODE}