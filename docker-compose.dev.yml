services:
  # MySQL Database (ARM64 compatible)
  mysql:
    image: mysql:8.0
    container_name: exolog-mysql-dev
    restart: unless-stopped
    platform: linux/amd64
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: exolog
      MYSQL_USER: exolog
      MYSQL_PASSWORD: hvSK8JnIDF
      MYSQL_ALLOW_EMPTY_PASSWORD: "yes"
    command: --default-authentication-plugin=mysql_native_password --skip-ssl
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - exolog

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: exolog-redis-dev
    restart: unless-stopped
    command: ["redis-server", "--bind", "0.0.0.0"]
    ports:
      - "6379:6379"
    networks:
      - exolog

  # PHP Application with all extensions
  app:
    image: webdevops/php-nginx:8.1
    container_name: exolog-app-dev
    restart: unless-stopped
    platform: linux/amd64
    ports:
      - "8080:80"
    environment:
      - WEB_DOCUMENT_ROOT=/app/public
      - PHP_DISPLAY_ERRORS=1
      - PHP_MEMORY_LIMIT=512M
      - PHP_MAX_EXECUTION_TIME=300
      - PHP_POST_MAX_SIZE=100M
      - PHP_UPLOAD_MAX_FILESIZE=100M
    volumes:
      - .:/app
      - ./docker/nginx/app.conf:/opt/docker/etc/nginx/vhost.conf:ro
    depends_on:
      - mysql
      - redis
    networks:
      - exolog

  # Dealer Interface
  dealer:
    image: webdevops/php-nginx:8.1
    container_name: exolog-dealer-dev
    restart: unless-stopped
    platform: linux/amd64
    ports:
      - "8081:80"
    environment:
      - WEB_DOCUMENT_ROOT=/app/exologadmin.webkracht.nl/htdocs/dealer
      - PHP_DISPLAY_ERRORS=1
      - PHP_MEMORY_LIMIT=512M
      - PHP_MAX_EXECUTION_TIME=300
    volumes:
      - .:/app
      - ./docker/nginx/dealer.conf:/opt/docker/etc/nginx/vhost.conf:ro
    depends_on:
      - mysql
      - redis
    networks:
      - exolog

  # Adminer for database management
  adminer:
    image: adminer:latest
    container_name: exolog-adminer-dev
    restart: unless-stopped
    ports:
      - "8082:8080"
    environment:
      ADMINER_DEFAULT_SERVER: mysql
    depends_on:
      - mysql
    networks:
      - exolog

  # MailHog for email testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: exolog-mailhog-dev
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - exolog

volumes:
  mysql_data:

networks:
  exolog:
    driver: bridge
