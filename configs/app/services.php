<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'gitlab' => [
        'token' => env('GITLAB_PERSONAL_ACCESS_TOKEN')
    ],

    'openprovider' => [
        'username' => env('OPENPROVIDER_USERNAME'),
        'password' => env('OPENPROVIDER_PASSWORD'),
    ],

    'cloudflare' => [
        'token' => env('CLOUDFLARE_API_TOKEN'),
    ],

    'cyrus' => [
        'host' => env('CYRUS_HOST'),
        'port' => env('CYRUS_PORT'),
        'username' => env('CYRUS_USERNAME'),
        'password' => env('CYRUS_PASSWORD'),
    ],

    'poste' => [
        'url' => env('POSTE_API_URL'),
        'user' => env('POSTE_API_USER'),
        'password' => env('POSTE_API_PASSWORD'),
        'mail_domain' => env('EXOLOG_MAIL_DOMAIN', 'mail.dc2.exolog.tech'),
    ],

];
