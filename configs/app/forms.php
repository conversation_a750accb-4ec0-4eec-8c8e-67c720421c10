<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Frontend forms middleware
    |--------------------------------------------------------------------------
    |
    */
    'middleware' => [
        'request' => [
            '_default' => [
                \Exolog\Module\Forms\Middleware\RequestSiteFormHandler::class,
                \Exolog\Module\Forms\Middleware\ReCaptcha::class,
                \Exolog\Module\Forms\Middleware\ReactDateFields::class,
                \Exolog\Module\Forms\Middleware\CheckboxFields::class,
                \Exolog\Module\Forms\Middleware\UploadFiles::class,
                \Exolog\Module\Forms\Middleware\ConvertEmptyStringToNull::class,
            ]
        ],
        'react' => [
            '_default' => [
                \Exolog\Module\Forms\Middleware\ReactSiteFormHandler::class,
                \Exolog\Module\Forms\Middleware\UseremailField::class,
            ]
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Validation aliases
    |--------------------------------------------------------------------------
    |
    */
    'validation_rules' => [
        'unique_react' => \Exolog\Module\Forms\Validations\UniqueReact::class
    ],
];