<?php


return [
    'domains' => [
        //run outer handler for setup domain for subdomains
        'use_ssl_for_subdomain' => env('DEALER_USE_SSL_FOR_SUBDOMAIN', true),

        //Add DNS record for an acme challenge for domains in OpenProvider
        //this is not a real acme challenge just forward to CloudFare
        'setup_acme_challenge_le' => env('DEALER_SETUP_ACME_CHALLENGE_LE', false),

        /*  Invoke when domain update(insert)/delete from Dealer admin panel
        params:
            --path_ssl_cert                 -path to ssl certs ()
            --host_name                     -domain name
            --use_ssl [0|1]                 -use SSL for this site
            --use_le  [0|1]                 -use Let's Encrypt SSL
        */
        'handlers' => [
            'onDomainUpdate' => base_path('exologadmin.webkracht.nl/htdocs/scripts/update_site_domain.sh'),
            'onDomainDelete' => base_path('exologadmin.webkracht.nl/htdocs/scripts/delete_site_domain.sh'),
        ],
    ],
];
