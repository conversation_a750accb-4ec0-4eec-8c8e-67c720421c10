<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 10.07.2020
 * Time: 22:56
 */
$GLOBALS['ECO']['maintenance']['processes'] = [

    [
        'id' => 1,
        'title' => 'Restore parent container',
        'description' => 'Restore "react_parent_container". The back reference from react to container',
        'api' => '/maintenance/restoreParentContainer'
    ],
    [
        'id' => 2,
        'title' => 'Empty server cache',
        'description' => 'Empty server cache',
        'api' => '/maintenance/emptyCache'
    ],
    [
        'id' => 3,
        'title' => 'Build site skeleton',
        'description' => 'Creates or recreates system entities such as "Folder", "Page", "Form", "Group" etc.',
        'dialogComponent' => 'SkeletonConfirmDialog',
        'api' => '/maintenance/buildSkeleton'
    ],
    [
        'id' => 4,
        'title' => 'Generate sitemap',
        'description' => 'Schedule a task to generate a sitemaps and put it to cache.',
        'api' => '/maintenance/generateSitemap'
    ],
    [
        'id' => 5,
        'title' => 'Restore picture fields',
        'description' => 'The task of restoring broken image references in the "Picture" field and recalculate hash for all images.',
        'api' => '/maintenance/pictureRecovery'
    ]

];