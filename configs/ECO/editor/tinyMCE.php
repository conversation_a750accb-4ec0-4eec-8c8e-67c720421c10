<?php
$GLOBALS['ECO']['editor']['tinyMCE'] = [
    'toolbar' => [
        'buttons' => [
            // Core
            'bold' => 'Bold',
            'italic' => 'Italic',
            'underline' => 'Underline',
            'strikethrough' => 'Strikethrough',
            'alignleft' => 'Align left',
            'aligncenter' => 'Align center',
            'alignright' => 'Align right',
            'alignjustify' => 'Justify',
            'styleselect' => 'Formats',
            'formatselect' => 'Paragraph',
            'fontselect' => 'Font Family',
            'fontsizeselect' => 'Font Sizes',
            'cut' => 'Cut',
            'copy' => 'Copy',
            'paste' => 'Paste',
            'bullist' => 'Bulleted list',
            'numlist' => 'Numbered list',
            'outdent' => 'Decrease indent',
            'indent' => 'Increase indent',
            'blockquote' => 'Blockquote',
            'undo' => 'Undo',
            'redo' => 'Redo',
            'removeformat' => 'Clear formatting',
            'subscript' => 'Subscript',
            'superscript' => 'Superscript',

            // From plugins
            'hr' => 'Horizontal line',
            'link' => 'Insert/edit link',
            'unlink' => 'Remove link',
            'image' => 'Insert/edit image',
            'charmap' => 'Special character',
            'pastetext' => 'Paste as text',
            'print' => 'Print',
            'anchor' => 'Anchor',
            'searchreplace' => 'Find and replace',
            'visualblocks' => 'Show blocks',
            'visualchars' => 'Show invisible characters',
            'code' => 'Source code',
            'fullscreen' => 'Fullscreen',
            'insertdatetime' => 'Insert date/time',
            'media' => 'Insert/edit video',
            'nonbreaking' => 'Nonbreaking space',
            'table' => 'Table',
            'ltr' => 'Left to right',
            'rtl' => 'Right to left',
            'emoticons' => 'Emoticons',
            'forecolor' => 'Text color',
            'backcolor' => 'Background color',
            'responsivefilemanager' => 'Filemanager',
            //'followlink' => 'Follow link',
            //'editcssbutton' => 'Css editor',
            'table_of_contents' => 'Table of contents',
            'editorvar' => 'Template variables',
        ],
        'text_buttons' => [
            'styleselect',
            'formatselect',
            'fontselect',
            'fontsizeselect',
            'editorvar',
        ]
    ]
];