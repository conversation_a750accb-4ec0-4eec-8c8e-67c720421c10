<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 06.11.2019
 * Time: 14:47
 */

$GLOBALS['ECO']['forms']['fqt'] = [
    'section' => [
        'fqt_id' => 25,
        'fqt_name' => 'section',
        'fqt_displayname' => 'Section',
        'fqt_info' => 'Allow group fields to section',
        'icon' => 'field_section',
        'builder' => [
            'component' => 'fqtSection'
        ],
        'front' => [
            'reactForm' => [
                'component' => 'feSection'
            ]
        ],
        'react_fqt_wrapper' => \Exolog\Module\Forms\FQT\FQTDump::class,
        'FFQT' => \Exolog\Module\FF\FFQT\Dummy::class
    ],
    'text' => [
        'fqt_id' => 1,
        'fqt_name' => 'text',
        'fqt_displayname' => 'Text',
        'fqt_info' => '',
        'icon' => 'field_text',
        'front' => [
            'reactForm' => [
                'component' => 'feInput'
            ]
        ],
        'react_fqt_wrapper' => \Exolog\Module\Forms\FQT\Text\Text::class,
        'FFQT' => \Exolog\Module\FF\FFQT\Text::class

    ],
    'textarea' => [
        'fqt_id' => 2,
        'fqt_name' => 'textarea',
        'fqt_displayname' => 'Text Area',
        'fqt_info' => '',
        'icon' => 'field_textarea',
        'front' => [
            'reactForm' => [
                'component' => 'feTextarea'
            ]
        ],
        'react_fqt_wrapper' => \Exolog\Module\Forms\FQT\Text\Text::class,
        'FFQT' => \Exolog\Module\FF\FFQT\TextArea::class

    ],
    'radio' => [
        'fqt_id' => 4,
        'fqt_name' => 'radio',
        'fqt_displayname' => 'Radio Group',
        'fqt_info' => '',
        'icon' => 'field_radio_group',
        'builder' => [
            'component' => 'fqtMulti'
        ],
        'front' => [
            'reactForm' => [
                'component' => 'feRadio'
            ]
        ],
        'react_fqt_wrapper' => \Exolog\Module\Forms\FQT\FQTDump::class,
        'FFQT' => \Exolog\Module\FF\FFQT\Radio::class,

    ],
    'checkbox' => [
        'fqt_id' => 5,
        'fqt_name' => 'checkbox',
        'fqt_displayname' => 'Checkbox',
        'fqt_info' => '',
        'icon' => 'far fa-check-square',
        'front' => [
            'reactForm' => [
                'component' => 'feCheckbox'
            ]
        ],
        'react_fqt_wrapper' => \Exolog\Module\Forms\FQT\FQTDump::class,
        'FFQT' => \Exolog\Module\FF\FFQT\Checkbox::class,

    ],
    'select' => [
        'fqt_id' => 6,
        'fqt_name' => 'select',
        'fqt_displayname' => 'Select',
        'fqt_info' => '',
        'icon' => 'field_select',
        'builder' => [
            'component' => 'fqtMulti'
        ],
        'front' => [
            'reactForm' => [
                'component' => 'feSelect'
            ]
        ],
        'react_fqt_wrapper' => \Exolog\Module\Forms\FQT\FQTDump::class,
        'FFQT' => \Exolog\Module\FF\FFQT\Select::class,

    ],
    'file' => [
        'fqt_id' => 7,
        'fqt_name' => 'file',
        'fqt_displayname' => 'File Upload',
        'fqt_info' => '',
        'icon' => 'fas fa-upload',
        'front' => [
            'reactForm' => [
                'component' => 'feFile'
            ]
        ],
        'custom_params' => [
            'params' => [
                $GLOBALS['ECO']['forms']['custom_params']['upload_path'],
            ],
        ],
        'react_fqt_wrapper' => \Exolog\Module\Forms\FQT\FQTDump::class,
        'FFQT' => \Exolog\Module\FF\FFQT\File::class,
        'upload_path' => 'protected::uploads'


    ],
    'numeric' => [
        'fqt_id' => 16,
        'fqt_name' => 'numeric',
        'fqt_displayname' => 'Number',
        'fqt_info' => '',
        'icon' => 'field_number',
        'front' => [
            'reactForm' => [
                'component' => 'feInput'
            ]
        ],
        'react_fqt_wrapper' => \Exolog\Module\Forms\FQT\FQTDump::class,
        'FFQT' => \Exolog\Module\FF\FFQT\Number::class,

    ],
    'password' => [
        'fqt_id' => 17,
        'fqt_name' => 'password',
        'fqt_displayname' => 'Password',
        'fqt_info' => '',
        'icon' => 'fas fa-key',
        'front' => [
            'reactForm' => [
                'component' => 'feInput'
            ]
        ],
        'react_fqt_wrapper' => \Exolog\Module\Forms\FQT\FQTBase::class,
        'FFQT' => \Exolog\Module\FF\FFQT\Password::class,

    ],
    'useremail' => [
        'fqt_id' => 18,
        'fqt_name' => 'useremail',
        'fqt_displayname' => 'User',
        'fqt_info' => '',
        'icon' => 'far fa-user',
        'front' => [
            'reactForm' => [
                'component' => 'feInput'
            ]
        ],
        'react_fqt_wrapper' => \Exolog\Module\Forms\FQT\FQTBase::class,
        'FFQT' => \Exolog\Module\FF\FFQT\Useremail::class,

    ],
    'picture' => [
        'fqt_id' => 20,
        'fqt_name' => 'picture',
        'fqt_displayname' => 'Picture',
        'fqt_info' => '',
        'icon' => 'fas fa-image',
        'builder' => [

        ],
        'front' => [
            'reactForm' => [
                'component' => 'fePicture'
            ]
        ],
        'custom_params' => [
            'params' => [
                $GLOBALS['ECO']['forms']['custom_params']['renderParams'],
            ],
        ],
        'react_fqt_wrapper' => \Exolog\Module\Forms\FQT\Picture\Picture::class,
        'FFQT' => \Exolog\Module\FF\FFQT\Dummy::class
    ],
    'permalink' => [
        'fqt_id' => 26,
        'fqt_name' => 'permalink',
        'fqt_displayname' => 'Permalink',
        'fqt_info' => 'Allow assigning permalinks to react',
        'icon' => 'field_permalinks',
        'front' => [
            'reactForm' => [
                'component' => 'fePermalink'
            ]
        ],
        'react_fqt_wrapper' => \Exolog\Module\Forms\FQT\Permalink\FQTPermalink::class,
        'FFQT' => \Exolog\Module\FF\FFQT\Dummy::class,
        'singleton' => true
    ],
    'container' => [
        'fqt_id' => 21,
        'fqt_name' => 'container',
        'fqt_displayname' => 'Container',
        'fqt_info' => 'Allow create references to other reacts',
        'icon' => 'fas fa-boxes',
        'builder' => [
            'component' => 'fqtContainer'
        ],
        'front' => [
            'reactForm' => [
                'component' => 'feContainer'
            ]
        ],
        'custom_params' => [
            'params' => [

            ],
        ],
        'react_fqt_wrapper' => \Exolog\Module\Forms\FQT\Container\Container::class,
        'FFQT' => \Exolog\Module\FF\FFQT\Dummy::class
    ],
    'view' => [
        'fqt_id' => 22,
        'fqt_name' => 'view',
        'fqt_displayname' => 'View',
        'fqt_info' => 'Allow creating references to blade view',
        'icon' => 'far fa-file-code',
        'front' => [
            'reactForm' => [
                'component' => 'feView'
            ]
        ],
        'custom_params' => [
            'params' => [
            ],
        ],
        'react_fqt_wrapper' => \Exolog\Module\Forms\FQT\View\View::class,
        'FFQT' => \Exolog\Module\FF\FFQT\Dummy::class,
    ],
    'unit' => [
        'fqt_id' => 23,
        'fqt_name' => 'unit',
        'fqt_displayname' => 'Unit',
        'fqt_info' => 'Allows you to create references to blade component',
        'icon' => 'fas fa-cube',
        'front' => [
            'reactForm' => [
                'component' => 'feUnit'
            ]
        ],
        'custom_params' => [
            'params' => [
            ],
        ],
        'react_fqt_wrapper' => \Exolog\Module\Forms\FQT\Unit\Unit::class,
        'FFQT' => \Exolog\Module\FF\FFQT\Dummy::class,
    ],
    'mailable' => [
        'fqt_id' => 24,
        'fqt_name' => 'mailable',
        'fqt_displayname' => 'Mailable',
        'fqt_info' => 'Allows to send this react like mail',
        'icon' => 'far fa-envelope',
        'front' => [
            'reactForm' => [
                'component' => 'feMailable'
            ]
        ],
        'custom_params' => [
            'params' => [
            ],
        ],
        'react_fqt_wrapper' => \Exolog\Module\Forms\FQT\Mailable\Mailable::class,
        'FFQT' => \Exolog\Module\FF\FFQT\Dummy::class,
        'singleton' => true
    ],
    'captcha' => [
        'fqt_id' => 15,
        'fqt_name' => 'captcha',
        'fqt_displayname' => 'Captcha Field',
        'fqt_info' => '',
        'icon' => 'field_text',
        'builder' => [

        ],
        'front' => [
            'reactForm' => [
                'hidden' => true
            ]
        ],
        'custom_params' => [
            'params' => [
                $GLOBALS['ECO']['forms']['custom_params']['minScore'],
                $GLOBALS['ECO']['forms']['custom_params']['site_key'],
                $GLOBALS['ECO']['forms']['custom_params']['secret_key'],
            ],
        ],
        'react_fqt_wrapper' => \Exolog\Module\Forms\FQT\FQTBase::class,
        'FFQT' => \Exolog\Module\FF\FFQT\Captcha::class
    ],
    'hidden' => [
        'fqt_id' => 9,
        'fqt_name' => 'hidden',
        'fqt_displayname' => 'Hidden Input',
        'fqt_info' => '',
        'icon' => 'field_hidden',
        'front' => [
            'reactForm' => [
                'component' => 'feInput'
            ]
        ],
        'react_fqt_wrapper' => \Exolog\Module\Forms\FQT\FQTBase::class,
        'FFQT' => \Exolog\Module\FF\FFQT\Hidden::class,
    ],
    'date' => [
        'fqt_id' => 10,
        'fqt_name' => 'date',
        'fqt_displayname' => 'Date',
        'fqt_info' => '',
        'icon' => 'far fa-calendar-alt',
        'front' => [
            'reactForm' => [
                'component' => 'feDate'
            ],
            'exedit' => [
                'contenttype' => 'date'
            ]
        ],
        'store_format' => [
            'php' => 'Y-m-d',
            'flatpicker' => 'Y-m-d'
        ],
        'react_fqt_wrapper' => \Exolog\Module\Forms\FQT\Date\Date::class,
        'FFQT' => \Exolog\Module\FF\FFQT\Date::class,

    ],
    'datetime' => [
        'fqt_id' => 11,
        'fqt_name' => 'datetime',
        'fqt_displayname' => 'DateTime',
        'fqt_info' => '',
        'icon' => 'far fa-calendar-alt',
        'front' => [
            'reactForm' => [
                'component' => 'feDate'
            ],
            'exedit' => [
                'contenttype' => 'datetime'
            ]
        ],
        'store_format' => [
            'php' => 'Y-m-d H:i:s',
            'flatpicker' => 'Y-m-d H:i:S'
        ],
        'react_fqt_wrapper' => \Exolog\Module\Forms\FQT\Date\Date::class,
        'FFQT' => \Exolog\Module\FF\FFQT\DateTime::class,
    ],

    'json' => [
        'fqt_id' => 27,
        'fqt_name' => 'json',
        'fqt_displayname' => 'JSON',
        'fqt_info' => '',
        'icon' => 'json',
        'front' => [
            'reactForm' => [
                'component' => 'feJson'
            ],
            'exedit' => [
                'contenttype' => 'json'
            ]
        ],
        'react_fqt_wrapper' => \Exolog\Module\Forms\FQT\Json\Json::class,
        'FFQT' => \Exolog\Module\FF\FFQT\Dummy::class,
    ],


    /**
     * @deprecated
     */
    'template' => [
        'fqt_id' => 19,
        'fqt_name' => 'template',
        'fqt_displayname' => 'Template',
        'fqt_info' => '',
        'icon' => 'far fa-file-code',
        'builder' => [
            'hidden' => true
        ],
        'front' => [
            'reactForm' => [
                'component' => 'feText'
            ]
        ],
        'custom_params' => [
            'params' => [
                $GLOBALS['ECO']['forms']['custom_params']['group_name'],
                $GLOBALS['ECO']['forms']['custom_params']['group_id'],
                $GLOBALS['ECO']['forms']['custom_params']['type']
            ],
        ],
        'FFQT' => \Exolog\Module\FF\FFQT\Dummy::class,
    ],
    'plaintext' => [
        'fqt_id' => 3,
        'fqt_name' => 'plaintext',
        'fqt_displayname' => 'Paragraph',
        'fqt_info' => '',
        'icon' => 'fas fa-paragraph',
        'builder' => [
            'hidden' => true
        ],
        'front' => [
            'reactForm' => [
                'component' => 'fePlainText'
            ]
        ]
    ],
    'htmlarea' => [
        'fqt_id' => 13,
        'fqt_name' => 'htmlarea',
        'fqt_displayname' => 'Html area',
        'fqt_info' => '',
        'icon' => 'field_text',
        'builder' => [
            'hidden' => true
        ],
        'front' => [
            'reactForm' => [
                'component' => 'feHtml'
            ]
        ]
    ],
    'preset' => [
        'fqt_id' => 8,
        'fqt_name' => 'preset',
        'fqt_displayname' => 'Preset',
        'fqt_info' => '',
        'icon' => 'field_text',
        'builder' => [
            'hidden' => true
        ]
    ],
    'tinymce' => [
        'fqt_id' => 12,
        'fqt_name' => 'tinymce',
        'fqt_displayname' => 'TinyMCE Field',
        'fqt_info' => '',
        'icon' => 'field_textarea',
        'builder' => [
            'hidden' => true
        ]
    ],
    'tinymce2' => [
        'fqt_id' => 14,
        'fqt_name' => 'tinymce2',
        'fqt_displayname' => 'TinyMCE-2 Field',
        'fqt_info' => '',
        'icon' => 'field_text',
        'builder' => [
            'hidden' => true
        ]
    ],

];
