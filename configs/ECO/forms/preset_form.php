<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 06.11.2019
 * Time: 14:47
 */

$GLOBALS['ECO']['forms']['preset_form'] = [

    '_blank' => [
        'name' => '_blank',
        'form' => [
            'form_name' => 'New form',
            'form_mailsubject' => 'Reaction from {form_name}',
            'form_permalink_id' => null,
            'form_questions_tree' => [],
        ],
    ],

    'systemurl' => [
        'name' => 'systemurl',
        'form' => [
            'form_name' => 'systemurl',
            'form_mailsubject' => 'Reaction from {form_name}',
            'form_permalink_id' => null,
            'form_questions_tree' => [
                $GLOBALS['ECO']['forms']['preset_fq']['publish']['fq'],
                $GLOBALS['ECO']['forms']['preset_fq']['menu_title']['fq'],
                [
                    'fq_name' => 'type',
                    'fq_displayname' => 'type',
                    'fq_type' => $GLOBALS['ECO']['forms']['fqt']['select']['fqt_id'],
                    'fq_custom_params' => '',
                    'values' => [
                        [
                            'fqv_value' => 'URL',
                            'fqv_displayvalue' => 'URL',
                            'fqv_isdefault' => 1,
                            'fqv_custom_params' => '',
                        ],
                        [
                            'fqv_value' => 'REACT',
                            'fqv_displayvalue' => 'Reaction',
                            'fqv_isdefault' => 0,
                            'fqv_custom_params' => '',
                        ],
                        [
                            'fqv_value' => 'SEARCH',
                            'fqv_displayvalue' => 'Search',
                            'fqv_isdefault' => 0,
                            'fqv_custom_params' => '',
                        ],
                        [
                            'fqv_value' => 'FILE',
                            'fqv_displayvalue' => 'File manager',
                            'fqv_isdefault' => 0,
                            'fqv_custom_params' => '',
                        ],
                        [
                            'fqv_value' => 'ROUTE',
                            'fqv_displayvalue' => 'Route to any screen of exo_admin',
                            'fqv_isdefault' => 0,
                            'fqv_custom_params' => '',
                        ],
                    ]

                ],
                [
                    'fq_name' => 'url',
                    'fq_displayname' => 'url or value',
                    'fq_type' => $GLOBALS['ECO']['forms']['fqt']['text']['fqt_id'],
                    'fq_custom_params' => ''
                ],
                [
                    'fq_name' => 'params',
                    'fq_displayname' => 'params',
                    'fq_type' => $GLOBALS['ECO']['forms']['fqt']['textarea']['fqt_id'],
                    'fq_custom_params' => ''
                ],
                [
                    'fq_name' => 'target',
                    'fq_displayname' => 'target',
                    'fq_type' => $GLOBALS['ECO']['forms']['fqt']['select']['fqt_id'],
                    'fq_custom_params' => '',
                    'values' => [
                        [
                            'fqv_value' => '_top',
                            'fqv_displayvalue' => 'Top - opens the linked document in the full body of the window',
                            'fqv_isdefault' => 1,
                            'fqv_custom_params' => '',
                        ],
                        [
                            'fqv_value' => '_blank',
                            'fqv_displayvalue' => 'Blank - opens the linked document in a new window or tab',
                            'fqv_isdefault' => 0,
                            'fqv_custom_params' => '',
                        ],
                        [
                            'fqv_value' => '_modal',
                            'fqv_displayvalue' => 'Modal - opens the linked document in the modal window',
                            'fqv_isdefault' => 0,
                            'fqv_custom_params' => '',
                        ],
                    ]
                ],
                /* [
                    'fq_name' => 'icon',
                    'fq_displayname' => 'icon',
                    'fq_type' => $GLOBALS['ECO']['forms']['fqt']['text']['fqt_id'],
                    'fq_custom_params' => 'default="efa efa-star"',
                    'fq_info' => 'use Font Awesome 6 icon class with prefix "efa"'
                ],*/
            ],
        ],
    ],

    'page' => [
        'name' => 'page',
        'form' => [
            'form_name' => 'page',
            'form_mailsubject' => '{menu_title}',
            'form_permalink_id' => null,
            'form_questions_tree' => [
                [
                    'fq_name' => 'permalink',
                    'fq_displayname' => 'permalink',
                    'fq_type' => $GLOBALS['ECO']['forms']['fqt']['permalink']['fqt_id'],
                    'fq_custom_params' => ''
                ],
                $GLOBALS['ECO']['forms']['preset_fq']['publish']['fq'],
                $GLOBALS['ECO']['forms']['preset_fq']['no_sitemap']['fq'],
                $GLOBALS['ECO']['forms']['preset_fq']['menu_title']['fq'],
                $GLOBALS['ECO']['forms']['preset_fq']['alias']['fq'],
            ],
        ],
        'form_permalinks' => [
            [
                'fp_uri' => '/{alias}',
                'fp_sitemap_active' => false,
                'fp_history_active' => false,
                'fp_sitemap_name' => null,
                'fp_react_layout' => null
            ]
        ]
    ],

    'pageblock' => [
        'name' => 'pageblock',
        'form' => [
            'form_name' => 'pageblock',
            'form_mailsubject' => '{menu_title}',
            'form_permalink_id' => null,
            'form_questions_tree' => [
                $GLOBALS['ECO']['forms']['preset_fq']['publish']['fq'],
                $GLOBALS['ECO']['forms']['preset_fq']['menu_title']['fq'],
                [
                    'fq_name' => 'unit',
                    'fq_displayname' => 'unit',
                    'fq_type' => $GLOBALS['ECO']['forms']['fqt']['unit']['fqt_id'],
                    'fq_custom_params' => ''
                ],
            ],
        ],
    ],

    'URL' => [
        'name' => 'URL',
        'form' => [
            'form_name' => 'URL',
            'form_mailsubject' => '{menu_title}',
            'form_permalink_id' => null,
            'form_questions_tree' => [
                $GLOBALS['ECO']['forms']['preset_fq']['publish']['fq'],
                [
                    'fq_name' => 'no_sitemap',
                    'fq_displayname' => 'no_sitemap',
                    'fq_type' => $GLOBALS['ECO']['forms']['fqt']['checkbox']['fqt_id'],
                    'fq_custom_params' => 'default=1'
                ],
                $GLOBALS['ECO']['forms']['preset_fq']['menu_title']['fq'],
                [
                    'fq_name' => 'url',
                    'fq_displayname' => 'url',
                    'fq_type' => $GLOBALS['ECO']['forms']['fqt']['text']['fqt_id'],
                    'fq_custom_params' => ''
                ],
                [
                    'fq_name' => 'target',
                    'fq_displayname' => 'target',
                    'fq_type' => $GLOBALS['ECO']['forms']['fqt']['select']['fqt_id'],
                    'fq_custom_params' => '',
                    'values' => [
                        [
                            'fqv_value' => '_self',
                            'fqv_displayvalue' => 'Self - opens the linked document in the same frame as it was clicked',
                            'fqv_isdefault' => 1,
                            'fqv_custom_params' => '',
                        ],
                        [
                            'fqv_value' => '_blank',
                            'fqv_displayvalue' => 'Blank - opens the linked document in a new window or tab',
                            'fqv_isdefault' => 0,
                            'fqv_custom_params' => '',
                        ],
                    ]
                ],
                [
                    'fq_name' => 'permalink',
                    'fq_displayname' => 'permalink',
                    'fq_type' => $GLOBALS['ECO']['forms']['fqt']['permalink']['fqt_id'],
                    'fq_custom_params' => ''
                ],
            ],
        ],
    ],

    'mail' => [
        'name' => 'mail',
        'form' => [
            'form_name' => 'mail',
            'form_mailsubject' => '{menu_title}',
            'form_permalink_id' => null,
            'form_questions_tree' => [
                [
                    'fq_name' => 'mailable',
                    'fq_displayname' => 'mailable',
                    'fq_type' => $GLOBALS['ECO']['forms']['fqt']['mailable']['fqt_id'],
                    'fq_custom_params' => 'default={"view":"mail","subject":"ExologCMS"}'
                ],
                $GLOBALS['ECO']['forms']['preset_fq']['publish']['fq'],
                [
                    'fq_name' => 'permalink',
                    'fq_displayname' => 'permalink',
                    'fq_type' => $GLOBALS['ECO']['forms']['fqt']['permalink']['fqt_id'],
                    'fq_custom_params' => ''
                ],
                $GLOBALS['ECO']['forms']['preset_fq']['menu_title']['fq'],
                $GLOBALS['ECO']['forms']['preset_fq']['alias']['fq'],
                [
                    'fq_name' => 'blocks',
                    'fq_displayname' => 'blocks',
                    'fq_type' => $GLOBALS['ECO']['forms']['fqt']['container']['fqt_id'],
                    'fq_custom_params' => ''
                ],
            ],
        ],
    ],

];
