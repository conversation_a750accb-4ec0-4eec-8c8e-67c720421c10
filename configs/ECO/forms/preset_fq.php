<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 06.11.2019
 * Time: 14:47
 */

$GLOBALS['ECO']['forms']['preset_fq'] = [
    'publish' => [
        'name' => 'publish',
        'icon' => $GLOBALS['ECO']['forms']['fqt']['checkbox']['icon'],
        'fq' => [
            'fq_name' => 'publish',
            'fq_displayname' => 'publish',
            'fq_type' => $GLOBALS['ECO']['forms']['fqt']['checkbox']['fqt_id'],
            'fq_custom_params' => 'default=1']
    ],
    'no_sitemap' => [
        'name' => 'no_sitemap',
        'icon' => $GLOBALS['ECO']['forms']['fqt']['checkbox']['icon'],
        'fq' => [
            'fq_name' => 'no_sitemap',
            'fq_displayname' => 'no_sitemap',
            'fq_type' => $GLOBALS['ECO']['forms']['fqt']['checkbox']['fqt_id'],
            'fq_custom_params' => 'default=0']
    ],
    'meta_title' => [
        'name' => 'meta_title',
        'icon' => $GLOBALS['ECO']['forms']['fqt']['text']['icon'],
        'fq' => [
            'fq_name' => 'meta_title',
            'fq_displayname' => 'meta_title',
            'fq_type' => $GLOBALS['ECO']['forms']['fqt']['text']['fqt_id'],
            'fq_custom_params' => '']
    ],
    'title' => [
        'name' => 'title',
        'icon' => $GLOBALS['ECO']['forms']['fqt']['text']['icon'],
        'fq' => [
            'fq_name' => 'title',
            'fq_displayname' => 'title',
            'fq_type' => $GLOBALS['ECO']['forms']['fqt']['text']['fqt_id'],
            'fq_custom_params' => '']
    ],
    'menu_title' => [
        'name' => 'menu_title',
        'icon' => $GLOBALS['ECO']['forms']['fqt']['text']['icon'],
        'fq' => [
            'fq_name' => 'menu_title',
            'fq_displayname' => 'menu_title',
            'fq_type' => $GLOBALS['ECO']['forms']['fqt']['text']['fqt_id'],
            'fq_custom_params' => '']
    ],
    'alias' => [
        'name' => 'alias',
        'icon' => $GLOBALS['ECO']['forms']['fqt']['text']['icon'],
        'fq' => [
            'fq_name' => 'alias',
            'fq_displayname' => 'alias',
            'fq_type' => $GLOBALS['ECO']['forms']['fqt']['text']['fqt_id'],
            'fq_custom_params' => '']
    ],
];
