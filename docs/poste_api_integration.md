# Poste API Integration Documentation

## Overview

This document describes the Poste API integration implemented in the Exolog CMS system. The integration provides a modern replacement for the legacy Cyrus IMAP server, offering simplified mail management through the Poste API.

## Features

- **Domain Management**: Automated creation and deletion of mail domains
- **Mailbox Management**: Full CRUD operations for user mailboxes
- **DNS Integration**: Automatic management of MX, SPF, DMARC, and DKIM records
- **Migration Support**: Tools to migrate from existing Cyrus installations
- **Backward Compatibility**: Seamless fallback to legacy Cyrus system

## Configuration

### Environment Variables

Add the following variables to your `.env` file:

```env
POSTE_API_URL=https://mail.dc2.exolog.tech/admin/api/v1/
POSTE_API_USER=<EMAIL>
POSTE_API_PASSWORD=HS3jTSH9aJ
EXOLOG_MAIL_DOMAIN=mail.dc2.exolog.tech
```

### Service Configuration

The Poste API configuration is automatically loaded from `configs/app/services.php`:

```php
'poste' => [
    'url' => env('POSTE_API_URL'),
    'user' => env('POSTE_API_USER'),
    'password' => env('POSTE_API_PASSWORD'),
    'mail_domain' => env('EXOLOG_MAIL_DOMAIN'),
],
```

## Core Components

### PosteApiClient

Low-level HTTP client for communicating with the Poste API.

**Location**: `lib/Exolog/Module/Mail/PosteApiClient.php`

**Features**:
- HTTP Basic Authentication
- Comprehensive error handling
- Request/response logging
- Support for GET, POST, PUT, DELETE methods

### PosteApiService

Main service class providing high-level API operations.

**Location**: `lib/Exolog/Module/Mail/PosteApiService.php`

**Key Methods**:
- `addDomain($domain)` - Create a new mail domain
- `removeDomain($domain)` - Delete a mail domain
- `addMailbox($email, $password, $name, $options)` - Create a mailbox
- `removeMailbox($email)` - Delete a mailbox
- `updateMailboxPassword($email, $password)` - Update mailbox password
- `getDkimRecord($domain)` - Get DKIM configuration

### PosteMailboxService

Service class for mailbox management, replacing CyrusService.

**Location**: `lib/Exolog/Module/Mail/PosteMailboxService.php`

**Key Methods**:
- `createMailbox($email, $name, $password, $destinations, $options)` - Create mailbox
- `deleteMailbox($email)` - Delete mailbox
- `isExistMailbox($email)` - Check if mailbox exists
- `setPassword($email, $password)` - Update password
- `getMailboxInfo($email)` - Get mailbox details

### PosteDomainHandler

Domain handler replacing MailhostHandler for Poste API integration.

**Location**: `lib/Exolog/Module/Mail/PosteDomainHandler.php`

**Key Methods**:
- `saveHost($domain)` - Create/update domain
- `deleteHost($domain)` - Delete domain
- `hostExists($domain)` - Check if domain exists

### PosteDnsManager

DNS record management for mail domains.

**Location**: `lib/Exolog/Module/Mail/PosteDnsManager.php`

**Key Methods**:
- `addMailDnsRecords($domain)` - Add all mail DNS records
- `removeMailDnsRecords($domain)` - Remove all mail DNS records
- `updateDkimRecord($domain)` - Update DKIM record

## Integration Points

### Domain Observer

The `DomainMailhostObserver` has been updated to use Poste API when configured:

**Location**: `lib/Exolog/Module/Domains/Observer/DomainMailhostObserver.php`

**Behavior**:
- Automatically creates domains in Poste when mail is enabled
- Sets up DNS records for new mail domains
- Falls back to Cyrus if Poste is not configured

### Users Controller

The `UsersController::saveUserMailbox()` method supports both Poste and Cyrus:

**Location**: `lib/Exolog/Core/API/Controllers/UsersController.php`

**Behavior**:
- Uses Poste API when configured
- Maintains full backward compatibility with Cyrus
- Provides detailed error handling and logging

## Command Line Tools

### Domain DNS Management

Manage DNS records for mail domains:

```bash
# Add DNS records for a domain
php artisan domain:dns add example.com

# Remove DNS records for a domain
php artisan domain:dns remove example.com

# Update DKIM record for a domain
php artisan domain:dns update-dkim example.com

# Force operation without confirmation
php artisan domain:dns add example.com --force
```

### Migration from Cyrus

Migrate existing mailboxes from Cyrus to Poste:

```bash
# Migrate all domains (dry run)
php artisan mail:migrate-to-poste --dry-run

# Migrate specific domain
php artisan mail:migrate-to-poste --domain=example.com

# Force migration without confirmation
php artisan mail:migrate-to-poste --force

# Skip DNS record creation
php artisan mail:migrate-to-poste --skip-dns
```

## DNS Records

The integration automatically manages the following DNS records:

### MX Record
```
example.com. 900 IN MX 10 mail.dc2.exolog.tech.
```

### SPF Record
```
example.com. 900 IN TXT "v=spf1 a mx -all"
```

### DMARC Record
```
_dmarc.example.com. 900 IN TXT "v=DMARC1; p=none"
```

### DKIM Record
```
[selector]._domainkey.example.com. 900 IN TXT "v=DKIM1; k=rsa; p=[public_key]"
```

## Migration Process

### Pre-Migration Checklist

1. **Backup Data**: Ensure all mail data is backed up
2. **Test Environment**: Test the migration in a staging environment
3. **DNS TTL**: Lower DNS TTL values before migration
4. **User Communication**: Notify users of potential downtime

### Migration Steps

1. **Configure Poste API**: Set up environment variables
2. **Test Connection**: Verify API connectivity
3. **Run Dry Run**: Execute migration with `--dry-run` flag
4. **Migrate Domains**: Run actual migration
5. **Verify DNS**: Check DNS record propagation
6. **Test Mail Flow**: Verify mail delivery works
7. **Update Configuration**: Switch to Poste for new operations

### Post-Migration

1. **Monitor Logs**: Check for any errors or issues
2. **User Support**: Assist users with any mail client configuration
3. **Performance Monitoring**: Monitor system performance
4. **Cleanup**: Remove old Cyrus configurations when stable

## Error Handling

### API Errors

All API errors are logged with detailed context:

```php
Log::error('Poste API error', [
    'endpoint' => $endpoint,
    'method' => $method,
    'status_code' => $response->getStatusCode(),
    'response_body' => $response->getBody()->getContents()
]);
```

### Fallback Behavior

When Poste API is unavailable:
- System falls back to Cyrus operations
- Errors are logged but don't break functionality
- Users receive appropriate error messages

## Testing

### Unit Tests

Comprehensive unit tests are provided:

- `tests/Unit/Mail/PosteApiServiceTest.php`
- `tests/Unit/Mail/PosteMailboxServiceTest.php`

Run tests with:
```bash
php artisan test tests/Unit/Mail/
```

### Integration Testing

Test the integration manually:

1. **Create Domain**: Enable mail for a domain
2. **Create Mailbox**: Add a user mailbox
3. **Send Test Email**: Verify mail delivery
4. **Check DNS**: Verify DNS records are created
5. **Update Password**: Test password changes

## Troubleshooting

### Common Issues

**API Connection Failed**
- Check POSTE_API_URL configuration
- Verify network connectivity
- Check API credentials

**DNS Records Not Created**
- Verify DNS provider configuration
- Check domain ownership
- Review DNS provider API limits

**Mailbox Creation Failed**
- Check domain exists in Poste
- Verify mailbox doesn't already exist
- Review Poste server logs

### Logging

All operations are logged with appropriate levels:
- `INFO`: Successful operations
- `WARNING`: Non-critical issues
- `ERROR`: Failed operations

Check logs in `storage/logs/laravel.log` for detailed information.

## Security Considerations

### API Credentials

- Store API credentials securely in environment variables
- Use strong passwords for Poste admin account
- Regularly rotate API credentials

### Network Security

- Use HTTPS for all API communications
- Implement proper firewall rules
- Consider VPN for API access

### Access Control

- Limit API access to necessary IP addresses
- Use role-based access in Poste admin interface
- Monitor API usage for suspicious activity

## Performance Considerations

### API Rate Limiting

- Implement appropriate delays between API calls
- Use batch operations when available
- Monitor API response times

### DNS Propagation

- Allow time for DNS changes to propagate
- Use appropriate TTL values
- Monitor DNS resolution

### Monitoring

- Set up monitoring for API availability
- Track mail delivery metrics
- Monitor system resource usage
