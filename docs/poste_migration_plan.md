# Poste API Migration Plan

## Overview
This document outlines the plan to migrate from the current Cyrus IMAP server to Poste API for email management in the Exolog CMS system.

## Current System
- Cyrus IMAP server for mailbox management
- Custom database tables for mail domain and account storage
- Manual DNS record management

## Target System
- Poste API for complete mail management
- Automated DNS record management (MX, SPF, DMARC, DKIM)
- Simplified integration with Exolog CMS

## Migration Tasks

### Phase 1: Infrastructure Setup (Week 1)

1. **Environment Configuration**
   - Add Poste API credentials to `.env` file
   ```
   POSTE_API_URL=https://mail.dc2.exolog.tech/admin/api/v1/
   POSTE_API_USER=<EMAIL>
   POSTE_API_PASSWORD=HS3jTSH9aJ
   EXOLOG_MAIL_DOMAIN=mail.dc2.exolog.tech
   ```
   - Create configuration file for Poste API settings

2. **Create Poste API Client Service**
   - Implement `PosteApiService` class based on the sample client code
   - Add methods for all required API operations
   - Implement proper error handling and logging

### Phase 2: Core Implementation (Week 2-3)

3. **Domain Management**
   - Create `PosteDomainHandler` to replace `<PERSON>host<PERSON><PERSON>ler`
   - Implement domain creation/deletion through Poste API
   - Add DNS record management for mail domains

4. **Mailbox Management**
   - Create `PosteMailboxService` to replace `CyrusService`
   - Implement mailbox CRUD operations
   - Add password management functionality

5. **DNS Record Management**
   - Implement automatic DNS record creation/deletion
   - Support for MX, SPF, DMARC, and DKIM records
   - Fetch DKIM records from Poste API

### Phase 3: Integration (Week 4)

6. **Update Domain Observer**
   - Modify `DomainMailhostObserver` to use new Poste services
   - Ensure proper handling of domain mail enabling/disabling

7. **Update User Controller**
   - Modify `UsersController::saveUserMailbox()` to use Poste API
   - Update mailbox creation and management logic

8. **Create Migration Command**
   - Implement command to migrate existing mailboxes to Poste
   - Add data validation and error handling

### Phase 4: Testing & Deployment (Week 5)

9. **Testing**
   - Unit tests for all new services
   - Integration tests for the complete workflow
   - Manual testing of critical paths

10. **Documentation**
    - Update system documentation
    - Create admin guide for new email system
    - Document API endpoints and parameters

11. **Deployment**
    - Create database migration scripts
    - Prepare rollback plan
    - Schedule maintenance window

## Implementation Details

### PosteApiService

```php
<?php

namespace Exolog\Module\Mail;

use Illuminate\Support\Facades\Log;

class PosteApiService
{
    private $client;

    public function __construct()
    {
        $this->client = new Client(
            config('services.poste.url'),
            config('services.poste.user'),
            config('services.poste.password')
        );
    }

    public function addDomain(string $domainName)
    {
        return $this->client->post('domains', ['name' => $domainName]);
    }

    public function removeDomain(string $domainName)
    {
        return $this->client->delete("domains/$domainName");
    }

    public function addMailbox(string $email, string $password, string $name)
    {
        return $this->client->post('boxes', [
            'email' => $email,
            'passwordPlaintext' => $password,
            'name' => $name
        ]);
    }

    public function getDkimRecord(string $domainName)
    {
        return $this->client->get("domains/$domainName/dkim");
    }

    // Additional methods for other operations
}
```

### DNS Record Management

```php
<?php

namespace Exolog\Module\Domains\Commands;

use Exolog\Module\DNS\DnsProviderFactory;
use Exolog\Module\Domains\Model\Domain;
use Exolog\Module\Mail\PosteApiService;
use Illuminate\Console\Command;

class ManageDomainDnsRecords extends Command
{
    protected $signature = 'domain:dns {action} {domain}';
    protected $description = 'Add or remove DNS records for mail domains';

    private $posteService;

    public function __construct(PosteApiService $posteService)
    {
        parent::__construct();
        $this->posteService = $posteService;
    }

    public function handle()
    {
        $action = $this->argument('action');
        $domainName = $this->argument('domain');

        if (!in_array($action, ['add', 'remove'])) {
            $this->error('Action must be either "add" or "remove"');
            return 1;
        }

        $domain = Domain::where('domain_name', $domainName)->first();
        if (!$domain) {
            $this->error("Domain $domainName not found");
            return 1;
        }

        $dnsProvider = DnsProviderFactory::make($domainName);
        if (!$dnsProvider) {
            $this->error("No DNS provider found for $domainName");
            return 1;
        }

        if ($action === 'add') {
            $this->addDnsRecords($dnsProvider, $domainName);
        } else {
            $this->removeDnsRecords($dnsProvider, $domainName);
        }

        return 0;
    }

    private function addDnsRecords($dnsProvider, $domainName)
    {
        // Add MX record
        $dnsProvider->updateRecord([
            'name' => $domainName,
            'ttl' => '900',
            'type' => 'MX',
            'priority' => 10,
            'value' => config('services.poste.mail_domain')
        ]);

        // Add SPF record
        $dnsProvider->updateRecord([
            'name' => $domainName,
            'ttl' => '900',
            'type' => 'TXT',
            'value' => 'v=spf1 a mx -all'
        ]);

        // Add DMARC record
        $dnsProvider->updateRecord([
            'name' => "_dmarc.$domainName",
            'ttl' => '900',
            'type' => 'TXT',
            'value' => 'v=DMARC1; p=none'
        ]);

        // Add DKIM record
        $dkimInfo = $this->posteService->getDkimRecord($domainName);
        if ($dkimInfo && isset($dkimInfo->selector) && isset($dkimInfo->record)) {
            $dnsProvider->updateRecord([
                'name' => "{$dkimInfo->selector}._domainkey.$domainName",
                'ttl' => '900',
                'type' => 'TXT',
                'value' => $dkimInfo->record
            ]);
        }

        $this->info("Added DNS records for $domainName");
    }

    private function removeDnsRecords($dnsProvider, $domainName)
    {
        // Implementation for removing records
        // ...
    }
}
```

## Migration Risks and Mitigation

| Risk | Impact | Mitigation |
|------|--------|------------|
| Data loss during migration | High | Create full backups before migration, implement verification steps |
| Service interruption | Medium | Schedule migration during low-traffic period, prepare rollback plan |
| API compatibility issues | Medium | Thorough testing with Poste API before deployment |
| DNS propagation delays | Low | Inform users about potential delays, implement gradual migration |

## Success Criteria

1. All existing mail domains successfully migrated to Poste
2. All mailboxes accessible through new system
3. DNS records properly configured for all domains
4. No disruption to email service for end users
5. Admin interface fully functional with new API