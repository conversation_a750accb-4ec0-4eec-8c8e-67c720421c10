#!/bin/bash

# Local Development Setup (No Docker Required)
# This script sets up Exolog for local development using native tools

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🚀 Exolog Local Development Setup (No Docker)"
echo "=============================================="

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check PHP
    if ! command -v php &> /dev/null; then
        print_error "PHP is not installed. Please install PHP 7.4+ first."
        print_status "On macOS: brew install php"
        exit 1
    fi
    
    # Check Composer
    if ! command -v composer &> /dev/null; then
        print_error "Composer is not installed. Please install Composer first."
        print_status "Visit: https://getcomposer.org/download/"
        exit 1
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js first."
        print_status "On macOS: brew install node"
        exit 1
    fi
    
    # Check NPM
    if ! command -v npm &> /dev/null; then
        print_error "NPM is not installed. Please install NPM first."
        exit 1
    fi
    
    print_success "All prerequisites are installed"
    
    # Show versions
    echo ""
    print_status "Installed versions:"
    echo "  • PHP: $(php --version | head -n1)"
    echo "  • Composer: $(composer --version)"
    echo "  • Node.js: $(node --version)"
    echo "  • NPM: $(npm --version)"
    echo ""
}

# Setup environment
setup_environment() {
    print_status "Setting up environment..."
    
    if [ ! -f .env ]; then
        cp .env.local .env
        print_success "Environment file created from .env.local"
        
        # Update database settings for local development
        sed -i '' 's/DB_HOST=mysql/DB_HOST=127.0.0.1/' .env
        sed -i '' 's/REDIS_HOST=redis/REDIS_HOST=127.0.0.1/' .env
        sed -i '' 's/MAIL_HOST=mailhog/MAIL_HOST=127.0.0.1/' .env
        
        print_success "Environment configured for local development"
    else
        print_warning "Environment file already exists, skipping..."
    fi
}

# Install PHP dependencies
install_php_dependencies() {
    print_status "Installing PHP dependencies..."
    
    composer install --no-dev --optimize-autoloader
    
    print_success "PHP dependencies installed"
}

# Setup Laravel
setup_laravel() {
    print_status "Setting up Laravel application..."
    
    # Generate application key
    php artisan key:generate
    
    # Clear and cache configuration
    php artisan config:clear
    php artisan config:cache
    
    print_success "Laravel application configured"
}

# Install frontend dependencies
install_frontend_dependencies() {
    print_status "Installing frontend dependencies..."
    
    # Install dependencies for each frontend app
    print_status "Installing dealer frontend dependencies..."
    cd resources/js/dealer && npm install && cd ../../..
    
    print_status "Installing admin frontend dependencies..."
    cd resources/js/exo_admin && npm install && cd ../../..
    
    print_status "Installing editor frontend dependencies..."
    cd resources/js/exo_editor && npm install && cd ../../..
    
    print_success "Frontend dependencies installed"
}

# Build frontend applications
build_frontend() {
    print_status "Building frontend applications..."
    
    print_status "Building dealer frontend..."
    cd resources/js/dealer && npm run build && cd ../../..
    
    print_status "Building admin frontend..."
    cd resources/js/exo_admin && npm run build && cd ../../..
    
    print_status "Building editor frontend..."
    cd resources/js/exo_editor && npm run build && cd ../../..
    
    print_success "Frontend applications built"
}

# Setup database instructions
setup_database_instructions() {
    print_warning "Database Setup Required:"
    echo ""
    echo "You need to set up a MySQL database manually:"
    echo ""
    echo "1. Install MySQL:"
    echo "   • macOS: brew install mysql"
    echo "   • Start: brew services start mysql"
    echo ""
    echo "2. Create database:"
    echo "   mysql -u root -p"
    echo "   CREATE DATABASE exolog;"
    echo "   CREATE USER 'exolog'@'localhost' IDENTIFIED BY 'password';"
    echo "   GRANT ALL PRIVILEGES ON exolog.* TO 'exolog'@'localhost';"
    echo "   FLUSH PRIVILEGES;"
    echo "   EXIT;"
    echo ""
    echo "3. Run migrations:"
    echo "   php artisan migrate"
    echo ""
    echo "4. (Optional) Install Redis for caching:"
    echo "   • macOS: brew install redis"
    echo "   • Start: brew services start redis"
    echo ""
}

# Display final information
display_info() {
    echo ""
    print_success "🎉 Exolog local development setup completed!"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Set up your database (see instructions above)"
    echo "2. Run migrations: php artisan migrate"
    echo "3. Start the Laravel server: php artisan serve"
    echo "4. Start frontend development servers (optional):"
    echo "   • npm run serve:dealer"
    echo "   • npm run serve:exo_admin"
    echo "   • npm run serve:exo_editor"
    echo ""
    echo "🌐 Access URLs:"
    echo "  • Laravel App: http://localhost:8000"
    echo "  • Dealer Dev: http://localhost:3000 (if running dev server)"
    echo "  • Admin Dev: http://localhost:3001 (if running dev server)"
    echo "  • Editor Dev: http://localhost:3002 (if running dev server)"
    echo ""
    echo "🔧 Useful Commands:"
    echo "  • Start Laravel: php artisan serve"
    echo "  • Run migrations: php artisan migrate"
    echo "  • Clear cache: php artisan cache:clear"
    echo "  • Build frontend: npm run build:direct:dealer"
    echo "  • Watch frontend: npm run serve:dealer"
    echo ""
    echo "📁 Important Files:"
    echo "  • Environment: .env"
    echo "  • Laravel logs: storage/logs/"
    echo "  • Frontend apps: resources/js/"
    echo ""
    print_warning "Remember to set up your database before running migrations!"
}

# Main execution
main() {
    check_prerequisites
    setup_environment
    install_php_dependencies
    setup_laravel
    install_frontend_dependencies
    build_frontend
    setup_database_instructions
    display_info
}

# Handle script arguments
case "${1:-}" in
    "clean")
        print_status "Cleaning up environment..."
        rm -rf vendor/
        rm -rf resources/js/*/node_modules/
        rm -rf storage/logs/*.log
        php artisan cache:clear 2>/dev/null || true
        php artisan config:clear 2>/dev/null || true
        print_success "Environment cleaned"
        ;;
    "frontend")
        print_status "Starting all frontend development servers..."
        npm run serve:all
        ;;
    "serve")
        print_status "Starting Laravel development server..."
        php artisan serve
        ;;
    *)
        main
        ;;
esac
