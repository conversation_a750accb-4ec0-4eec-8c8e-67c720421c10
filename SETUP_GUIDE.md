# Exolog Project Setup Guide

This is a complex Laravel application with multiple Vue.js frontends, Docker containers, Caddy reverse proxy, and various services.

## Project Architecture

### Services Overview
- **Laravel Backend** (PHP 7.4)
- **Multiple Vue.js Frontends**:
  - `dealer` - Dealer management interface
  - `exo_admin` - Admin interface  
  - `exo_editor` - Content editor
- **Caddy** - Reverse proxy and SSL termination
- **MySQL 5.7** - Main database
- **Redis** - Caching and sessions
- **Poste.io** - Mail server
- **Plausible Analytics** - Analytics platform
- **GitLab Runner** - CI/CD

### Ports Configuration
- **80/443** - Caddy (HTTP/HTTPS)
- **81** - Dealer frontend
- **82** - Exolog frontend  
- **85** - Mail server web interface
- **3307** - MySQL
- **6379** - Redis
- **8882** - Plausible Analytics

## Prerequisites

1. **Docker & Docker Compose** installed
2. **Git** installed
3. **Node.js & NPM** (for local development)
4. **PHP 7.4** (for local development)
5. **Composer** (for local development)

## Quick Setup (Recommended)

### 1. Clone the Repository
```bash
<NAME_EMAIL>:fidela/exolog-reactJson.git
cd exolog-reactJson
```

### 2. Environment Configuration
```bash
# Copy environment file
cp .env.example .env

# Edit .env file with your configuration
nano .env
```

### 3. Required Environment Variables
Create a `.env.docker` file with these variables:
```env
# Database
MYSQL_ROOT_PASSWORD=your_secure_password
DB_DATABASE=exolog
DB_USERNAME=exolog_user
DB_PASSWORD=your_db_password

# Server Configuration
EXOLOG_SERVER_DOMAIN=localhost

# Plausible Analytics
PLAUSIBLE_BASE_URL=http://localhost:8882
PLAUSIBLE_SECRET_KEY_BASE=your_secret_key_base_64_chars
POSTGRES_PASSWORD=your_postgres_password
PLAUSIBLE_MAILER_EMAIL=admin@localhost

# SMTP Configuration
SMTP_HOST_ADDR=mailserver
SMTP_HOST_PORT=587
SMTP_USER_NAME=admin@localhost
SMTP_USER_PWD=your_smtp_password
SMTP_HOST_SSL_ENABLED=false

# Google OAuth (optional)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Security
TOTP_VAULT_KEY=your_totp_vault_key_32_chars
```

### 4. Directory Structure Setup
```bash
# Create required directories
sudo mkdir -p /home/<USER>/{app,mysql_data,mail,caddy_config,plausible,nginx,supervisor,gitlab-runner}
sudo mkdir -p /home/<USER>/plausible/{db,event-data,event-logs,clickhouse}

# Set permissions
sudo chown -R $USER:$USER /home/<USER>
```

### 5. Copy Project Files
```bash
# Copy project to docker directory
sudo cp -r . /home/<USER>/app/
sudo chown -R $USER:$USER /home/<USER>/app
```

## Manual Setup Steps

### 1. Database Setup
```bash
# Create database dump (if you have existing data)
# Place your database dump at: /home/<USER>/exolog_full.sql
```

### 2. Nginx Configuration
Create `/home/<USER>/nginx/dealer_vhost.conf`:
```nginx
server {
    listen 80;
    server_name _;
    root /app/exologadmin.webkracht.nl/htdocs/dealer;
    index index.php index.html;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

Create `/home/<USER>/nginx/vhost.conf`:
```nginx
server {
    listen 80;
    server_name _;
    root /app/homepages.webkracht.nl/htdocs;
    index index.php index.html;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

### 3. Supervisor Configuration
Create `/home/<USER>/supervisor/laravel-worker.conf`:
```ini
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /app/artisan queue:work --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=application
numprocs=8
redirect_stderr=true
stdout_logfile=/app/storage/logs/worker.log
stopwaitsecs=3600
```

### 4. Plausible Configuration Files
Create clickhouse configuration files in `/home/<USER>/plausible/clickhouse/`:

`logs.xml`:
```xml
<clickhouse>
    <logger>
        <level>warning</level>
        <console>true</console>
    </logger>
    <query_thread_log remove="remove"/>
    <query_log remove="remove"/>
    <text_log remove="remove"/>
    <trace_log remove="remove"/>
    <metric_log remove="remove"/>
    <asynchronous_metric_log remove="remove"/>
    <session_log remove="remove"/>
    <part_log remove="remove"/>
</clickhouse>
```

`ipv4-only.xml`:
```xml
<clickhouse>
    <listen_host>0.0.0.0</listen_host>
</clickhouse>
```

`low-resources.xml`:
```xml
<clickhouse>
    <max_server_memory_usage_to_ram_ratio>0.3</max_server_memory_usage_to_ram_ratio>
    <max_concurrent_queries>50</max_concurrent_queries>
    <background_pool_size>2</background_pool_size>
    <background_merges_mutations_concurrency_ratio>2</background_merges_mutations_concurrency_ratio>
</clickhouse>
```

## Running the Application

### 1. Start Services
```bash
# Navigate to docker directory
cd docker

# Start all services
docker-compose up -d

# Check status
docker-compose ps
```

### 2. Initialize Application
```bash
# Wait for MySQL to be ready, then run initialization
docker-compose exec composer-npm sh -c "
    composer install &&
    php artisan key:generate &&
    php artisan migrate --force &&
    php artisan exolog:optimize &&
    php artisan queue:restart
"
```

### 3. Build Frontend Applications
```bash
# Build all frontend apps
docker-compose exec composer-npm sh -c "
    cd resources/js/exo_admin && npm install && npm run deploy &&
    cd ../exo_editor && npm install && npm run deploy &&
    cd ../dealer && npm install && npm run deploy
"
```

### 4. Access Applications
- **Main Application**: http://localhost:82
- **Dealer Interface**: http://localhost:81
- **Mail Server**: http://localhost:85
- **Plausible Analytics**: http://localhost:8882

## Development Setup

### 1. Local Development Environment
```bash
# Install PHP dependencies
composer install

# Generate application key
php artisan key:generate

# Run migrations
php artisan migrate

# Install frontend dependencies
cd resources/js/dealer && npm install
cd ../exo_admin && npm install
cd ../exo_editor && npm install
```

### 2. Development Servers
```bash
# Start Laravel development server
php artisan serve --port=8000

# Start frontend development servers (in separate terminals)
npm run serve:dealer     # http://localhost:8080
npm run serve:exo_admin  # http://localhost:8081
npm run serve:exo_editor # http://localhost:8082

# Or start all frontends simultaneously
npm run serve:all
```

## Automated Setup Options

### Option 1: Quick Start (Recommended for Development)
```bash
# Simple local development setup
./quick-start.sh

# Other commands
./quick-start.sh clean    # Clean environment
./quick-start.sh stop     # Stop services
./quick-start.sh restart  # Restart services
./quick-start.sh logs     # View logs
```

### Option 2: Full Production Setup
```bash
# Complete production-ready setup
./setup.sh
```

### Option 3: Manual Docker Setup
```bash
# Use simplified Docker Compose for local development
docker-compose -f docker-compose.local.yml up -d

# Access applications
# Main: http://localhost:8082
# Dealer: http://localhost:8081
# Adminer: http://localhost:8080
# MailHog: http://localhost:8025
```

## Troubleshooting

### Common Issues

1. **Permission Issues**
```bash
sudo chown -R $USER:$USER /home/<USER>
sudo chmod -R 755 /home/<USER>
```

2. **Database Connection Issues**
```bash
# Check MySQL container
docker-compose logs mysql

# Reset database
docker-compose down
sudo rm -rf /home/<USER>/mysql_data
docker-compose up -d mysql
```

3. **Frontend Build Issues**
```bash
# Clear node modules and reinstall
cd resources/js/dealer
rm -rf node_modules package-lock.json
npm install
npm run build
```

4. **Laravel Issues**
```bash
# Clear Laravel caches
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
```

5. **Port Conflicts**
```bash
# Check what's using the ports
sudo lsof -i :8081
sudo lsof -i :8082

# Kill processes if needed
sudo kill -9 <PID>
```

### Logs
```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f dealer
docker-compose logs -f mysql
docker-compose logs -f caddy
```

## Development Workflow

### Frontend Development
```bash
# Start development servers for all frontends
npm run serve:all

# Or individually
npm run serve:dealer     # http://localhost:3000
npm run serve:exo_admin  # http://localhost:3001
npm run serve:exo_editor # http://localhost:3002

# Build for production
npm run build:direct:dealer
npm run build:direct:exo_admin
npm run build:direct:exo_editor
```

### Backend Development
```bash
# Laravel artisan commands
php artisan migrate
php artisan make:controller ExampleController
php artisan queue:work

# Or using Docker
docker-compose exec dealer php artisan migrate
```

### Database Management
- **Adminer**: http://localhost:8080
  - Server: mysql
  - Username: exolog
  - Password: password
  - Database: exolog

### Mail Testing
- **MailHog**: http://localhost:8025
  - Catches all outgoing emails
  - No configuration needed for testing

## Production Deployment

### 1. Environment Configuration
- Set `APP_ENV=production` in `.env`
- Set `APP_DEBUG=false`
- Configure proper database credentials
- Set up SSL certificates with Caddy

### 2. Security Considerations
- Change all default passwords
- Configure firewall rules
- Set up proper backup procedures
- Enable Laravel's security features

### 3. Performance Optimization
- Enable Redis caching
- Configure Laravel queues
- Optimize database queries
- Set up CDN for static assets

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Caddy Proxy   │    │   Frontend Apps │    │   Backend APIs  │
│   (Port 80/443) │────│   (Vue.js SPAs) │────│   (Laravel PHP) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │   Static Assets │              │
         │              │   (CSS/JS/IMG)  │              │
         │              └─────────────────┘              │
         │                                               │
         └─────────────────────┬─────────────────────────┘
                               │
                    ┌─────────────────┐
                    │   Data Layer    │
                    │                 │
                    │  ┌───────────┐  │
                    │  │   MySQL   │  │
                    │  └───────────┘  │
                    │  ┌───────────┐  │
                    │  │   Redis   │  │
                    │  └───────────┘  │
                    │  ┌───────────┐  │
                    │  │   Mail    │  │
                    │  └───────────┘  │
                    └─────────────────┘
```

## Useful Commands

```bash
# Restart all services
docker-compose restart

# Stop all services
docker-compose down

# View running containers
docker-compose ps

# Execute commands in containers
docker-compose exec dealer bash
docker-compose exec mysql mysql -u root -p

# Update application
git pull
docker-compose exec composer-npm composer install
docker-compose restart

# Clear all Docker data (CAUTION: This removes all data)
docker-compose down -v
docker system prune -a
```

## Support

For issues and questions:
1. Check the logs first: `docker-compose logs -f`
2. Verify environment configuration
3. Ensure all required directories exist
4. Check Docker container status: `docker-compose ps`
5. Review this setup guide
6. Check the project's GitLab issues
