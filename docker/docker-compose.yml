services:
  caddy:
    image: caddy:2
    container_name: caddy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /home/<USER>/docker/Caddyfile:/etc/caddy/Caddyfile
      - /home/<USER>/mail/certs:/data/caddy
      - /home/<USER>/caddy_config:/config
    environment:
      - ACME_AGREE=true
    command: ["/bin/sh", "-c", "test -f /config/caddy/autosave.json && caddy run --resume || caddy run --config /etc/caddy/Caddyfile --adapter caddyfile"]
    networks:
      - exolog

  mailserver:
    image: analogic/poste.io
    container_name: mailserver
    hostname: ${EXOLOG_SERVER_DOMAIN}
    environment:
      - TZ=Europe/Amsterdam
      - HTTPS=OFF
    ports:
      - "25:25"     # SMTP
      - "85:80"     # HTTP
      - "110:110"   # POP3
      - "143:143"   # IMAP
      - "465:465"   # SMTPS (Legacy)
      - "587:587"   # MSA
      - "993:993"   # IMAPS
      - "995:995"   # POP3S
      - "4190:4190" # Sieve
    volumes:
      - /home/<USER>/mail/data:/data
    networks:
      - exolog

  mysql:
    image: mysql:5.7
    container_name: mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_DATABASE}      
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    command: --default-authentication-plugin=mysql_native_password
    volumes:
      - /home/<USER>/mysql_data:/var/lib/mysql
      - /home/<USER>/exolog_full.sql:/docker-entrypoint-initdb.d/exolog_full.sql
    ports:
      - 3307:3306
      - 33070:33060
    networks:
      - exolog

  redis:
    image: redis:alpine
    container_name: redis
    restart: unless-stopped
    command: ["redis-server", "--bind", "0.0.0.0"]
    ports:
      - "6379:6379"
    networks:
      - exolog
  dealer:
    image: webdevops/php-nginx:7.4
    container_name: dealer
    restart: unless-stopped
    ports:
      - "81:80"
    environment:
      - WEB_DOCUMENT_ROOT=/app/exologadmin.webkracht.nl/htdocs/dealer
      - FPM_PM_MAX_CHILDREN=100
      - FPM_PM_START_SERVERS=10
      - FPM_PM_MAX_SPARE_SERVERS=20
      - SERVICE_SUPERVISOR_USER=root
    volumes:
      - /home/<USER>/app:/app
      - /home/<USER>/supervisor/laravel-worker.conf:/opt/docker/etc/supervisor.d/laravel-worker.conf:ro
      - /home/<USER>/nginx/dealer_vhost.conf:/opt/docker/etc/nginx/vhost.conf:ro
    networks:
      - exolog

  exolog:
    image: webdevops/php-nginx:7.4
    container_name: exolog
    restart: unless-stopped
    ports:
      - "82:80"
    environment:
      - WEB_DOCUMENT_ROOT=/app/homepages.webkracht.nl/htdocs
      - FPM_PM_MAX_CHILDREN=100
      - FPM_PM_START_SERVERS=10
      - FPM_PM_MAX_SPARE_SERVERS=20
      - SERVICE_SUPERVISOR_USER=root
    volumes:
      - /home/<USER>/app:/app
      - /home/<USER>/nginx/vhost.conf:/opt/docker/etc/nginx/vhost.conf:ro
    networks:
      - exolog

  gitlab-runner:
    image: gitlab/gitlab-runner:latest
    container_name: gitlab-runner
    restart: always
    volumes:
       - /var/run/docker.sock:/var/run/docker.sock
       - /home/<USER>/app:/app
       - /home/<USER>/gitlab-runner/config:/etc/gitlab-runner
    networks:
       - exolog

  composer-npm:
    build:
      context: .
      dockerfile: Dockerfile.build
    container_name: composer-npm
    volumes:
      - /home/<USER>/app:/app
    working_dir: /app
    command: >
      sh -c "php -m && composer update && composer install &&
             php artisan exolog:optimize &&
             php artisan migrate --force &&
             php artisan queue:restart && npm update && 
             cd resources/js/exo_admin/ && npm i && npm run deploy &&
             cd ../exo_editor/ && npm i && npm run deploy &&
             cd ../dealer/ && npm i && npm run deploy && git config --global --add safe.directory /app &&
             cd /app/ && REFNAME=$(git symbolic-ref --short HEAD || git rev-parse --short HEAD) && 
             SHORTSHA=$(git rev-parse --short HEAD) && echo "$REFNAME" > version.info && echo "$SHORTSHA" >> version.info"
    networks:
      - exolog
    depends_on:
      - mysql
      - redis

  plausible-postgres:
    image: postgres:16
    container_name: plausible-postgres
    restart: always
    environment:
      POSTGRES_DB: plausible
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - /home/<USER>/plausible/db:/var/lib/postgresql/data
    networks:
      - exolog

  plausible:
    image: ghcr.io/plausible/community-edition:v3.0.1
    container_name: plausible
    restart: always
    depends_on:
      - plausible-clickhouse
      - plausible-postgres
    environment:
      BASE_URL: ${PLAUSIBLE_BASE_URL}
      SECRET_KEY_BASE: ${PLAUSIBLE_SECRET_KEY_BASE}
      DATABASE_URL: ecto://postgres:${POSTGRES_PASSWORD}@plausible-postgres:5432/plausible
      CLICKHOUSE_DATABASE_URL: http://plausible-clickhouse:8123/plausible_events
      MAILER_EMAIL: ${PLAUSIBLE_MAILER_EMAIL}
      SMTP_HOST_ADDR: ${SMTP_HOST_ADDR}
      SMTP_HOST_PORT: ${SMTP_HOST_PORT}
      SMTP_USER_NAME: ${SMTP_USER_NAME}
      SMTP_USER_PWD: ${SMTP_USER_PWD}
      SMTP_HOST_SSL_ENABLED: ${SMTP_HOST_SSL_ENABLED}
      GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID}
      GOOGLE_CLIENT_SECRET: ${GOOGLE_CLIENT_SECRET}
      TOTP_VAULT_KEY: ${TOTP_VAULT_KEY}
    command: sh -c "/entrypoint.sh db createdb && /entrypoint.sh db migrate && /entrypoint.sh run"
    ports:
      - 8882:8000
    networks:
      - exolog

  plausible-clickhouse:
    image: clickhouse/clickhouse-server:24.12-alpine
    container_name: plausible-clickhouse
    restart: always
    volumes:
      - /home/<USER>/plausible/event-data:/var/lib/clickhouse
      - /home/<USER>/plausible/event-logs:/var/log/clickhouse-server
      - /home/<USER>/plausible/clickhouse/logs.xml:/etc/clickhouse-server/config.d/logs.xml:ro
      - /home/<USER>/plausible/clickhouse/ipv4-only.xml:/etc/clickhouse-server/config.d/ipv4-only.xml:ro
      - /home/<USER>/plausible/clickhouse/low-resources.xml:/etc/clickhouse-server/config.d/low-resources.xml:ro
    ulimits:
      nofile:
        soft: 262144
        hard: 262144
    environment:
      - CLICKHOUSE_SKIP_USER_SETUP=1
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 -O - http://127.0.0.1:8123/ping || exit 1"]
      start_period: 1m
    networks:
      - exolog

volumes:
  caddy_data:
  caddy_config:

networks:
  exolog:
    driver: bridge
