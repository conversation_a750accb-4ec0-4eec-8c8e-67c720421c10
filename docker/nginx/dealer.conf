server {
    listen 80;
    server_name _;
    root /app/exologadmin.webkracht.nl/htdocs/dealer;
    index index.php index.html;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    # Handle API routes (Laravel backend)
    location ~ ^/(api|redir|utils)/ {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # Handle Vue.js SPA routes
    location /app/ {
        alias /app/exologadmin.webkracht.nl/htdocs/dealer/app/;
        try_files $uri $uri/ /app/index.html;
    }

    # Root redirect to app
    location = / {
        return 301 /app/;
    }

    # Fallback for other routes
    location / {
        try_files $uri $uri/ /app/index.html;
    }

    # PHP-FPM configuration
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # Increase timeouts
        fastcgi_read_timeout 300;
        fastcgi_connect_timeout 300;
        fastcgi_send_timeout 300;
    }

    # Deny access to sensitive files
    location ~ /\. {
        deny all;
    }

    # Static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }
}
