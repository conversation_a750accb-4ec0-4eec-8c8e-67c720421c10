# Makefile for VPS
# Clone project to /home/<USER>/app
# Run services

REPO_URL = ********************:fidela/exolog-reactJson.git
APP_DIR = /home/<USER>/app
COMPOSE_DIR = $(APP_DIR)/docker
COMPOSE_FILE_NAME = docker-compose.yml
LOCAL_COMPOSE = ./$(COMPOSE_FILE_NAME)

.PHONY: all up down restart logs ps

all: clone up

clone:
	sudo -u docker_user ssh-keyscan -H gitlab.fidela.nl >> /home/<USER>/.ssh/known_hosts
    sudo -u docker_user	git clone $(REPO_URL) $(APP_DIR)
	cd $(APP_DIR)
    git config --global --add safe.directory $(APP_DIR)
    sudo -u docker_user git switch new_docker"
up:
	cd $(COMPOSE_DIR) && docker compose up -d

down:
	cd $(COMPOSE_DIR) && docker compose down

restart:
	cd $(COMPOSE_DIR) && docker compose restart

logs:
	cd $(COMPOSE_DIR) && docker compose logs -f

ps:
	cd $(COMPOSE_DIR) && docker compose ps
