FROM php:7.4.0-fpm-alpine                                                                                                                                               
ENV ALPINE_MIRROR "http://dl-cdn.alpinelinux.org/alpine"
RUN echo "${ALPINE_MIRROR}/v3.12/main/" >> /etc/apk/repositories
RUN apk add -U --no-cache nghttp2-dev nodejs npm --repository="http://dl-cdn.alpinelinux.org/alpine/v3.12/main/"
ADD https://raw.githubusercontent.com/mlocati/docker-php-extension-installer/master/install-php-extensions /usr/local/bin/
RUN apk --no-cache update \
    && apk --no-cache upgrade \
    && apk add --no-cache --virtual \
    .build-deps \
    $PHPIZE_DEPS \
    gcc \
    g++ \
    autoconf \
    automake \
    make \
    tar \
    && apk add --no-cache \
    gcc \
    g++ \
    jq \
    make \
    automake \
    libxslt-dev \
    libgcrypt-dev \
    tidyhtml-dev \
    net-snmp-dev \
    aspell-dev \
    freetds-dev \
    openldap-dev \
    gettext-dev \
    imap-dev \
    openssh \
    sudo \
    make \
    shadow \
    libmcrypt-dev \
    gmp-dev \
    openssl \
    mariadb-client \
    curl \
    git \
    freetype \
    libpng \
    libjpeg-turbo \
    freetype-dev \
    libpng-dev \
    libjpeg-turbo-dev \
    libwebp-dev \
    libzip-dev \
    bzip2-dev \
    bzip2 \
    postgresql-dev \
    supervisor \
    bash \
    nginx \
    pngquant \
    jpegoptim \
    zip \
    icu-dev \
    libxml2-dev \
    dcron \
    wget \
    rsync \
    unzip \
    zip \
    ca-certificates \
    oniguruma-dev \
    && phpModules=" \
    bcmath \
    bz2 \
    calendar \
    exif \
    gd \
    gettext \
    gmp \
    imap \
    intl \
    ldap \
    mysqli \
    pcntl \
    pdo_dblib \
    pdo_mysql \
    pdo_pgsql \
    pgsql \
    pspell \
    redis \
    shmop \
    snmp \
    soap \
    sockets \
    sysvmsg \
    sysvsem \
    sysvshm \
    tidy \
    zip \
    xsl \
    " \
    && NPROC=$(getconf _NPROCESSORS_ONLN) \
    && docker-php-ext-configure gd --enable-gd --with-freetype --with-jpeg --with-webp \
    && mkdir -p /usr/src/php/ext/igbinary  \
    && mkdir -p /usr/src/php/ext/redis  \
    && curl -fsSL https://pecl.php.net/get/igbinary | tar xvz -C "/usr/src/php/ext/igbinary" --strip 1 \
    && curl -fsSL https://pecl.php.net/get/redis | tar xvz -C "/usr/src/php/ext/redis" --strip 1 \
    && docker-php-ext-install -j${NPROC} $phpModules && docker-php-ext-install zip \
    && apk del --no-cache gcc g++ freetype-dev libpng-dev libjpeg-turbo-dev .build-deps \
    && chmod uga+x /usr/local/bin/install-php-extensions && sync && install-php-extensions imagick \
    && curl -s http://getcomposer.org/installer | php && mv composer.phar /usr/local/bin/composer

# Додатково
WORKDIR /app