<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 07.04.2020
 * Time: 17:02
 */

namespace Exolog\Dealer\SiteRemover;


use Exolog\Dealer\DealerException;
use Exolog\Module\Site\Model\Site;
use Exolog\Module\Support\Concerns\HasMakeFactory;
use Illuminate\Support\Facades\DB;

//TODO add logs to BufferLogger
class SiteRemoverService
{
    use HasMakeFactory;

    private DomainsRemover $domainsRemover;

    public function __construct(DomainsRemover $domainsRemover)
    {
        $this->domainsRemover = $domainsRemover;
    }

    /**
     * @throws DealerException
     */
    public function remove($site_id): void
    {
        $site = $this->getSiteRow($site_id);
        if (empty($site)) {
            throw new DealerException("Site isn't found!");
        }

        $this->cleanupDomains($site);
        $this->cleanupDB($site);
        $this->cleanupFiles($site);
    }

    private function cleanupDB($site): void
    {
        DB::statement(SqlStatements::$deleteChildEnities, [':site_id' => $site['site_id']]);
        DB::statement(SqlStatements::$deleteParentEntities, [':site_id' => $site['site_id']]);
        DB::statement(SqlStatements::$deleteSite, [':site_id' => $site['site_id']]);
    }


    /**
     * @throws DealerException
     */
    private function cleanupFiles($site): void
    {
        if (empty($site['site_alias'])) {
            throw new DealerException('Error! Not able to delete files!');
        }
        $path = sites_path($site['site_alias']);
        if (PHP_OS === 'Windows') {
            exec(sprintf("rd /s /q %s", escapeshellarg($path)));
        } else {
            exec(sprintf("rm -rf %s", escapeshellarg($path)));
        }
    }

    private function getSiteRow($site_id)
    {
        return Site::onlyTrashed()->findOrFail($site_id);
    }

    /**
     * @throws DealerException
     */
    private function cleanupDomains($site): void
    {
        $this->domainsRemover->cleanupDomains($site);
    }
}