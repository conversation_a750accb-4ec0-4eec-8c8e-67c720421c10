<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 27.04.2020
 * Time: 15:07
 */

namespace Exolog\Dealer\SiteRemover;


use Exolog\Dealer\DealerException;
use Exolog\Dealer\Handler\MailhostHandler;
use Exolog\Module\Database\Scopes\SiteScope;
use Exolog\Module\Domains\Model\Domain;
use Exolog\Module\Support\Facades\Path;
use Illuminate\Support\Facades\Artisan;

class DomainsRemover
{

    /**
     * @throws DealerException
     */
    public function cleanupDomains($site): void
    {

        $mailHostHandler = MailhostHandler::make(['site_id' => $site['site_id']]);

        $domains = $this->getDomains($site['site_id']);
        foreach ($domains as $domain) {

            if ($domain['domain_ismail']) {
                $mailHostHandler->deleteHost($domain['domain_name']);
            }

            $path = $this->getSslPath($domain);


            Artisan::queue('dealer:cleanup-domain', [
                    'cert_path' => $path,
                    'domain_name' => $domain['domain_name']
                ]
            );
        }
    }

    private function getDomains($site_id)
    {
        return Domain::query()
            ->withoutGlobalScope(SiteScope::class)
            ->where('domain_site', $site_id)
            ->get();
    }


    private function getSslPath($domain): string
    {
        return Path::getSiteConfPath() . '/ssl/' . $domain['domain_name'] . '/';
    }
}