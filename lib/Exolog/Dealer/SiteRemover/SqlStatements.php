<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 09.04.2020
 * Time: 13:02
 */

namespace Exolog\Dealer\SiteRemover;


class SqlStatements
{
    public static string $deleteSite = 'DELETE FROM site where site_id=:site_id;';

    public static string $deleteParentEntities = '
DELETE FROM domain WHERE domain_site=:site_id;
DELETE FROM domaingroup WHERE domaingroup_site=:site_id;
DELETE FROM edition WHERE e_site=:site_id;
DELETE FROM formquestion WHERE fq_site=:site_id;
DELETE FROM formquestionvalue WHERE fqv_site=:site_id;
DELETE FROM `groups` WHERE group_site=:site_id;
DELETE FROM paymentmethod WHERE pm_site=:site_id;
DELETE FROM read_newsletter WHERE rn_site=:site_id;
DELETE FROM user_setting WHERE us_site=:site_id;
DELETE FROM usergroup WHERE ug_site=:site_id;
DELETE FROM user WHERE u_site=:site_id;
DELETE FROM var WHERE var_site=:site_id;
DELETE FROM site_site where sisi_child=:site_id;
DELETE FROM site_site where sisi_parent=:site_id;
';

    public static string $deleteChildEnities = '
delete from user_usergroup where uug_user in (select d.u_id from user d where d.u_site=:site_id);
DELETE FROM react WHERE react_form in (select form_id from form where form_site_id =:site_id);
DELETE FROM form WHERE form_site_id = :site_id;
';

}