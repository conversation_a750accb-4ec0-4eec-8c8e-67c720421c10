<?php


namespace Exolog\Dealer\Routing\Middleware;


use Closure;
use Exolog\Dealer\Handler\AuthHandler;
use Exolog\Module\API\Api;
use Exolog\Module\Support\Facades\Site;
use Illuminate\Support\Facades\DB;

class AuthenticateSiteOwner
{
    /**
     * @var Api
     */
    private Api $api;
    private AuthHandler $authHandler;

    public function __construct(Api $api, AuthHandler $authHandler)
    {
        $this->api = $api;
        $this->authHandler = $authHandler;
    }

    public function handle($request, Closure $next)
    {
        if (!($this->authHandler->isSessionSuper() ||
            $this->isUserExists())
        ) {
            //todo code
            return $this->api->errorMsg('You are not authorized!');
        }

        return $next($request);
    }

    private function isUserExists()
    {
        $email = $this->authHandler->getSessionAdmin()['a_email'];

        return DB::table('user as u')
            ->join('user_usergroup as ug', 'u.u_id', '=', 'ug.uug_user')
            ->join('usergroup as g', 'ug.uug_usergroup', '=', 'g.ug_id')
            ->where('u.u_site', '=', Site::id())
            ->where('g.ug_isdeveloper', '=', 1)
            ->where('u.u_email', '=', $email)
            ->select('u.u_id')
            ->exists();
    }
}