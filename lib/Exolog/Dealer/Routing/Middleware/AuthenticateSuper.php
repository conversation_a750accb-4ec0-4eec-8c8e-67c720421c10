<?php


namespace Exolog\Dealer\Routing\Middleware;


use Closure;
use Exolog\Dealer\Handler\AuthHandler;
use Exolog\Module\API\Api;
use Exolog\Module\Http\Request;

class AuthenticateSuper
{
    /**
     * @var Api
     */
    private Api $api;
    private AuthHandler $authHandler;

    public function __construct(Api $api, AuthHandler $authHandler)
    {
        $this->api = $api;
        $this->authHandler = $authHandler;
    }


    /**
     * Handle an incoming request.
     *
     */
    public function handle(Request $request, Closure $next)
    {
        if (!$this->authHandler->isSessionSuper()) {
            return $this->api->errorMsg(['errorAlias' => 'auth']);
        }
        return $next($request);
    }
}
