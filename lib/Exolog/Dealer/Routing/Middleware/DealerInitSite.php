<?php


namespace Exolog\Dealer\Routing\Middleware;


use Closure;
use Exolog\Module\Http\Request;

class DealerInitSite
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param Closure $next
     * @param string[] ...$guards
     * @return mixed
     *
     */
    public function handle(Request $request, Closure $next)
    {

        $site_id = $request['site_id'];

        if (empty($site_id)) {
            $site = $request['site'];
            if (!empty($site) && array_key_exists('site_id', $site)) {
                $site_id = $site['site_id'];
            }
        }

        if (!empty($site_id)) {
            dealer()->setSiteId($site_id);
        }

        return $next($request);
    }
}
