<?php


namespace Exolog\Dealer\Routing\Middleware;


use Closure;
use Exolog\Module\API\Api;

class DealerWrapResult
{
    private Api $api;

    public function __construct(Api $api)
    {
        $this->api = $api;
    }

    public function handle($request, Closure $next)
    {
        $response = $next($request);

        /*if ($response instanceof JsonResponse) {
            $this->api->logMsgPush(BuffLog::getRecords());
            $response->setData(
                array_merge($this->api->result, $response->getData(true))
            );
        }*/
        return $response;
    }
}