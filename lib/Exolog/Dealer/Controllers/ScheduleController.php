<?php

namespace Exolog\Dealer\Controllers;


use Exolog\Dealer\ApiController;
use Exolog\Module\Schedule\Http\Requests\ScheduleRequest;
use Exolog\Module\Schedule\Http\Services\CommandService;
use Exolog\Module\Schedule\Model\Schedule;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class ScheduleController extends ApiController
{
    /**
     * Display a listing of the schedule.
     *
     */
    public function all(Request $request)
    {
        $schedules = Schedule::query();
        $schedules = $schedules->vueTablePaginate();
        return $this->api->dataMsg($schedules);
    }

    /**
     * Display the schedule.
     */
    public function get()
    {
        $schedule = Schedule::findOrFail(request('id'));
        $schedule->load([
            'histories' => function ($query) {
                $query->latest();
            }
        ]);
        return $this->api->dataMsg($schedule);
    }

    /**
     * Update the schedule
     */
    public function update(ScheduleRequest $request)
    {

        if ($request['id']) {
            $schedule = Schedule::findOrFail($request['id']);
            $schedule->update($request->all());
            return $this->api->dataMsg(true);
        }
        $schedule = Schedule::create($request->all());
        return $this->api->dataMsg($schedule);
    }

    /**
     * Remove the schedule
     */
    public function delete(\Exolog\Module\Http\Request $request)
    {

        $schedule_ids = Arr::wrap($request['id']);
        foreach ($schedule_ids as $schedule_id) {
            $schedule = Schedule::findOrFail($schedule_id);
            $schedule->delete();
        }
        return $this->api->dataMsg(true);
    }

    public function inactivate(\Exolog\Module\Http\Request $request)
    {

        $schedule_ids = Arr::wrap($request['id']);
        foreach ($schedule_ids as $schedule_id) {
            $schedule = Schedule::findOrFail($schedule_id);
            $schedule->status = Schedule::STATUS_INACTIVE;
            $schedule->save();
        }
        return $this->api->dataMsg(true);
    }

    public function activate(\Exolog\Module\Http\Request $request)
    {

        $schedule_ids = Arr::wrap($request['id']);
        foreach ($schedule_ids as $schedule_id) {
            $schedule = Schedule::findOrFail($schedule_id);
            $schedule->status = Schedule::STATUS_ACTIVE;
            $schedule->save();
        }
        return $this->api->dataMsg(true);
    }

    public function commands(CommandService $commandService)
    {
        return $this->api->dataMsg($commandService->get());
    }
}