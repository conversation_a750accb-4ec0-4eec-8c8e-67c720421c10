<?php

namespace Exolog\Dealer\Controllers;

use Exolog\Dealer\ApiController;
use Exolog\Dealer\DealerException;
use Exolog\Dealer\Handler\DomainHandler;
use Exolog\Module\API\Api;
use Exolog\Module\Domains\DomainSslStatus;
use Exolog\Module\Domains\Model\Domain;
use Exolog\Module\DNS\DnsProviderFactory;
use Exolog\Module\DNS\CloudflarePageRules;
use Exolog\Module\DNS\CloudflareDnsSetup;
use Exolog\Module\Editions\Model\Edition;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;


class DomainController extends ApiController
{

    public function resetSllStatus(DomainHandler $domainHandler): Api
    {
        $domain_id = request('domain_id');
        /** @var Domain $domain */
        $domain = Domain::crossSiteQuery()->findOrFail($domain_id);
        coreInit()->resolveSite($domain->domain_site);
        $domainHandler->setDomainSslStatus($domain_id, DomainSslStatus::DOMAIN_SSL_STATUS_OK);

        Log::info('SSL status manually reset', [
            'domain_id' => $domain_id,
            'domain_name' => $domain->domain_name,
            'previous_status' => 'PROCESSING',
            'new_status' => 'OK'
        ]);

        return $this->api->dataMsg(['status' => $domainHandler->getDomainSslStatus($domain_id)]);
    }

    /**
     * Reset all domains stuck in PROCESSING state
     */
    public function resetStuckDomains(DomainHandler $domainHandler): Api
    {
        try {
            $stuckDomains = Domain::crossSiteQuery()
                ->where('domain_ssl_status', DomainSslStatus::DOMAIN_SSL_STATUS_PROCESSING)
                ->get();

            $resetCount = 0;
            foreach ($stuckDomains as $domain) {
                $domainHandler->setDomainSslStatus($domain->domain_id, DomainSslStatus::DOMAIN_SSL_STATUS_OK);
                $resetCount++;

                Log::info('Stuck domain reset', [
                    'domain_id' => $domain->domain_id,
                    'domain_name' => $domain->domain_name
                ]);
            }

            return $this->api->dataMsg([
                'message' => "Reset {$resetCount} stuck domains",
                'reset_count' => $resetCount
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to reset stuck domains', [
                'error' => $e->getMessage()
            ]);

            return $this->api->errorMsg([
                'message' => 'Failed to reset stuck domains: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * @throws DealerException
     */
    public function addAliasDomain(DomainHandler $domainHandler)
    {
        $domainHandler->addAliasDomain();
        return $this->api->dataMsg(['message' => 'Domain with alias added.']);
    }

    public function fixAliasDomain(DomainHandler $domainHandler)
    {
        $domainHandler->fixAliasDomain();
        return $this->api->dataMsg(['message' => 'Domains fixed.']);
    }


    public function forceSSL(DomainHandler $domainHandler)
    {
        $domain_id = request('domain_id');
        $domainHandler->obtainSSL($domain_id, 1);
        return $this->api;
    }

    /**
     * @throws DealerException
     */
    public function addIdentitySES()
    {

        $domain_id = request('domain_id');

        $domain = Domain::findOrFail($domain_id);
        $domain->addIdentitySES();
        return $this->api->dataMsg(['message' => 'Domain added to AWS SES.']);
    }

    /**
     * @throws DealerException
     */
    public function checkIdentitySES()
    {
        $domain_id = request('domain_id');

        $domain = Domain::findOrFail($domain_id);
        return $this->api->dataMsg($domain->checkIdentitySES());
    }

    /**
     * Check if domain already exists in database globally
     */
    public function checkDomainExists()
    {
        $domain_name = request('domain_name');

        if (empty($domain_name)) {
            return $this->api->dataMsg([
                'exists' => false,
                'message' => 'Domain name is required'
            ]);
        }

        // Clean domain name (remove www prefix if present)
        $targetDomain = strtolower(trim($domain_name));
        if (str_starts_with($targetDomain, 'www.')) {
            $targetDomain = substr($targetDomain, 4);
        }

        // Check if domain exists globally across all sites
        $exists = Domain::crossSiteQuery()->where('domain_name', $targetDomain)->exists();

        return $this->api->dataMsg([
            'exists' => $exists,
            'domain_name' => $targetDomain,
            'message' => $exists ? 'Domain already exists in the system' : 'Domain is available'
        ]);
    }

    /**
     * Check if domain exists in DNS provider (Cloudflare)
     */
    public function checkDnsProvider()
    {
        $domain_name = request('domain_name');

        if (empty($domain_name)) {
            return $this->api->dataMsg([
                'found_in_cloudflare' => false,
                'parent_in_cloudflare' => false,
                'message' => 'Domain name is required'
            ]);
        }

        try {
            $targetDomain = $domain_name;
            $parentDomain = $this->getParentDomain($targetDomain);

            // Check if the exact domain exists in Cloudflare
            $dnsProvider = DnsProviderFactory::make($targetDomain);
            if ($dnsProvider && $dnsProvider->exists()) {
                // If target domain is found, check if parent is different and also exists
                $parentInCloudflare = false;
                if ($targetDomain !== $parentDomain) {
                    $parentDnsProvider = DnsProviderFactory::make($parentDomain);
                    $parentInCloudflare = $parentDnsProvider && $parentDnsProvider->exists();
                } else {
                    // If target domain IS the parent domain, then parent is also in Cloudflare
                    $parentInCloudflare = true;
                }

                // Check if WWW record already exists
                $wwwRecordExists = $this->checkWwwRecordExists($dnsProvider, $targetDomain);

                return $this->api->dataMsg([
                    'found_in_cloudflare' => true,
                    'parent_in_cloudflare' => $parentInCloudflare,
                    'parent_domain' => $parentDomain,
                    'www_record_exists' => $wwwRecordExists,
                    'message' => 'Domain found in Cloudflare'
                ]);
            }

            // Check if parent domain exists in Cloudflare (for subdomains)
            if ($targetDomain !== $parentDomain) {
                $parentDnsProvider = DnsProviderFactory::make($parentDomain);
                if ($parentDnsProvider && $parentDnsProvider->exists()) {
                    return $this->api->dataMsg([
                        'found_in_cloudflare' => false,
                        'parent_in_cloudflare' => true,
                        'parent_domain' => $parentDomain,
                        'message' => 'Parent domain found in Cloudflare'
                    ]);
                }
            }

            return $this->api->dataMsg([
                'found_in_cloudflare' => false,
                'parent_in_cloudflare' => false,
                'parent_domain' => $parentDomain,
                'message' => 'Domain not found in Cloudflare'
            ]);

        } catch (\Exception $e) {
            Log::error('DNS provider check failed', [
                'domain' => $domain_name,
                'error' => $e->getMessage()
            ]);

            return $this->api->dataMsg([
                'found_in_cloudflare' => false,
                'parent_in_cloudflare' => false,
                'message' => 'Failed to check DNS provider: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Check if WWW A record exists for the domain
     * @param $dnsProvider
     * @param string $domainName
     * @return bool
     */
    private function checkWwwRecordExists($dnsProvider, string $domainName): bool
    {
        try {
            // First check for Page Rules (preferred method)
            try {
                $pageRulesService = new CloudflarePageRules($domainName);
                if ($pageRulesService->wwwPageRuleExists()) {
                    Log::info('Found existing WWW Page Rule', ['domain' => $domainName]);
                    return true;
                }
            } catch (\Exception $pageRuleException) {
                Log::warning('Could not check Page Rules, falling back to DNS records', [
                    'domain' => $domainName,
                    'error' => $pageRuleException->getMessage()
                ]);
            }

            // Fallback: Check for existing WWW DNS A records
            $records = $dnsProvider->getRecords();

            $wwwARecords = array_filter($records, function($record) use ($domainName) {
                return $record['type'] === 'A' &&
                       (strtolower($record['name']) === 'www.' . strtolower($domainName) ||
                        strtolower($record['name']) === 'www');
            });

            if (!empty($wwwARecords)) {
                Log::info('Found existing WWW DNS A record', ['domain' => $domainName]);
                return true;
            }

            return false;

        } catch (\Exception $e) {
            Log::warning('Failed to check WWW record existence', [
                'domain' => $domainName,
                'error' => $e->getMessage()
            ]);
            // If we can't check, assume it doesn't exist (safer to show checkbox)
            return false;
        }
    }

    /**
     * Check domain ownership/management capability
     */
    public function checkOwnership()
    {
        $domain_name = request('domain_name');

        if (empty($domain_name)) {
            return $this->api->dataMsg([
                'can_manage' => false,
                'message' => 'Domain name is required'
            ]);
        }

        try {
            $targetDomain = $domain_name;
            $parentDomain = $this->getParentDomain($targetDomain);

            // Check if we can manage this domain through DNS provider
            $dnsProvider = DnsProviderFactory::make($parentDomain);

            if (!$dnsProvider) {
                return $this->api->dataMsg([
                    'can_manage' => false,
                    'message' => 'Domain not found in any supported DNS provider'
                ]);
            }

            if (!$dnsProvider->exists()) {
                return $this->api->dataMsg([
                    'can_manage' => false,
                    'message' => 'Domain not found in Cloudflare. Please add the domain to Cloudflare first.'
                ]);
            }

            // If we reach here, we can manage the domain
            return $this->api->dataMsg([
                'can_manage' => true,
                'message' => 'Domain can be managed through Cloudflare'
            ]);

        } catch (\Exception $e) {
            Log::error('Domain ownership check failed', [
                'domain' => $domain_name,
                'error' => $e->getMessage()
            ]);

            return $this->api->dataMsg([
                'can_manage' => false,
                'message' => 'Failed to verify domain ownership: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Setup domain DNS records with correct IP and manage WWW records
     */
    public function setupDomainDns()
    {
        $domain_name = request('domain_name');
        $create_www_rule = request('create_www_rule', false);

        if (empty($domain_name)) {
            return $this->api->dataMsg([
                'success' => false,
                'message' => 'Domain name is required'
            ]);
        }

        try {
            $targetDomain = $domain_name;
            $parentDomain = $this->getParentDomain($targetDomain);
            $wwwDomain = 'www.' . $targetDomain;

            // Get DNS provider for the parent domain
            $dnsProvider = DnsProviderFactory::make($parentDomain);

            if (!$dnsProvider || !$dnsProvider->exists()) {
                return $this->api->dataMsg([
                    'success' => false,
                    'message' => 'Domain not found in Cloudflare'
                ]);
            }

            // Get the correct server IP
            $serverIp = $this->getServerIp($targetDomain);
            if (!$serverIp || $serverIp === '0.0.0.0') {
                return $this->api->dataMsg([
                    'success' => false,
                    'message' => 'Could not determine server IP'
                ]);
            }

            $results = [];

            // 1. Set/update the main domain A record
            $mainRecord = [
                'type' => 'A',
                'name' => $targetDomain === $parentDomain ? '@' : $targetDomain,
                'content' => $serverIp,
                'ttl' => 300
            ];

            try {
                $dnsProvider->updateRecord($mainRecord, true); // upsert = true
                $results[] = "Main domain A record updated to {$serverIp}";
            } catch (\Exception $e) {
                Log::warning('Failed to update main domain A record', [
                    'domain' => $targetDomain,
                    'ip' => $serverIp,
                    'error' => $e->getMessage()
                ]);
                $results[] = "Warning: Failed to update main domain A record";
            }

            // 2. Handle WWW Page Rule
            if ($create_www_rule) {
                try {
                    // Create Cloudflare Page Rule for WWW redirect
                    $pageRulesService = new CloudflarePageRules($targetDomain);

                    if ($pageRulesService->createWwwRedirectRule()) {
                        $results[] = "WWW Page Rule created successfully (301 redirect to non-WWW)";
                    } else {
                        $results[] = "Warning: Failed to create WWW Page Rule";
                    }
                } catch (\Exception $e) {
                    Log::warning('Failed to create WWW Page Rule', [
                        'domain' => $targetDomain,
                        'error' => $e->getMessage()
                    ]);
                    $results[] = "Warning: Failed to create WWW Page Rule - " . $e->getMessage();
                }
            }

            return $this->api->dataMsg([
                'success' => true,
                'message' => 'DNS setup completed successfully',
                'details' => $results
            ]);

        } catch (\Exception $e) {
            Log::error('Domain DNS setup failed', [
                'domain' => $domain_name,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->api->dataMsg([
                'success' => false,
                'message' => 'DNS setup failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Check and remove WWW A record if it exists (legacy method)
     */
    public function checkAndRemoveWwwRecord()
    {
        $domain_name = request('domain_name');

        if (empty($domain_name)) {
            return $this->api->dataMsg([
                'success' => false,
                'message' => 'Domain name is required'
            ]);
        }

        try {
            $targetDomain = $domain_name;
            $parentDomain = $this->getParentDomain($targetDomain);
            $wwwDomain = 'www.' . $targetDomain;

            // Get DNS provider for the parent domain
            $dnsProvider = DnsProviderFactory::make($parentDomain);

            if (!$dnsProvider) {
                return $this->api->dataMsg([
                    'success' => false,
                    'message' => 'Domain not found in any DNS provider'
                ]);
            }

            if (!$dnsProvider->exists()) {
                return $this->api->dataMsg([
                    'success' => false,
                    'message' => 'Domain not found in Cloudflare'
                ]);
            }

            // Get all DNS records for the domain
            $records = $dnsProvider->getRecords();

            // Look for WWW A records
            $wwwARecords = array_filter($records, function($record) use ($wwwDomain, $targetDomain) {
                return $record['type'] === 'A' &&
                       (strtolower($record['name']) === strtolower($wwwDomain) ||
                        strtolower($record['name']) === 'www.' . strtolower($targetDomain) ||
                        strtolower($record['name']) === 'www');
            });

            if (empty($wwwARecords)) {
                return $this->api->dataMsg([
                    'success' => true,
                    'removed' => false,
                    'message' => 'No WWW A record found'
                ]);
            }

            // Remove each WWW A record found
            $removedCount = 0;
            foreach ($wwwARecords as $record) {
                try {
                    // deleteRecord expects array with type and name, not just ID
                    $recordToDelete = [
                        'type' => $record['type'] ?? 'A',
                        'name' => $record['name'] ?? $wwwDomain
                    ];

                    if ($dnsProvider->deleteRecord($recordToDelete)) {
                        $removedCount++;
                    }
                } catch (\Exception $e) {
                    Log::warning('Failed to remove WWW A record', [
                        'domain' => $targetDomain,
                        'record_id' => $record['id'] ?? 'unknown',
                        'record_type' => $record['type'] ?? 'A',
                        'record_name' => $record['name'] ?? $wwwDomain,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            if ($removedCount > 0) {
                Log::info('WWW A records removed', [
                    'domain' => $targetDomain,
                    'removed_count' => $removedCount
                ]);

                return $this->api->dataMsg([
                    'success' => true,
                    'removed' => true,
                    'count' => $removedCount,
                    'message' => "Removed {$removedCount} WWW A record(s)"
                ]);
            } else {
                return $this->api->dataMsg([
                    'success' => false,
                    'message' => 'Failed to remove WWW A records'
                ]);
            }

        } catch (\Exception $e) {
            Log::error('WWW A record check/removal failed', [
                'domain' => $domain_name,
                'error' => $e->getMessage()
            ]);

            return $this->api->dataMsg([
                'success' => false,
                'message' => 'Failed to check/remove WWW A record: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Check and update/create WWW A record
     */
    public function checkAndUpdateWwwRecord()
    {
        $domain_name = request('domain_name');

        if (empty($domain_name)) {
            return $this->api->dataMsg([
                'success' => false,
                'message' => 'Domain name is required'
            ]);
        }

        try {
            // Clean domain name (remove www prefix if present)
            $targetDomain = strtolower(trim($domain_name));
            if (str_starts_with($targetDomain, 'www.')) {
                $targetDomain = substr($targetDomain, 4);
            }

            // Get expected server IP using same logic as updateDnsRecords
            $serverIp = $this->getExpectedServerIp($targetDomain);
            if ($serverIp === '0.0.0.0' || empty($serverIp)) {
                return $this->api->dataMsg([
                    'success' => false,
                    'message' => 'Could not determine the correct server IP address'
                ]);
            }

            // Initialize DNS provider using same logic as updateDnsRecords
            $parentDomain = $this->getParentDomain($targetDomain);
            $dnsProvider = DnsProviderFactory::make($parentDomain);

            if (!$dnsProvider) {
                return $this->api->dataMsg([
                    'success' => false,
                    'message' => 'Domain not found in any DNS provider'
                ]);
            }

            if (!$dnsProvider->exists()) {
                return $this->api->dataMsg([
                    'success' => false,
                    'message' => 'Domain not found in Cloudflare. Please add the domain to Cloudflare first.'
                ]);
            }

            // Use parent domain's DNS provider if this is a subdomain
            if ($parentDomain !== $targetDomain) {
                $parentDnsProvider = DnsProviderFactory::make($parentDomain);
                if ($parentDnsProvider && $parentDnsProvider->exists()) {
                    $dnsProvider = $parentDnsProvider;
                }
            }

            // Get all DNS records for the domain
            $records = $dnsProvider->getRecords();

            // Look for existing WWW A records
            $wwwARecords = array_filter($records, function($record) use ($targetDomain) {
                return $record['type'] === 'A' &&
                       (strtolower($record['name']) === 'www.' . strtolower($targetDomain) ||
                        strtolower($record['name']) === 'www');
            });

            $updated = false;
            $created = false;

            if (!empty($wwwARecords)) {
                // Update existing WWW A records
                foreach ($wwwARecords as $record) {
                    if ($record['content'] !== $serverIp) {
                        try {
                            $recordToUpdate = [
                                'type' => 'A',
                                'name' => 'www',
                                'content' => $serverIp,
                                'ttl' => 300
                            ];

                            if ($dnsProvider->updateRecord($recordToUpdate, true)) {
                                $updated = true;
                                Log::info('WWW A record updated', [
                                    'domain' => $targetDomain,
                                    'old_ip' => $record['content'],
                                    'new_ip' => $serverIp
                                ]);
                            }
                        } catch (\Exception $e) {
                            Log::warning('Failed to update WWW A record', [
                                'domain' => $targetDomain,
                                'record' => $record,
                                'error' => $e->getMessage()
                            ]);
                        }
                    }
                }
            } else {
                // Create new WWW A record
                try {
                    $recordToCreate = [
                        'type' => 'A',
                        'name' => 'www',
                        'content' => $serverIp,
                        'ttl' => 300
                    ];

                    if ($dnsProvider->updateRecord($recordToCreate, true)) {
                        $created = true;
                        Log::info('WWW A record created', [
                            'domain' => $targetDomain,
                            'ip' => $serverIp
                        ]);
                    }
                } catch (\Exception $e) {
                    Log::warning('Failed to create WWW A record', [
                        'domain' => $targetDomain,
                        'ip' => $serverIp,
                        'error' => $e->getMessage()
                    ]);

                    return $this->api->dataMsg([
                        'success' => false,
                        'message' => 'Failed to create WWW A record: ' . $e->getMessage()
                    ]);
                }
            }

            return $this->api->dataMsg([
                'success' => true,
                'updated' => $updated,
                'created' => $created,
                'message' => $updated ? 'WWW A record updated successfully' :
                           ($created ? 'WWW A record created successfully' : 'WWW A record already exists with correct IP')
            ]);

        } catch (\Exception $e) {
            Log::error('WWW A record check/update failed', [
                'domain' => $domain_name,
                'error' => $e->getMessage()
            ]);

            return $this->api->dataMsg([
                'success' => false,
                'message' => 'Failed to check/update WWW A record: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 🔧 TEMPORARY: Test Page Rule Creation
     */
    public function testPageRule()
    {
        $domain_name = request('domain_name', 'onlinedrogist.co.nl');

        try {
            Log::info('🔧 TEST: Starting Page Rule test', ['domain' => $domain_name]);

            // Test CloudflarePageRules service
            $pageRulesService = new CloudflarePageRules($domain_name);

            Log::info('🔧 TEST: CloudflarePageRules service created successfully');

            // Check if WWW Page Rule exists
            $exists = $pageRulesService->wwwPageRuleExists();

            Log::info('🔧 TEST: WWW Page Rule exists check', ['exists' => $exists]);

            if ($exists) {
                return $this->api->dataMsg([
                    'success' => true,
                    'message' => 'WWW Page Rule already exists for ' . $domain_name,
                    'action' => 'none_needed'
                ]);
            }

            // Create WWW Page Rule
            Log::info('🔧 TEST: Attempting to create WWW Page Rule');

            $created = $pageRulesService->createWwwRedirectRule();

            Log::info('🔧 TEST: Page Rule creation result', ['created' => $created]);

            if ($created) {
                return $this->api->dataMsg([
                    'success' => true,
                    'message' => 'WWW Page Rule created successfully for ' . $domain_name,
                    'action' => 'created',
                    'pattern' => "www.{$domain_name}/*",
                    'redirect_to' => "https://{$domain_name}/\$1"
                ]);
            } else {
                return $this->api->dataMsg([
                    'success' => false,
                    'message' => 'Failed to create WWW Page Rule for ' . $domain_name,
                    'action' => 'failed'
                ]);
            }

        } catch (\Exception $e) {
            Log::error('🔧 TEST: Exception during Page Rule test', [
                'domain' => $domain_name,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->api->dataMsg([
                'success' => false,
                'message' => 'Exception: ' . $e->getMessage(),
                'action' => 'exception'
            ]);
        }
    }

    /**
     * Setup subdomain DNS records (A record only, no www CNAME)
     */
    public function setupSubdomainDns()
    {
        $subdomain_name = request('subdomain_name');
        $parent_domain = request('parent_domain');

        if (empty($subdomain_name) || empty($parent_domain)) {
            return $this->api->dataMsg([
                'success' => false,
                'message' => 'Subdomain name and parent domain are required'
            ]);
        }

        try {
            // Clean subdomain name
            $cleanSubdomain = strtolower(trim($subdomain_name));
            $cleanParentDomain = strtolower(trim($parent_domain));

            // Construct full subdomain
            $fullSubdomain = $cleanSubdomain . '.' . $cleanParentDomain;

            Log::info('🔧 Starting subdomain DNS setup', [
                'subdomain' => $cleanSubdomain,
                'parent_domain' => $cleanParentDomain,
                'full_subdomain' => $fullSubdomain
            ]);

            // Get expected IP for the subdomain
            $expectedIp = $this->getExpectedServerIp($fullSubdomain);
            if ($expectedIp === '0.0.0.0') {
                throw new \Exception('Could not determine the correct server IP address');
            }

            // Use CloudflareDnsSetup for subdomain A record creation
            $dnsSetup = new CloudflareDnsSetup($cleanParentDomain);
            $result = $dnsSetup->setupSubdomainARecord($fullSubdomain, $expectedIp);

            if ($result) {
                Log::info('✅ Subdomain A record setup successful', [
                    'subdomain' => $fullSubdomain,
                    'ip' => $expectedIp
                ]);

                return $this->api->dataMsg([
                    'success' => true,
                    'subdomain_name' => $fullSubdomain,
                    'dns_updated' => true,
                    'message' => 'Subdomain A record created successfully',
                    'details' => ["Subdomain A record created for {$fullSubdomain} pointing to {$expectedIp}"]
                ]);
            } else {
                throw new \Exception('Failed to create subdomain A record');
            }

        } catch (\Exception $e) {
            Log::error('Subdomain DNS setup failed', [
                'subdomain' => $subdomain_name ?? 'unknown',
                'parent_domain' => $parent_domain ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->api->dataMsg([
                'success' => false,
                'message' => 'Subdomain DNS setup failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Complete domain setup: DNS update (if needed) + WWW record (if requested) + save to database
     */
    public function setupDomainComplete()
    {
        $domain_name = request('domain_name');
        $create_www_rule = request('create_www_rule', false);
        $force_add = request('force_add', false);

        if (empty($domain_name)) {
            return $this->api->dataMsg([
                'success' => false,
                'message' => 'Domain name is required'
            ]);
        }

        try {
            // Clean domain name (remove www prefix if present)
            $targetDomain = strtolower(trim($domain_name));
            if (str_starts_with($targetDomain, 'www.')) {
                $targetDomain = substr($targetDomain, 4);
            }

            $results = [];
            $dnsUpdated = false;
            $wwwCreated = false;

            // Step 1: Complete DNS setup using CloudflareDnsSetup (A record + www CNAME with proxy)
            $parentDomain = $this->getParentDomain($targetDomain);

            // Get expected IP
            $expectedIp = $this->getExpectedServerIp($targetDomain);
            if ($expectedIp === '0.0.0.0') {
                throw new \Exception('Could not determine the correct server IP address');
            }

            Log::info('🔧 Starting complete DNS setup', [
                'domain' => $targetDomain,
                'parent_domain' => $parentDomain,
                'expected_ip' => $expectedIp
            ]);

            try {
                // Use CloudflareDnsSetup for complete DNS configuration
                $dnsSetup = new CloudflareDnsSetup($parentDomain);
                $dnsResults = $dnsSetup->setupDnsRecords($expectedIp);

                // Add DNS setup results
                foreach ($dnsResults as $result) {
                    $results[] = $result;
                }

                $dnsUpdated = true;
                Log::info('✅ Complete DNS setup finished', [
                    'domain' => $targetDomain,
                    'results' => $dnsResults
                ]);

            } catch (\Exception $e) {
                Log::error('❌ DNS setup failed, falling back to legacy method', [
                    'domain' => $targetDomain,
                    'error' => $e->getMessage()
                ]);

                // Fallback to old DNS provider method
                $dnsProvider = DnsProviderFactory::make($parentDomain);

                if (!$dnsProvider) {
                    throw new \Exception('Domain not found in any DNS provider');
                }

                if (!$dnsProvider->exists()) {
                    throw new \Exception('Domain not found in Cloudflare. Please add the domain to Cloudflare first.');
                }

                // Check if DNS update is needed by checking current records
                $needsDnsUpdate = false;
                if ($force_add) {
                    $needsDnsUpdate = true;
                    Log::info('Force add requested, will update DNS', ['domain' => $targetDomain]);
                } else {
                    // Check current DNS status
                    $actualIp = $this->resolveDomainToIp($targetDomain);
                    if ($actualIp !== $expectedIp) {
                        $needsDnsUpdate = true;
                        Log::info('DNS update needed', [
                            'domain' => $targetDomain,
                            'current_ip' => $actualIp,
                            'expected_ip' => $expectedIp
                        ]);
                    }
                }

                if ($needsDnsUpdate) {
                    // Update A record for the domain/subdomain
                    $record = [
                        'name' => $targetDomain,
                        'type' => 'A',
                        'value' => $expectedIp,
                        'ttl' => 300
                    ];

                    $result = $dnsProvider->updateRecord($record, true);

                    if ($result) {
                        Log::info('DNS A record updated successfully (fallback)', [
                            'domain' => $targetDomain,
                            'ip' => $expectedIp
                        ]);
                        $results[] = 'DNS A record updated successfully (legacy method)';
                        $dnsUpdated = true;
                    } else {
                        throw new \Exception('Failed to update DNS record');
                    }
                } else {
                    Log::info('DNS update not needed', ['domain' => $targetDomain]);
                    $results[] = 'DNS records already configured correctly';
                }
            }

            // Step 2: Handle WWW Page Rule if requested
            Log::info('🔧 DEBUG: WWW Rule handling', [
                'create_www_rule' => $create_www_rule,
                'domain' => $targetDomain,
                'type' => gettype($create_www_rule),
                'value' => var_export($create_www_rule, true)
            ]);

            if ($create_www_rule) {
                Log::info('🔧 Creating WWW Page Rule (checkbox was checked)', ['domain' => $targetDomain]);

                try {
                    // Create Cloudflare Page Rule for WWW redirect
                    $pageRulesService = new CloudflarePageRules($parentDomain);

                    Log::info('🔧 CloudflarePageRules service created, calling createWwwRedirectRule', ['domain' => $targetDomain]);

                    $ruleCreated = $pageRulesService->createWwwRedirectRule();

                    Log::info('🔧 Page Rule creation result', [
                        'domain' => $targetDomain,
                        'success' => $ruleCreated
                    ]);

                    if ($ruleCreated) {
                        $results[] = 'WWW Page Rule created successfully (301 redirect to non-WWW)';
                        $wwwCreated = true;
                        Log::info('✅ WWW Page Rule created successfully', [
                            'domain' => $targetDomain,
                            'pattern' => "www.{$targetDomain}/*",
                            'redirect_to' => "https://{$targetDomain}/\$1"
                        ]);
                    } else {
                        $results[] = 'Failed to create WWW Page Rule';
                        Log::error('❌ Failed to create WWW Page Rule', ['domain' => $targetDomain]);
                    }

                } catch (\Exception $e) {
                    // Page Rule failure is not critical, log warning but continue
                    Log::error('🔧 Exception during WWW Page Rule setup', [
                        'domain' => $targetDomain,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    $results[] = 'Warning: Failed to setup WWW Page Rule - ' . $e->getMessage();
                }
            } else {
                Log::info('🔧 WWW Page Rule creation skipped (checkbox not checked)', [
                    'domain' => $targetDomain,
                    'create_www_rule' => $create_www_rule
                ]);
                $results[] = 'WWW Page Rule skipped (checkbox not checked)';
            }

            // Step 3: Save domain to database
            $site_id = request('site_id');
            if (empty($site_id)) {
                throw new \Exception('Site ID is required to save domain');
            }

            // Get default edition ID
            $default_edition_id = getDefaultEdition();

            // Prepare domain data for saving with all required fields
            $domainData = [
                'domain_id' => null, // New domain
                'domain_name' => $targetDomain,
                'domain_isdefault' => 0,
                'domain_ismain' => 0,
                'domain_isssl' => 0,
                'domain_isletsencrypt' => 1,
                'domain_ismail' => 0,
                'domain_edition_id' => $default_edition_id,
                'domain_site' => $site_id,
                'domain_ssl_status' => DomainSslStatus::DOMAIN_SSL_STATUS_OK,
                // Additional fields that might be expected by frontend
                'is_readonly' => 0,
                'ssl' => [
                    'cert' => '',
                    'private_key' => '',
                    'CA' => ''
                ]
            ];

            // Get current site domains
            $domainHandler = app(DomainHandler::class);

            // Set the site context for the domain handler
            dealer()->setSiteId($site_id);

            $currentDomainsCollection = $domainHandler->getSiteDomains();

            // Convert Collection to array (getSiteDomains returns Collection)
            $currentDomains = $currentDomainsCollection ? $currentDomainsCollection->toArray() : [];

            // Add the new domain to the list
            $updatedDomains = array_merge($currentDomains, [$domainData]);

            // Save all domains (this will create the new domain)
            $domainHandler->updateSiteDomains($site_id, $updatedDomains);

            Log::info('Domain setup and database save completed successfully', [
                'domain' => $targetDomain,
                'site_id' => $site_id,
                'dns_updated' => $dnsUpdated,
                'www_created' => $wwwCreated
            ]);

            $results[] = 'Domain saved to database successfully';

            return $this->api->dataMsg([
                'success' => true,
                'domain_name' => $targetDomain,
                'dns_updated' => $dnsUpdated,
                'www_created' => $wwwCreated,
                'domain_saved' => true,
                'message' => 'Domain setup and save completed successfully',
                'details' => $results
            ]);

        } catch (\Exception $e) {
            Log::error('Complete domain setup failed', [
                'domain' => $domain_name,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->api->dataMsg([
                'success' => false,
                'message' => 'Domain setup failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Check SSL certificate status for a domain
     */
    public function checkSslStatus()
    {
        $domain_name = request('domain_name');
        $force_refresh = request('force_refresh', false);

        // Ensure domain_name is a string (fix for array parameter issue)
        if (is_array($domain_name)) {
            Log::error('SSL Status Check: domain_name received as array', [
                'domain_name_array' => $domain_name,
                'force_refresh' => $force_refresh
            ]);

            return $this->api->dataMsg([
                'status' => 'Error',
                'message' => 'Invalid domain name format (array received)',
                'can_renew' => false
            ]);
        }

        if (empty($domain_name) || !is_string($domain_name)) {
            return $this->api->dataMsg([
                'status' => 'Error',
                'message' => 'Domain name is required and must be a string',
                'can_renew' => false
            ]);
        }

        Log::info('SSL Status Check Started', [
            'domain_name' => $domain_name,
            'force_refresh' => $force_refresh
        ]);

        // Cache configuration
        $cacheKey = "ssl_status_{$domain_name}";
        $cacheTimeout = 10; // 10 minutes for SSL checks (same as DNS)

        // Check cache first (unless forcing refresh)
        if (!$force_refresh) {
            $cachedResult = Cache::get($cacheKey);
            if ($cachedResult) {
                Log::info('SSL Status Retrieved from Cache', [
                    'domain_name' => $domain_name,
                    'cached_status' => $cachedResult['status']
                ]);

                return $this->api->dataMsg($cachedResult);
            }
        }

        try {
            $result = $this->performSslCheck($domain_name);

            // Cache the result
            Cache::put($cacheKey, $result, now()->addMinutes($cacheTimeout));

            return $this->api->dataMsg($result);

        } catch (\Exception $e) {
            Log::error('SSL status check failed', [
                'domain' => $domain_name,
                'error' => $e->getMessage()
            ]);

            $result = [
                'status' => 'Error',
                'message' => 'Failed to check SSL status: ' . $e->getMessage(),
                'can_renew' => false
            ];

            // Cache error results for shorter time (5 minutes)
            Cache::put($cacheKey, $result, now()->addMinutes(5));

            return $this->api->dataMsg($result);
        }
    }

    /**
     * Check domain DNS status against Cloudflare
     */
    public function checkDnsStatus()
    {
        $domain_name = request('domain_name');
        $force_refresh = request('force_refresh', false);

        // Ensure domain_name is a string (fix for array parameter issue)
        if (is_array($domain_name)) {
            Log::error('DNS Status Check: domain_name received as array', [
                'domain_name_array' => $domain_name,
                'force_refresh' => $force_refresh
            ]);

            return $this->api->dataMsg([
                'status' => 'Error',
                'message' => 'Invalid domain name format (array received)',
                'can_update' => false,
                'mx_info' => [
                    'has_mx_records' => false,
                    'mx_points_to_our_ip' => false,
                    'show_mail_toggle' => false,
                    'message' => 'Invalid domain name format'
                ]
            ]);
        }

        if (empty($domain_name) || !is_string($domain_name)) {
            return $this->api->dataMsg([
                'status' => 'Error',
                'message' => 'Domain name is required and must be a string',
                'can_update' => false,
                'mx_info' => [
                    'has_mx_records' => false,
                    'mx_points_to_our_ip' => false,
                    'show_mail_toggle' => false,
                    'message' => 'Domain name is required'
                ]
            ]);
        }

        Log::info('DNS Status Check Started', [
            'domain_name' => $domain_name,
            'force_refresh' => $force_refresh
        ]);

        // Cache configuration
        $cacheKey = "dns_status_{$domain_name}";
        $cacheTimeout = 10; // 10 minutes

        // Check cache first (unless forcing refresh)
        if (!$force_refresh) {
            $cachedResult = Cache::get($cacheKey);
            if ($cachedResult) {
                Log::info('DNS Status Retrieved from Cache', [
                    'domain_name' => $domain_name,
                    'cached_status' => $cachedResult['status']
                ]);

                return $this->api->dataMsg($cachedResult);
            }
        }

        try {
            $targetDomain = $domain_name;
            $parentDomain = $this->getParentDomain($targetDomain);

            // Check if this is the server domain or a subdomain of the server domain
            $serverDomain = config('exolog.server_domain');
            $isServerDomain = $targetDomain === $serverDomain;
            $isServerSubdomain = $serverDomain && str_ends_with($targetDomain, '.' . $serverDomain);

            Log::info('Server domain check', [
                'target_domain' => $targetDomain,
                'server_domain_config' => $serverDomain,
                'is_server_domain' => $isServerDomain,
                'is_server_subdomain' => $isServerSubdomain
            ]);

            if ($isServerDomain || $isServerSubdomain) {
                Log::info('Server domain/subdomain detected - always OK', [
                    'domain' => $targetDomain,
                    'server_domain' => $serverDomain,
                    'type' => $isServerDomain ? 'server_domain' : 'server_subdomain'
                ]);

                // Even for server domains, check MX records for mail functionality
                $mxInfo = [
                    'has_mx_records' => false,
                    'mx_points_to_our_ip' => false,
                    'show_mail_toggle' => false,
                    'message' => 'Server domain - MX records not checked'
                ];

                return $this->api->dataMsg([
                    'status' => 'OK',
                    'message' => $isServerDomain ? 'Server domain (source of truth)' : 'Server subdomain (always valid)',
                    'can_update' => false,
                    'mx_info' => $mxInfo
                ]);
            }

            // Step 1: Check actual DNS resolution first (more efficient)
            $expectedIp = $this->getExpectedServerIp($targetDomain);
            $actualIp = $this->resolveDomainToIp($targetDomain);

            Log::info('DNS Resolution Check', [
                'domain' => $targetDomain,
                'actual_ip' => $actualIp,
                'expected_ip' => $expectedIp,
                'matches' => $actualIp === $expectedIp
            ]);

            // If domain already resolves to correct IP, still need to check MX records for mail status
            if ($actualIp && $actualIp === $expectedIp) {
                // Even though DNS is OK, we still need to check MX records for mail functionality
                // Get DNS records to check MX configuration
                $parentDomain = $this->getParentDomain($targetDomain);
                $dnsProvider = DnsProviderFactory::make($parentDomain);

                $mxInfo = [
                    'has_mx_records' => false,
                    'mx_points_to_our_ip' => false,
                    'show_mail_toggle' => false,
                    'message' => 'Could not check MX records - no DNS provider'
                ];

                if ($dnsProvider) {
                    try {
                        $records = $dnsProvider->getRecords();
                        $mxInfo = $this->checkMxRecordsFromData($targetDomain, $records);
                    } catch (\Exception $e) {
                        Log::warning('Failed to get DNS records for MX check on resolved domain', [
                            'domain' => $targetDomain,
                            'error' => $e->getMessage()
                        ]);
                        $mxInfo['message'] = 'Error checking MX records: ' . $e->getMessage();
                    }
                }

                $result = [
                    'status' => 'OK',
                    'message' => 'Domain resolves to correct IP',
                    'can_update' => false,
                    'current_ip' => $actualIp,
                    'mx_info' => $mxInfo
                ];

                // Cache successful results for 10 minutes
                Cache::put($cacheKey, $result, now()->addMinutes($cacheTimeout));

                return $this->api->dataMsg($result);
            }

            // Step 2: Only check Cloudflare if IP is incorrect or domain doesn't resolve
            // For subdomains, check the parent domain's DNS provider
            $dnsProvider = DnsProviderFactory::make($parentDomain);

            if (!$dnsProvider) {
                $result = [
                    'status' => 'Check DNS',
                    'message' => $actualIp ?
                        "Domain resolves to {$actualIp} but parent domain {$parentDomain} not in any DNS provider" :
                        "Parent domain {$parentDomain} not found in any DNS provider",
                    'can_update' => false,
                    'current_ip' => $actualIp
                ];

                // Cache Check DNS results for shorter time (5 minutes)
                Cache::put($cacheKey, $result, now()->addMinutes(5));

                return $this->api->dataMsg($result);
            }

            if (!$dnsProvider->exists()) {
                $result = [
                    'status' => 'Check DNS',
                    'message' => $actualIp ?
                        "Domain resolves to {$actualIp} but parent domain {$parentDomain} not in Cloudflare - please add it" :
                        "Parent domain {$parentDomain} not found in Cloudflare",
                    'can_update' => false,
                    'current_ip' => $actualIp
                ];

                // Cache Check DNS results for shorter time (5 minutes)
                Cache::put($cacheKey, $result, now()->addMinutes(5));

                return $this->api->dataMsg($result);
            }



            // Domain exists in Cloudflare, now check if A records point to correct IP
            $records = $dnsProvider->getRecords();

            // Debug: Log all DNS records to see what we're getting
            Log::info('DNS Records Retrieved', [
                'domain' => $targetDomain,
                'parent_domain' => $parentDomain,
                'total_records' => count($records),
                'records' => $records
            ]);

            // For subdomains (like www.domain.com), look for the specific subdomain record
            $targetName = $targetDomain;
            $parentDomain = $this->getParentDomain($targetName);

            $aRecords = array_filter($records, function($record) use ($targetName, $parentDomain) {
                $recordName = $record['name'];

                // Handle different record name formats from Cloudflare
                // Accept both A records and CNAME records
                if ($record['type'] !== 'A' && $record['type'] !== 'CNAME') {
                    return false;
                }

                // Exact match (full domain name)
                if ($recordName === $targetName) {
                    return true;
                }

                // Root domain with @ symbol
                if ($recordName === '@' && $targetName === $parentDomain) {
                    return true;
                }

                // Wildcard match (*.domain.com matches subdomain.domain.com)
                if (str_starts_with($recordName, '*.')) {
                    $wildcardDomain = substr($recordName, 2); // Remove "*."
                    if ($wildcardDomain === $parentDomain && $targetName !== $parentDomain) {
                        return true;
                    }
                }

                // Wildcard stored as relative name (e.g., "*" for *.domain.com)
                if ($recordName === '*' && $targetName !== $parentDomain) {
                    return true;
                }

                // Subdomain stored as relative name (e.g., "www" for www.domain.com)
                if ($targetName !== $parentDomain) {
                    $subdomain = str_replace('.' . $parentDomain, '', $targetName);
                    if ($recordName === $subdomain) {
                        return true;
                    }
                }

                return false;
            });

            // Debug: Log A record filtering results
            Log::info('A Record Filtering Results', [
                'domain' => $targetDomain,
                'target_name' => $targetName,
                'parent_domain' => $parentDomain,
                'found_a_records' => count($aRecords),
                'a_records' => $aRecords
            ]);

            if (empty($aRecords)) {
                // Check MX records even if A records are missing
                $mxInfo = $this->checkMxRecordsFromData($targetDomain, $records);

                $result = [
                    'status' => 'Update DNS',
                    'message' => 'No A record found for domain',
                    'can_update' => true,
                    'mx_info' => $mxInfo
                ];

                // Cache Update DNS results for shorter time (5 minutes)
                Cache::put($cacheKey, $result, now()->addMinutes(5));

                return $this->api->dataMsg($result);
            }

            // Get expected IP (try to resolve from config or other domains)
            $expectedIp = $this->getExpectedServerIp($targetDomain);

            // Debug: Log IP comparison
            Log::info('IP Comparison', [
                'domain' => $targetDomain,
                'expected_ip' => $expectedIp,
                'a_records_count' => count($aRecords)
            ]);

            foreach ($aRecords as $record) {
                $recordType = $record['type'];
                $recordContent = $record['content'] ?? $record['value'] ?? '';

                Log::info('Checking DNS Record', [
                    'domain' => $targetDomain,
                    'record_name' => $record['name'],
                    'record_type' => $recordType,
                    'record_content' => $recordContent,
                    'expected_ip' => $expectedIp
                ]);

                if ($recordType === 'A') {
                    // Direct A record - compare IP directly
                    if ($recordContent !== $expectedIp) {
                        $result = [
                            'status' => 'Update DNS',
                            'message' => "A record points to {$recordContent}, expected {$expectedIp}",
                            'can_update' => true,
                            'current_ip' => $recordContent,
                            'expected_ip' => $expectedIp
                        ];

                        // Cache Update DNS results for shorter time (5 minutes)
                        Cache::put($cacheKey, $result, now()->addMinutes(5));

                        return $this->api->dataMsg($result);
                    }
                } elseif ($recordType === 'CNAME') {
                    // CNAME record - check if target domain has correct A record
                    $cnameTarget = $recordContent;

                    // Find A record for CNAME target
                    $cnameParentDomain = $this->getParentDomain($cnameTarget);
                    $targetARecord = array_filter($records, function($r) use ($cnameTarget, $cnameParentDomain) {
                        return $r['type'] === 'A' &&
                               ($r['name'] === $cnameTarget ||
                                ($r['name'] === '@' && $cnameTarget === $cnameParentDomain));
                    });

                    if (empty($targetARecord)) {
                        $result = [
                            'status' => 'Update DNS',
                            'message' => "CNAME points to {$cnameTarget} but no A record found for target",
                            'can_update' => true
                        ];

                        // Cache Update DNS results for shorter time (5 minutes)
                        Cache::put($cacheKey, $result, now()->addMinutes(5));

                        return $this->api->dataMsg($result);
                    }

                    $targetRecord = reset($targetARecord);
                    $targetIp = $targetRecord['content'] ?? $targetRecord['value'] ?? '';

                    if ($targetIp !== $expectedIp) {
                        $result = [
                            'status' => 'Update DNS',
                            'message' => "CNAME target {$cnameTarget} points to {$targetIp}, expected {$expectedIp}",
                            'can_update' => true,
                            'current_ip' => $targetIp,
                            'expected_ip' => $expectedIp
                        ];

                        // Cache Update DNS results for shorter time (5 minutes)
                        Cache::put($cacheKey, $result, now()->addMinutes(5));

                        return $this->api->dataMsg($result);
                    }
                }
            }

            // Check MX records to determine if mail toggle should be shown
            // Use the same records data we already fetched to avoid duplicate API calls
            $mxInfo = $this->checkMxRecordsFromData($targetDomain, $records);

            $result = [
                'status' => 'OK',
                'message' => 'Domain DNS configuration is correct',
                'can_update' => false,
                'mx_info' => $mxInfo
            ];

            // Cache the successful result for 10 minutes
            Cache::put($cacheKey, $result, now()->addMinutes($cacheTimeout));

            return $this->api->dataMsg($result);

        } catch (\Exception $e) {
            Log::error('DNS status check failed', [
                'domain' => $targetDomain,
                'error' => $e->getMessage()
            ]);

            $result = [
                'status' => 'Error',
                'message' => 'Failed to check DNS status: ' . $e->getMessage(),
                'can_update' => false,
                'mx_info' => [
                    'has_mx_records' => false,
                    'mx_points_to_our_ip' => false,
                    'show_mail_toggle' => false,
                    'message' => 'Could not check MX records due to DNS error'
                ]
            ];

            // Cache error results for shorter time (2 minutes)
            Cache::put($cacheKey, $result, now()->addMinutes(2));

            return $this->api->dataMsg($result);
        }
    }

    /**
     * Check MX records from existing DNS records data (optimized to avoid duplicate API calls)
     *
     * @param string $domainName The domain to check
     * @param array $records The DNS records already fetched from the provider
     * @return array Information about MX records and whether mail toggle should be shown
     */
    private function checkMxRecordsFromData(string $domainName, array $records): array
    {
        try {
            // Get parent domain for MX record checking
            $parentDomain = $this->getParentDomain($domainName);

            // Filter for MX records - check both exact domain and parent domain
            $mxRecords = array_filter($records, function($record) use ($domainName, $parentDomain) {
                if ($record['type'] !== 'MX') {
                    return false;
                }

                $recordName = $record['name'];

                // Remove trailing dot if present
                $recordName = rtrim($recordName, '.');
                $domainName = rtrim($domainName, '.');
                $parentDomain = rtrim($parentDomain, '.');

                // Check for exact match or parent domain match
                return $recordName === $domainName ||
                       $recordName === $parentDomain ||
                       $recordName === "{$domainName}." ||
                       $recordName === "{$parentDomain}.";
            });

            Log::info('MX Record Search Debug', [
                'domain' => $domainName,
                'parent_domain' => $parentDomain,
                'total_records' => count($records),
                'mx_records_found' => count($mxRecords),
                'all_mx_records' => array_filter($records, function($record) {
                    return $record['type'] === 'MX';
                }),
                'filtered_mx_records' => $mxRecords
            ]);

            if (empty($mxRecords)) {
                return [
                    'has_mx_records' => false,
                    'mx_points_to_our_ip' => false,
                    'show_mail_toggle' => false,
                    'message' => 'No MX records found for domain'
                ];
            }

            // Check if any MX record points to our mail server
            $mailServerDomain = config('services.poste.mail_domain');
            $mailServerIp = config('mail.server_ip'); // Also check IP
            $pointsToOurServer = false;

            Log::info('Checking MX records against our mail server', [
                'domain' => $domainName,
                'mx_records_count' => count($mxRecords),
                'mail_server_domain' => $mailServerDomain,
                'mail_server_ip' => $mailServerIp,
                'mx_records' => $mxRecords
            ]);

            foreach ($mxRecords as $record) {
                // Check if MX record points to our mail server domain or IP
                $recordContent = $record['content'] ?? $record['value'] ?? '';

                // Check for mail server domain match
                if (!empty($mailServerDomain) && strpos($recordContent, $mailServerDomain) !== false) {
                    $pointsToOurServer = true;
                    Log::info('Found MX record pointing to our mail server domain', [
                        'domain' => $domainName,
                        'mx_content' => $recordContent,
                        'mail_server_domain' => $mailServerDomain
                    ]);
                    break;
                }

                // Check for mail server IP match
                if (!empty($mailServerIp) && strpos($recordContent, $mailServerIp) !== false) {
                    $pointsToOurServer = true;
                    Log::info('Found MX record pointing to our mail server IP', [
                        'domain' => $domainName,
                        'mx_content' => $recordContent,
                        'mail_server_ip' => $mailServerIp
                    ]);
                    break;
                }

                // Check for common mail subdomain patterns (mail.domain.com)
                $mailSubdomain = "mail.{$parentDomain}";
                if (strpos($recordContent, $mailSubdomain) !== false) {
                    $pointsToOurServer = true;
                    Log::info('Found MX record pointing to mail subdomain', [
                        'domain' => $domainName,
                        'mx_content' => $recordContent,
                        'mail_subdomain' => $mailSubdomain
                    ]);
                    break;
                }
            }

            return [
                'has_mx_records' => true,
                'mx_points_to_our_ip' => $pointsToOurServer,
                'show_mail_toggle' => $pointsToOurServer,
                'message' => $pointsToOurServer
                    ? 'MX records point to our mail server'
                    : 'MX records do not point to our mail server',
                'mx_records' => $mxRecords
            ];
        } catch (\Exception $e) {
            Log::error('Failed to check MX records from data', [
                'domain' => $domainName,
                'error' => $e->getMessage()
            ]);

            return [
                'has_mx_records' => false,
                'mx_points_to_our_ip' => false,
                'show_mail_toggle' => false,
                'message' => 'Error checking MX records: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Perform SSL certificate check for a domain
     *
     * @param string $domainName The domain to check
     * @return array SSL status information
     */
    private function performSslCheck(string $domainName): array
    {
        try {
            // Check if domain has SSL enabled in database
            $domain = Domain::crossSiteQuery()->where('domain_name', $domainName)->first();

            if (!$domain) {
                return [
                    'status' => 'No SSL',
                    'message' => 'Domain not found in system',
                    'can_renew' => false
                ];
            }

            if (!$domain->domain_isssl) {
                return [
                    'status' => 'No SSL',
                    'message' => 'SSL is disabled for this domain',
                    'can_renew' => false
                ];
            }

            // Check SSL certificate using OpenSSL
            $context = stream_context_create([
                "ssl" => [
                    "capture_peer_cert" => true,
                    "verify_peer" => false,
                    "verify_peer_name" => false,
                ],
            ]);

            $socket = @stream_socket_client(
                "ssl://{$domainName}:443",
                $errno,
                $errstr,
                10,
                STREAM_CLIENT_CONNECT,
                $context
            );

            if (!$socket) {
                return [
                    'status' => 'No SSL',
                    'message' => "Cannot connect to SSL port: {$errstr}",
                    'can_renew' => true
                ];
            }

            $cert = stream_context_get_params($socket);
            fclose($socket);

            if (!isset($cert['options']['ssl']['peer_certificate'])) {
                return [
                    'status' => 'No SSL',
                    'message' => 'No SSL certificate found',
                    'can_renew' => true
                ];
            }

            $certResource = $cert['options']['ssl']['peer_certificate'];
            $certInfo = openssl_x509_parse($certResource);

            if (!$certInfo) {
                return [
                    'status' => 'Invalid',
                    'message' => 'Invalid SSL certificate',
                    'can_renew' => true
                ];
            }

            // Check certificate expiration
            $validFrom = $certInfo['validFrom_time_t'];
            $validTo = $certInfo['validTo_time_t'];
            $now = time();

            if ($now < $validFrom) {
                return [
                    'status' => 'Invalid',
                    'message' => 'SSL certificate is not yet valid',
                    'can_renew' => true,
                    'expires_at' => date('Y-m-d H:i:s', $validTo)
                ];
            }

            if ($now > $validTo) {
                return [
                    'status' => 'Expired',
                    'message' => 'SSL certificate has expired',
                    'can_renew' => true,
                    'expired_at' => date('Y-m-d H:i:s', $validTo)
                ];
            }

            // Check if certificate expires soon (within 30 days)
            $daysUntilExpiry = ($validTo - $now) / (24 * 60 * 60);

            if ($daysUntilExpiry <= 30) {
                return [
                    'status' => 'Expiring Soon',
                    'message' => sprintf('SSL certificate expires in %d days', round($daysUntilExpiry)),
                    'can_renew' => true,
                    'expires_at' => date('Y-m-d H:i:s', $validTo),
                    'days_until_expiry' => round($daysUntilExpiry)
                ];
            }

            // Certificate is valid
            return [
                'status' => 'Valid',
                'message' => sprintf('SSL certificate is valid (expires in %d days)', round($daysUntilExpiry)),
                'can_renew' => true,
                'expires_at' => date('Y-m-d H:i:s', $validTo),
                'days_until_expiry' => round($daysUntilExpiry),
                'issuer' => $certInfo['issuer']['CN'] ?? 'Unknown'
            ];

        } catch (\Exception $e) {
            Log::error('SSL check failed', [
                'domain' => $domainName,
                'error' => $e->getMessage()
            ]);

            return [
                'status' => 'Error',
                'message' => 'Failed to check SSL certificate: ' . $e->getMessage(),
                'can_renew' => true
            ];
        }
    }

    /**
     * Get expected server IP for domain
     */
    private function getExpectedServerIp(string $domainName): string
    {
        Log::info('Getting Expected Server IP', [
            'domain' => $domainName,
            'server_domain_config' => config('exolog.server_domain'),
            'mail_server_ip_config' => config('mail.server_ip')
        ]);

        // First try to get IP from the current site's hostname
        $siteHostname = $this->getCurrentSiteHostname();
        if ($siteHostname) {
            $siteIp = $this->resolveHostnameToIp($siteHostname);
            if ($siteIp && $siteIp !== '0.0.0.0') {
                Log::info('Using IP from current site hostname', [
                    'site_hostname' => $siteHostname,
                    'resolved_ip' => $siteIp
                ]);
                return $siteIp;
            }
        }

        // Fallback: try to get IP from the server domain configuration
        $serverDomain = config('exolog.server_domain');
        if ($serverDomain) {
            try {
                $dnsProvider = DnsProviderFactory::make($serverDomain);
                if ($dnsProvider && $dnsProvider->exists()) {
                    $records = $dnsProvider->getRecords();
                    $aRecords = array_filter($records, function($record) use ($serverDomain) {
                        return $record['type'] === 'A' &&
                               ($record['name'] === $serverDomain || $record['name'] === '@');
                    });

                    if (!empty($aRecords)) {
                        $record = reset($aRecords);
                        $serverIp = $record['content'] ?? $record['value'] ?? '';
                        if ($serverIp) {
                            Log::info('Using IP from server domain DNS records', [
                                'server_domain' => $serverDomain,
                                'resolved_ip' => $serverIp
                            ]);
                            return $serverIp;
                        }
                    }
                }
            } catch (\Exception $e) {
                Log::info('Could not resolve server IP from server domain', [
                    'server_domain' => $serverDomain,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Try to detect server IP dynamically
        $dynamicIp = $this->detectServerIpDynamically();
        if ($dynamicIp && $dynamicIp !== '0.0.0.0') {
            Log::info('Using dynamically detected server IP', ['ip' => $dynamicIp]);
            return $dynamicIp;
        }

        // Fallback to config (for local development)
        $configIp = config('mail.server_ip');
        if ($configIp && config('app.env') === 'local') {
            // Validate that the configured IP is not a private/local IP for DNS creation
            if (filter_var($configIp, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                Log::info('Using config server IP for local environment', ['ip' => $configIp]);
                return $configIp;
            } else {
                Log::warning('Configured server IP is private/local, not suitable for DNS creation', [
                    'configured_ip' => $configIp,
                    'environment' => config('app.env')
                ]);
            }
        }

        // For local development, use a fallback public IP if no valid public IP is configured
        if (config('app.env') === 'local') {
            $fallbackIp = config('exolog.fallback_public_ip', '**************'); // Default to your server IP
            Log::info('Using fallback public IP for local DNS creation', [
                'fallback_ip' => $fallbackIp,
                'reason' => 'Local environment needs public IP for Cloudflare DNS'
            ]);
            return $fallbackIp;
        }

        // Final fallback: try to resolve from server domain
        if ($serverDomain && $serverDomain !== $domainName) {
            try {
                $dnsProvider = DnsProviderFactory::make($serverDomain);
                if ($dnsProvider && $dnsProvider->exists()) {
                    $records = $dnsProvider->getRecords();
                    $aRecords = array_filter($records, function($record) use ($serverDomain) {
                        return $record['type'] === 'A' &&
                               ($record['name'] === $serverDomain || $record['name'] === '@');
                    });

                    if (!empty($aRecords)) {
                        $record = reset($aRecords);
                        return $record['content'] ?? $record['value'] ?? '';
                    }
                }
            } catch (\Exception $e) {
                Log::info('Could not resolve IP from server domain fallback', [
                    'server_domain' => $serverDomain,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Final fallback
        return '0.0.0.0';
    }

    /**
     * Dynamically detect the server's public IP address
     */
    private function detectServerIpDynamically(): string
    {
        // Method 1: Try to get IP from server's own domain DNS
        $serverDomain = config('exolog.server_domain');
        if ($serverDomain) {
            try {
                $ip = gethostbyname($serverDomain);
                if ($ip && $ip !== $serverDomain && filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE)) {
                    Log::info('Server IP detected from server domain DNS', [
                        'server_domain' => $serverDomain,
                        'detected_ip' => $ip
                    ]);
                    return $ip;
                }
            } catch (\Exception $e) {
                Log::debug('Failed to resolve server domain', [
                    'server_domain' => $serverDomain,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Method 2: Try external IP detection services (with timeout and fallbacks)
        $ipServices = [
            'https://api.ipify.org',
            'https://ipv4.icanhazip.com',
            'https://checkip.amazonaws.com',
            'https://ipinfo.io/ip'
        ];

        foreach ($ipServices as $service) {
            try {
                $context = stream_context_create([
                    'http' => [
                        'timeout' => 5,
                        'user_agent' => 'Exolog-Server/1.0'
                    ]
                ]);

                $ip = trim(file_get_contents($service, false, $context));

                if ($ip && filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE)) {
                    Log::info('Server IP detected from external service', [
                        'service' => $service,
                        'detected_ip' => $ip
                    ]);
                    return $ip;
                }
            } catch (\Exception $e) {
                Log::debug('Failed to get IP from external service', [
                    'service' => $service,
                    'error' => $e->getMessage()
                ]);
                continue;
            }
        }

        // Method 3: Try to get from network interface (Linux/Unix systems)
        try {
            $output = shell_exec("curl -s --max-time 3 https://api.ipify.org 2>/dev/null || curl -s --max-time 3 https://ipv4.icanhazip.com 2>/dev/null");
            if ($output) {
                $ip = trim($output);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE)) {
                    Log::info('Server IP detected via shell command', ['detected_ip' => $ip]);
                    return $ip;
                }
            }
        } catch (\Exception $e) {
            Log::debug('Failed to get IP via shell command', ['error' => $e->getMessage()]);
        }

        Log::warning('Could not dynamically detect server IP address');
        return '0.0.0.0';
    }

    /**
     * Update DNS records for domain to point to correct server IP
     */
    public function updateDnsRecords()
    {
        $domain_name = request('domain_name');

        // Validate input
        if (empty($domain_name)) {
            return $this->api->dataMsg([
                'success' => false,
                'message' => 'Domain name is required'
            ]);
        }

        try {
            $targetDomain = $domain_name;
            $parentDomain = $this->getParentDomain($targetDomain);

            $dnsProvider = DnsProviderFactory::make($parentDomain);

            if (!$dnsProvider) {
                return $this->api->dataMsg([
                    'success' => false,
                    'message' => 'Domain not found in any DNS provider'
                ]);
            }

            if (!$dnsProvider->exists()) {
                return $this->api->dataMsg([
                    'success' => false,
                    'message' => 'Domain not found in Cloudflare. Please add the domain to Cloudflare first.'
                ]);
            }

            // Get expected IP
            $expectedIp = $this->getExpectedServerIp($targetDomain);

            if ($expectedIp === '0.0.0.0') {
                return $this->api->dataMsg([
                    'success' => false,
                    'message' => 'Could not determine the correct server IP address'
                ]);
            }

            // Use the same target domain and parent domain already calculated above
            // No need to recalculate since we already have them

            // Use parent domain's DNS provider if this is a subdomain
            if ($parentDomain !== $targetDomain) {
                $parentDnsProvider = DnsProviderFactory::make($parentDomain);
                if ($parentDnsProvider && $parentDnsProvider->exists()) {
                    $dnsProvider = $parentDnsProvider;
                }
            }

            // Update A record for the domain/subdomain
            $record = [
                'name' => $targetDomain,
                'type' => 'A',
                'value' => $expectedIp,
                'ttl' => 300
            ];

            $result = $dnsProvider->updateRecord($record, true);

            if ($result) {
                Log::info('DNS A record updated successfully', [
                    'domain' => $targetDomain,
                    'ip' => $expectedIp
                ]);

                return $this->api->dataMsg([
                    'success' => true,
                    'message' => "DNS A record updated to point to {$expectedIp}"
                ]);
            } else {
                return $this->api->dataMsg([
                    'success' => false,
                    'message' => 'Failed to update DNS record'
                ]);
            }

        } catch (\Exception $e) {
            Log::error('DNS record update failed', [
                'domain' => $targetDomain,
                'error' => $e->getMessage()
            ]);

            return $this->api->dataMsg([
                'success' => false,
                'message' => 'Failed to update DNS records: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Resolve domain to IP address using actual DNS lookup
     */
    private function resolveDomainToIp(string $domain): ?string
    {
        try {
            // Try to resolve A record
            $records = dns_get_record($domain, DNS_A);
            if (!empty($records)) {
                return $records[0]['ip'] ?? null;
            }

            // Try to resolve CNAME and then A record
            $cnameRecords = dns_get_record($domain, DNS_CNAME);
            if (!empty($cnameRecords)) {
                $target = $cnameRecords[0]['target'] ?? null;
                if ($target) {
                    $targetRecords = dns_get_record($target, DNS_A);
                    if (!empty($targetRecords)) {
                        return $targetRecords[0]['ip'] ?? null;
                    }
                }
            }

            return null;
        } catch (\Exception $e) {
            Log::info('DNS resolution failed', [
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get parent domain from a subdomain
     * e.g., www.webopinie.nl -> webopinie.nl
     * e.g., test01.dc2.exolog.tech -> dc2.exolog.tech
     * e.g., onlinedrogist.co.nl -> onlinedrogist.co.nl (already root domain)
     */
    private function getParentDomain(string $domain): string
    {
        $parts = explode('.', $domain);

        // If it's already a simple domain (2 parts), return as is
        if (count($parts) <= 2) {
            return $domain;
        }

        // Common multi-part TLDs that should be treated as single TLD
        $multiPartTlds = [
            'co.uk', 'co.nl', 'co.za', 'co.jp', 'co.kr', 'co.in', 'co.nz',
            'com.au', 'com.br', 'com.mx', 'com.ar', 'com.co', 'com.pe',
            'net.au', 'org.uk', 'ac.uk', 'gov.uk', 'edu.au', 'asn.au',
            'id.au', 'conf.au', 'info.au', 'oz.au', 'telememo.au'
        ];

        // Check if this domain uses a multi-part TLD
        foreach ($multiPartTlds as $tld) {
            if (str_ends_with($domain, '.' . $tld)) {
                $tldParts = explode('.', $tld);
                $tldPartCount = count($tldParts);

                // If domain has exactly the TLD parts + 1 domain part, it's already a root domain
                if (count($parts) === $tldPartCount + 1) {
                    return $domain; // Already root domain like onlinedrogist.co.nl
                }

                // If it has more parts, remove the first part to get parent
                // e.g., www.onlinedrogist.co.nl -> onlinedrogist.co.nl
                return implode('.', array_slice($parts, 1));
            }
        }

        // For standard TLDs (.com, .nl, .tech, etc.)
        // For domains like test01.dc2.exolog.tech, we want dc2.exolog.tech (remove first part)
        // For domains like www.webopinie.nl, we want webopinie.nl (remove first part)
        return implode('.', array_slice($parts, 1));
    }

    /**
     * Get the current site's hostname
     */
    private function getCurrentSiteHostname(): ?string
    {
        try {
            // Get from database (use the first active site with hostname)
            $site = \DB::table('site')
                ->whereNotNull('site_hostname')
                ->where('site_hostname', '!=', '')
                ->where('site_deleted', 0)
                ->orderBy('site_id')
                ->first();

            if ($site && $site->site_hostname) {
                Log::info('Found site hostname from database', [
                    'site_id' => $site->site_id,
                    'site_name' => $site->site_name,
                    'hostname' => $site->site_hostname
                ]);
                return $site->site_hostname;
            }

            Log::info('No site hostname found in database');
            return null;
        } catch (\Exception $e) {
            Log::error('Error getting current site hostname', ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * Resolve hostname to IP address using multiple methods
     */
    private function resolveHostnameToIp(string $hostname): string
    {
        try {
            // Method 1: DNS lookup via Cloudflare API (most reliable for our domains)
            $dnsProvider = DnsProviderFactory::make($hostname);
            if ($dnsProvider && $dnsProvider->exists()) {
                $records = $dnsProvider->getRecords();
                $aRecords = array_filter($records, function($record) use ($hostname) {
                    return $record['type'] === 'A' &&
                           ($record['name'] === $hostname || $record['name'] === '@');
                });

                if (!empty($aRecords)) {
                    $record = reset($aRecords);
                    $ip = $record['content'] ?? $record['value'] ?? '';
                    if ($ip && filter_var($ip, FILTER_VALIDATE_IP)) {
                        Log::info('Resolved hostname via DNS provider', [
                            'hostname' => $hostname,
                            'ip' => $ip
                        ]);
                        return $ip;
                    }
                }
            }

            // Method 2: Standard DNS lookup
            $ip = gethostbyname($hostname);
            if ($ip && $ip !== $hostname && filter_var($ip, FILTER_VALIDATE_IP)) {
                Log::info('Resolved hostname via gethostbyname', [
                    'hostname' => $hostname,
                    'ip' => $ip
                ]);
                return $ip;
            }

            Log::warning('Could not resolve hostname to IP', ['hostname' => $hostname]);
            return '0.0.0.0';
        } catch (\Exception $e) {
            Log::error('Error resolving hostname to IP', [
                'hostname' => $hostname,
                'error' => $e->getMessage()
            ]);
            return '0.0.0.0';
        }
    }

}