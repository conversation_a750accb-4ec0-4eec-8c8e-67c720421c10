<?php

namespace Exolog\Dealer\Controllers;

use Exolog\Dealer\ApiController;
use Exolog\Dealer\Handler\AdminHandler;
use Exolog\Dealer\Handler\AuthHandler;
use Exolog\Dealer\Models\Admin;


class AdminController extends ApiController
{

    public function me(AuthHandler $authHandler)
    {
        return $this->api->dataMsg(['admin' => $authHandler->getCurrentProfile()]);
    }

    public function get(AdminHandler $adminHandler)
    {
        $validated = request()->validate([
            'a_id' => 'required',
        ]);

        return $this->api->dataMsg($adminHandler->getProfileFromAdmin(Admin::findOrFail($validated['a_id'])));
    }

    public function all(AdminHandler $adminHandler)
    {

        $admins = Admin::all();

        $result = [];
        //todo use Resource
        foreach ($admins as $admin) {
            $result[] = $adminHandler->getProfileFromAdmin($admin);
        }

        return $this->api->dataMsg($result);
    }

    public function create(AdminHandler $adminHandler)
    {
        $validated = request()->validate([
            'admin.a_email' => 'required',
            'admin.a_name' => 'required',
            'admin.a_password' => 'required',
            'admin.a_issuper' => 'nullable',
        ]);

        return $this->api->dataMsg(['a_id' => $adminHandler->createAdmin($validated['admin'])->a_id]);
    }

    public function update(AuthHandler $authHandler, AdminHandler $adminHandler)
    {
        $validated = request()->validate([
            'admin.a_id' => 'required',
            'admin.a_name' => 'required',
            'admin.a_email' => 'required',
            'admin.a_password' => 'nullable',
            'admin.a_issuper' => 'nullable',
        ]);

        $admin = $validated['admin'];

        if (!($admin['a_id'] == $authHandler->getSessionAdmin()['a_id'] || $authHandler->isSessionSuper())
        ) {
            return $this->api->errorMsg(array('errorAlias' => 'auth'));
        }

        if (!$authHandler->isSessionSuper()) {
            $admin['a_issuper'] = false;
        }

        return $this->api->dataMsg($adminHandler->updateAdmin($admin));
    }

    public function delete(AuthHandler $authHandler, AdminHandler $adminHandler)
    {
        request()->validate([
            'a_id' => 'required',
        ]);

        $admin_id = request('a_id');

        if (!($admin_id == $authHandler->getSessionAdmin()['a_id'] || $authHandler->isSessionSuper())
        ) {
            return $this->api->errorMsg('You are not authorized!');
        }

        if (empty($admin_id)) {
            return $this->api->errorMsg(array('errorAlias' => 'required'));
        }

        return $this->api->dataMsg($adminHandler->deleteAdmin($admin_id));
    }
}

