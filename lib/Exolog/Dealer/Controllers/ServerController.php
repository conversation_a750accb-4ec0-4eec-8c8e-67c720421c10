<?php

namespace Exolog\Dealer\Controllers;

use Exolog\Dealer\ApiController;
use Exolog\Module\Version\GitlabVersionCheckService;


class ServerController extends ApiController
{

    public function config()
    {
        $result = [];
        $result['server_name'] = config('exolog.server_domain');
        $result['options'] = [];
        $result['options']['USE_SSL_FOR_SUBDOMAIN'] = config('dealer.domains.use_ssl_for_subdomain');

        return $this->api->dataMsg($result);
    }

    public function version()
    {
        $result = GitlabVersionCheckService::make()->getVersion();
        return $this->api->dataMsg($result);
    }
}