<?php

namespace Exolog\Dealer\Controllers;

use Exolog\Dealer\ApiController;
use Exolog\Dealer\Handler\AdminHandler;
use Exolog\Dealer\Handler\AuthHandler;
use Exolog\Dealer\Models\Admin;
use Exolog\Module\API\Api;
use Exolog\Module\Http\Request;
use Exolog\Module\Support\Facades\Auth;
use Exolog\Module\Users\Model\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Validation\ValidationException;
use PragmaRX\Google2FA\Exceptions\IncompatibleWithGoogleAuthenticatorException;
use PragmaRX\Google2FA\Exceptions\InvalidCharactersException;
use PragmaRX\Google2FA\Exceptions\SecretKeyTooShortException;
use PragmaRX\Google2FA\Google2FA;

/**
 *
 * API for auth in Dealer admin Panel
 */
class AuthController extends ApiController
{
    private $guard;

    public function __construct(Api $api)
    {
        $this->guard = Auth::guard('dealer');
        parent::__construct($api);
    }

    /**
     * @throws ValidationException
     */
    public function login(Request $request, AdminHandler $adminHandler, AuthHandler $authHandler)
    {

        $admin = $this->validateCredentials($request);

        if (!empty($admin['a_options']['ga_secret'])) {

            $gaCode = request('gaCode');

            if (empty($gaCode)) {
                return $this->api->dataMsg(['is_2fa_enabled' => true]);
            }

            $ga_secret = $admin['a_options']['ga_secret'];
            $google2fa = new Google2FA();

            //check if admin right setup generator
            try {
                if (!$google2fa->verify($gaCode, $ga_secret)) {
                    return $this->api->errorMsg("Wrong code. Please try again.");
                }
            } catch (IncompatibleWithGoogleAuthenticatorException $e) {
                return $this->api->errorMsg("Wrong code. Please try again. IncompatibleWithGoogleAuthenticatorException");
            } catch (InvalidCharactersException $e) {
                return $this->api->errorMsg("Wrong code. Please try again. InvalidCharactersException");
            } catch (SecretKeyTooShortException $e) {
                return $this->api->errorMsg("Wrong code. Please try again. SecretKeyTooShortException");
            }
        }

        $this->guard->login($admin);

        if ($request->hasSession()) {
            $request->session()->regenerate();
        }


        $admin->a_loggedin++;
        $admin->a_lastlogin = now();
        $admin->save();

        return $this->api->dataMsg(['admin' => $adminHandler->getProfileFromAdmin($admin)]);
    }

    public function logout()
    {
        $this->guard->logout();
        return $this->api;
    }


    public function create2FA()
    {
        $ga = new Google2FA();
        $ga_secret = $ga->generateSecretKey();

        session()->put('ga_secret', $ga_secret);

        $codeURL = (sprintf("otpauth://totp/%s?secret=%s&issuer=%s",
            $this->guard->user()['a_email'],
            $ga_secret,
            request()->getHttpHost()));
        $imgURL = $codeURL;

        return $this->api->dataMsg([
            'barcode' => $imgURL,
            'code' => $ga_secret
        ]);
    }

    public function enable2FA(AdminHandler $adminHandler)
    {
        $ga_secret = session()->get('ga_secret');
        $ga = new Google2FA();
        $gaCode = request('gaCode');
        if (!$ga->verify($gaCode, $ga_secret)) {
            return $this->api->errorMsg("Wrong code. Please try again.");
        }

        $admin = request()->user();
        $admin->setAttribute('a_options->ga_secret', $ga_secret);
        $admin->save();

        return $this->api;

    }

    public function disable2FA()
    {

        $admin_id = request('a_id');
        /** @var Admin $current_admin */
        $current_admin = $this->guard->user();

        if (!($current_admin->a_issuper || $admin_id === $this->guard->id())) {
            return $this->api->errorMsg(array('errorAlias' => 'auth'));
        }

        /** @var Admin $admin */
        $admin = Admin::find($admin_id);

        $options = $admin->a_options;
        unset($options['ga_secret']);
        $admin->a_options = $options;

        $admin->save();

        return $this->api;

    }

    public function getSiteAuth($site_id, AuthHandler $authHandler)
    {
        dealer()->setSiteId($site_id);

        $result = User::resolveOrFail($authHandler->getSessionAdmin()['a_email'])->genUserLoginLink(
            ['redir' => '/exo_admin/']
        );
        if ($result) {
            return Redirect::to($result);
        }

        return 'Error while getting link. Check the existence of your user on this site!';
    }

    public function sendPasswordLink(AdminHandler $adminHandler)
    {

        $a_email = request('a_email');

        if (empty($a_email)) {
            return $this->api->errorMsg(['errorAlias' => 'required']);
        }

        $adminHandler->sendAdminPasswordLink($a_email);

        return $this->api->dataMsg(true);
    }

    public function changePassword(AdminHandler $adminHandler)
    {
        $a_email = request('a_email');
        $password = request('password');
        $hash = request('hash');

        if (empty($hash) || empty($password) || empty($a_email)) {
            return $this->api->errorMsg(array('errorAlias' => 'required'));
        }

        /** @var Admin $admin */
        $admin = Admin::query()->where('a_email', $a_email)->firstOrFail();

        $settings = $admin['a_options'];
        $token = $settings['token'];
        if (empty($token)) {
            return $this->api->errorMsg(array('error' => "Not valid password change link."));
        }
        if ($token['hash'] !== $hash) {
            return $this->api->errorMsg(array('error' => "Not valid password change link."));

        }
        $now = time();
        $time = (int)$token['time'];
        $ttl = (int)$token['ttl'];
        $ttl *= 3600;
        $diff = $now - $time;
        if ($diff <= $ttl) {
            unset($settings[$token]);
            $admin->a_options = $settings;
            $admin['a_password'] = Hash::make($password);
            $admin->save();

            $result = [];
            if (!empty($admin['a_options']['ga_secret'])) {
                $result['is_2fa_enabled'] = true;
            }

            return $this->api->dataMsg($result);
        }
        return $this->api->errorMsg(['error' => "Password change link is out of date."]);
    }

    private function validateCredentials($request): Admin
    {

        $model = $this->guard->getProvider()->getModel();

        return tap($model::where('a_email', $request->a_email)->first(), function ($user) use ($request) {
            if (!$user || !$this->guard->getProvider()->validateCredentials($user,
                    ['password' => $request->password])) {

                $this->throwFailedAuthenticationException($request);
            }
        });
    }

    /**
     * @throws ValidationException
     */
    private function throwFailedAuthenticationException($request): void
    {
        throw ValidationException::withMessages([
            'a_email' => [trans('auth.failed')],
        ]);
    }
}
