<?php

namespace Exolog\Dealer\Controllers;

use Exolog\Dealer\ApiController;
use Exolog\Dealer\DealerException;
use Exolog\Dealer\Handler\AdminHandler;
use Exolog\Dealer\Handler\AuthHandler;
use Exolog\Dealer\Handler\DomainHandler;
use Exolog\Dealer\Handler\SiteHandler;
use Exolog\Dealer\Models\Admin;
use Exolog\Module\Domains\Model\Domain;
use Exolog\Module\Http\Request;
use Exolog\Module\Site\Model\Site;
use Exolog\Module\Skeleton\SkeletonService;
use Exolog\Module\Support\Facades\Vars;
use Illuminate\Database\Eloquent\Builder;

class SiteController extends ApiController
{
    public function all(SiteHandler $siteHandler, AuthHandler $authHandler)
    {
        $query = Site::query()->with('domains');

        if (request('trashed')) {
            $query->onlyTrashed();
        }

        if (!$authHandler->isSessionSuper()) {
            $email = $authHandler->getSessionAdmin()['a_email'];
            $query->whereHas('users', function (Builder $q) use ($email) {
                $q->where('u_email', $email);
            });
        }
        $sites = $query->get();

        $sites = $siteHandler->performChecks($sites);

        $sites->each(function (Site $site) {
            $site['main_domain_name'] = Domain::crossSiteQuery()
                ->where('domain_site', $site->site_id)
                ->where('domain_ismain', 1)
                ->value('domain_name');
        });
        //TODO Add resource
        /*
           site_id,
                 site_alias,
                 site_name,
        */

        return $this->api->dataMsg($sites);
    }

    public function get(DomainHandler $domainHandler)
    {
        $site_id = request('site_id');

        $site = Site::findOrFail($site_id);

        $site['vars'] = Vars::getAll(['var_type' => 'site', 'var_parent' => $site_id]);
        $site['domains'] = $domainHandler->getSiteDomains();

        return $this->api->dataMsg($site);
    }

    /**
     * @throws DealerException
     */
    public function create(SiteHandler $siteHandler)
    {
        $site = request('site');

        if (empty($site['site_alias'])) {
            return $this->api->errorMsg(array('errorAlias' => 'required'));
        }
        if (empty($site['site_name'])) {
            return $this->api->errorMsg(array('errorAlias' => 'required'));
        }

        return $this->api->dataMsg(['site_id' => $siteHandler->createSite($site)]);
    }


    /**
     * @throws DealerException
     */
    public function update(SiteHandler $siteHandler, DomainHandler $domainHandler)
    {
        $site = request('site');

        if (empty($site['site_alias'])) {
            return $this->api->errorMsg(array('errorAlias' => 'required'));
        }
        if (empty($site['site_name'])) {
            return $this->api->errorMsg(array('errorAlias' => 'required'));
        }

        $updatedSite = $siteHandler->updateSite($site);

        // Include updated domains in the response
        $updatedSite['domains'] = $domainHandler->getSiteDomains();

        return $this->api->dataMsg($updatedSite);
    }

    public function softDelete(SiteHandler $siteHandler)
    {
        $site_id = request('site_id');
        if (empty($site_id)) {
            return $this->api->errorMsg(array('errorAlias' => 'required'));
        }
        $siteHandler->softDeleteSite($site_id);

        return $this->api->dataMsg(true);

    }

    public function softUndelete(SiteHandler $siteHandler)
    {
        $site_id = request('site_id');
        if (empty($site_id)) {
            return $this->api->errorMsg(array('errorAlias' => 'required'));
        }
        $siteHandler->softUndeleteSite($site_id);
        return $this->api->dataMsg(true);
    }

    /**
     * @throws DealerException
     */
    public function delete(SiteHandler $siteHandler)
    {
        $site_id = request('site_id');

        if (empty($site_id)) {
            return $this->api->errorMsg(array('errorAlias' => 'required'));
        }
        $siteHandler->deleteSite($site_id);
        return $this->api;
    }

    public function addUser(SiteHandler $siteHandler, Request $request)
    {
        $validated = $request->validate([
            'site_id' => 'required',
            'admin_id' => 'required',
        ]);

        $admin = Admin::findOrFail($validated['admin_id']);
        return $this->api->dataMsg($siteHandler->createSiteUserByAdmin($validated['site_id'], $admin));
    }

    public function getAdminsNotUser(Request $request, AdminHandler $adminHandler)
    {

        $validated = $request->validate([
            'site_id' => 'required',
        ]);

        $site_id = $validated['site_id'];

        $admins = Admin::query()->whereNotIn('a_email',
            function (\Illuminate\Database\Query\Builder $query) use ($site_id) {
                $query->select('u_email')
                    ->from('user')
                    ->where('u_site', $site_id);

            })->get();

        $result = [];

        foreach ($admins as $admin) {
            $result[] = $adminHandler->getProfileFromAdmin($admin);
        }

        return $this->api->dataMsg($result);
    }

    public function checkAliasUnique(SiteHandler $siteHandler)
    {
        $result = [];

        $alias = request('alias');

        if ($siteHandler->checkAliasUnique($alias, 0)) {
            $result['valid'] = false;
            $result['message'] = 'This alias already exists';
            return $this->api->dataMsg($result);
        }

        if ($siteHandler->checkAliasUnique($alias, 1)) {
            $result['valid'] = false;
            $result['message'] = 'This alias already exists in the deleted sites! Please delete site permanently to use this alias!';
            return $this->api->dataMsg($result);
        }

        $result['valid'] = true;
        $result['message'] = '';

        return $this->api->dataMsg($result);
    }

    public function buildSkeleton(Request $request)
    {
        $validated = $request->validate([
            'site_id' => 'required',
        ]);

        dealer()->setSiteId($validated['site_id']);
        SkeletonService::make()->build();

        return $this->api;
    }

    public function fixDefaultEdition(Request $request, SiteHandler $siteHandler)
    {
        $validated = $request->validate([
            'site_id' => 'required',
        ]);

        $siteHandler->insertDefaultEdition($validated['site_id']);

        return $this->api->dataMsg(true);
    }

}
