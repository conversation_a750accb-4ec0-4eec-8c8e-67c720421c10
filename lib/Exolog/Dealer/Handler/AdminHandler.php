<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 21.11.2018
 * Time: 14:57
 */

namespace Exolog\Dealer\Handler;


use Exolog\Dealer\Mail\AdminPasswordChangeLink;
use Exolog\Dealer\Models\Admin;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;


class AdminHandler
{

    //todo replace with resource
    public function getProfileFromAdmin($admin)
    {
        $profile = [];
        $profile['a_id'] = $admin['a_id'];
        $profile['a_email'] = $admin['a_email'];
        $profile['a_issuper'] = $admin['a_issuper'];
        $profile['a_name'] = $admin['a_name'];
        $profile['a_info'] = $admin['a_info'];
        $profile['a_datenew'] = $admin['a_datenew'];
        $profile['a_lastlogin'] = $admin['a_lastlogin'];
        $profile['a_loggedin'] = $admin['a_loggedin'];
        $profile['is2fa'] = !empty($admin['a_options']['ga_secret']);

        return $profile;
    }

    public function createAdmin(array $adminAttributes): Admin
    {
        $admin = new Admin($adminAttributes);
        $admin->a_password = Hash::make($adminAttributes['a_password']);
        $admin->save();

        return $admin;
    }

    public function updateAdmin(array $adminAttributes): Admin
    {

        if (isset($adminAttributes['a_password'])) {
            $adminAttributes['a_password'] = Hash::make($adminAttributes['a_password']);
        }

        /** @var Admin $admin */
        $admin = Admin::query()->findOrFail($adminAttributes['a_id']);

        $admin->fill($adminAttributes);
        $admin->save();

        return $admin;

    }

    public function deleteAdmin($admin_id): bool
    {
        $admin = Admin::query()->findOrFail($admin_id);
        $admin->delete();
        return true;
    }


    public function sendAdminPasswordLink(string $a_email): void
    {
        /** @var Admin $admin */
        $admin = Admin::query()->where('a_email', $a_email)->firstOrFail();
        $link = $this->generateChangePasswordLink($admin);
        Mail::to($a_email)->send(new AdminPasswordChangeLink($admin, $link));
    }

    public function generateChangePasswordLink(Admin $admin): string
    {
        $token = [
            'a_id' => $admin['a_id'],
            'hash' => Str::random(),
            'time' => time(),
            'ttl' => 24
        ];

        $settings = $admin['a_options'];
        $settings['token'] = $token;
        $admin->a_options = $settings;
        $admin->save();

        return route('dealer.change-password', ['hash' => $token['hash'], 'email' => $admin['a_email']]);
    }
}