<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 21.11.2018
 * Time: 14:57
 */

namespace Exolog\Dealer\Handler;


use Exception;
use Exolog\Dealer\DealerException;
use Exolog\Module\Mail\PosteDomainHandler;
use Exolog\Module\Support\Concerns\HasMakeFactory;
use Exolog\Module\Support\Facades\Vars;


class MailhostHandler
{

    use HasMakeFactory;

    private int $site_id;

    /**
     * @throws DealerException
     */
    public function __construct($site_id)
    {

        if (empty($site_id)) {
            throw new DealerException('Site id is not defined!');
        }

        $this->site_id = $site_id;

    }

    /**
     * @param $mailhost
     * @throws DealerException
     * @throws Exception
     */
    public function deleteHost(string $mailhost): void
    {
        $posteDomainHandler = new PosteDomainHandler($this->site_id);
        $posteDomainHandler->deleteHost($mailhost);
    }

    /**
     * @param $mail<PERSON><PERSON>
     * @throws DealerException
     * @throws Exception
     */
    public function softDeleteHost(string $mailhost): void
    {
        $posteDomainHandler = new PosteDomainHandler($this->site_id);
        $posteDomainHandler->softDeleteHost($mailhost);
    }

    /**
     * @param $mailhost
     * @throws DealerException
     * @throws Exception
     */
    public function softUndeleteHost(string $mailhost): void
    {
        $posteDomainHandler = new PosteDomainHandler($this->site_id);
        $posteDomainHandler->softUndeleteHost($mailhost);
    }

    /**
     * @throws Exception
     */
    public function saveHost(string $mailhost)
    {
        $posteDomainHandler = new PosteDomainHandler($this->site_id);
        $posteDomainHandler->saveHost($mailhost);
    }

    /**
     * determine ex-W24-228 thingy that gets prefixed to accountuser usernames.
     *
     * @return string
     */
    private function getPrefix(): string
    {
        $site_id = $this->site_id;

        return "ex-" . config('exolog.server_name') . "-" . $site_id;
    }

    private function getMaxaccounts()
    {
        return Vars::site('mail_maxaccounts', 10);
    }

    private function getQuota()
    {
        return Vars::site('mail_quota', 20000);
    }
}