<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 21.11.2018
 * Time: 14:57
 */

namespace Exolog\Dealer\Handler;


use Exolog\Dealer\Models\Admin;
use Exolog\Module\Support\Facades\Auth;

class AuthHandler
{
    private AdminHandler $adminHandler;

    public function __construct(AdminHandler $adminHandler)
    {
        $this->adminHandler = $adminHandler;
    }

    public function getSessionAdmin(): ?Admin
    {
        /** @var Admin */
        return Auth::guard('dealer')->user();
    }

    public function isSessionSuper(): bool
    {
        return $this->getSessionAdmin()->a_issuper;
    }

    public function getCurrentProfile()
    {
        return $this->adminHandler->getProfileFromAdmin($this->getSessionAdmin());
    }
}