<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 21.11.2018
 * Time: 14:57
 */

namespace Exolog\Dealer\Handler;


use Exception;
use Exolog\Dealer\Checker\SiteDomainsChecker;
use Exolog\Dealer\DealerException;
use Exolog\Module\API\Api;
use Exolog\Module\Domains\DomainSslStatus;
use Exolog\Module\Domains\Model\Domain;
use Exolog\Module\OpenProvider\OpenProvider;
use Exolog\Module\Support\Concerns\HasMakeFactory;
use Exolog\Module\Support\Facades\Path;
use Exolog\Module\Support\Facades\Site;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use RuntimeException;


class DomainHandler
{
    use HasMakeFactory;

    private Api $api;

    public function __construct(Api $api)
    {
        $this->api = $api;
    }

    public function getSiteDomains(): Collection
    {
        $domains = Domain::all();

        foreach ($domains as $domain) {
            $domain['ssl'] = $this->getSslFiles($domain);
            $domain['is_readonly'] = $this->isReadOnlyDomain($domain);
        }
        return $domains;
    }

    /**
     * @param $domain
     * @return void
     */
    private function saveSiteDomain($domain): void
    {
        // Debug logging to identify the "ismail" issue
        Log::info('🔧 saveSiteDomain called with domain data:', [
            'domain_data' => $domain,
            'domain_id_value' => $domain['domain_id'] ?? 'not_set',
            'domain_id_type' => gettype($domain['domain_id'] ?? null),
            'all_keys' => array_keys($domain)
        ]);

        // Check if domain is stuck in processing state and reset if needed
        if ($this->getDomainSslStatus($domain['domain_id']) === DomainSslStatus::DOMAIN_SSL_STATUS_PROCESSING) {
            // Check if domain has been processing for too long (more than 10 minutes)
            if ($this->isDomainStuckInProcessing($domain['domain_id'])) {
                Log::warning('Domain stuck in PROCESSING state, resetting to OK', [
                    'domain_id' => $domain['domain_id'],
                    'domain_name' => $domain['domain_name'] ?? 'unknown'
                ]);
                $this->setDomainSslStatus($domain['domain_id'], DomainSslStatus::DOMAIN_SSL_STATUS_OK);
            } else {
                // Still actively processing, block the update
                throw new RuntimeException('The domain is processed by an external handler!');
            }
        }

        $origDomain = Domain::find($domain['domain_id']);

        $domainDB = Domain::saveFromArray($domain);

        // If this is a new subdomain, create A record in Cloudflare
        if ($origDomain === null && $this->isSubdomain($domainDB->domain_name)) {
            $this->createSubdomainARecord($domainDB->domain_name);
        }

        if ($domainDB->domain_isletsencrypt === 0) {
            $this->putSslFiles($domain);
        }

        if ($origDomain === null ||
            $origDomain->domain_isssl !== $domainDB->domain_isssl ||
            $origDomain->domain_isletsencrypt !== $domainDB->domain_isletsencrypt) {
            //todo replace with buffer log
            $this->api->logMsgPush("Start runHandlerBackground. onDomainUpdate {$domainDB->domain_id}");
            $this->runExternalHandler('onDomainUpdate', $domainDB);
        }
    }

    public function setDomainSslStatus($domain_id, $domain_ssl_status): void
    {
        /** @var Domain $domain */
        $domain = Domain::crossSiteQuery()->findOrFail($domain_id);
        $domain->domain_ssl_status = $domain_ssl_status;
        $domain->save();
    }

    private function deleteSiteDomain($domain_id): void
    {
        $origDomain = Domain::findOrFail($domain_id);

        Log::info('Deleting domain from database', [
            'domain_id' => $domain_id,
            'domain_name' => $origDomain->domain_name
        ]);

        // Delete the domain from database immediately
        // Don't wait for external handler to complete
        Domain::destroy($domain_id);

        // Run external handler for cleanup (SSL certs, proxy config, etc.)
        // This is for cleanup only - domain is already deleted from DB
        try {
            $this->runExternalHandler('onDomainDelete', $origDomain);
        } catch (\Exception $e) {
            Log::warning('External domain deletion handler failed, but domain was deleted from database', [
                'domain_id' => $domain_id,
                'domain_name' => $origDomain->domain_name,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * @param $site_id
     * @param $domains
     * @throws DealerException
     */
    public function updateSiteDomains($site_id, array $domains): void
    {
        Log::info('🔧 === UPDATING SITE DOMAINS ===', [
            'site_id' => $site_id,
            'incoming_domains_count' => count($domains),
            'incoming_domains' => $domains
        ]);

        if (empty($domains)) {
            return;
        }

        //looking for main domain in site's domain
        $mainDomain = null;

        $default_edition_id = getDefaultEdition();

        $domains = $this->addWebmailRelatedHost($domains);

        foreach ($domains as $domain) {
            if ($domain['domain_ismain'] === 1) {
                $mainDomain = $domain['domain_name'];
            }
        }

        foreach ($domains as $index => $domain) {
            //set default edition for new or empty domains
            if (empty($domain['domain_edition_id'])) {
                $domains[$index]['domain_edition_id'] = $default_edition_id;
            }

            //for creation while no main, set alias.server.name as main and default
            if (SiteDomainsChecker::make()->isAliasDomain($domain['domain_name'], Site::alias())) {
                if (empty($mainDomain)) {
                    $mainDomain = $domains[$index]['domain_name'];
                    $domains[$index]['domain_ismain'] = 1;
                    $domains[$index]['domain_isdefualt'] = 1;
                }
            }
        }

        $ids = Arr::pluck($domains, 'domain_id');

        // Filter out null values and ensure all IDs are integers
        $validIds = array_filter($ids, function($id) {
            return $id !== null && is_numeric($id) && $id > 0;
        });

        // Convert to integers
        $validIds = array_map('intval', $validIds);

        Log::info('🔧 Valid domain IDs from incoming array:', [
            'valid_ids' => $validIds
        ]);

        // Get all existing domains for this site and delete ones not in the new array
        $existingDomains = Domain::query()->get(); // This uses site scope automatically

        Log::info('🔧 Existing domains in database:', [
            'existing_domains' => $existingDomains->map(function($d) {
                return ['id' => $d->domain_id, 'name' => $d->domain_name];
            })->toArray()
        ]);

        foreach ($existingDomains as $existingDomain) {
            // If this existing domain is not in the new domains array, delete it
            if (!in_array($existingDomain->domain_id, $validIds)) {
                Log::info('🗑️ Deleting domain not found in updated domains array', [
                    'domain_id' => $existingDomain->domain_id,
                    'domain_name' => $existingDomain->domain_name,
                    'site_id' => $site_id
                ]);
                $this->deleteSiteDomain($existingDomain->domain_id);
            } else {
                Log::info('🔧 Keeping domain found in updated domains array', [
                    'domain_id' => $existingDomain->domain_id,
                    'domain_name' => $existingDomain->domain_name
                ]);
            }
        }

        foreach ($domains as $domain) {
            $this->saveSiteDomain($domain);
        }
    }


    public function getDomainSslStatus($domain_id): ?string
    {
        if ($domain = Domain::find($domain_id)) {
            return $domain->domain_ssl_status;
        }
        return null;
    }

    public function getSslPath($domain): string
    {
        return Path::getSiteConfPath() . '/ssl/' . $domain['domain_name'] . '/';
    }

    /**
     * @param $domain
     */
    protected function putSslFiles($domain): void
    {
        $exolog = exolog();
        $path = $this->getSslPath($domain);

        if (!isset($domain['ssl'])) {
            return;
        }

        $ssl = $domain['ssl'];

        $ssl['cert'] = trim($ssl['cert']);
        $ssl['private_key'] = trim($ssl['private_key']);
        $ssl['CA'] = trim($ssl['CA']);

        if (!file_exists(Path::getSiteConfPath() . '/ssl/')) {
            mkdir(Path::getSiteConfPath() . '/ssl/', 0766);
        }

        if (!file_exists($path)) {
            mkdir($path, 0766);
        }

        if (!empty($ssl['cert'])) {
            file_put_contents($path . 'cert.crt', $ssl['cert']);
        }

        if (!empty($ssl['private_key'])) {
            file_put_contents($path . 'private.key', $ssl['private_key']);
        }

        if (!empty($ssl['CA'])) {
            file_put_contents($path . 'CA.crt', $ssl['CA']);
        }

        if (!empty($ssl['cert']) && !empty($ssl['CA'])) {
            file_put_contents($path . 'fullchain.crt', $ssl['cert'] . "\n" . $ssl['CA']);
        }
    }

    protected function getSslFiles(Domain $domain): array
    {
        $path = Path::getSiteConfPath() . '/ssl/' . $domain->domain_name . '/';

        $ssl = array('cert' => '', 'private_key' => '', 'CA' => '');

        if (file_exists($path . 'cert.crt')) {
            $ssl['cert'] = file_get_contents($path . 'cert.crt');
        }

        if (file_exists($path . 'private.key')) {
            $ssl['private_key'] = file_get_contents($path . 'private.key');
        }


        if (file_exists($path . 'CA.crt')) {
            $ssl['CA'] = file_get_contents($path . 'CA.crt');
        }

        return $ssl;
    }

    public function checkDomainIsOpenProvider($domain_name)
    {
        return Domain::isDomainOpenProvider($this->getRootDomain($domain_name));
    }

    /**
     * @param $domain_name
     * @return bool
     * @throws DealerException
     * @throws Exception
     */
    protected function addRecAcmeChallenge($domain_name)
    {
        $openProvider = new OpenProvider();

        $rootDomain = $this->getRootDomain($domain_name);

        $request = $openProvider->getDns(array('domain' => $rootDomain, 'records' => true));

        $domain = $request->getValue();

        if (empty($domain['records'])) {
            throw new DealerException('Can\'t find domain DNS records for this domain.
         To use Let’s Encrypt SSL Domain must be under control OpenProvider.');
        }

        $found = false;

        foreach ($domain['records'] as $record) {
            if ($record['name'] == '_acme-challenge.' . $domain_name && $record['type'] === 'CNAME') {
                if ($record['value'] === '_acme-challenge.exologssl.nl') {
                    return true;
                } else {
                    $record['value'] = '_acme-challenge.exologssl.nl';
                    $found = true;
                    break;
                }
            }
        }

        if (!$found) {
            $domain['records'][] = array(
                'name' => "_acme-challenge." . $domain_name,
                'ttl' => "900",
                'type' => "CNAME",
                'value' => "_acme-challenge.exologssl.nl"
            );
        }

        $data = $openProvider->updateDns($domain);

        if (!empty($data) && $data->getFaultCode() != 0) {
            Log::error('DNS record update error!', ['domain' => $domain, 'data' => $data->getRaw()]);
            throw new DealerException('DNS record update error!. ' . $data->getFaultCode() . ' - ' . $data->getFaultString() . ' Value:' . $data->getValue());
        }

        return true;

    }

    protected function runExternalHandler($handler, Domain $domain, $force = 0)
    {
        //todo log replace with buffer
        //do not use outer handler for subdomain (RUN_HANDLER_FOR_SUBDOMAIN) by default
        if (!config('dealer.domains.use_ssl_for_subdomain') && $domain->isSubDomain()) {
            return false;
        }

        $this->setDomainSslStatus($domain->domain_id, DomainSslStatus::DOMAIN_SSL_STATUS_PROCESSING);

        $this->api->infoMsgPush("Domain \"$domain->domain_name\" is in the queue for processing. Please wait...");

        $site_id = dealer()->getSiteId();

        Artisan::queue('dealer:execute-handler', [
                'site_id' => $site_id,
                'domain_id' => $domain->domain_id,
                'handler' => $handler,
                'force' => $force
            ]
        );
    }

    /**
     * @throws DealerException
     */
    public function execHandler($domain_id, $handler, $force = 0): int
    {
        $domain = Domain::findOrFail($domain_id);

        $certPath = $this->getSslPath($domain);

        $handlers = config('dealer.domains.handlers');
        $handlerScript = $handlers[$handler];

        if (empty($handlerScript)) {
            throw new DealerException('Invalid handler parameter');
        }

        if ($handler === 'onDomainUpdate') {
            if ($domain->domain_isssl === 1 && $domain->domain_isletsencrypt === 1) {
                if (config('dealer.domains.setup_acme_challenge_le')) {
                    if ($this->checkDomainIsOpenProvider($domain->domain_name)) {
                        try {
                            $this->addRecAcmeChallenge($domain->domain_name);
                        } catch (Exception $e) {
                            $this->setDomainSslStatus($domain->domain_id,
                                DomainSslStatus::DOMAIN_SSL_STATUS_ERROR_ADD_ACME);
                            throw $e;
                        }
                    }
                }
            }
        }


        $host = $domain->domain_name;
        $use_ssl = $domain->domain_isssl;
        $use_le = $domain->domain_isletsencrypt;
        $force = $force ?? 0;

//update
########   EXIT CODES #########
#  0 - OK                     #
#  1 - ERROR                  #
#  2 - NOT FQDN               #
#  3 - NO CNAME for challenge #
###############################

//delete
########   EXIT CODES #########
#  0 - OK                     #
#  1 - ERROR                  #
#  2 - NOT FQDN               #
###############################

        consoleLog("exec $handlerScript $certPath $host $use_ssl $use_le $force");
        exec("$handlerScript $certPath $host $use_ssl $use_le $force", $output, $result_code);

        switch ($result_code) {
            case 0:
                $result = DomainSslStatus::DOMAIN_SSL_STATUS_OK;
                break;
            case 1:
                $result = DomainSslStatus::DOMAIN_SSL_STATUS_ERROR;
                break;
            case 2:
                $result = DomainSslStatus::DOMAIN_SSL_STATUS_ERROR_NOT_FQDN;
                break;
            case 3:
                $result = DomainSslStatus::DOMAIN_SSL_STATUS_ERROR_NO_CNAME;
                break;
            case 4:
                $result = DomainSslStatus::DOMAIN_SSL_STATUS_ERROR_ACCMESH_CERT;
                break;
            default :
                $result = DomainSslStatus::DOMAIN_SSL_STATUS_ERROR;
        }
        consoleLog('Script output:');
        consoleLog($output);
        consoleLog('External scripts result:' . $result);

        if ($handler === 'onDomainUpdate') {
            $this->setDomainSslStatus($domain['domain_id'], $result);
            consoleLog('Domain updated in DB');
        } elseif ($handler === 'onDomainDelete') {
            // Domain is already deleted in deleteSiteDomain method
            // This is just cleanup/external handler completion
            consoleLog('Domain deletion external handler completed');
        }

        if ($result_code) {
            Log::stack(['exolog-critical'])->error(
                "External handler:
            exec $handlerScript $certPath $host $use_ssl $use_le $force 
            Result:$result",
                ['output' => $output]
            );
        }

        return $result_code;
    }

    protected function getRootDomain($url)
    {
        return preg_replace('/^(?:https?:\/\/)?(?:[^\/]+\.)?([^.\/]+\.[^.\/]+).*$/', '$1', $url);
    }

    /**
     * @throws DealerException
     */
    public function addAliasDomain()
    {
        if (!Site::isInit()) {
            throw new DealerException('Exolog was not initialized.');
        }
        $domains = $this->getSiteDomains();
        $domains = $domains->each(function (Domain $domain) {
            $domain['domain_ismain'] = 0;
        })->toArray();

        $domains[] = [
            'domain_id' => null,
            'domain_name' => strtolower(Site::alias() . '.' . config('exolog.server_domain')),
            'domain_isssl' => 1,
            'domain_ismain' => 1,
            'domain_isletsencrypt' => 1,
            'is_readonly' => 1,
        ];
        $this->updateSiteDomains(Site::id(), $domains);
    }

    /**
     * @throws DealerException
     */
    public function fixAliasDomain()
    {
        if (!Site::isInit()) {
            throw new DealerException('Exolog site was not initialized.');
        }

        $domains = $this->getSiteDomains();

        foreach ($domains as $key => $domain) {
            //site can have only one alias.server
            if (SiteDomainsChecker::make()->invalidAliasDomain($domain['domain_name'], Site::alias())) {
                unset($domains[$key]);
            }
        }
        $this->updateSiteDomains(Site::id(), $domains);

        $domains = $this->getSiteDomains();

        $checks = SiteDomainsChecker::make()->checkDomains($domains, Site::alias());
        if (!$checks['haveAliasDomain']) {
            $this->addAliasDomain();
        }
    }

    /**
     * @param $domain_id
     * @param int $force
     */
    public function obtainSSL($domain_id, int $force = 1): void
    {
        $domain = Domain::findOrFail($domain_id);
        $this->runExternalHandler('onDomainUpdate', $domain, $force);
    }

    private function isReadOnlyDomain($domain): int
    {
        return SiteDomainsChecker::make()->isAliasDomain($domain['domain_name'], Site::alias()) ? 1 : 0;
    }


    /**
     * If site has webmail.* domain add parent domain as well as mailhost only (no ssl, no update DNS)
     */
    private function addWebmailRelatedHost(array $domains): array
    {
        foreach ($domains as $domain) {
            if (Str::startsWith($domain['domain_name'], 'webmail.')) {
                $parentDomainName = Str::after($domain['domain_name'], 'webmail.');
                $parentDomain = collect($domains)->firstWhere('domain_name', $parentDomainName);
                if (is_null($parentDomain)) {
                    $parentDomain = [
                        'domain_id' => null,
                        'domain_name' => $parentDomainName,
                        'domain_ismain' => 0,
                        'domain_isdefault' => 0,
                        'domain_isssl' => 0,
                        'domain_isletsencrypt' => 1,
                        'domain_ismail' => 1,
                        'ignore_dns' => true
                    ];

                    $domains[] = $parentDomain;
                }
            }
        }

        return $domains;
    }

    /**
     * Check if a domain is a subdomain (contains more than one dot and is not a top-level domain)
     */
    private function isSubdomain(string $domainName): bool
    {
        // Count dots in domain name
        $dotCount = substr_count($domainName, '.');

        // A subdomain has more than one dot (e.g., mail.example.com has 2 dots)
        // But we need to exclude common top-level domains like example.co.uk
        if ($dotCount < 2) {
            return false;
        }

        // Get the parent domain (everything after the first subdomain)
        $parts = explode('.', $domainName);
        if (count($parts) < 3) {
            return false;
        }

        // Skip server domain subdomains (like alias.server.domain)
        $serverDomain = config('exolog.server_domain');
        if ($serverDomain && str_ends_with($domainName, $serverDomain)) {
            return false;
        }

        return true;
    }

    /**
     * Create A record for subdomain in Cloudflare
     */
    private function createSubdomainARecord(string $subdomainName): void
    {
        try {
            Log::info('🔧 Creating A record for new subdomain', [
                'subdomain' => $subdomainName
            ]);

            // Extract parent domain from subdomain
            $parts = explode('.', $subdomainName);
            if (count($parts) < 3) {
                Log::warning('Invalid subdomain format, skipping DNS creation', [
                    'subdomain' => $subdomainName
                ]);
                return;
            }

            // Get parent domain (remove first part)
            array_shift($parts);
            $parentDomain = implode('.', $parts);

            // Get server IP from site's hostname (like anastaciatoistonog.dc2.exolog.tech)
            $serverIp = $this->getSiteHostnameIp();
            if (!$serverIp || $serverIp === '0.0.0.0') {
                Log::warning('Could not determine server IP from site hostname for subdomain', [
                    'subdomain' => $subdomainName
                ]);
                return;
            }

            // Use CloudflareDnsSetup to create A record
            $dnsSetup = new \Exolog\Module\DNS\CloudflareDnsSetup($parentDomain);
            $result = $dnsSetup->setupSubdomainARecord($subdomainName, $serverIp);

            if ($result) {
                Log::info('✅ Subdomain A record created successfully', [
                    'subdomain' => $subdomainName,
                    'parent_domain' => $parentDomain,
                    'ip' => $serverIp
                ]);
            } else {
                Log::warning('❌ Failed to create subdomain A record', [
                    'subdomain' => $subdomainName,
                    'parent_domain' => $parentDomain
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Exception while creating subdomain A record', [
                'subdomain' => $subdomainName,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Get IP address from the current site's hostname
     * This uses the hostname from the "Common" tab (like anastaciatoistonog.dc2.exolog.tech)
     */
    private function getSiteHostnameIp(): string
    {
        try {
            // Get the current site's hostname
            $siteHostname = $this->getCurrentSiteHostname();
            if (!$siteHostname) {
                Log::warning('No site hostname found');
                return '0.0.0.0';
            }

            Log::info('Resolving site hostname to IP', [
                'site_hostname' => $siteHostname
            ]);

            // Resolve hostname to IP using DNS lookup
            $dnsRecords = dns_get_record($siteHostname, DNS_A);
            if (!empty($dnsRecords) && isset($dnsRecords[0]['ip'])) {
                $resolvedIp = $dnsRecords[0]['ip'];
                Log::info('Successfully resolved site hostname to IP', [
                    'site_hostname' => $siteHostname,
                    'resolved_ip' => $resolvedIp
                ]);
                return $resolvedIp;
            }

            // Fallback: try gethostbyname
            $ip = gethostbyname($siteHostname);
            if ($ip && $ip !== $siteHostname && filter_var($ip, FILTER_VALIDATE_IP)) {
                Log::info('Resolved site hostname via gethostbyname', [
                    'site_hostname' => $siteHostname,
                    'resolved_ip' => $ip
                ]);
                return $ip;
            }

            Log::warning('Could not resolve site hostname to IP', [
                'site_hostname' => $siteHostname
            ]);
            return '0.0.0.0';

        } catch (\Exception $e) {
            Log::error('Error getting IP from site hostname', [
                'error' => $e->getMessage()
            ]);
            return '0.0.0.0';
        }
    }

    /**
     * Get the current site's hostname (dynamically generated like in frontend)
     * Uses same logic as EditSite.vue: alias + '.' + server_name
     */
    private function getCurrentSiteHostname(): ?string
    {
        try {
            // Get site alias from current site
            $siteAlias = Site::get('site_alias');

            // Get server name from config (same as frontend $store.state.server.server_name)
            $serverName = config('exolog.server_domain', 'dc2.exolog.tech');

            if (!$siteAlias || !$serverName) {
                Log::warning('Missing site alias or server name for hostname generation', [
                    'site_alias' => $siteAlias,
                    'server_name' => $serverName
                ]);
                return null;
            }

            // Generate hostname same as frontend: alias + '.' + server_name
            $hostname = strtolower(str_replace(' ', '-', $siteAlias)) . '.' . $serverName;

            Log::info('Generated site hostname from alias and server name', [
                'site_alias' => $siteAlias,
                'server_name' => $serverName,
                'generated_hostname' => $hostname
            ]);

            return $hostname;

        } catch (\Exception $e) {
            Log::error('Error generating site hostname', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get valid public server IP for DNS creation from current site's main domain
     */
    private function getValidPublicServerIp(string $domainName): string
    {
        Log::info('Getting valid public server IP for DNS creation', [
            'domain' => $domainName
        ]);

        // First try to get IP from current site's main domain
        $siteMainDomainIp = $this->getIpFromSiteMainDomain();
        if ($siteMainDomainIp && filter_var($siteMainDomainIp, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
            Log::info('Using public IP from site main domain', [
                'server_ip' => $siteMainDomainIp,
                'source' => 'site_main_domain'
            ]);
            return $siteMainDomainIp;
        }

        // Fallback: try the basic server domain method
        $serverIp = $this->getExpectedServerIp($domainName);

        // Check if the IP is public (not private/local)
        if ($serverIp && filter_var($serverIp, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
            Log::info('Using public IP from server domain resolution', [
                'server_ip' => $serverIp,
                'source' => 'server_domain'
            ]);
            return $serverIp;
        }

        Log::warning('No valid public IP found from domain resolution, using fallback', [
            'site_main_domain_ip' => $siteMainDomainIp,
            'server_domain_ip' => $serverIp,
            'reason' => 'All resolved IPs are private/local'
        ]);

        // Final fallback to configured public IP
        $fallbackIp = config('exolog.fallback_public_ip', '**************');

        Log::info('Using fallback public IP for DNS creation', [
            'fallback_ip' => $fallbackIp,
            'reason' => 'No valid public IP from domain resolution'
        ]);

        return $fallbackIp;
    }

    /**
     * Get IP address from current site's main domain
     */
    private function getIpFromSiteMainDomain(): ?string
    {
        try {
            // Get the main domain for the current site
            $mainDomain = Domain::query()->where('domain_ismain', 1)->first();

            if (!$mainDomain) {
                Log::info('No main domain found for current site');
                return null;
            }

            $mainDomainName = $mainDomain->domain_name;
            Log::info('Found site main domain', [
                'main_domain' => $mainDomainName,
                'site_id' => $mainDomain->domain_site
            ]);

            // Resolve the main domain to IP
            $dnsRecords = dns_get_record($mainDomainName, DNS_A);
            if (!empty($dnsRecords) && isset($dnsRecords[0]['ip'])) {
                $resolvedIp = $dnsRecords[0]['ip'];
                Log::info('Resolved site main domain to IP', [
                    'main_domain' => $mainDomainName,
                    'resolved_ip' => $resolvedIp
                ]);
                return $resolvedIp;
            }

            Log::info('Could not resolve site main domain to IP', [
                'main_domain' => $mainDomainName
            ]);
            return null;

        } catch (\Exception $e) {
            Log::error('Error getting IP from site main domain', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Check if a domain has been stuck in PROCESSING state for too long
     * This prevents domains from being permanently blocked by failed SSL processing
     */
    private function isDomainStuckInProcessing($domain_id): bool
    {
        if (!$domain_id) {
            return false;
        }

        try {
            $domain = Domain::find($domain_id);
            if (!$domain) {
                return false;
            }

            // If domain doesn't have SSL enabled, it shouldn't be processing
            if (!$domain->domain_isssl) {
                Log::info('Domain without SSL found in PROCESSING state, considering it stuck', [
                    'domain_id' => $domain_id,
                    'domain_name' => $domain->domain_name
                ]);
                return true;
            }

            // For now, consider any domain in PROCESSING state as potentially stuck
            // In a more sophisticated implementation, you could track when processing started
            // and only consider it stuck after a certain time period

            // Check if there are any active queue jobs for this domain
            // If no active jobs and still processing, it's likely stuck
            try {
                $activeJobs = \Illuminate\Support\Facades\DB::table('jobs')
                    ->where('payload', 'like', '%"domain_id":' . $domain_id . '%')
                    ->count();

                if ($activeJobs === 0) {
                    Log::info('No active queue jobs found for domain in PROCESSING state, considering it stuck', [
                        'domain_id' => $domain_id,
                        'domain_name' => $domain->domain_name
                    ]);
                    return true;
                }
            } catch (\Exception $e) {
                // If jobs table doesn't exist or query fails, assume stuck to allow updates
                Log::warning('Could not check queue jobs, assuming domain is stuck to allow updates', [
                    'domain_id' => $domain_id,
                    'error' => $e->getMessage()
                ]);
                return true;
            }

            return false;

        } catch (\Exception $e) {
            Log::error('Error checking if domain is stuck in processing', [
                'domain_id' => $domain_id,
                'error' => $e->getMessage()
            ]);
            // If we can't determine, assume it's stuck to allow updates to proceed
            return true;
        }
    }

}
