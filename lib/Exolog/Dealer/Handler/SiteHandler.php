<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 21.11.2018
 * Time: 14:57
 */

namespace Exolog\Dealer\Handler;


use Exolog\Dealer\Checker\SiteChecker;
use Exolog\Dealer\Checker\SiteDomainsChecker;
use Exolog\Dealer\DealerException;
use Exolog\Dealer\SiteRemover\SiteRemoverService;
use Exolog\Module\Skeleton\SkeletonService;
use Exolog\Module\Support\Facades\BuffLog;
use Exolog\Module\Support\Facades\Site;
use Exolog\Module\Support\Facades\Vars;
use Exolog\Module\Users\Model\User;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;


class SiteHandler
{
    private DomainHandler $domainHandler;
    private AuthHandler $authHandler;

    public function __construct(
        DomainH<PERSON>ler $domainH<PERSON>,
        AuthHandler   $authHandler
    )
    {
        $this->domainHandler = $domainHandler;
        $this->authHandler = $authHandler;
    }


    /**
     * @throws DealerException
     */
    public function createSite($siteData)
    {

        $domains = $siteData['domains'];

        $siteData['site_alias'] = strtolower(trim($siteData['site_alias']));
        $siteData['site_name'] = trim($siteData['site_name']);

        if ($this->checkAliasUnique($siteData['site_alias'], 0)) {
            throw new DealerException('Found site with the same alias!');
        }

        if ($this->checkAliasUnique($siteData['site_alias'], 1)) {
            throw new DealerException('Found a site with the same alias in deleted sites! Please delete the site permanently to use this alias');
        }

        if (empty($siteData['site_alias']) || empty($siteData['site_name'])) {
            throw new InvalidArgumentException('Site name is empty');
        }

        $site = new \Exolog\Module\Site\Model\Site($siteData);
        $site->save();
        $site_id = $site->site_id;

        $this->insertDefaultEdition($site_id);

        dealer()->setSiteId($site_id);

        if (array_key_exists('vars', $siteData)) {
            Vars::massUpdate($siteData['vars'], 'site', $site_id);
        }
        SkeletonService::make()->build();

        Log::info('Site created! id=' . $site_id, ['skeleton_log' => BuffLog::getRecords()]);
        $this->domainHandler->updateSiteDomains($site_id, $domains);

        $this->createSiteUserByAdmin($site_id, $this->authHandler->getSessionAdmin());

        return $site_id;
    }

    /**
     * @throws DealerException
     */
    public function updateSite($siteData)
    {

        $site_id = $siteData['site_id'];
        $site = \Exolog\Module\Site\Model\Site::findOrFail($site_id);

        $site->fill(Arr::only($siteData, ['site_name']));
        $site->save();

        if (array_key_exists('vars', $siteData)) {
            Vars::massUpdate($siteData['vars'], 'site', $site_id);
        }

        $domains = $siteData['domains'];
        $this->domainHandler->updateSiteDomains($site_id, $domains);
        return $site;
    }


    public function createSiteUserByAdmin($site_id, $admin)
    {
        $email = $admin['a_email'];

        if (!$this->isUserExists($site_id, $email)) {
            $user = User::create([
                'u_email' => $email,
                'u_lang' => 2,
            ]);
        } else {
            $user = User::findByEmailOrFail($email);
        }
        $user->addToGroup('Developers');
        $user->addToGroup('Admins');

        return $user->u_id;
    }

    /**
     * @throws DealerException
     */
    public function softDeleteSite($site_id): void
    {
        $mailHostHandler = MailhostHandler::make(['site_id' => $site_id]);
        $domains = $this->domainHandler->getSiteDomains();
        foreach ($domains as $domain) {
            if ($domain['domain_ismail']) {
                $mailHostHandler->softDeleteHost($domain['domain_name']);
            }
        }
        $site = \Exolog\Module\Site\Model\Site::findOrFail($site_id);
        $site->delete();
    }

    /**
     * @throws DealerException
     */
    public function softUndeleteSite($site_id): void
    {
        /** @var \Exolog\Module\Site\Model\Site $site */
        \Exolog\Module\Site\Model\Site::withTrashed()
            ->restore();

        $mailHostHandler = MailhostHandler::make(['site_id' => $site_id]);

        dealer()->setSiteId($site_id);
        $domains = $this->domainHandler->getSiteDomains();
        foreach ($domains as $domain) {
            if ($domain['domain_ismail']) {
                $mailHostHandler->softUndeleteHost($domain['domain_name']);
            }
        }
    }

    /**
     * @param $site_id
     * @throws DealerException
     */
    public function deleteSite($site_id): void
    {
        SiteRemoverService::make()->remove($site_id);
    }

    protected function isUserExists($site_id, $user_email): bool
    {
        return DB::table('user')
            ->where('u_site', $site_id)
            ->where('u_email', $user_email)
            ->select('u_id')
            ->exists();
    }

    public function performChecks($sites): Collection
    {
        $sites = SiteChecker::make()->check($sites);
        return SiteDomainsChecker::make()->check($sites);
    }

    public function checkAliasUnique($site_alias, $site_deleted): bool
    {
        return DB::table('site')
            ->where('site_alias', '=', $site_alias)
            ->when($site_deleted, function (Builder $q) {
                $q->whereNotNull(\Exolog\Module\Site\Model\Site::DELETED_AT);
            })
            ->when(!$site_deleted, function (Builder $q) {
                $q->whereNull(\Exolog\Module\Site\Model\Site::DELETED_AT);
            })
            ->exists();
    }


    public function insertDefaultEdition($site_id): void
    {
        Site::init(\Exolog\Module\Site\Model\Site::resolve($site_id));
        SkeletonService::make()->build(['edition']);
    }
}
