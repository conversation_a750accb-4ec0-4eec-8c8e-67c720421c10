<?php

namespace Exolog\Dealer\Mail;

use Exolog\Dealer\Models\Admin;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class AdminPasswordChangeLink extends Mailable
{
    use Queueable, SerializesModels;


    private Admin $admin;
    private string $link;

    /**
     * Create a new message instance.
     *
     */
    public function __construct(Admin $admin, string $link)
    {
        $this->admin = $admin;
        $this->link = $link;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $this->subject('Exolog Dealer');

        return $this->view('system.dealer.mail.admin-password-change', [
            'admin' => $this->admin,
            'link' => $this->link
        ]);
    }
}
