<?php

namespace Exolog\Dealer\Console\Commands;

use Exolog\Module\Console\Kernel;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CleanupDomain extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dealer:cleanup-domain {cert_path} {domain_name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Execute external bash script to remove server configs.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        define('APP_DEALER', true);

        app(Kernel::class)->initDealer();

        $certPath = $this->argument('cert_path');
        $host = $this->argument('domain_name');

        $handler = config('dealer.domains.handlers.onDomainDelete');

        $this->info("certPath = $certPath");
        $this->info("host = $host");
        $this->info("handler = $handler");

        if (empty($certPath) || empty($host) || empty($handler)) {
            $this->error('No value given for the required parameter');
            return 1;
        }

        $this->info("exec $handler $certPath $host");
        exec("$handler $certPath $host", $output, $result);

        $this->info('Script output:');
        $this->info(json_encode($output));
        $this->info('External scripts result:' . $result);


        if ($result) {
            Log::stack(['dealer-external'])->error(
                "External handler:
           exec $handler $certPath $host
           Result:$result",
                ['output' => $output]
            );
        }

        return 0;
    }
}