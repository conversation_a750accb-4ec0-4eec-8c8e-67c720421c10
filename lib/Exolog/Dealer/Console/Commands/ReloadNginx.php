<?php

namespace Exolog\Dealer\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Process\Exception\ProcessFailedException;
use Symfony\Component\Process\Process;

class ReloadNginx extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dealer:reload-nginx';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reload nginx service.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $process = Process::fromShellCommandline('sudo /usr/sbin/nginx -s reload');
        $process->run();

        if (!$process->isSuccessful()) {
            throw new ProcessFailedException($process);
        }
        $this->info('nginx reloaded');

        return 0;
    }
}