<?php

namespace Exolog\Dealer\Console\Commands;

use Exolog\Dealer\Handler\DomainHandler;
use Exolog\Module\Console\Kernel;
use Illuminate\Console\Command;

class ExecuteDomainHandler extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dealer:execute-handler {site_id} {domain_id} {handler} {force}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Execute external bash script to update server configs.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        define('APP_DEALER', true);

        app(Kernel::class)->initDealer();

        $site_id = $this->argument('site_id');
        $domain_id = $this->argument('domain_id');
        $handler = $this->argument('handler');
        $force = $this->argument('force');

        $this->info("site_id = $site_id");
        $this->info("domain_id = $domain_id");
        $this->info("handler = $handler");
        $this->info("force = $force");

        dealer()->setSiteId($site_id);
        return DomainHandler::make()->execHandler($domain_id, $handler, $force);
    }
}