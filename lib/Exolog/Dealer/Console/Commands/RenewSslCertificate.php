<?php

namespace Exolog\Dealer\Console\Commands;

use Exolog\Dealer\DealerException;
use Exolog\Dealer\Handler\DomainHandler;
use Exolog\Module\Console\Kernel;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RenewSslCertificate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dealer:renew-ssl';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Renew SSL Certificates for all domains';

    /**
     * Execute the console command.
     *
     * @return int
     * @throws DealerException
     */
    public function handle()
    {
        define('APP_DEALER', true);

        sleep(10);

        app(Kernel::class)->initDealer();

        $domainHandler = DomainHandler::make();

        $domains = DB::table('domain as d')
            ->select('d.domain_id', 's.site_id')
            ->join('site as s', 'd.domain_site', '=', 's.site_id')
            ->whereNull('s.site_deleted_at')
            ->where('d.domain_isletsencrypt', 1)
            ->get();

        $domains->each(function ($domain) use ($domainHandler) {
            $handler = 'onDomainUpdate';
            $site_id = $domain['site_id'];
            $domain_id = $domain['domain_id'];

            $this->info("Renew for site_id = $site_id, domain_id = $domain_id, handler = $handler");

            dealer()->setSiteId($site_id);

            $domainHandler->execHandler($domain_id, $handler);
        });

        return 0;

    }
}