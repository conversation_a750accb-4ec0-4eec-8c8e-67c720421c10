<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 21.04.2020
 * Time: 13:06
 */

namespace Exolog\Dealer\Checker;


use Exolog\Module\Domains\Model\Domain;
use Exolog\Module\Site\Model\Site;
use Exolog\Module\Support\Concerns\HasMakeFactory;
use Illuminate\Support\Collection;

class SiteDomainsChecker
{
    use HasMakeFactory;

    public const API_FIX_ALIAS_DOMAIN = '/domain/fixAliasDomain';
    public const API_ADD_ALIAS_DOMAIN = '/domain/addAliasDomain';

    public function check(Collection $sites): Collection
    {
        return $sites->map(function (Site $site) {
            $domains = Domain::crossSiteQuery()->where('domain_site', $site['site_id'])->get();
            $checks = $this->checkDomains($domains, $site['site_alias']);

            $domains_checks = [
                'haveAliasDomain' => [
                    'valid' => $checks['haveAliasDomain'],
                    'api' => self::API_ADD_ALIAS_DOMAIN
                ],
                'validAliasDomain' => [
                    'valid' => $checks['validAliasDomain'],
                    'api' => self::API_FIX_ALIAS_DOMAIN
                ]
            ];

            data_set($site, 'checks',
                array_merge(
                    data_get($site, 'checks', []),
                    ['domains' => $domains_checks])
            );


            return $site;
        });
    }

    public function isAliasDomain($domain_name, $alias): bool
    {
        return strtolower($domain_name) === strtolower($alias . '.' . config('exolog.server_domain'));
    }

    public function invalidAliasDomain($domain_name, $alias): bool
    {
        return endsWith(strtolower($domain_name), strtolower(config('exolog.server_domain'))) &&
            !endsWith(strtolower($domain_name), strtolower($alias . '.' . config('exolog.server_domain')));

    }

    /**
     * @param Collection<Domain> $domains
     * @param $alias
     * @return bool[]
     */
    public function checkDomains(Collection $domains, $alias): array
    {
        $haveAliasDomain = false;
        $validAliasDomain = true;
        foreach ($domains as $domain) {
            if ($this->isAliasDomain($domain['domain_name'], $alias)) {
                $haveAliasDomain = true;
            }
            //site can have only one alias.server
            if ($this->invalidAliasDomain($domain['domain_name'], $alias)) {
                $validAliasDomain = false;
            }
        }
        return ['haveAliasDomain' => $haveAliasDomain, 'validAliasDomain' => $validAliasDomain];
    }
}