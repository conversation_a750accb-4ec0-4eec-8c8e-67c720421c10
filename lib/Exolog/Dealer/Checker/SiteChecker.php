<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 21.04.2020
 * Time: 13:06
 */

namespace Exolog\Dealer\Checker;


use Exolog\Module\Domains\Model\Domain;
use Exolog\Module\Editions\Model\Edition;
use Exolog\Module\Site\Model\Site;
use Exolog\Module\Support\Concerns\HasMakeFactory;
use Illuminate\Support\Collection;

class SiteChecker
{
    use HasMakeFactory;

    public const API_FIX_DEFAULT_EDITION = '/site/fixDefaultEdition';

    public function check(Collection $sites): Collection
    {
        return $sites->map(function (Site $site) {
            $count = Edition::crossSiteQuery()
                ->where('e_site', $site->site_id)
                ->where('e_isdefault', 1)->count();

            $site_checks = [
                'hasDefaultEdition' => [
                    'valid' => $count > 0,
                    'api' => self::API_FIX_DEFAULT_EDITION
                ],
            ];

            data_set($site, 'checks',
                array_merge(
                    data_get($site, 'checks', []),
                    ['site' => $site_checks])
            );


            return $site;
        });
    }

    public function isAliasDomain($domain_name, $alias): bool
    {
        return strtolower($domain_name) === strtolower($alias . '.' . config('exolog.server_domain'));
    }

    public function invalidAliasDomain($domain_name, $alias): bool
    {
        return endsWith(strtolower($domain_name), strtolower(config('exolog.server_domain'))) &&
            !endsWith(strtolower($domain_name), strtolower($alias . '.' . config('exolog.server_domain')));

    }

    /**
     * @param Collection<Domain> $domains
     * @param $alias
     * @return bool[]
     */
    public function checkDomains(Collection $domains, $alias): array
    {
        $haveAliasDomain = false;
        $validAliasDomain = true;
        foreach ($domains as $domain) {
            if ($this->isAliasDomain($domain['domain_name'], $alias)) {
                $haveAliasDomain = true;
            }
            //site can have only one alias.server
            if ($this->invalidAliasDomain($domain['domain_name'], $alias)) {
                $validAliasDomain = false;
            }
        }
        return ['haveAliasDomain' => $haveAliasDomain, 'validAliasDomain' => $validAliasDomain];
    }
}