<?php


namespace Exolog\Dealer\Models;


use Illuminate\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

//use Illuminate\Foundation\Auth\Access\Authorizable;


/**
 * @property string $a_password
 * @property \Carbon\Carbon $a_lastlogin
 * @property integer $a_loggedin
 * @property bool $a_issuper
 * @property array $a_options
 * @property integer $a_id
 * @method static findOrFail(array|string $admin_id)
 */
class Admin extends Model implements AuthenticatableContract
    //AuthorizableContract,
    //CanResetPasswordContract
{
    use Authenticatable;

    //use Authorizable,
    //use CanResetPassword,
    //use MustVerifyEmail;


    protected $table = 'admin';
    protected $primaryKey = 'a_id';
    public $timestamps = false;

    protected $fillable = [
        'a_email',
        'a_password',
        'a_name',
        'a_issuper',
    ];
    protected $attributes = [
        'a_issuper' => false,
        'a_alias' => 'admin',
    ];


    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [
        'a_password',
        'a_options',
        'a_settings',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'a_loggedin' => 'integer',
        'a_lastlogin' => 'datetime',
        'a_options' => 'json',
        'a_issuper' => 'boolean',
        'a_datenew' => 'datetime'
    ];

    protected static function booted()
    {

    }

    public function getAuthPassword()
    {
        return $this->a_password;
    }

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        $this->attributes['a_datenew'] = Carbon::now();
    }
}