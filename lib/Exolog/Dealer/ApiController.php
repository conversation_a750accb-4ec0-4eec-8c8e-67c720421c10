<?php
/**
 * Created by PhpStorm.
 * User: madcap
 * Date: 19.09.2018
 * Time: 14:43
 */

namespace Exolog\Dealer;

use Exolog\Module\API\Api;
use Illuminate\Routing\Controller;

abstract class ApiController extends Controller
{

    /**
     * Object Api
     *
     * @var Api
     */
    protected Api $api;

    /**
     * Construct
     *
     * @param Api $api
     */
    public function __construct(Api $api)
    {
        $this->api = $api;
    }
}