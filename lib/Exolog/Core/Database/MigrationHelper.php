<?php

namespace Exolog\Core\Database;

use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

/**
 * Helper class for safe database migrations
 * Provides methods to check for table/column existence before creating/modifying
 */
class MigrationHelper
{
    /**
     * Safely create a table only if it doesn't exist
     *
     * @param string $tableName
     * @param callable $callback
     * @return bool True if table was created, false if it already existed
     */
    public static function safeCreateTable(string $tableName, callable $callback): bool
    {
        if (!Schema::hasTable($tableName)) {
            Schema::create($tableName, $callback);
            return true;
        }
        return false;
    }

    /**
     * Safely add a column only if it doesn't exist
     *
     * @param string $tableName
     * @param string $columnName
     * @param callable $callback
     * @return bool True if column was added, false if it already existed
     */
    public static function safeAddColumn(string $tableName, string $columnName, callable $callback): bool
    {
        if (!Schema::hasColumn($tableName, $columnName)) {
            Schema::table($tableName, $callback);
            return true;
        }
        return false;
    }

    /**
     * Safely add multiple columns only if they don't exist
     *
     * @param string $tableName
     * @param array $columns Array of column names to check
     * @param callable $callback
     * @return array Array of column names that were added
     */
    public static function safeAddColumns(string $tableName, array $columns, callable $callback): array
    {
        $missingColumns = [];
        foreach ($columns as $column) {
            if (!Schema::hasColumn($tableName, $column)) {
                $missingColumns[] = $column;
            }
        }

        if (!empty($missingColumns)) {
            Schema::table($tableName, $callback);
        }

        return $missingColumns;
    }

    /**
     * Safely add an index only if it doesn't exist
     *
     * @param string $tableName
     * @param string|array $columns
     * @param string|null $indexName
     * @return bool True if index was added, false if it already existed
     */
    public static function safeAddIndex(string $tableName, $columns, string $indexName = null): bool
    {
        $indexName = $indexName ?: $tableName . '_' . (is_array($columns) ? implode('_', $columns) : $columns) . '_index';
        
        if (!self::hasIndex($tableName, $indexName)) {
            Schema::table($tableName, function ($table) use ($columns, $indexName) {
                $table->index($columns, $indexName);
            });
            return true;
        }
        return false;
    }

    /**
     * Check if an index exists on a table
     *
     * @param string $tableName
     * @param string $indexName
     * @return bool
     */
    public static function hasIndex(string $tableName, string $indexName): bool
    {
        $connection = Schema::getConnection();
        $schemaManager = $connection->getDoctrineSchemaManager();
        
        try {
            $indexes = $schemaManager->listTableIndexes($tableName);
            return isset($indexes[strtolower($indexName)]);
        } catch (\Exception $e) {
            // Fallback to raw SQL check
            $database = $connection->getDatabaseName();
            $result = DB::select("
                SELECT COUNT(*) as count 
                FROM information_schema.statistics 
                WHERE table_schema = ? 
                AND table_name = ? 
                AND index_name = ?
            ", [$database, $tableName, $indexName]);
            
            return $result[0]->count > 0;
        }
    }

    /**
     * Safely add a foreign key only if it doesn't exist
     *
     * @param string $tableName
     * @param string $column
     * @param string $referencedTable
     * @param string $referencedColumn
     * @param string|null $constraintName
     * @return bool True if foreign key was added, false if it already existed
     */
    public static function safeAddForeignKey(
        string $tableName, 
        string $column, 
        string $referencedTable, 
        string $referencedColumn = 'id',
        string $constraintName = null
    ): bool {
        $constraintName = $constraintName ?: $tableName . '_' . $column . '_foreign';
        
        if (!self::hasForeignKey($tableName, $constraintName)) {
            Schema::table($tableName, function ($table) use ($column, $referencedTable, $referencedColumn, $constraintName) {
                $table->foreign($column, $constraintName)->references($referencedColumn)->on($referencedTable);
            });
            return true;
        }
        return false;
    }

    /**
     * Check if a foreign key constraint exists
     *
     * @param string $tableName
     * @param string $constraintName
     * @return bool
     */
    public static function hasForeignKey(string $tableName, string $constraintName): bool
    {
        $database = DB::connection()->getDatabaseName();
        $result = DB::select("
            SELECT COUNT(*) as count 
            FROM information_schema.key_column_usage 
            WHERE constraint_schema = ? 
            AND table_name = ? 
            AND constraint_name = ?
            AND referenced_table_name IS NOT NULL
        ", [$database, $tableName, $constraintName]);
        
        return $result[0]->count > 0;
    }

    /**
     * Drop table if exists (safe wrapper)
     *
     * @param string $tableName
     * @return bool True if table was dropped, false if it didn't exist
     */
    public static function safeDropTable(string $tableName): bool
    {
        if (Schema::hasTable($tableName)) {
            Schema::dropIfExists($tableName);
            return true;
        }
        return false;
    }

    /**
     * Drop column if exists (safe wrapper)
     *
     * @param string $tableName
     * @param string|array $columns
     * @return array Array of columns that were dropped
     */
    public static function safeDropColumns(string $tableName, $columns): array
    {
        $columns = is_array($columns) ? $columns : [$columns];
        $existingColumns = [];
        
        foreach ($columns as $column) {
            if (Schema::hasColumn($tableName, $column)) {
                $existingColumns[] = $column;
            }
        }
        
        if (!empty($existingColumns)) {
            Schema::table($tableName, function ($table) use ($existingColumns) {
                $table->dropColumn($existingColumns);
            });
        }
        
        return $existingColumns;
    }
}
