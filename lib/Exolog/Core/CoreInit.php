<?php

namespace Exolog\Core;

use Exolog\Module\Domains\Model\Domain;
use Exolog\Module\Editions\Model\Edition;
use Exolog\Module\Http\Request;
use Exolog\Module\Support\DebugService;
use Exolog\Module\Support\Facades\Defaults;
use Exolog\Module\Support\Facades\Site;
use Exolog\Module\Support\Facades\Vars;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Contracts\Http\Kernel;
use Illuminate\Filesystem\Filesystem;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;


/**
 * Class for exolog initialization
 */
class CoreInit
{
    /**
     * By hostname gives domain record
     * On null takes current HTTP_HOST
     */
    private function findDomainRecordByHost(string $hostname)
    {
        return DB::table('domain')
            ->where('domain_name', $hostname)
            ->first();
    }

    /**
     * By domain record gives host to redir default edition domain or to main
     * If redir is not needed returns null
     */
    private function getRedirectDomain($domain)
    {
        if ($domain['domain_isdefault'] == 1 || $domain['domain_ismain'] == 1) {
            return null;
        }

        if ($domain['domain_edition_id']) {
            $redirectDomain = DB::table('domain')
                ->where('domain_isdefault', 1)
                ->where('domain_edition_id', $domain['domain_edition_id'])
                ->where('domain_site', $domain['domain_site'])
                ->value('domain_name');
        } else {
            $redirectDomain = DB::table('domain')
                ->where('domain_ismain', 1)
                ->where('domain_site', $domain['domain_site'])
                ->value('domain_name');
        }

        return $domain['domain_name'] !== $redirectDomain ? $redirectDomain : null;
    }

    public function resolveSiteByRequest(Request $request)
    {
        $this->resetExolog();

        $domain = $this->findDomainRecordByHost($request->getHost());
        if (is_null($domain)) {
            runRedir(config('exolog.domain_not_found_redirect'));
        }
        $redirectDomain = $this->getRedirectDomain($domain);

        if (!is_null($redirectDomain) && !Str::startsWith($domain['domain_name'], 'webmail.')) {
            runRedir($request->getScheme() . '://' . $redirectDomain . $request->getRequestUri(), 301);
        }
        Defaults::set('domain', $domain);

        $site_id = $domain['domain_site'];

        $this->initSite($site_id);
    }

    public function resolveSite($site_id): void
    {
        $this->resetExolog();
        $this->initSite($site_id);
    }

    /**
     * Init site data, folders, events
     *
     * @param $site_id
     * @throws FileNotFoundException
     * <AUTHOR>
     */
    protected function initSite($site_id): void
    {

        $site = \Exolog\Module\Site\Model\Site::resolve($site_id);

        if (is_null($site)) {
            throw new NotFoundHttpException("Site not found or deleted! [site: $site_id]");
        }

        Site::init($site);
        $this->initEdition();

        /** @var Filesystem $fs */
        $fs = app('files');

        $config_path = sites_path('config/app/site.php');
        if ($fs->exists($config_path)) {
            $config = $fs->requireOnce($config_path);
            if (data_get($config, 'debug')) {
                DebugService::enable();
            }
        }

        //add site class to composer autoload
        $composerLoader = app('composerLoader');
        $composerLoader->setPsr4("Exolog\\Site\\", site_path('class/'));


        if ($fs->exists(site_path('vendor/autoload.php'))) {
            $fs->requireOnce(site_path('vendor/autoload.php'));
        }

        app(Kernel::class)->initSite();

        // set language
        Defaults::set('language', 'dutch');

        if ($lang = Vars::site('language')->getValue()) {
            Defaults::set('language', $lang);
        }
        if ($lang = Vars::edition('language')->getValue()) {
            Defaults::set('language', $lang);
        }

        //setlocale(LC_CTYPE, "uk_UA.UTF-8");
        app()->setLocale(language2locale(Defaults::get('language')));

        if (app()->runningInConsole()) {
            $mainDomain = Domain::getMainDomain();
            url()->forceRootUrl('https://' . $mainDomain->domain_name);
            config()->set('app.url', 'https://' . $mainDomain->domain_name);
        }
    }


    private function initEdition(): void
    {
        $domain = Defaults::get('domain');
        $edition_id = $domain['domain_edition_id'] ?? null;

        $edition = $edition_id ? Edition::findOrFail($edition_id) : Edition::getDefaultOrFail();
        Defaults::setEdition($edition);
    }

    private function resetExolog(): void
    {
        app()->bootExolog();
    }
}
