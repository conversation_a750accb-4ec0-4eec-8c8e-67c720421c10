<?php

namespace Exolog\Core;


use Exolog\Module\Support\Facades\Site;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;
use RuntimeException;
use Spatie\ResponseCache\Facades\ResponseCache;


class Exolog
{
    public function getEditionDefaultUrl($e_id, $allowEmpty = false): string
    {
        //Edition::getDefault()->getDefaultDomain()->domain_name;
        //todo make it cached
        if (empty($e_id)) {
            throw new InvalidArgumentException('Parameter edition id is empty!');
        }
        $domain = DB::table('domain')->select('*')
            ->where('domain_isdefault', 1)
            ->where('domain_site', Site::get('site_id'))
            ->where('domain_edition_id', $e_id)
            ->first();

        if (empty($domain) && !$allowEmpty) {
            throw new RuntimeException("The Default domain for the edition isn't found! edition_id='{$e_id}'");
        }
        $protocol = $domain['domain_isssl'] ? 'https://' : 'http://';
        return $protocol . $domain['domain_name'];
    }

    public function emptyCache(): void
    {
        ResponseCache::clear();
        Cache::flush();
    }

    #[NoReturn]
    public function runRedir($redir, $code = 302): void
    {
        while (ob_get_level() !== 0) {
            ob_end_clean();
        }
        header('Cache-Control: no-store, no-cache, must-revalidate, post-check=0, pre-check=0');
        header("Location: $redir", true, $code);
        exit;
    }
}
