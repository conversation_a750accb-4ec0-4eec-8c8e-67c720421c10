<?php

namespace Exolog\Core\Forms\Traits;

use Exolog\Core\Forms\React;
use Exolog\Module\Forms\PictureFieldRecoveryService;
use Exolog\Module\Permalinks\PermalinksService;
use Exolog\Module\Reacts\Events\ReactCreated;
use Exolog\Module\Reacts\Events\ReactCreating;
use Exolog\Module\Reacts\Events\ReactDeleted;
use Exolog\Module\Reacts\Events\ReactDeleting;
use Exolog\Module\Reacts\Events\ReactSaved;
use Exolog\Module\Reacts\Events\ReactSaving;
use Exolog\Module\Reacts\Events\ReactUpdated;
use Exolog\Module\Reacts\Events\ReactUpdating;
use Exolog\Module\Support\Facades\Site;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use RuntimeException;

trait HasReactPersistence
{
    public function save(array $options = []): React
    {
        $form = $this->getReactForm();

        $this['react_date_changed'] = now();

        if (!isset($this['react_subject'])) {
            $subject_template = $form->form_mailsubject;
            $this['react_subject'] = dv(Str::format($subject_template, $this->toArray()),
                "Reaction from ${form['form_name']}");
        }

        $this->updateAlias();

        $options = $this->mergeReactOptions($options);

        PictureFieldRecoveryService::make()->updateReactFieldsHash($this);

        $fields = $this->toArray(true);
        // prepare fields to save
        foreach ($fields as $fq_name => $field_value) {

            $fq = $form->getFQ($fq_name);

            if ($fq === null) {
                throw new RuntimeException('Form question not found');
            }

            if (in_array($fq->getTypeName(), ['picture', 'unit', 'view', 'container', 'mailable'])) {
                if (is_array($field_value)) {
                    $fields[$fq_name] = json_encode($field_value, JSON_THROW_ON_ERROR);
                }
            }
            if (in_array($fq->getTypeName(), ['date', 'datetime'])) {
                $store_format = $fq->getECO('store_format.php');
                $fields[$fq_name] = date($store_format, strtotime($field_value));
            }

            if (is_array($field_value) && $fq->isMultiple()) {
                $fields[$fq_name] = implode('|', $field_value);
            }

        }

        if (empty($this['react_user']) && auth()->check()) {
            $this['react_user'] = auth()->id();
        }

        if (!$options['skip_event']) {
            ['fields' => $fields] = ReactSaving::dispatch([
                'react' => $this,
                'fields' => $fields
            ]);
        }

        if ($this->isNew()) {

            $this['react_date'] = now();
            $this['react_site'] = Site::id();
            $this['react_form'] = $form->form_id;
            $this['react_confirmstring'] = Str::upper(Str::random(15));

            if (!$options['skip_event']) {
                ['fields' => $fields] = ReactCreating::dispatch([
                    'react' => $this,
                    'fields' => $fields
                ]);
            }

            $react_values = [
                'react_date' => $this['react_date'],
                'react_date_changed' => $this['react_date_changed'],
                'react_site' => $this['react_site'],
                'react_subject' => $this['react_subject'],
                'react_form' => $this['react_form'],
                'react_user' => $this['react_user'],
                'react_confirmstring' => $this['react_confirmstring'],
                'react_jdoc' => json_encode($fields, JSON_THROW_ON_ERROR),
                'react_parent_containers' => $this['react_parent_containers'] ?? null,
                'react_form_permalink_id' => $this['react_form_permalink_id'] ?? null,
                'react_layout' => isset($this['react_layout']) ? json_encode($this['react_layout'],
                    JSON_THROW_ON_ERROR) : null,
                'react_edition_id' => $this['react_edition_id'],
            ];

            if (isset($this['react_layout_scope'])) {
                $react_values['react_layout_scope'] = $this['react_layout_scope'];
            }

            $react_id = DB::table('react')->insertGetId($react_values);

            if (!$options['skip_event']) {
                ReactCreated::dispatch(['react_id' => $react_id]);
            }

            $this['react_id'] = $react_id;

        } else {

            if (!$options['skip_event']) {
                ['fields' => $fields] = ReactUpdating::dispatch([
                    'react' => $this,
                    'fields' => $fields
                ]);
            }

            $updates = [];

            foreach ($fields as $key => $fields_value) {
                $updates["react_jdoc->$key"] = $fields_value;
            }

            foreach (static::react_reserved_keys as $key) {
                //skip not updatable fields
                if (in_array($key, [
                    'react_id',
                    'react_site',
                    'react_date',
                    'react_form',
                ])) {
                    continue;
                }

                if (array_key_exists($key, $this->toArray())) {
                    $updates[$key] = $this[$key];
                }
            }

            DB::table('react')
                ->where('react_id', $this->id())
                ->update($updates);

            if (!$options['skip_event']) {
                ReactUpdated::dispatch(['react' => $this]);
            }
        }

        $this->__isDirty = false;
        $this->syncOriginal();
        $this->fireAfterSaveCallback();


        if (empty($options['skip_permalink'])) {
            PermalinksService::make()->updateReactPermalinks($this, $options);
        }

        if (!$options['skip_event']) {
            ReactSaved::dispatch(['react' => $this]);
        }

        return $this;
    }

    public function forceDelete(): void
    {
        ReactDeleting::dispatch(['react' => $this, 'force' => true]);

        if ($this->id()) {
            DB::table('react')->where('react_id', $this->id())->delete();
        }
        $this['react_isdeleted'] = 1;

        ReactDeleted::dispatch(['react' => $this, 'force' => true]);
    }

    public function restore(): void
    {
        if ($this->id()) {
            DB::table('react')->where('react_id', $this->id())->update(['react_isdeleted' => 0]);
        }
        $this['react_isdeleted'] = 0;
    }

    public function delete(): void
    {
        ReactDeleting::dispatch(['react' => $this, 'force' => false]);

        if ($this->id()) {
            DB::table('react')->where('react_id', $this->id())->update(['react_isdeleted' => 1]);
        }
        $this['react_isdeleted'] = 1;

        ReactDeleted::dispatch(['react' => $this, 'force' => false]);
    }

    public static function saveFromArray($form, array $attributes, array $options = []): React
    {
        $react_id = $options['react_id'] ?? null;

        if ($react_id) {
            $reactModel = react()->findOrFail($react_id);
            $reactModel->fill($attributes);
        } else {
            $reactModel = new self($form, $attributes, $options);
        }

        $reactModel->save();

        return $reactModel;
    }

    abstract public function toArray(): array;

    abstract public function id();

    abstract public function isNew();

    abstract public function getReactRelatedEditionId();

    abstract public function getReactForm();

    abstract public function mergeReactOptions(array $options);

    public function refresh()
    {

    }
}