<?php

namespace Exolog\Core\Forms;

use ArrayObject;
use Exception;
use Exolog\Core\Forms\Traits\HasCallbacks;
use Exolog\Core\Forms\Traits\HasReactPersistence;
use Exolog\Module\Container\Container;
use Exolog\Module\Container\ContainerFactory;
use Exolog\Module\Container\ContainerHolderNotFoundException;
use Exolog\Module\Forms\FQT\FQTBase;
use Exolog\Module\Forms\FQT\Json\Json;
use Exolog\Module\Forms\FQT\Mailable\Mailable;
use Exolog\Module\Forms\FQT\Permalink\FQTPermalink;
use Exolog\Module\Forms\FQT\Picture\Picture;
use Exolog\Module\Forms\FQT\Text\Text;
use Exolog\Module\Forms\FQT\Unit\Unit;
use Exolog\Module\Forms\Model\Form;
use Exolog\Module\Forms\Model\FormQuestion;
use Exolog\Module\Permalinks\Model\Permalink;
use Exolog\Module\ReactLayout\ReactLayout;
use Exolog\Module\ReactLayout\ReactLayoutFactory;
use Exolog\Module\Reacts\Query\Queryable;
use Exolog\Module\Reacts\ReactBindsToForm;
use Exolog\Module\Reacts\ReactResponsable;
use Exolog\Module\View\RenderPDFService;
use Galahad\Aire\Contracts\BindsToForm;
use Illuminate\Contracts\Support\Responsable;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Support\Traits\ForwardsCalls;
use InvalidArgumentException;
use RuntimeException;

/**
 * Class React
 * @method Mailable getMailable()
 * @method FQTPermalink getPermalink()
 *
 */
class React extends ArrayObject implements BindsToForm, Responsable
{
    use Queryable;
    use ForwardsCalls;
    use ReactBindsToForm;
    use ReactResponsable;
    use HasReactPersistence;
    use HasCallbacks;

    protected const react_reserved_keys = [
        'react_id',
        'react_site',
        'react_date',
        'react_date_changed',
        'react_subject',
        'react_form',
        'react_user',
        'react_isdeleted',
        'react_confirmstring',
        'react_parent_containers',
        'react_form_permalink_id',
        'react_edition_id',
        'react_layout',
        'react_layout_scope',
    ];
    protected static array $memberForms = [];
    protected int $__form_id;
    protected bool $__isDirty = false;
    protected array $react_options = [];
    protected array $original;

    /**
     * @var array<FQTBase>
     */
    private array $wrappers = [];

    public function __construct($form, $attributes = [], $options = [])
    {
        $this->setReactForm($form);

        if (empty($attributes['react_id'])) {
            $defaultValues = $this->defaultValues();
            $attributes = array_merge($defaultValues, $attributes);
        }

        parent::__construct($attributes);
        $this->syncOriginal();

        $this->mergeReactOptions($options);
    }

    protected function setReactForm($form): void
    {
        if ($form instanceof Form) {
            $this->__form_id = $form->form_id;
            static::$memberForms[$form->form_id] = $form;
        } elseif (is_numeric($form)) {
            $this->__form_id = $form;
        } else {
            $formModel = Form::findByName($form);
            if (is_null($formModel)) {
                throw new InvalidArgumentException("Invalid form name: '$form'");
            }
            static::$memberForms[$formModel->form_id] = $formModel;
            $this->__form_id = $formModel->form_id;
        }
    }

    protected function defaultValues(): array
    {
        $form = $this->getReactForm();
        return $form->form_questions->mapWithKeys(function (FormQuestion $fq) {
            return [$fq->fq_name => $fq->getDefaultValue()];
        })->toArray();
    }

    public function getReactForm(): Form
    {
        $form_id = $this->__form_id;
        if (!array_key_exists($form_id, static::$memberForms)) {
            static::$memberForms[$form_id] = Form::find($form_id);
        }
        return static::$memberForms[$form_id];
    }

    public function toArray($onlyFields = false): array
    {
        return $onlyFields ? Arr::only($this->getArrayCopy(), $this->getReactKeys($onlyFields)) : $this->getArrayCopy();
    }

    public function getReactKeys($onlyFields = false): array
    {
        if ($onlyFields) {
            return $this->getReactForm()->form_questions->pluck('fq_name')->toArray();
        }
        return array_keys((array)$this);
    }

    public function mergeReactOptions(array $options): array
    {
        foreach ($options as $key => $option) {
            if (in_array($key, static::react_reserved_keys, true)) {
                $this[$key] = $option;
            } else {
                $this->react_options[$key] = $option;
            }
        }
        return $this->react_options;
    }

    public function __call($key, $arguments = [])
    {
        if (strpos($key, 'get') === 0) {
            $key = $this->extractKeyFromMethod($key);
            return $this->getFieldValue($key, $arguments);
        }
        if (strpos($key, 'set') === 0) {
            $key = $this->extractKeyFromMethod($key);
            return $this->setFieldValue($key, $arguments[0]);
        }

        return $this->forwardCallTo(self::query(), $key, $arguments);
    }

    protected function extractKeyFromMethod($key, $removePrefix = true): string
    {
        $pieces = preg_split('/(?=[A-Z])/', $key);
        //unset get/set
        if ($removePrefix) {
            unset($pieces[0]);
        }

        return strtolower(implode('_', $pieces));
    }

    public function hasField($key): bool
    {
        return !is_null($this->getReactForm()->getFQ($key));
    }

    public function getFieldLabel($key): string
    {
        return $this->getReactForm()->getFQ($key)->fq_displayname;
    }

    public function isDirty(): bool
    {
        return $this->__isDirty;
    }

    public function cloneReact(): React
    {
        return new React($this->__form_id, $this->toArray(true));
    }

    public function addParentContainer($container): React
    {
        $containers = $this->getParentContainersArray();

        if ($container instanceof Container) {
            $containerId = $container->getContainerId();
        } else {
            $containerId = $container;
        }

        if (!in_array($containerId, $containers, true)) {
            $containers[] = $containerId;

            $this['react_parent_containers'] = json_encode($containers);
        }
        return $this;
    }

    private function getParentContainersArray()
    {
        if (array_key_exists('react_parent_containers', $this->toArray()) &&
            !empty($this['react_parent_containers'])) {
            $containers = json_decode($this['react_parent_containers'], true);
        } else {
            $containers = [];
        }
        return $containers;
    }

    public function removeParentContainer($container): React
    {
        $containers = $this->getParentContainersArray();

        if ($container instanceof Container) {
            $containerId = $container->getContainerId();
        } else {
            $containerId = $container;
        }

        if (in_array($containerId, $containers, true)) {
            $pos = array_search($containerId, $containers, true);

            // Remove from array
            unset($containers[$pos]);

            $this['react_parent_containers'] = json_encode($containers);
        }
        return $this;
    }

    /**
     * @return <Container>[]
     */
    public function parentContainers(): array
    {
        $containers = $this->getParentContainersArray();

        $containerObjs = [];

        foreach ($containers as $container) {
            try {
                $containerObjs[] = ContainerFactory::resolve($container);
            } catch (ContainerHolderNotFoundException $e) {
                continue;
            }
        }
        return $containerObjs;
    }

    public function setReactRelatedEditionId($e_id): void
    {
        $this['react_edition_id'] = $e_id;
    }

    public function getReactFormId(): int
    {
        return $this->__form_id;
    }

    public function getPrimaryPermalink(): ?Permalink
    {
        //TODO Remove it
        /** @var Permalink */
        return Permalink::query()->where('p_react_id', $this->id())->where('p_type', Permalink::TYPE_PRIMARY)->first();
    }

    public function isMailable(): bool
    {
        return $this->mailableFQ() !== null;
    }

    private function mailableFQ(): ?FormQuestion
    {
        return $this->getReactForm()->form_questions->first(function (FormQuestion $item) {
            return $item->fq_type === ECO('forms.fqt.mailable.fqt_id');
        });
    }

    public function previewMailView()
    {
        return $this->getFieldValue($this->mailableFQ()->fq_name)->view();
    }

    /**
     * @param $key
     * @param array $arguments
     * @return Json|FQTBase|Unit|Text|Picture|Mailable|FQTPermalink|\Exolog\Module\Forms\FQT\Container\Container|mixed
     */
    public function getFieldValue($key, array $arguments = [])
    {
        if ($wrapper = $this->getFieldWrapper($key, $arguments)) {
            return $wrapper;
        }

        return $this[$key];
    }

    /**
     * @param array{toemail: string|array,fromEmail: string, fromName: string, subject: string, attachment: array} $mail_params
     * @throws Exception
     */
    public function send(array $mail_params): void
    {
        $this->getFieldValue($this->mailableFQ()->fq_name)->send($mail_params);
    }

    public function fill(array $attributes): React
    {
        foreach ($attributes as $key => $value) {
            $this->setFieldValue($key, $value);
        }
        return $this;
    }

    public function setFieldValue($key, $value): self
    {
        if (in_array($key, static::react_reserved_keys, true) || $this->hasField($key)) {

            if ($wrapper = $this->getFieldWrapper($key)) {
                $wrapper->setData($value);
            } else {
                $this[$key] = $value;
            }

            $this->__isDirty = true;
        } else {
            throw new RuntimeException("Field '$key' not found!");
        }
        return $this;
    }

    public function getRawOriginal($key = null)
    {
        if (!is_null($key)) {
            return $this->original[$key];
        }
        return $this->original;

    }

    public function getReactLayout(): ReactLayout
    {
        return ReactLayoutFactory::make($this);
    }

    public function toPDF(array $params = [])
    {
        return RenderPDFService::make()->htmlToPdf($this->render(), $params);
    }

    public function getReactOptions(): array
    {
        return $this->react_options;
    }

    protected function updateAlias(): void
    {
        if ($this->hasField('menu_title') && $this->hasField('alias') && (string)$this['alias'] === '') {

            $alias = Str::slug($this['menu_title']);

            if ($alias === '') {
                return;
            }

            //search for similar reacts
            $count = 0;
            do {
                if ($count > 0) {
                    $alias .= '-' . $count;
                }

                $exists = react()
                    ->edition($this->getReactRelatedEditionId())
                    ->form($this->getReactForm()->form_name)
                    ->where('alias', $alias)
                    ->where('react_id', '<>', $this->id())
                    ->exists();

                $count++;
            } while ($exists);

            if (!empty($alias)) {
                $this['alias'] = $alias;
            }
        }

    }

    public function getReactRelatedEditionId()
    {
        return $this['react_edition_id'] ?? getCurrentEdition();
    }

    public function id(): ?int
    {
        return is_numeric($this['react_id']) ? (int)$this['react_id'] : null;
    }

    public function __get($name)
    {
        return $this->getFieldValue($name);
    }

    public function isNew(): bool
    {
        return is_null($this->id());
    }

    private function getFieldWrapper($key, array $arguments = []): ?FQTBase
    {

        if (!empty($this->wrappers[$key])) {
            return $this->wrappers[$key]->with(!empty($arguments[0]) ? $arguments[0] : []);
        }

        $fq = $this->getReactForm()->getFQ($key);

        if ($fq === null && !in_array($key, static::react_reserved_keys, true)) {
            throw new RuntimeException("Field '$key' not found in this React instance!");
        }

        if ($fq && $wrapperClass = $fq->getECO('react_fqt_wrapper')) {
            $this->wrappers[$key] = new $wrapperClass($this, $fq, $arguments);
            return $this->wrappers[$key];
        }

        return null;
    }

    private function syncOriginal(): void
    {
        $this->original = $this->toArray();
    }

    public function getReactURL(bool $absolute = true): ?string
    {
        if ($this->hasField('permalink')) {
            return $this->getPermalink()->getURL($absolute);
        }
        return null;
    }
}
