<?php

namespace Exolog\Core\API\Controllers;

use Exolog\Module\API\ApiController;
use Exolog\Module\Http\Request;
use Exolog\Module\Newsletter\Model\Newsletter;
use Illuminate\Support\Arr;

class NewslettersController extends ApiController
{
    public function terminate(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);

        $ids = Arr::wrap($request['rn_id']);
        foreach ($ids as $schedule_id) {
            $schedule = Newsletter::findOrFail($schedule_id);
            $schedule->terminate();
        }
        return $this->api->dataMsg(true);
    }

    public function getNewsletters()
    {
        $this->checkAuth(['level' => 'admin']);
        $newsletters = Newsletter::query()->with('usergroup')->vueTablePaginate();
        return $this->api->dataMsg($newsletters);
    }

    public function getNewsletter()
    {
        $this->checkAuth(['level' => 'admin']);
        $newsletter = Newsletter::query()->with('usergroup', 'user')->findOrFail(\request('rn_id'));
        return $this->api->dataMsg($newsletter);
    }
}

