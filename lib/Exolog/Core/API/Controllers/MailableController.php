<?php

namespace Exolog\Core\API\Controllers;

use Exolog\Core\Forms\React;
use Exolog\Module\API\ApiController;
use Exolog\Module\Forms\Model\FormQuestion;
use Exolog\Module\Newsletter\Model\Newsletter;
use Exolog\Module\Users\Model\Usergroup;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Validation\ValidationException;
use RuntimeException;


class MailableController extends ApiController
{

    /**
     * @throws AuthenticationException
     * @throws ValidationException
     */
    public function send()
    {
        $this->checkAuth(['level' => 'admin']);


        $validated = request()->validate([
            'ug_id' => 'required_without_all:email,fq_id',
            'email' => 'required_without_all:ug_id,fq_id',
            'fq_id' => 'required_without_all:ug_id,email',
            'react_id' => 'required',
            'scheduled' => 'nullable|date_format:Y-m-d\TH:i:s.v\Z',
            'subject' => 'nullable|string',
            'QBRules' => 'required_with:fq_id|array',
        ]);

        if (count(array_filter(request()->only(['ug_id', 'email', 'fq_id']))) > 1) {
            throw ValidationException::withMessages([
                'ug_id' => 'Only one of ug_id, email or fq_id is allowed',
                'email' => 'Only one of ug_id, email or fq_id is allowed',
                'fq_id' => 'Only one of ug_id, email or fq_id is allowed',
            ]);
        }

        $ug_id = $validated['ug_id'];
        $email = $validated['email'];
        $react_id = $validated['react_id'];
        $fq_id = $validated['fq_id'];
        $scheduled = $validated['scheduled'];
        $subject = $validated['subject'];

        $react = react()->findOrFail($react_id);

        if ($scheduled && $email) {
            throw new RuntimeException('Not implemented yet!');
        }

        if ($scheduled && ($ug_id || $fq_id)) {

            $newsletter = Newsletter::create([
                'rn_send_from' => $react->getMailable()->getFrom(),
                'rn_send_fromname' => $react->getMailable()->getFromName(),
                'rn_subject' => $subject ?? $react->getMailable()->getSubject(),
                'rn_mail_body' => $react->getMailable()->view()->render(),
                'rn_usergroup' => $ug_id,
                'rn_fq_id' => $fq_id,
                'rn_extra' => [
                    'QBRules' => $validated['QBRules'] ?? null,
                ],
                'rn_date_scheduled' => $scheduled,
                'rn_react_id' => $react_id
            ]);


            return $this->api->dataMsg(['newsletter_id' => $newsletter->rn_id]);
        }

        $params = ['toemail' => $email ?? $ug_id ?? 'fq_id:' . $fq_id];
        if ($subject !== null) {
            $params['subject'] = $subject;
        }
        if ($fq_id) {
            $params['QBRules'] = $validated['QBRules'];
        }

        $sendLog = $react->getMailable()
            ->send($params)
            ->getSendLog();

        return $this->api->dataMsg($sendLog);
    }

    /**
     * @throws AuthenticationException
     * @throws ValidationException
     */
    public function estimatedRecipients()
    {
        $this->checkAuth(['level' => 'admin']);


        $validated = request()->validate([
            'ug_id' => 'required_without_all:email,fq_id',
            'email' => 'required_without_all:ug_id,fq_id',
            'fq_id' => 'required_without_all:ug_id,email',
            'QBRules' => 'required_with:fq_id|array',
        ]);

        if (count(array_filter(request()->only(['ug_id', 'email', 'fq_id']))) > 1) {
            throw ValidationException::withMessages([
                'ug_id' => 'Only one of ug_id, email or fq_id is allowed',
                'email' => 'Only one of ug_id, email or fq_id is allowed',
                'fq_id' => 'Only one of ug_id, email or fq_id is allowed',
            ]);
        }

        $ug_id = $validated['ug_id'];
        $email = $validated['email'];
        $fq_id = $validated['fq_id'];


        if ($email) {
            $count = 1;
        } elseif ($ug_id) {
            $ug = Usergroup::findOrFail($ug_id);
            $count = $ug->users->count();
        } elseif ($fq_id) {
            $fq = FormQuestion::findOrFail($fq_id);
            $count = React::query()
                ->form($fq->fq_form)
                ->applyQBRules($validated['QBRules'])
                ->get()
                ->filter(function ($react) use ($fq) {
                    return !empty($react[$fq->fq_name]);
                })->count();
        } else {
            $count = 0;
        }

        return $this->api->dataMsg($count);
    }

}
