<?php

namespace Exolog\Core\API\Controllers;

use Exolog\Module\API\ApiController;
use Exolog\Module\Forms\Model\Form;
use Exolog\Module\Forms\Model\FormPermalink;
use Exolog\Module\Groups\Model\Group;
use Exolog\Module\Http\Request;
use Exolog\Module\Support\Facades\Vars;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class FormsController extends ApiController
{

    public function getForms()
    {
        $this->checkAuth(['level' => 'admin']);
        $query = Form::query()->with('form_permalinks');
        if (request('group_id')) {
            $query->whereExists(function (Builder $q) {
                $q->select(DB::raw(1))
                    ->from('relations')
                    ->where('r_group', request('group_id'))
                    ->where('r_type', 'form')
                    ->where('r_obj', DB::raw('form.form_id'));
            });
        }
        $forms = $query->vueTablePaginate();
        return $this->api->dataMsg($forms);
    }


    public function getForm(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);

        $params = $request->validate([
            'form_id' => 'required|numeric',
            'container' => 'nullable|string',
        ]);

        $form = Form::findOrFail($params['form_id']);
        $form->load([
            'form_questions_tree',
            'form_questions_tree.form_questions_tree',
            'form_questions_tree.form_questions_tree.form_questions_tree'
        ]);

        $form['vars'] = Vars::getAll(['var_parent' => $form->form_id]);

        return $this->api->dataMsg($form);
    }

    public function saveForm(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);

        $form = Form::saveFromArray($request->all());

        if ($request->has('vars')) {
            Vars::massUpdate($request['vars'], 'form', $form->form_id);
        }

        if ($request['group_id']) {
            Group::findOrFail($request['group_id'])->addRelation($form->form_id);
        }

        return $this->api->dataMsg($form);
    }

    public function deleteForm(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);
        $params = $request->validate(['form_id' => 'required']);
        Form::destroy($params['form_id']);
        return $this->api->dataMsg(true);
    }

    public function getFormPermalinks()
    {
        $this->checkAuth(['level' => 'admin']);
        $this->api->dataMsg([
            'form_permalinks' =>
                Form::findOrFail(request('form_id'))->getFormPermalinks()
        ]);
    }

    public function saveFormPermalinks()
    {
        $this->checkAuth(['level' => 'admin']);
        Form::findOrFail(request('form_id'))
            ->saveFormPermalinks(request('form_permalinks'), request('update_permalinks', false));
    }

    public function updatePermalinks()
    {
        $this->checkAuth(['level' => 'admin']);
        $this->api->dataMsg(
            FormPermalink::find(request('fp_id'))->updatePermalinks()->count()
        );
    }
}
