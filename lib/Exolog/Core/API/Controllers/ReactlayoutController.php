<?php

namespace Exolog\Core\API\Controllers;

use Exolog\Module\API\ApiController;
use Exolog\Module\ReactLayout\ReactLayoutConfigurator;
use Exolog\Module\Support\Facades\RL;

class ReactlayoutController extends ApiController
{

    private ReactLayoutConfigurator $configurator;

    public function __construct()
    {
        parent::__construct();
        $this->configurator = app(ReactLayoutConfigurator::class);
    }

    public function loadTemplates()
    {
        $this->api->dataMsg(RL::getTemplates());
    }

    public function loadReactLayout()
    {
        $params = request()->validate([
            'layout' => 'required',
        ]);
        $result = $this->configurator->load($params['layout']);
        $this->api->dataMsg($result);
    }

    public function saveReactScope()
    {
        $params = request()->validate([
            'layout' => 'required',
            'scope' => 'required',
        ]);
        $this->configurator->saveScope($params['layout'], $params['scope']);
        $this->api->dataMsg(true);
    }

    public function saveReactLayout()
    {
        $params = request()->validate([
            'layout' => 'required',
            'react_layout' => 'required',
            'scope' => 'required',
        ]);
        $this->configurator->save($params['layout'], $params['scope'], $params['react_layout']);
        $this->api->dataMsg(true);
    }

    public function loadBlocks()
    {
        $reacts = RL::getBlocks()->keyBy('react_id');
        $this->api->dataMsg($reacts);
    }

    public function searchBlocks()
    {
        $params = request()->all();
        $items = RL::getBlocks($params);

        $this->api->dataMsg([
            'data' => $items,
            'total' => count($items)
        ]);
    }

    public function saveFilter()
    {
        RL::saveQBRules(request('QBRules'));
    }

    public function loadFilter()
    {
        $this->api->dataMsg(['QBRules' => RL::getQBRules()]);
    }
}
