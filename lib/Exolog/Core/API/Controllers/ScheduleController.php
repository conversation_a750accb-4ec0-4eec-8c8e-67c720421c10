<?php

namespace Exolog\Core\API\Controllers;


use Exolog\Module\API\ApiController;
use Exolog\Module\Schedule\Http\Requests\ScheduleRequest;
use Exolog\Module\Schedule\Http\Services\CommandService;
use Exolog\Module\Schedule\Model\Schedule;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class ScheduleController extends ApiController
{
    /**
     * Display a listing of the schedule.
     *
     */
    public function getSchedules(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);

        $schedules = Schedule::onlySite();
        $schedules = $schedules->vueTablePaginate();
        return $this->api->dataMsg($schedules);
    }

    /**
     * Display the schedule.
     */
    public function getSchedule()
    {
        $this->checkAuth(['level' => 'admin']);

        $schedule = Schedule::findOrFail(request('id'));
        $schedule->load([
            'histories' => function ($query) {
                $query->latest();
            }
        ]);
        return $this->api->dataMsg($schedule);
    }

    /**
     * Update the schedule
     */
    public function saveSchedule(ScheduleRequest $request)
    {
        $this->checkAuth(['level' => 'admin']);

        if ($request['id']) {
            $schedule = Schedule::findOrFail($request['id']);
            $schedule->update($request->all());
            return $this->api->dataMsg(true);
        }
        $schedule = Schedule::create($request->all());
        return $this->api->dataMsg($schedule);
    }

    /**
     * Remove the schedule
     */
    public function deleteSchedule(\Exolog\Module\Http\Request $request)
    {
        $this->checkAuth(['level' => 'admin']);

        $schedule_ids = Arr::wrap($request['id']);
        foreach ($schedule_ids as $schedule_id) {
            $schedule = Schedule::findOrFail($schedule_id);
            $schedule->delete();
        }
        return $this->api->dataMsg(true);
    }

    public function inactivate(\Exolog\Module\Http\Request $request)
    {
        $this->checkAuth(['level' => 'admin']);

        $schedule_ids = Arr::wrap($request['id']);
        foreach ($schedule_ids as $schedule_id) {
            $schedule = Schedule::findOrFail($schedule_id);
            $schedule->status = Schedule::STATUS_INACTIVE;
            $schedule->save();
        }
        return $this->api->dataMsg(true);
    }

    public function activate(\Exolog\Module\Http\Request $request)
    {
        $this->checkAuth(['level' => 'admin']);

        $schedule_ids = Arr::wrap($request['id']);
        foreach ($schedule_ids as $schedule_id) {
            $schedule = Schedule::findOrFail($schedule_id);
            $schedule->status = Schedule::STATUS_ACTIVE;
            $schedule->save();
        }
        return $this->api->dataMsg(true);
    }

    public function commands(CommandService $commandService)
    {
        $this->checkAuth(['level' => 'admin']);

        return $this->api->dataMsg($commandService->getForSite());
    }
}