<?php

namespace Exolog\Core\API\Controllers;

use Exolog\Module\API\ApiController;
use Exolog\Module\Domains\Model\Domain;
use Exolog\Module\Http\Request;
use Exolog\Module\Support\Facades\Auth;
use Exolog\Module\Users\Model\User;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Auth\StatefulGuard;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Validation\ValidationException;
use PragmaRX\Google2FA\Exceptions\IncompatibleWithGoogleAuthenticatorException;
use PragmaRX\Google2FA\Exceptions\InvalidCharactersException;
use PragmaRX\Google2FA\Exceptions\SecretKeyTooShortException;
use PragmaRX\Google2FA\Google2FA;

/**
 * API for auth
 */
class AuthController extends ApiController
{
    /**
     * @var Guard|StatefulGuard
     */
    private $guard;

    public function __construct()
    {
        parent::__construct();
        $this->guard = Auth::guard('system');
    }

    private function getCurrentUserData()
    {
        /** @var User $user */
        $user = $this->guard->user();
        if ($user === null) {
            return false;
        }
        $role = 'user';
        if (!empty($user['u_isadmin'])) {
            $role = 'admin';
        }
        if (!empty($user['u_isdeveloper'])) {
            $role = 'developer';
        }

        if (!empty($user->u_two_factor_secret)) {
            $user['u_2fa'] = true;
        } else {
            $user['u_2fa'] = false;
        }

        return [
            'id' => $user['u_id'],
            'email' => $user['u_email'],
            'alt_email' => $user['u_alt_email'],
            'lang' => $user['u_lang'],
            'first_name' => $user['u_first_name'],
            'between_name' => $user['u_between_name'],
            'last_name' => $user['u_last_name'],
            'sex' => $user['u_sex'],
            'address' => $user['u_address'],
            'zipcode' => $user['u_zipcode'],
            'city' => $user['u_city'],
            'country' => $user['u_country'],
            'state' => $user['u_state'],
            'telephone' => $user['u_telephone'],
            'info' => $user['u_info'],
            'date_added' => $user['u_date_added'],
            //todo
            'isadmin' => ($user['u_isadmin'] === 1),
            'isdeveloper' => ($user['u_isdeveloper'] === 1),
            'role' => $role,
            'is2FA' => $user['u_2fa'],
        ];
    }

    public function getCurrentUser()
    {
        $user = $this->getCurrentUserData();

        if (!empty($user)) {
            return $this->api->dataMsg($user);
        }
        return $this->api->errorMsg(array('errorAlias' => 'auth'));
    }

    public function signOut(Request $request)
    {
        Auth::signOut();

        if ($request->has('redir')) {
            return Redirect::to($request['redir']);
        }
    }

    public function backendHashLogin(Request $request)
    {
        $hash = $request['hash'];

        if (!empty($hash)) {
            Auth::loginUsingHash($hash);
        } else {
            $this->throwFailedAuthenticationException();
        }
        if ($request->has('redir')) {
            return Redirect::to($request['redir']);
        }
        return $this->api->result(true);
    }

    public function signIn(Request $request)
    {
        $validated = $request->validate([
            'email' => 'required|email',
            'password' => 'required',
            'code' => 'nullable',
        ]);

        if (Auth::signIn(
            $validated,
            $request->get('remember', false)
        )) {
            return $this->api->dataMsg($this->getCurrentUserData());
        }
        $this->throwFailedAuthenticationException();
    }

    /**
     * @throws ValidationException
     */
    private function throwFailedAuthenticationException(): void
    {
        throw ValidationException::withMessages([
            'email' => [trans('auth.failed')],
        ]);
    }

    /**
     * @throws IncompatibleWithGoogleAuthenticatorException
     * @throws InvalidCharactersException
     * @throws SecretKeyTooShortException
     */
    public function create2FA()
    {

        $u_id = request('u_id');

        if (!empty($u_id)) {
            $this->checkAuth(['level' => 'admin']);
            $user = User::query()->findOrFail($u_id);
        } else {
            $user = Auth::user();
        }

        $ga = new Google2FA();

        $ga_secret = $ga->generateSecretKey();

        session()->put('ga_secret', $ga_secret);

        $barcode = (sprintf("otpauth://totp/%s?secret=%s&issuer=%s",
            $user['u_email'],
            $ga_secret,
            Domain::getMainDomain()->domain_name
        ));

        $result = [];
        $result['barcode'] = $barcode;
        $result['code'] = $ga_secret;
        $this->api->dataMsg($result);
    }

    /**
     * @throws IncompatibleWithGoogleAuthenticatorException
     * @throws InvalidCharactersException
     * @throws SecretKeyTooShortException
     */
    public function enable2FA()
    {
        $u_id = request('u_id');

        if (!empty($u_id)) {
            $this->checkAuth(['level' => 'admin']);
        } else {
            $u_id = Auth::id();
        }

        $ga_secret = session()->get('ga_secret');
        $ga = new Google2FA();

        if (!$ga->verify(request('ga_code'), $ga_secret)) {
            return $this->api->errorMsg('Wrong code. Please try again.');
        }

        $user = User::findOrFail($u_id);
        $user->u_two_factor_secret = $ga_secret;
        $user->save();
    }

    public function disable2FA()
    {
        $u_id = request('u_id');
        if (!empty($u_id)) {
            $this->checkAuth(['level' => 'admin']);
        } else {
            $u_id = Auth::id();
        }

        $user = User::findOrFail($u_id);

        $user->u_two_factor_secret = null;
        $user->save();

    }

    public function checkUser2FA()
    {
        $result = array('data' => array(), 'success' => true);

        $userEmail = request('email');
        $u_id = request('u_id');

        if (empty($userEmail) && empty($u_id)) {
            $result['success'] = false;
            $result['error'] = "Invalid parameter user email";
            return $this->api->result($result);
        }

        if ($userEmail) {
            $user = User::findByEmail($userEmail);
        } else {
            $user = User::findOrFail($u_id);
        }

        if ($user === null) {
            $result['data']['is_2fa'] = false;
            return $this->api->result($result);
        }

        $result['data']['is_2fa'] = !empty($user->u_two_factor_secret);

        return $this->api->result($result);
    }
}
