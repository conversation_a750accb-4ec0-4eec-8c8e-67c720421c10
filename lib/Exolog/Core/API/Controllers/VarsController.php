<?php

namespace Exolog\Core\API\Controllers;

use Exolog\Module\API\ApiController;
use Exolog\Module\Support\Facades\Vars;

class VarsController extends ApiController
{

    public function getVars()
    {
        $this->checkAuth(['level' => 'admin']);

        $options = request()->all();
        $result = Vars::getAll($options);
        $this->api->dataMsg($result);
    }

    public function saveVars()
    {
        $this->checkAuth(['level' => 'admin']);

        $vars = request()->all();
        Vars::massUpdate($vars);
        $this->api->dataMsg(true);
    }
}
