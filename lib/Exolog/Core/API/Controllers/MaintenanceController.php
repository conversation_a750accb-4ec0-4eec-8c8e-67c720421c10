<?php

namespace Exolog\Core\API\Controllers;

use Exolog\Module\API\ApiController;
use Exolog\Module\Container\ContainerService;
use Exolog\Module\Forms\PictureFieldRecoveryService;
use Exolog\Module\Sitemap\SitemapSchedule;
use Exolog\Module\Skeleton\SkeletonService;
use Exolog\Module\Support\Facades\BuffLog;
use Exolog\Module\Support\Facades\Site;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

class MaintenanceController extends ApiController
{

    /**
     * maintenance function to recovery parent containers in reacts
     */
    public function restoreParentContainer()
    {

        $this->checkAuth(['level' => 'admin']);
        $log = [];

        DB::table('react')
            ->whereIn('react_form',
                function ($query) {
                    $query->select('form_id')
                        ->from('form')
                        ->where('form_site_id', Site::id());
                }
            )
            ->where('react_isdeleted', '0')
            ->update(['react_parent_containers' => null]);

        foreach (ContainerService::collcetContainers() as $container) {
            $log[] = 'Base Container = ' . $container->getContainerId();

            $reacts = $container->getReactsArray();
            foreach ($reacts as $react) {
                $log[] = '   React = ' . $react['react_id'];
                $react->addParentContainer($container);
                $react->save();
            }
        }

        $this->api->dataMsg(['log' => $log]);
    }


    public function buildSkeleton()
    {
        $params = request()->all();
        $this->checkAuth(['level' => 'developer']);
        SkeletonService::make()->build($params['skeletons']);
        $this->api->dataMsg(['log' => BuffLog::getRecords()]);
    }

    public function emptyCache()
    {
        $this->checkAuth(['level' => 'developer']);
        exolog()->emptyCache();
        BuffLog::info('Done.');
        $this->api->dataMsg(['log' => BuffLog::getRecords()]);
    }

    public function generateSitemap(SitemapSchedule $sitemapSchedule)
    {
        $this->checkAuth(['level' => 'developer']);
        Artisan::queue('exolog:site:generate-sitemap', ['--site_id' => Site::id()]);
        BuffLog::info('Done. The task was created and will run in a few minutes.');
        $this->api->dataMsg(['log' => BuffLog::getRecords()]);
    }

    public function pictureRecovery()
    {
        $this->checkAuth(['level' => 'admin']);
        $service = PictureFieldRecoveryService::make();
        $service->recovery();
        BuffLog::info('Done.');
        $this->api->dataMsg(['log' => BuffLog::getRecords()]);
    }
}
