<?php

namespace Exolog\Core\API\Controllers;

use Exolog\Module\API\ApiController;
use Exolog\Module\Groups\Model\Group;
use Exolog\Module\Http\Request;
use Illuminate\Support\Arr;

class GroupsController extends ApiController
{

    function getGroupTree(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);
        $groups = Group::getGroupTree($request['group_type']);
        $this->api->dataMsg($groups);
    }

    function getGroup(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);
        $this->api->dataMsg(Group::findOrFail($request['group_id']));
    }

    function saveGroup(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);

        $validated = $request->validateWithAttributes([
            'group_id' => 'nullable|integer',
            'group_type' => 'required',
            'group_alias' => 'nullable',
            'group_name' => 'required',
            'group_parent' => 'nullable|integer',
            'group_info' => 'nullable',
        ]);

        if ($validated['group_id']) {
            $group = Group::findOrFail($validated['group_id']);
            $group->update($validated);
        } else {
            $group = Group::create($validated);
        }
        return $this->api->dataMsg($group);

    }

    function deleteGroup(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);

        $ids = Arr::wrap($request['group_id']);
        foreach ($ids as $group_id) {
            $edition = Group::findOrFail($group_id);
            $edition->delete();
        }
        $this->api->dataMsg(true);
    }

    function addRelation(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);
        foreach (Arr::wrap($request['r_obj']) as $r_obj) {
            Group::findOrFail($request['group_id'])->addRelation($r_obj);
        }
        $this->api->dataMsg(true);
    }

    function removeRelation(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);
        Group::findOrFail($request['group_id'])->removeRelation($request['r_obj']);
        $this->api->dataMsg(true);
    }
}
