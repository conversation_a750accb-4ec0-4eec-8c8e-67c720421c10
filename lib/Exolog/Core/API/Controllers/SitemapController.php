<?php

namespace Exolog\Core\API\Controllers;

use Exolog\Module\API\ApiController;
use Exolog\Module\Http\Request;
use Exolog\Module\Sitemap\SitemapManager;

class SitemapController extends ApiController
{

    public function getReacts(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);
        $params = $request->all();
        $this->api->dataMsg(SitemapManager::make()->getReacts($params));
    }

}
