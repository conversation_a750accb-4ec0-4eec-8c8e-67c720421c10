<?php

namespace Exolog\Core\API\Controllers;

use Exolog\Module\API\ApiController;
use Exolog\Module\Domains\Model\Domain;
use Exolog\Module\Http\Request;

class DomainsController extends ApiController
{
    public function getDomains()
    {
        $this->checkAuth(['level' => 'admin']);
        $domains = Domain::query()->vueTablePaginate();
        return $this->api->dataMsg($domains);
    }

    public function saveDomain(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);
        $domain = $request['domain'];
        $domain = Domain::saveFromArray($domain);
        $this->api->dataMsg($domain->domain_id);
    }

    public function validateSubdomain(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);
        $subdomain = $request['subdomain'];

        $exists = Domain::crossSiteQuery()->where('domain_name', $subdomain)->exists();
        if ($exists) {
            $this->api->dataMsgPush("This domain already exists in system.");
        }
    }

}
