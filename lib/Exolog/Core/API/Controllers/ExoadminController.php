<?php

namespace Exolog\Core\API\Controllers;

use Exolog\Module\API\ApiController;
use Exolog\Module\Editions\Model\Edition;
use Exolog\Module\Forms\Model\Form;
use Exolog\Module\Support\Facades\Defaults;
use Exolog\Module\Support\Facades\Vars;
use Exolog\Module\Version\GitlabVersionCheckService;
use Illuminate\Support\Arr;

class ExoadminController extends ApiController
{

    public function getConfig()
    {

        $this->checkAuth(['level' => 'admin']);

        $config = [];
        $config['defaults'] = Defaults::toArray();
        $config['constants'] = $this->geConstantConfig();
        $config['jsonConfig'] = getSiteJsonConfig();
        $config['edition'] = $this->getEditionConfig();
        $config['language'] = $this->getLanguageConfig();
        $config['customButtons'] = $this->getCustomBtnConfig();
        $config['app'] = $this->getAppConfig();

        $config['ECO'] = ECO()->get();

        $this->api->dataMsg($config);
    }


    private function getEditionConfig()
    {
        $config = [];
        $config['editions'] = Edition::all();
        $config['current_edition_id'] = getCurrentEdition();
        $config['default_edition_id'] = getDefaultEdition();

        return $config;
    }

    private function getLanguageConfig(): array
    {
        $config = [];
        $config['current'] = Defaults::get('language');
        $config['default'] = Vars::edition('language', getDefaultEdition())->getValue();
        return $config;
    }

    private function getCustomBtnConfig(): array
    {
        $config = [];
        $config['form_name'] = ECO('define.SYSTEM_URL_FORM');
        $form = Form::findByName($config['form_name']);
        if ($form !== null) {
            $config['form_id'] = $form->form_id;
            $var = Vars::form('container_custom_buttons', $form->form_id);
            if (empty($var->getId())) {
                $var->save();
            }
            $config['container'] = $var->getContainer()->getContainerId();
        }

        return $config;

    }

    private function geConstantConfig(): array
    {
        $config = [];
        return $config;
    }

    public function getECO()
    {
        $this->checkAuth(['level' => 'admin']);
        $this->api->dataMsg(ECO()->get());
    }

    public function getVersion()
    {
        $this->checkAuth(['level' => 'admin']);
        $this->api->dataMsg(GitlabVersionCheckService::make()->getVersion());
    }

    private function getAppConfig(): array
    {
        return [
            'app' => Arr::only(config('app'), ['timezone']),
            'database-schedule' => Arr::only(config('database-schedule'), ['timezone'])
        ];
    }

}
