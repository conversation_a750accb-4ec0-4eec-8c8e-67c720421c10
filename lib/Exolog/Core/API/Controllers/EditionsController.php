<?php

namespace Exolog\Core\API\Controllers;


use Exolog\Module\API\ApiController;
use Exolog\Module\Domains\Model\Domain;
use Exolog\Module\Editions\Model\Edition;
use Exolog\Module\Http\Request;
use Exolog\Module\Support\Facades\Site;
use Exolog\Module\Support\Facades\Vars;
use Illuminate\Support\Arr;

class EditionsController extends ApiController
{

    public function getEditions()
    {
        $this->checkAuth(['level' => 'admin']);
        $editions = Edition::query()->vueTablePaginate();
        return $this->api->dataMsg($editions);
    }

    public function getEdition(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);

        //validate request
        $validated = $request->validateWithAttributes([
            'e_id' => 'required|integer',

        ]);
        $edition = Edition::findOrFail($validated['e_id']);
        $edition['vars'] = Vars::getAll(['var_parent' => $edition->e_id]);
        $edition['domains'] = Domain::query()->where('domain_edition_id', $edition->e_id)->get()->toArray();
        return $this->api->dataMsg($edition);
    }

    public function saveEdition(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);

        $validated = $request->validateWithAttributes([
            'e_id' => 'nullable|integer',
            'e_title' => 'required',
            'e_info' => 'nullable|string',
            'e_isclosed' => 'boolean',
        ]);
        if ($validated['e_id']) {
            $edition = Edition::findOrFail($validated['e_id']);
            $edition->update($validated);
        } else {
            $edition = Edition::create($validated);
        }

        if ($request->has('vars')) {
            Vars::massUpdate($request['vars'], 'edition', $edition->e_id);
        }

        if ($request->has('domains')) {
            $ids = Arr::pluck($request['domains'], 'domain_id');
            Domain::query()
                ->where('domain_edition_id', $edition->e_id)
                ->whereNotIn('domain_id', $ids)
                ->update(['domain_edition_id' => null]);

            foreach ($request['domains'] as $domain) {
                $domain['domain_edition_id'] = $edition->e_id;
                Domain::saveFromArray($domain);
            }
        }

        return $this->api->dataMsg($edition);
    }

    public function deleteEdition(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);

        $ids = Arr::wrap($request['e_id']);
        foreach ($ids as $e_id) {
            $edition = Edition::findOrFail($e_id);
            $edition->delete();
        }

        $this->api->dataMsg(true);
    }

    public function setDefaultEdition()
    {
        $this->checkAuth(['level' => 'admin']);
        $e_id = request('e_id');
        $edition = Edition::findOrFail($e_id);
        $edition->setDefault();
        $this->api->dataMsg(true);
    }

    function getExoAdminTree()
    {
        $this->checkAuth(['level' => 'admin']);

        $tree = [];
        $tree[] = [
            'id' => 0,
            'text' => Site::get('site_name'),
            'data' => [
                'type' => 'root'
            ],
            'state' => [
                'expanded' => true,
                'editable' => true,
                'append' => true,
                'deleteble' => false,
            ],
            'children' => []
        ];

        $editions = Edition::all();

        foreach ($editions as $edition) {
            $tree[0]['children'][] = [
                'id' => $edition['e_id'],
                'text' => $edition['e_title'] . ($edition['e_isdefault'] == 1 ? ' (default)' : ''),
                'data' => [
                    'origin' => $edition,
                    'type' => 'edition',
                    'icon' => 'fa fa-code-fork'
                ],
                'state' => [
                    //'expanded' => $edition['e_isdefault'] == 1,
                    'editable' => true,
                    'append' => true,
                ],
                'children' => []
            ];
        }

        $this->api->dataMsg($tree);
    }
}
