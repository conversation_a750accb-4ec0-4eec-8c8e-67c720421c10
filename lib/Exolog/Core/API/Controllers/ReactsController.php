<?php

namespace Exolog\Core\API\Controllers;

use Exolog\Core\Abstracts\Controler;
use Exolog\Core\Forms\React;
use Exolog\Module\API\ApiController;
use Exolog\Module\Forms\Admin\ReactForm;
use Exolog\Module\Forms\FormProcessor;
use Exolog\Module\Http\Request;
use Illuminate\Support\Arr;

class ReactsController extends ApiController
{
    /**
     * Controler_Api_Forms::getReact()
     *
     * Returns reaction data by reaction id.
     *
     * @param mixed $params
     * @return
     * <AUTHOR> Semenov
     *
     */
    function getReact()
    {
        $this->checkAuth(['level' => 'admin']);

        $react_id = request('react_id');
        if (empty($react_id)) {
            return $this->api->errorMsg(array('error' => 'no react_id defined'));
        }
        $result = &$this->api->result;
        $react = react($react_id);

        $result['data'] = $react;
    }

    public function saveReact()
    {
        $this->checkAuth(['level' => 'admin']);
        $params = request()->all();

        $react = new React($params['form'], $params['react']);
        $react->save();
        $this->api->dataMsg(['react' => $react]);
    }

    public function deleteReact(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);

        foreach (Arr::wrap($request['react_id']) as $id) {
            react($id)->delete();
        }
        $this->api->dataMsg(true);
    }

    public function updateReactEdition()
    {
        $this->checkAuth(['level' => 'admin']);
        $params = request()->all();
        if (empty($params['e_id'])) {
            return $this->api->errorMsg(array('error' => 'No e_id defined'));
        }


        foreach ($params['reacts'] as $item) {
            $react = react()->findOrFail($item['react_id']);
            $react->setReactRelatedEditionId($params['e_id']);
            $react->save();
        }

        $this->api->dataMsg(true);
    }


    public function saveReactFieldValue()
    {
        $this->checkAuth(['level' => 'admin']);
        $params = request()->all();

        if (empty($params['react_id'])) {
            return $this->api->errorMsg(array('error' => 'no react_id defined'));
        }

        if (empty($params['name'])) {
            return $this->api->errorMsg(array('error' => 'no field name defined'));
        }

        if (!isset($params['value'])) {
            return $this->api->errorMsg(array('error' => 'no value defined'));
        }

        $react = react()->find($params['react_id']);


        if (is_null($react)) {
            return $this->api->errorMsg('React not found!');
        }

        $react->setFieldValue($params['name'], $params['value']);

        $react->save();

        $this->api->dataMsg($params['react_id']);
    }

    public function searchByFormsFields()
    {
        $this->checkAuth(['level' => 'admin']);
        $params = request()->only([
            "filter",
            "limit",
            "way",
            "page",
            "byColumn",
            "order",
            "form_questions",
            "query",
        ]);
        $result = react()->search($params)->exoPaginate($params);
        $this->api->dataMsg($result);

    }

    public function setHomepageReact()
    {
        $this->checkAuth(['level' => 'admin']);
        $params = request()->all();
        $react = \react()->findOrFail($params['react_id']);
        $react->save(['use_as_homepage' => true]);
        $this->api->dataMsg(true);
    }


    public function getPermalink()
    {
        $this->checkAuth(['level' => 'admin']);
        $react = react()->findOrFail(request('react_id'));
        $this->api->dataMsg($react->getPermalink()->url);
    }

    public function loadReactForm()
    {

        $this->checkAuth(['level' => 'admin']);

        $react_id = request('react_id');
        $form_id = request('form_id');

        if (empty($react_id) || $react_id === 'new') {
            if (empty($form_id)) {
                return $this->api->errorMsg('form_id or react_id is not defined!');
            }
            $react = new React($form_id);
        } else {
            $react = React::find($react_id);
            if (is_null($react)) {
                return $this->api->errorMsg('React not found! react_id=' . $react_id);
            }
        }

        $this->api->dataMsg(
            new ReactForm($react, request()->only(['container']))
        );
    }

    public function submitReactForm()
    {
        $this->checkAuth(['level' => 'admin']);

        //todo remove it
        $bag = request()->request;
        $bag->set('formconf', $bag->get('__formconf'));

        $react = FormProcessor::make()->run();

        $this->api->dataMsg(
        //TODO multiselect value as array after save/string in new
            new ReactForm(React::find($react->id()))
        );
    }
}
