<?php

namespace Exolog\Core\API\Controllers;

use Exolog\Module\API\ApiController;
use Exolog\Module\ExoEditor\XEditableService;
use Exolog\Module\Forms\Model\Form;
use Exolog\Module\Support\Facades\ExoFile;
use Exolog\Module\Support\Facades\Vars;
use Illuminate\Support\Facades\Storage;

class EditorController extends ApiController
{
    private XEditableService $xEditableService;

    public function __construct()
    {
        parent::__construct();
        $this->xEditableService = new XEditableService();
    }

    public function getXEditable()
    {
        $this->checkAuth(['level' => 'admin']);

        $elements = request('elements');
        $result = $this->xEditableService->prepare($elements);
        $this->api->dataMsg($result);
    }

    public function handleXEditable()
    {
        $this->checkAuth(['level' => 'admin']);

        $value = request('value');
        $exedit = request('exedit');
        $this->api->dataMsg($this->xEditableService->handle($value, $exedit));
    }

    public function saveRelatedStyles()
    {
        $this->checkAuth(['level' => 'admin']);
        $validated = request()->validate([
            'optionField' => 'required',
            'optionId' => 'required',
            'styles' => '',
        ]);

        $JSONFILE = getSiteJsonConfig();
        $optionId = $validated['optionId'];
        $optionField = 'extra_' . $validated['optionField'];
        $JSONFILE[$optionId][$optionField]['styles'] = $validated['styles'] ?? [];
        putSiteJsonConfig($JSONFILE);
        $this->api->dataMsg(true);
    }

    public function getRelatedStyles()
    {
        $this->checkAuth(['level' => 'admin']);
        $validated = request()->validate([
            'optionField' => 'required',
            'optionId' => 'required',
        ]);
        $JSONFILE = getSiteJsonConfig();
        $optionId = $validated['optionId'];
        $optionField = 'extra_' . $validated['optionField'];
        $this->api->dataMsg(data_get($JSONFILE, "$optionId.$optionField.styles", []));
    }

    public function getStyleFormats()
    {
        $this->checkAuth(['level' => 'admin']);
        $edition = getCurrentEdition();
        $path = "resources/formats/$edition.json";
        if (Storage::disk('site')->exists($path)) {
            $content = Storage::disk('site')->get($path);
        } else {
            $path = "resources/formats/_default.json";
            if (Storage::disk('site')->exists($path)) {
                $content = Storage::disk('site')->get($path);
            } else {
                $content = Storage::get('homepages.webkracht.nl/htdocs/tmpl/skeleton/site/resources/formats/_default.json');
                Storage::disk('site')->put($path, $content);
            }
        }
        $this->api->dataMsg(compact('path', 'content'));
    }

    public function saveStyleFormats()
    {
        $this->checkAuth(['level' => 'admin']);
        $validated = request()->validate([
            'path' => 'required',
            'content' => 'required',
        ]);
        $path = $validated['path'];
        $content = $validated['content'];
        Storage::disk('site')->put($path, $content);
        $this->api->dataMsg(true);
    }

    public function getCustomButtons()
    {
        $this->checkAuth(['level' => 'admin']);
        $tree = Vars::form('container_custom_buttons', ECO('define.SYSTEM_URL_FORM'))->getItemsTree();
        $this->api->dataMsg(['tree' => $this->buildCustomBtnTree($tree)]);
    }

    private function buildCustomBtnTree($tree)
    {
        foreach ($tree as $key => $item) {
            if ($item['react']['publish'] != 1) {
                unset($tree[$key]);
            }
            $item['text'] = $item['react']['menu_title'];
            $item['type'] = $item['react']['type'];
            $item['icon'] = $item['react']['icon'];
            $item['params'] = $item['react']['params'];
            $item['href'] = $item['react']['url'];
            $item['target'] = $item['react']['target'] ?: '_blank';
            unset($item['react']);
            if ($item['children']) {
                $item['nodes'] = $item['children'];
                unset($item['children']);
                $item['nodes'] = $this->buildCustomBtnTree($item['nodes']);
            }
            $tree[$key] = $item;
        }
        return $tree;
    }

    public function addFormQuestionValue()
    {
        $this->checkAuth(['level' => 'admin']);

        $form_id = request('form_id');
        $field_name = request('field');
        $value = trim(request('value'));

        if (empty($value)) {
            return $this->api->errorMsg(array('error' => 'Value is empty.'));
        }

        $form = Form::findOrFail($form_id);
        $fq = $form->getFQ($field_name);

        if ($fq->values->where('fqv_value', $value)->count()) {
            return $this->api->errorMsg(array('error' => 'Duplicate value.'));
        }
        $fqv = $fq->values()->create([
            'fqv_value' => $value,
            'fqv_displayvalue' => $value,
            'fqv_isdefault' => 0
        ]);

        return $this->api->dataMsg($fqv);
    }

    public function deleteFormQuestionValue()
    {
        $this->checkAuth(['level' => 'admin']);

        $form_id = request('form_id');
        $field_name = request('field');
        $value = trim(request('value'));

        if (empty($value)) {
            return $this->api->errorMsg(array('error' => 'Value is empty.'));
        }

        $form = Form::findOrFail($form_id);
        $fq = $form->getFQ($field_name);

        if ($fqv = $fq->values->firstWhere('fqv_value', $value)) {
            $fqv->delete();
        } else {
            return $this->api->errorMsg(array('error' => 'Value not found'));

        }
        return $this->api->dataMsg(true);
    }

    public function loadToolbarButtons()
    {
        $id = request('id');
        $field = request('field');
        $optionId = request('optionId');

        $content = getSiteJsonConfig();

        $options = data_get($content, "$optionId.$field",
            []);// (isset($content[$optionId]) && isset($content[$optionId][$field]) ? $content[$optionId][$field] : array());
        $extraOptions = data_get($content, "$optionId.extra_$field",
            []); //(isset($content[$optionId]) && isset($content[$optionId]['extra_' . $field]) ? $content[$optionId]['extra_' . $field] : array());
        //$fieldOptions = data_get($content, "$id.$field", []);//(isset($content[$id]) && isset($content[$id][$field]) ? $content[$id][$field] : array());
        $fieldExtraOptions = data_get($content, "$id.extra_$field",
            []);//(isset($content[$id]) && isset($content[$id]['extra_' . $field]) ? $content[$id]['extra_' . $field] : array());

        array_shift($options);

        $this->api->dataMsg([
            'toolbar' => ECO('editor.tinyMCE.toolbar'),
            'options' => $options,
            'extraOptions' => $extraOptions,
            //'fieldOptions' => $fieldOptions,
            'fieldExtraOptions' => $fieldExtraOptions,
        ]);

    }

    public function saveToolbarButtons()
    {
        $id = request('id');
        $extra = request('extra');
        $field = request('field');
        $items = request('items');

        $content = getSiteJsonConfig();

        if ($id && $field && $extra) {
            foreach ($items as $key => $row) {
                data_set($content, "$id.$field.$key", $row);
            }
        } elseif ($id && $field) {
            data_set($content, "$id.$field", []);
            $content[$id][$field][] = '|';
            foreach ($items as $row) {
                $content[$id][$field][] = $row;
            }
        }
        putSiteJsonConfig($content);
    }

    public function encodeFileId()
    {
        $params = [
            'f' => request('fileId'),
            't' => request('transform')
        ];
        $this->api->dataMsg(ExoFile::encodeFileId($params));
    }
}
