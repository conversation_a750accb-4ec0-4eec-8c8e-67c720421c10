<?php

namespace Exolog\Core\API\Controllers;

use Exolog\Module\API\ApiController;
use Exolog\Module\Container\BaseContainer;
use Exolog\Module\Container\Container;
use Exolog\Module\Container\ContainerFactory;
use Exolog\Module\Container\FormContainerDataProvider;


class ContainerController extends ApiController
{
    public function getUselessItems()
    {
        $this->checkAuth(['level' => 'admin']);

        $containerObj = $this->containerFromRequest();
        $params = request()->all();

        $items = $containerObj->getUselessItemsPaginate($params);

        $this->api->dataMsg($items);
    }

    public function getContainerConfig()
    {
        $this->checkAuth(['level' => 'admin']);
        $containerObj = $this->containerFromRequest();
        $this->api->dataMsg($containerObj->getConfig());
    }

    public function setContainerConfig()
    {
        $this->checkAuth(['level' => 'admin']);
        $config = request('config');
        $containerObj = $this->containerFromRequest();
        $containerObj->setConfig($config)->save();
        $this->api->dataMsg($containerObj->getConfig());
    }

    public function getContainerConfigList()
    {
        $this->checkAuth(['level' => 'admin']);
        $result = [];
        $containers = request('containers');
        foreach ($containers as $containerParams) {
            $container = ContainerFactory::resolve($containerParams['container']);
            $result[] = $container->getConfig();
        }
        $this->api->dataMsg($result);
    }

    public function getItemsTree()
    {
        $this->checkAuth(['level' => 'admin']);
        $containerObj = $this->containerFromRequest();
        $this->api->dataMsg($containerObj->getItemsTree(['_ignore_publish' => true]));
    }

    public function setItems()
    {
        $this->checkAuth(['level' => 'admin']);
        $itemsTree = request('itemsTree');
        $containerObj = $this->containerFromRequest();
        $containerObj->setItems($itemsTree)->save();
        $this->api->dataMsg($containerObj->getItemsTree(['_ignore_publish' => true]));
    }

    public function addItems()
    {
        $this->checkAuth(['level' => 'admin']);
        $containerObj = $this->containerFromRequest();
        $containerObj->addItems(request('items'))->save();
        $this->api->dataMsg($containerObj->getItemsTree(['_ignore_publish' => true]));
    }

    public function copyItem()
    {
        $this->checkAuth(['level' => 'admin']);
        $path = request('path');
        $containerObj = $this->containerFromRequest();
        $containerObj->copyItem($path)->save();
        $this->api->dataMsg($containerObj->getItemsTree(['_ignore_publish' => true]));
    }

    public function removeItem()
    {
        $this->checkAuth(['level' => 'admin']);
        $path = request('path');
        $containerObj = $this->containerFromRequest();
        $containerObj->removeItem($path)->save();
        $this->api->dataMsg($containerObj->getItemsTree(['_ignore_publish' => true]));
    }

    function getRelatedReactRules()
    {
        $this->checkAuth(['level' => 'admin']);
        $params = request()->all();
        $containerObj = $this->containerFromRequest();

        $this->api->dataMsg($containerObj->responseRules()->getRelatedReactRules(
            $params['react_id'],
            $params['rules_name']
        ));
    }

    function saveRelatedReactRules()
    {
        $this->checkAuth(['level' => 'admin']);
        $params = request()->all();
        $containerObj = $this->containerFromRequest();

        $containerObj->responseRules()->saveRelatedReactRules(
            $params['react_id'],
            $params['rules_name'],
            $params['rules']
        );

        $this->api->dataMsg(true);
    }

    public function getContainerCommonConfig()
    {
        $this->checkAuth(['level' => 'admin']);
        $params = request('params');
        $config = FormContainerDataProvider::getCommonConfig($params);
        $this->api->dataMsg($config);
    }

    public function setContainerCommonConfig()
    {
        $this->checkAuth(['level' => 'admin']);
        $params = request('params');
        $config = request('config');
        FormContainerDataProvider::saveCommonConfig($config, $params);
        $this->api->dataMsg($config);
    }

    public function getContainerConfigByScope()
    {
        $this->checkAuth(['level' => 'admin']);
        $params = request()->all();
        $containerObj = $this->containerFromRequest();
        if ($params['useOwnConfig']) {
            $config = $containerObj->getDataProvider()->getContainerOwnConfig();
        } else {
            $config = $containerObj->getDataProvider()->getContainerCommonConfig();
        }
        $this->api->dataMsg($config);
    }

    /**
     * Function return form id for container placed inside react or form var
     */
    public function getHolderForm()
    {
        $this->checkAuth(['level' => 'admin']);
        $containerObj = $this->containerFromRequest();
        $form_id = null;
        if ($containerObj->getHolderType() === BaseContainer::HOLDER_TYPE_VAR) {
            $form_id = $containerObj->getHolder()->getParent();
        }
        if ($containerObj->getHolderType() === BaseContainer::HOLDER_TYPE_REACT) {
            $form_id = $containerObj->getHolder()->getReactFormId();
        }
        $this->api->dataMsg($form_id);
    }

    private function containerFromRequest(): Container
    {
        return ContainerFactory::resolve(request('container'));
    }
}
