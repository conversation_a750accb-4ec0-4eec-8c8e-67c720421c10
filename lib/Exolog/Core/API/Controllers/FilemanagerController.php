<?php

namespace Exolog\Core\API\Controllers;

use Exception;
use Exolog\Module\AdminFileManager\FileManager;
use Exolog\Module\AdminFileManager\FileManagerService;
use Exolog\Module\API\ApiController;
use Exolog\Module\Http\Request;
use Illuminate\Support\Facades\Storage;

class FilemanagerController extends ApiController
{
    private FileManagerService $fmService;

    public function __construct()
    {
        parent::__construct();
        $this->fmService = app(FileManagerService::class);
    }

    protected function getFileManager(): FileManager
    {
        $disk = request('disk');
        return $this->fmService->getFileManager($disk);
    }

    private function getParamPath()
    {
        return request('path');
    }

    public function initialize(Request $request): void
    {
        $this->checkAuth(['level' => 'admin']);

        $params = $request->only([
            'startSelection',
            'startDisk',
            'startFolder'
        ]);

        if ($params['startFolder'] !== null && !Storage::disk($params['startDisk'])->exists($params['startFolder'])) {
            $params['startFolder'] = null;
        }

        $this->api->dataMsg($this->fmService->getConfig($params));
    }

    /**
     * Get directories for the tree (upper level)
     * now we return full tree.isBatch is not working with vuex or I did not find way
     */
    public function tree()
    {
        $this->checkAuth(['level' => 'admin']);
        $this->api->dataMsg($this->getFileManager()->tree($this->getParamPath()));
    }

    /**
     * Get directories/files for the tree (recursively)
     */
    public function treeContent()
    {
        $this->checkAuth(['level' => 'admin']);
        $this->api->dataMsg($this->getFileManager()->treeContent($this->getParamPath()));
    }


    /**
     * Select disk
     */
    public function selectDisk()
    {
        $this->checkAuth(['level' => 'admin']);
        $this->api->dataMsg(['message' => 'diskSelected']);
    }

    /**
     * Get content (files and folders)
     */
    public function content()
    {
        $this->checkAuth(['level' => 'admin']);
        $this->api->dataMsg($this->getFileManager()->content($this->getParamPath()));
    }

    /**
     * Get propertires for a path (file or folder)
     */
    public function props()
    {
        $this->checkAuth(['level' => 'admin']);
        $this->api->dataMsg($this->getFileManager()->props($this->getParamPath()));
    }

    /**
     * URL
     */
    public function url()
    {
        $this->checkAuth(['level' => 'admin']);
        $this->api->dataMsg($this->getFileManager()->url($this->getParamPath()));
    }

    /**
     * Get file to editing or showing
     */
    public function getFile()
    {
        $this->checkAuth(['level' => 'admin']);
        $response = $this->getFileManager()->getFile($this->getParamPath());
        if (is_string($response)) {
            exolog()->runRedir($response);
        } else {
            return $response;
        }
    }

    /**
     * Image thumbnails
     */
    public function thumbnails()
    {
        $this->checkAuth(['level' => 'admin']);
        $response = $this->getFileManager()->thumbnails($this->getParamPath());
        if (is_string($response)) {
            return redirect($response);
        }

        return $response;
    }

    /**
     * Image preview
     */
    public function preview()
    {
        $this->checkAuth(['level' => 'admin']);
        $response = $this->getFileManager()->preview($this->getParamPath());
        if (is_string($response)) {
            return redirect($response);
        }
        return $response;
    }

    /**
     * Download file
     */
    public function download()
    {
        $this->checkAuth(['level' => 'admin']);
        $response = $this->getFileManager()->download($this->getParamPath());
        if (is_string($response)) {
            return redirect($response);
        }
        return $response;
    }

    /**
     * Create new file
     */
    public function createFile(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);
        $params = $request->all();
        $this->api->dataMsg($this->getFileManager()->createFile($params['name'], $params['path']));
    }

    /**
     * Update file
     */
    public function updateFile(Request $request)
    {

        $this->checkAuth(['level' => 'admin']);
        try {
            $this->api->dataMsg($this->getFileManager()->updateFile(
                $this->getParamPath(),
                $request->file('file')
            ));
        } catch (Exception $exception) {
            return $this->api->errorMsg($exception->getMessage());
        }
    }

    /**
     * Create new directory
     */
    public function createDirectory(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);
        $this->api->dataMsg(
            $this->getFileManager()->createDirectory($request['name'], $request['path'])
        );
    }

    /**
     * Upload file
     */
    public function upload(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);
        $this->getFileManager()->upload($request['path'],
            $request->file('files'),
            $request['fileURL'],
            $request['overwrite']);
    }

    /**
     * Delete selected items
     */
    public function delete(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);
        $this->api->dataMsg(
            $this->getFileManager()->delete($request['items'])
        );
    }

    /**
     * Rename file or folder
     */
    public function rename(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);
        $this->getFileManager()->rename($request['newName'], $request['oldName'], $request['type']);
        $this->api->dataMsg(true);
    }

    /**
     * Copy / Cut files and folders
     */
    public function paste(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);
        $this->getFileManager()->paste($request['path'], $request['clipboard']);
    }

    /**
     * Zip
     */
    public function zip(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);
        $file_id = $this->getFileManager()->zip($request['path'], $request['name'], $request['elements']);
        if ($file_id) {
            $this->api->infoMsg('Zip "' . $file_id . '" file has been created. ');
            $this->api->dataMsg($file_id);
        } else {
            return $this->api->errorMsg('Errors occurred while archiving');
        }
    }

    /**
     * Unzip
     */
    public function unzip(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);
        return $this->getFileManager()->unzip($request['path']);
    }
}
