<?php

namespace Exolog\Core\API\Controllers;


use Exolog\Core\Forms\React;
use Exolog\Module\API\ApiController;
use Exolog\Module\Cyrus\CyrusService;
use Exolog\Module\Domains\Model\Domain;
use Exolog\Module\Mail\PosteMailboxService;
use Exolog\Module\Http\Request;
use Exolog\Module\Support\Facades\Site;
use Exolog\Module\Support\Facades\Vars;
use Exolog\Module\Users\Export\ExportVCFService;
use Exolog\Module\Users\Model\User;
use Exolog\Module\Users\Model\Usergroup;
use Exolog\Module\Users\UserMailboxService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Unique;

class UsersController extends ApiController
{

    public function getUsergroups(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);
        $usergroups = Usergroup::query()->vueTablePaginate();

        return $this->api->dataMsg($usergroups);
    }

    public function getUsergroup(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);

        $params = $request->validateWithAttributes([
            'ug_id' => 'required|numeric',
        ]);

        $usergroup = Usergroup::find($params['ug_id']);
        $usergroup['vars'] = Vars::getAll(['var_parent' => $usergroup->ug_id]);
        return $this->api->dataMsg($usergroup);
    }

    public function saveUsergroup(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);

        $usergroupValue = $request->validateWithAttributes(
            [
                'ug_id' => 'nullable|numeric',
                'ug_name' => [
                    tap(Rule::unique(Usergroup::class), static function (Unique $rule) use ($request) {
                        if (is_numeric($request['ug_id'])) {
                            $rule->ignore($request['ug_id'], 'ug_id');
                        }
                        $rule->where('ug_site', Site::id());
                    }),
                ],
                'ug_parent' => 'nullable|numeric',
                'ug_alias' => 'nullable|string',
                'ug_info' => 'nullable|string',
                'vars' => 'nullable|array',

            ]);

        $usergroup = Usergroup::updateOrCreate([
            'ug_id' => $usergroupValue['ug_id']
        ], $usergroupValue);

        if ($usergroupValue['vars']) {
            Vars::massUpdate($usergroupValue['vars'], 'user', $usergroup->ug_id);
        }

        return $this->api->dataMsg($usergroup);
    }

    public function deleteUsergroup(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);
        $params = $request->validateWithAttributes([
            'ug_id' => 'required|numeric',
        ]);
        Usergroup::destroy($params['ug_id']);
        return $this->api->dataMsg(true);
    }

    public function sendPassChangeLink(Request $request)
    {
        $params = $request->validateWithAttributes([
            'email' => 'required|email',
        ]);

        $user = User::findByEmailOrFail($params['email']);
        $user->sendPasswordChangeLink(['path' => '/exo_admin/p/change-password']);
        return $this->api->dataMsg(true);
    }

    public function changePassword(Request $request)
    {
        $params = $request->validateWithAttributes([
            'hash' => 'required',
            'password' => 'required|min:8'
        ]);

        $pass = $params['password'];
        $hash = $params['hash'];
        $hash = decrypt($hash);

        if (!empty($hash)) {
            $now = time();
            $time = (int)$hash['time'];
            $ttl = (int)$hash['ttl'];
            $ttl *= 3600;
            $diff = $now - $time;
            if ($diff > $ttl) {
                return $this->api->errorMsg('Error! Password link is old.');
            }
        }

        if (!empty($hash) && (!empty($hash['u_id'])) && is_numeric($hash['u_id'])) {
            $user = User::findOrFail($hash['u_id']);
            $user->u_password = $pass;
            $user->save();
        }

        return $this->api->dataMsg(true);
    }

    public function getUsers(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);

        $query = User::query()
            ->select('*')
            ->when($request['react_count'], function (Builder $query) {
                $query->selectSub('SELECT count(*) from react where react_user = u_id and react_isdeleted = 0',
                    'react_count');

            })
            ->when($request['ug_id'], function (Builder $query) use ($request) {
                $query->whereHas('usergroups', function (Builder $q) use ($request) {
                    $q->where('ug_id', $request['ug_id']);
                });
            })
            ->when($request['trashed'], function (Builder $query) {
                $query->onlyTrashed();
            });

        $users = $query->vueTablePaginate();
        return $this->api->dataMsg($users);
    }

    public function getUser(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);

        $params = $request->validateWithAttributes([
            'u_id' => 'required|numeric',
        ]);

        $user = User::findOrFail($params['u_id']);
        $user->load([
            'usergroups',
        ]);

        $user['vars'] = Vars::getAll(['var_parent' => $user->u_id]);
        return $this->api->dataMsg($user);
    }

    public function saveUser(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);

        $user = User::saveFromArray($request->all());

        if ($request->has('vars')) {
            Vars::massUpdate($request['vars'], 'user', $user->u_id);
        }

        return $this->api->dataMsg($user);
    }

    public function deleteUser(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);

        $params = $request->validateWithAttributes([
            'cascade' => 'nullable|bool',
            'transferToUserId' => 'nullable|numeric',
            'u_id' => 'required',
        ]);

        $ids = Arr::wrap($params['u_id']);
        $cascade = $request->get('cascade', false);
        $transferToUserId = $request->get('transferToUserId');

        foreach ($ids as $id) {
            $user = User::findOrFail($id);

            if ($cascade) {
                // Delete reacts created by this user
                react()->where('react_user', $id)
                    ->get()
                    ->each(function (React $react) {
                        $react->delete();
                    });
            } elseif ($transferToUserId) {
                // Transfer reacts to another user
                $targetUser = User::findOrFail($transferToUserId);

                react()->where('react_user', $id)
                    ->update(['react_user' => $transferToUserId]);
            }
            // If neither cascade nor transfer, reacts will become orphaned (existing behavior)

            // Perform hard delete since we're handling reacts properly
            $user->forceDelete();
        }

        return $this->api->dataMsg(true);
    }

    public function addUsersToUsergroup(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);


        $params = $request->validateWithAttributes([
            'ug_id' => 'required|numeric',
            'u_id' => 'required'
        ]);

        $usergroup = Usergroup::findOrFail($params['ug_id']);
        foreach (Arr::wrap($params['u_id']) as $item) {
            $usergroup->addUser($item);
        }
        return $this->api->dataMsg(true);
    }

    public function removeUsersFromUsergroup(Request $request)
    {
        $this->checkAuth(['level' => 'admin']);


        $params = $request->validateWithAttributes([
            'ug_id' => 'required|numeric',
            'u_id' => 'required'
        ]);

        $usergroup = Usergroup::findOrFail($params['ug_id']);
        foreach (Arr::wrap($params['u_id']) as $item) {
            $usergroup->removeUser($item);
        }
        return $this->api->dataMsg(true);
    }

    public function getUserMailbox()
    {
        $this->checkAuth(['level' => 'admin']);

        $u_id = request('u_id');
        $user = User::findOrFail($u_id);
        $email = $user->u_email;

        \Log::info('🔄 getUserMailbox called', [
            'u_id' => $u_id,
            'user_email' => $email
        ]);

        [$userName, $mailDomain] = explode("@", $email);

        $data = [];

        // Get mailbox data from database (u_maildest column)
        \Log::info('📮 Getting mailbox data from database', ['user_id' => $u_id, 'email' => $email]);

        $data['is_popbox'] = (bool) $user->u_ismailbox;

        // Parse u_maildest field to get forward destinations
        \Log::info('📧 Processing u_maildest field', [
            'user_email' => $email,
            'u_maildest_raw' => $user->u_maildest,
            'u_maildest_empty' => empty($user->u_maildest),
            'u_maildest_length' => strlen($user->u_maildest ?? ''),
            'u_maildest_type' => gettype($user->u_maildest)
        ]);

        $mailboxdest = [];
        if (!empty($user->u_maildest)) {
            // u_maildest format can be: |<EMAIL>||<EMAIL>| OR |<EMAIL> | <EMAIL>|
            $trimmed = trim($user->u_maildest, '|');

            // Try double pipe separator first (new format)
            $destinations = explode('||', $trimmed);

            // If only one result, try single pipe separator (legacy format)
            if (count($destinations) === 1 && strpos($trimmed, '|') !== false) {
                $destinations = explode('|', $trimmed);
            }

            \Log::info('📧 Parsing u_maildest destinations', [
                'user_email' => $email,
                'raw_value' => $user->u_maildest,
                'trimmed_value' => $trimmed,
                'exploded_destinations' => $destinations,
                'destinations_count' => count($destinations),
                'separator_used' => strpos($trimmed, '||') !== false ? 'double_pipe' : 'single_pipe'
            ]);

            foreach ($destinations as $dest) {
                $cleanDest = trim($dest);
                if (!empty($cleanDest)) {
                    $mailboxdest[] = ['name' => $cleanDest];
                    \Log::info('📧 Added destination', ['user_email' => $email, 'destination' => $cleanDest]);
                }
            }
        } else {
            \Log::info('📧 u_maildest field is empty', ['user_email' => $email]);
        }
        $data['mailboxdest'] = $mailboxdest;

        // For maillisting, still check with appropriate service for compatibility
        if ($this->isPosteDomain($mailDomain)) {
            $posteService = new PosteMailboxService();
            $data['is_maillisting_dest'] = $posteService->isMaillistingDest($email);
            // Note: Aliases are not needed for mailbox data - we get forward destinations from database
            $aliases = [];
        } else {
            $cyrusService = new CyrusService();
            $data['is_maillisting_dest'] = @$cyrusService->is_maillisting_dest($email);
            $aliases = $cyrusService->get_all_aliases_on_site($email);
        }

        \Log::info('📥 Database data retrieved', [
            'email' => $email,
            'u_ismailbox' => $user->u_ismailbox,
            'u_maildest' => $user->u_maildest,
            'is_popbox' => $data['is_popbox'],
            'mailboxdest_count' => count($data['mailboxdest']),
            'mailboxdest' => $data['mailboxdest'],
            'is_maillisting_dest' => $data['is_maillisting_dest']
        ]);

        $data['maillisting_usergroup'] = Vars::user('maillisting_usergroup', $u_id)->getValue();
        $data['maillisting_moderatorgroup'] = Vars::user('maillisting_moderatorgroup', $u_id)->getValue();

        if (empty($data['maillisting_usergroup'])) {
            $data['maillisting_usergroup'] = null;
        }
        if (empty($data['maillisting_moderatorgroup'])) {
            $data['maillisting_moderatorgroup'] = null;
        }

        $response = [
            'mailbox' => $data,
            'aliases' => $aliases
        ];

        \Log::info('✅ getUserMailbox response prepared', [
            'email' => $email,
            'response' => $response
        ]);

        return $this->api->dataMsg($response);
    }

    public function saveUserMailbox()
    {
        $this->checkAuth(['level' => 'admin']);
        $exolog = exolog();

        $u_id = request('u_id');
        $mailbox = request('mailbox');

        $user = User::findOrFail($u_id);

        [$userName, $mailDomain] = explode("@", $user['u_email']);

        if (!in_array($mailDomain, Domain::mailhosts()->pluck('domain_name')->toArray(), true)) {
            return $this->api->errorMsg("You try save email setting for user with different domain. ($mailDomain)");
        }

        // Check if domain uses Poste or Cyrus
        if ($this->isPosteDomain($mailDomain)) {
            return $this->saveUserMailboxPoste($user, $mailbox);
        } else {
            return $this->saveUserMailboxCyrus($user, $mailbox);
        }
    }

    /**
     * Check if domain uses Poste API
     */
    private function isPosteDomain(string $domain): bool
    {
        // For now, assume all mail domains use Poste
        // You can add logic here to determine which domains use Poste vs Cyrus
        return true;
    }

    /**
     * Save forward destinations to database (u_maildest column)
     */
    private function saveForwardDestinationsToDatabase(User $user, array $destinations, bool $isMailbox)
    {
        \Log::info('💾 saveForwardDestinationsToDatabase called', [
            'user_email' => $user->u_email,
            'user_id' => $user->u_id,
            'input_destinations' => $destinations,
            'isMailbox' => $isMailbox,
            'current_u_maildest' => $user->u_maildest,
            'current_u_ismailbox' => $user->u_ismailbox
        ]);

        // Filter out maillisting and user's own email
        $filteredDestinations = [];
        foreach ($destinations as $dest) {
            if ($dest !== 'maillisting' && strtolower($dest) !== strtolower($user->u_email)) {
                $filteredDestinations[] = $dest;
            }
        }

        // Format for database: |<EMAIL>||<EMAIL>|
        $u_maildest = '';
        if (!empty($filteredDestinations)) {
            $u_maildest = '|' . implode('||', $filteredDestinations) . '|';
        }

        \Log::info('💾 Database update data prepared', [
            'user_email' => $user->u_email,
            'filtered_destinations' => $filteredDestinations,
            'u_maildest_to_save' => $u_maildest,
            'u_ismailbox_to_save' => $isMailbox ? 1 : 0
        ]);

        // Update user record
        try {
            $updateResult = $user->update([
                'u_ismailbox' => $isMailbox ? 1 : 0,
                'u_maildest' => $u_maildest
            ]);

            // Refresh user model to get updated values
            $user->refresh();

        } catch (\Exception $dbException) {
            \Log::error('💥 Database update failed', [
                'user_email' => $user->u_email,
                'error' => $dbException->getMessage(),
                'trace' => $dbException->getTraceAsString()
            ]);
            throw $dbException;
        }

        \Log::info('💾 Database update completed', [
            'user_email' => $user->u_email,
            'update_result' => $updateResult,
            'new_u_maildest' => $user->u_maildest,
            'new_u_ismailbox' => $user->u_ismailbox
        ]);
    }

    /**
     * Save user mailbox using Poste API
     */
    private function saveUserMailboxPoste(User $user, array $mailbox)
    {
        try {
            \Log::info('💾 saveUserMailboxPoste called', [
                'user_email' => $user->u_email,
                'mailbox_data' => $mailbox
            ]);

            $posteService = new PosteMailboxService();

            $mail_decs = empty($mailbox['mailboxdest']) ? [] : array_column($mailbox['mailboxdest'], 'name');

            \Log::info('📧 Forward destinations extracted', [
                'user_email' => $user->u_email,
                'original_mailboxdest' => $mailbox['mailboxdest'] ?? null,
                'extracted_mail_decs' => $mail_decs
            ]);

            // Validate that forward destinations don't include the user's own email
            if (!empty($mail_decs)) {
                foreach ($mail_decs as $destination) {
                    if (strtolower($destination) === strtolower($user->u_email)) {
                        \Log::error('❌ Validation failed: Cannot forward to same address', [
                            'user_email' => $user->u_email,
                            'destination' => $destination
                        ]);
                        return $this->api->errorMsg("Cannot forward to the same address: {$destination}");
                    }
                }
                \Log::info('✅ Forward destinations validation passed', [
                    'user_email' => $user->u_email,
                    'destinations' => $mail_decs
                ]);
            }

            if ($mailbox['is_maillisting_dest'] && !empty($mailbox['maillisting_usergroup'])) {
                $mail_decs[] = 'maillisting';
                Vars::user('maillisting_usergroup', $user->u_id)->setValue($mailbox['maillisting_usergroup'])->save();
                Vars::user('maillisting_moderatorgroup', $user->u_id)->setValue($mailbox['maillisting_moderatorgroup'])->save();
            } else {
                Vars::user('maillisting_usergroup', $user->u_id)->delete();
                Vars::user('maillisting_moderatorgroup', $user->u_id)->delete();
            }

            if ($mailbox['is_popbox']) {
                \Log::info('📮 Mailbox is enabled, processing...', ['user_email' => $user['u_email']]);

                // Create new mailbox
                if (!$posteService->mailboxExists($user['u_email'])) {
                    \Log::info('📮 Creating new mailbox with forward destinations', [
                        'user_email' => $user['u_email'],
                        'destinations' => $mail_decs,
                        'destinations_count' => count($mail_decs)
                    ]);
                    if (empty($mailbox['password'])) {
                        return $this->api->errorMsg('To create a new mailbox, please set password!');
                    }

                    // Create mailbox with forward destinations included in the initial API request
                    $posteService->createMailbox(
                        $user['u_email'],
                        $user['u_name'] ?: $user['u_email'],
                        $mailbox['password'],
                        $mail_decs  // Pass forward destinations to be included in redirectTo field
                    );
                } else {
                    \Log::info('📮 Updating existing mailbox', ['user_email' => $user['u_email']]);
                    // Update existing mailbox
                    if (!empty($mailbox['password'])) {
                        \Log::info('🔑 Updating mailbox password', ['user_email' => $user['u_email']]);
                        $posteService->updateMailboxPassword($user['u_email'], $mailbox['password']);
                    }

                    // Update destinations/forwarding (don't include user's own email)
                    \Log::info('📧 Setting forward destinations for existing mailbox', [
                        'user_email' => $user['u_email'],
                        'destinations' => $mail_decs
                    ]);
                    $posteService->setDestinations($user['u_email'], $mail_decs);
                }
            } else {
                // Delete mailbox if it exists
                if ($posteService->mailboxExists($user['u_email'])) {
                    $posteService->deleteMailbox($user['u_email']);
                }

                // Handle forwarding/aliases
                \Log::info('📧 Handling alias/forwarding for non-mailbox', [
                    'user_email' => $user['u_email'],
                    'destinations' => $mail_decs,
                    'destinations_count' => count($mail_decs)
                ]);

                if (isset($mail_decs) && is_array($mail_decs) && count($mail_decs)) {
                    if ($posteService->aliasExists($user['u_email'])) {
                        \Log::info('📧 Updating existing alias', ['user_email' => $user['u_email']]);
                        $posteService->setDestinations($user['u_email'], $mail_decs);
                    } else {
                        \Log::info('📧 Creating new alias', ['user_email' => $user['u_email']]);
                        $posteService->createAlias($user['u_email'], $mail_decs);
                    }
                } else {
                    // Delete alias when destination is empty
                    if ($posteService->aliasExists($user['u_email'])) {
                        \Log::info('🗑️ Deleting alias (no destinations)', ['user_email' => $user['u_email']]);
                        $posteService->deleteAlias($user['u_email']);
                    } else {
                        \Log::info('ℹ️ No alias to delete', ['user_email' => $user['u_email']]);
                    }
                }
            }

            // Save forward destinations to database (u_maildest column)
            \Log::info('💾 About to save to database', [
                'user_email' => $user->u_email,
                'mail_decs_before_db_save' => $mail_decs,
                'is_popbox' => $mailbox['is_popbox']
            ]);
            $this->saveForwardDestinationsToDatabase($user, $mail_decs, $mailbox['is_popbox']);

            UserMailboxService::make()->updateUserMailboxInfo($user);
            return $this->api->dataMsg(true);

        } catch (\Exception $e) {
            \Log::error('Failed to save user mailbox using Poste', [
                'user_email' => $user['u_email'],
                'error' => $e->getMessage()
            ]);
            return $this->api->errorMsg('Failed to save mailbox: ' . $e->getMessage());
        }
    }

    /**
     * Save user mailbox using Cyrus (legacy)
     */
    private function saveUserMailboxCyrus(User $user, array $mailbox)
    {
        $cyrusService = new CyrusService();

        $mail_decs = empty($mailbox['mailboxdest']) ? [] : array_column($mailbox['mailboxdest'], 'name');

        // Validate that forward destinations don't include the user's own email
        if (!empty($mail_decs)) {
            foreach ($mail_decs as $destination) {
                if (strtolower($destination) === strtolower($user['u_email'])) {
                    return $this->api->errorMsg("Cannot forward to the same address: {$destination}");
                }
            }
        }

        if ($mailbox['is_maillisting_dest'] && !empty($mailbox['maillisting_usergroup'])) {
            $mail_decs[] = 'maillisting';
            Vars::user('maillisting_usergroup', $user->u_id)->setValue($mailbox['maillisting_usergroup'])->save();
            Vars::user('maillisting_moderatorgroup', $user->u_id)->setValue($mailbox['maillisting_moderatorgroup'])->save();
        } else {
            Vars::user('maillisting_usergroup', $user->u_id)->delete();
            Vars::user('maillisting_moderatorgroup', $user->u_id)->delete();
        }

        if ($mailbox['is_popbox']) {

            $mail_decs[] = $user['u_email'];

            //create new popbox
            if (!$cyrusService->is_exist_mbox($user['u_email'])) {
                if (empty($mailbox['password'])) {
                    return $this->api->errorMsg('To create a new mailbox, please set password!');
                }
                $cyrusService->create_mailbox($user['u_email'], $user['u_email'], $mailbox['password'], $mail_decs);
            } //update popbox
            else {
                //update dest
                //Change mailbox password
                if (!empty($mailbox['password'])) {
                    $cyrusService->set_pass($user['u_email'], $mailbox['password']);
                }

                $cyrusService->set_dest($user['u_email'], $mail_decs);
            }
        } else {
            if ($cyrusService->is_exist_mbox($user['u_email'])) {
                $cyrusService->delete_mailbox($user['u_email']);
            }

            //Forward mail
            if (isset($mail_decs) && is_array($mail_decs) && count($mail_decs)) {
                if ($cyrusService->is_exist_alias($user['u_email'])) {
                    $cyrusService->set_dest($user['u_email'], $mail_decs);
                } else {
                    //create new alias
                    $cyrusService->create_alias($user['u_email'], $mail_decs);
                }
            } else {
                //delete alias when destination is empty
                if ($cyrusService->is_exist_alias($user['u_email'])) {
                    $cyrusService->delete_alias($user['u_email']);
                }
            }
        }

        // Save forward destinations to database (u_maildest column)
        $this->saveForwardDestinationsToDatabase($user, $mail_decs, $mailbox['is_popbox']);

        UserMailboxService::make()->updateUserMailboxInfo($user);
        return $this->api->dataMsg(true);
    }

    public function fixUserMailbox()
    {

        $this->checkAuth(['level' => 'admin']);

        $u_id = request('u_id');
        $user = User::findOrFail($u_id);

        $cyrusService = new CyrusService();

        if ($cyrusService->is_exist_mbox($user['u_email'])) {
            $cyrusService->create_standart_subfolders($user['u_email']);
            return $this->api->dataMsg(true);
        }
        return $this->api->dataMsg(false);
    }

    public function downloadUserVCF(): void
    {
        $this->checkAuth(['level' => 'admin']);
        $u_id = request('u_id');
        ExportVCFService::downloadUserCard($u_id);
        exit;
    }
}
