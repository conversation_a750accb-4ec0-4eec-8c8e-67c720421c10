<?php

namespace Exolog\Console\Commands;

use Exolog\Module\Mail\PosteMailboxService;
use Illuminate\Console\Command;
use Exception;

class TestGetDestinations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:get-destinations {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test getDestinations method to verify forward destinations are loaded correctly';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $email = $this->argument('email');

        $this->info("Testing getDestinations for: $email");

        try {
            $posteService = new PosteMailboxService();

            // Test with includeMailbox = true (default)
            $this->info("\n🧪 Test 1: getDestinations with includeMailbox = true");
            $destinations1 = $posteService->getDestinations($email, true);
            $this->line("Destinations: " . json_encode($destinations1, JSON_PRETTY_PRINT));

            // Test with includeMailbox = false
            $this->info("\n🧪 Test 2: getDestinations with includeMailbox = false");
            $destinations2 = $posteService->getDestinations($email, false);
            $this->line("Destinations: " . json_encode($destinations2, JSON_PRETTY_PRINT));

            // Check if user's own email is included
            $this->info("\n🔍 Analysis:");
            $hasOwnEmail1 = $this->checkForOwnEmail($destinations1, $email);
            $hasOwnEmail2 = $this->checkForOwnEmail($destinations2, $email);

            if ($hasOwnEmail1) {
                $this->error("❌ User's own email found in destinations (includeMailbox=true)");
            } else {
                $this->info("✅ User's own email NOT found in destinations (includeMailbox=true)");
            }

            if ($hasOwnEmail2) {
                $this->error("❌ User's own email found in destinations (includeMailbox=false)");
            } else {
                $this->info("✅ User's own email NOT found in destinations (includeMailbox=false)");
            }

            // Check mailbox existence
            $this->info("\n📧 Mailbox Status:");
            $mailboxExists = $posteService->mailboxExists($email);
            $this->line("Mailbox exists: " . ($mailboxExists ? 'YES' : 'NO'));

            $aliasExists = $posteService->aliasExists($email);
            $this->line("Alias exists: " . ($aliasExists ? 'YES' : 'NO'));

            $this->info("\n📊 Summary:");
            $this->line("- Total destinations (includeMailbox=true): " . count($destinations1));
            $this->line("- Total destinations (includeMailbox=false): " . count($destinations2));
            $this->line("- Contains user's own email: " . ($hasOwnEmail1 || $hasOwnEmail2 ? 'YES (❌ PROBLEM)' : 'NO (✅ GOOD)'));

            return 0;

        } catch (Exception $e) {
            $this->error("Error: " . $e->getMessage());
            $this->line("Stack trace: " . $e->getTraceAsString());
            return 1;
        }
    }

    /**
     * Check if destinations contain the user's own email
     */
    private function checkForOwnEmail(array $destinations, string $userEmail): bool
    {
        foreach ($destinations as $destination) {
            if (isset($destination['name']) && strtolower($destination['name']) === strtolower($userEmail)) {
                return true;
            }
        }
        return false;
    }
}
