<?php

namespace Exolog\Console\Commands;

use Exolog\Module\Mail\PosteMailboxService;
use Exolog\Module\Mail\PosteApiService;
use Illuminate\Console\Command;

class TestMailboxCreationWithForward extends Command
{
    protected $signature = 'test:mailbox-creation-forward {email} {password} {--destinations=*}';
    protected $description = 'Test creating mailbox with forward destinations included in initial API request';

    public function handle()
    {
        $email = $this->argument('email');
        $password = $this->argument('password');
        $destinations = $this->option('destinations');
        
        $this->info("🧪 Testing mailbox creation with forward destinations");
        $this->line("Email: $email");
        $this->line("Password: $password");
        $this->line("Forward destinations: " . (empty($destinations) ? '(none)' : implode(', ', $destinations)));
        
        try {
            $posteService = new PosteMailboxService();
            $posteApiService = new PosteApiService();
            
            // Check if mailbox already exists
            if ($posteApiService->mailboxExists($email)) {
                $this->warn("⚠️ Mailbox already exists. Deleting first...");
                $posteApiService->removeMailbox($email);
                $this->line("✅ Existing mailbox deleted");
            }
            
            $this->info("\n📮 Creating mailbox with forward destinations...");
            
            // Create mailbox with forward destinations
            $result = $posteService->createMailbox(
                $email,
                'Test User',
                $password,
                $destinations  // This should be included in redirectTo field
            );
            
            if ($result) {
                $this->info("✅ Mailbox created successfully!");
                
                // Verify the mailbox was created with correct redirect settings
                $this->info("\n🔍 Verifying mailbox creation...");
                
                $mailboxData = $posteApiService->getMailbox($email);
                
                $this->line("Mailbox exists: YES");
                $this->line("Email: " . ($mailboxData['email'] ?? 'N/A'));
                $this->line("Name: " . ($mailboxData['name'] ?? 'N/A'));
                
                if (isset($mailboxData['redirectTo'])) {
                    $this->line("RedirectTo field: " . json_encode($mailboxData['redirectTo']));
                    
                    if (is_array($mailboxData['redirectTo']) && !empty($mailboxData['redirectTo'])) {
                        $this->info("✅ Forward destinations properly set in Poste API:");
                        foreach ($mailboxData['redirectTo'] as $dest) {
                            $this->line("  → $dest");
                        }
                        
                        // Compare with expected destinations
                        $expectedDestinations = array_values($destinations);
                        $actualDestinations = array_values($mailboxData['redirectTo']);
                        
                        sort($expectedDestinations);
                        sort($actualDestinations);
                        
                        if ($expectedDestinations === $actualDestinations) {
                            $this->info("✅ Forward destinations match expected values!");
                        } else {
                            $this->error("❌ Forward destinations don't match:");
                            $this->line("Expected: " . implode(', ', $expectedDestinations));
                            $this->line("Actual: " . implode(', ', $actualDestinations));
                        }
                    } else {
                        if (empty($destinations)) {
                            $this->info("✅ No forward destinations set (as expected)");
                        } else {
                            $this->error("❌ Expected forward destinations but none found in API");
                        }
                    }
                } else {
                    if (empty($destinations)) {
                        $this->info("✅ No redirectTo field (as expected for no destinations)");
                    } else {
                        $this->error("❌ Expected redirectTo field but not found in API response");
                    }
                }
                
                // Test quota endpoint as well
                $this->info("\n📊 Testing quota information...");
                try {
                    $quotaData = $posteApiService->getMailboxQuota($email);
                    $this->line("Storage usage: " . ($quotaData['storage_usage'] ?? 'N/A'));
                    $this->line("Storage limit: " . ($quotaData['storage_limit'] ?? 'N/A'));
                } catch (\Exception $e) {
                    $this->warn("⚠️ Could not get quota info: " . $e->getMessage());
                }
                
            } else {
                $this->error("❌ Failed to create mailbox");
            }
            
        } catch (\Exception $e) {
            $this->error("❌ Error: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
        }
        
        $this->info("\n🏁 Test completed!");
    }
}
