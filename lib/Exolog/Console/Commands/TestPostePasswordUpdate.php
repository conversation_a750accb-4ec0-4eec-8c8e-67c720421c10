<?php

namespace Exolog\Console\Commands;

use Illuminate\Console\Command;
use Exolog\Module\Mail\PosteApiService;
use Exolog\Module\Mail\PosteMailboxService;
use Exception;

class TestPostePasswordUpdate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:poste-password-update {email} {password}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Poste API password update using PATCH request with email in URL';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $email = $this->argument('email');
        $password = $this->argument('password');

        $this->info("Testing Poste API password update for: {$email}");

        try {
            // Test direct API service
            $this->info("Testing PosteApiService directly...");
            $this->info("PATCH request will be sent to: /boxes/$email");
            $this->info("Request body will contain: {\"passwordPlaintext\": \"$password\"}");

            $posteApiService = new PosteApiService();
            $response = $posteApiService->updateMailboxPassword($email, $password);
            $this->info("PosteApiService response: " . json_encode($response));

            // Test mailbox service
            $this->info("Testing PosteMailboxService...");
            $posteMailboxService = new PosteMailboxService();
            $result = $posteMailboxService->updateMailboxPassword($email, $password);
            $this->info("PosteMailboxService result: " . ($result ? 'Success' : 'Failed'));

            $this->info("Password update test completed successfully!");
            return 0;

        } catch (Exception $e) {
            $this->error("Password update test failed: " . $e->getMessage());
            return 1;
        }
    }
}
