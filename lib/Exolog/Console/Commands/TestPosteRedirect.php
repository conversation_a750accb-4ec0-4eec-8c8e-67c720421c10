<?php

namespace Exolog\Console\Commands;

use Exolog\Module\Mail\PosteMailboxService;
use Illuminate\Console\Command;
use Exception;

class TestPosteRedirect extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:poste-redirect {email} {redirectTo?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Poste API mailbox redirect functionality';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $email = $this->argument('email');
        $redirectTo = $this->argument('redirectTo');

        $this->info("Testing Poste mailbox redirect for: $email");

        try {
            $posteService = new PosteMailboxService();

            // Check if mailbox exists
            if (!$posteService->mailboxExists($email)) {
                $this->error("Mailbox does not exist: $email");
                return 1;
            }

            if ($redirectTo) {
                // Set redirect
                $redirectEmails = array_map('trim', explode(',', $redirectTo));
                $this->info("Setting redirect to: " . implode(', ', $redirectEmails));
                
                $result = $posteService->updateMailboxRedirect($email, $redirectEmails);
                
                if ($result) {
                    $this->info("✅ Redirect updated successfully");
                } else {
                    $this->error("❌ Failed to update redirect");
                    return 1;
                }
            } else {
                // Clear redirect
                $this->info("Clearing redirect...");
                
                $result = $posteService->updateMailboxRedirect($email, []);
                
                if ($result) {
                    $this->info("✅ Redirect cleared successfully");
                } else {
                    $this->error("❌ Failed to clear redirect");
                    return 1;
                }
            }

            // Get current mailbox info to verify
            $this->info("\n📋 Current mailbox information:");
            $mailboxInfo = $posteService->getMailbox($email);
            
            if ($mailboxInfo) {
                $this->line("Email: " . ($mailboxInfo['email'] ?? 'N/A'));
                $this->line("Name: " . ($mailboxInfo['name'] ?? 'N/A'));
                $this->line("Redirect To: " . (isset($mailboxInfo['redirectTo']) && is_array($mailboxInfo['redirectTo']) 
                    ? implode(', ', $mailboxInfo['redirectTo']) 
                    : 'None'));
            } else {
                $this->warn("Could not retrieve mailbox information");
            }

            return 0;

        } catch (Exception $e) {
            $this->error("Error: " . $e->getMessage());
            return 1;
        }
    }
}
