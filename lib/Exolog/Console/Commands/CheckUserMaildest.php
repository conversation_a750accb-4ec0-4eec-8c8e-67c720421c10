<?php

namespace Exolog\Console\Commands;

use Exolog\Module\Users\Model\User;
use Illuminate\Console\Command;

class CheckUserMaildest extends Command
{
    protected $signature = 'check:user-maildest {email}';
    protected $description = 'Check what is stored in u_maildest field for a user';

    public function handle()
    {
        $email = $this->argument('email');
        
        $this->info("🔍 Checking u_maildest field for: $email");
        
        try {
            $user = User::where('u_email', $email)->first();
            
            if (!$user) {
                $this->error("❌ User not found: $email");
                return;
            }
            
            $this->line("User ID: {$user->u_id}");
            $this->line("Email: {$user->u_email}");
            $this->line("Is Mailbox: " . ($user->u_ismailbox ? 'YES' : 'NO'));
            
            $this->info("\n📧 u_maildest Field Analysis:");
            $this->line("Raw value: " . ($user->u_maildest ?? 'NULL'));
            $this->line("Is empty: " . (empty($user->u_maildest) ? 'YES' : 'NO'));
            $this->line("Length: " . strlen($user->u_maildest ?? ''));
            $this->line("Type: " . gettype($user->u_maildest));
            
            if (!empty($user->u_maildest)) {
                $this->info("\n🔧 Parsing Process:");
                $trimmed = trim($user->u_maildest, '|');
                $this->line("After trim('|'): '$trimmed'");
                
                $destinations = explode('||', $trimmed);
                $this->line("After explode('||'): " . json_encode($destinations));
                $this->line("Count: " . count($destinations));
                
                $this->info("\n📋 Parsed Destinations:");
                $mailboxdest = [];
                foreach ($destinations as $index => $dest) {
                    $cleanDest = trim($dest);
                    $this->line("[$index] Raw: '$dest' → Clean: '$cleanDest' → " . (empty($cleanDest) ? 'SKIPPED' : 'ADDED'));
                    if (!empty($cleanDest)) {
                        $mailboxdest[] = ['name' => $cleanDest];
                    }
                }
                
                $this->info("\n✅ Final mailboxdest array:");
                $this->line(json_encode($mailboxdest, JSON_PRETTY_PRINT));
            } else {
                $this->warn("⚠️ u_maildest field is empty - no forward destinations");
            }
            
            // Also check what getUserMailbox would return
            $this->info("\n🔄 Testing getUserMailbox logic:");
            $mailboxdest = [];
            if (!empty($user->u_maildest)) {
                // u_maildest format can be: |<EMAIL>||<EMAIL>| OR |<EMAIL> | <EMAIL>|
                $trimmed = trim($user->u_maildest, '|');

                // Try double pipe separator first (new format)
                $destinations = explode('||', $trimmed);

                // If only one result, try single pipe separator (legacy format)
                if (count($destinations) === 1 && strpos($trimmed, '|') !== false) {
                    $destinations = explode('|', $trimmed);
                    $this->line("Using single pipe separator (legacy format)");
                } else {
                    $this->line("Using double pipe separator (new format)");
                }

                $this->line("Final destinations after parsing: " . json_encode($destinations));

                foreach ($destinations as $dest) {
                    if (!empty(trim($dest))) {
                        $mailboxdest[] = ['name' => trim($dest)];
                    }
                }
            }
            
            $this->line("getUserMailbox would return:");
            $this->line("mailboxdest: " . json_encode($mailboxdest));
            
        } catch (\Exception $e) {
            $this->error("❌ Error: " . $e->getMessage());
        }
    }
}
