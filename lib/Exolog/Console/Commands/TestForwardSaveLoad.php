<?php

namespace Exolog\Console\Commands;

use Exolog\Module\Mail\PosteMailboxService;
use Illuminate\Console\Command;
use Exception;

class TestForwardSaveLoad extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:forward-save-load {email} {forward-email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the save/load cycle for forward destinations to verify they persist correctly';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $email = $this->argument('email');
        $forwardEmail = $this->argument('forward-email');

        $this->info("Testing save/load cycle for forward destinations");
        $this->line("User email: $email");
        $this->line("Forward to: $forwardEmail");

        try {
            $posteService = new PosteMailboxService();
            $posteApiService = new \Exolog\Module\Mail\PosteApiService();

            // Step 0: Check raw Poste API response
            $this->info("\n🔍 Step 0: Check raw Poste API response");
            try {
                $rawMailboxData = $posteApiService->getMailbox($email);
                $this->line("Raw mailbox data from Poste API: " . json_encode($rawMailboxData, JSON_PRETTY_PRINT));
            } catch (\Exception $e) {
                $this->error("Failed to get raw mailbox data: " . $e->getMessage());
            }

            // Step 1: Check initial state
            $this->info("\n📋 Step 1: Check initial state");
            $initialDestinations = $posteService->getDestinations($email);
            $this->line("Initial destinations: " . json_encode($initialDestinations, JSON_PRETTY_PRINT));

            // Step 2: Save forward destination
            $this->info("\n💾 Step 2: Save forward destination");
            $destinationsToSave = [
                ['name' => $forwardEmail]
            ];
            $this->line("Saving destinations: " . json_encode($destinationsToSave, JSON_PRETTY_PRINT));
            
            $saveResult = $posteService->setDestinations($email, $destinationsToSave);
            if ($saveResult) {
                $this->info("✅ Save successful");
            } else {
                $this->error("❌ Save failed");
                return 1;
            }

            // Step 3: Check raw API response after save
            $this->info("\n🔍 Step 3: Check raw API response after save");
            try {
                $rawMailboxDataAfter = $posteApiService->getMailbox($email);
                $this->line("Raw mailbox data after save: " . json_encode($rawMailboxDataAfter, JSON_PRETTY_PRINT));
            } catch (\Exception $e) {
                $this->error("Failed to get raw mailbox data after save: " . $e->getMessage());
            }

            // Step 4: Load destinations back
            $this->info("\n📥 Step 4: Load destinations back");
            $loadedDestinations = $posteService->getDestinations($email);
            $this->line("Loaded destinations: " . json_encode($loadedDestinations, JSON_PRETTY_PRINT));

            // Step 5: Verify results
            $this->info("\n🔍 Step 5: Verify results");
            
            // Check if forward email is present
            $forwardFound = false;
            $userEmailFound = false;
            
            foreach ($loadedDestinations as $dest) {
                if (isset($dest['name'])) {
                    if (strtolower($dest['name']) === strtolower($forwardEmail)) {
                        $forwardFound = true;
                    }
                    if (strtolower($dest['name']) === strtolower($email)) {
                        $userEmailFound = true;
                    }
                }
            }

            if ($forwardFound) {
                $this->info("✅ Forward email found in loaded destinations");
            } else {
                $this->error("❌ Forward email NOT found in loaded destinations");
            }

            if ($userEmailFound) {
                $this->error("❌ User's own email found in destinations (should not be there)");
            } else {
                $this->info("✅ User's own email NOT found in destinations (correct)");
            }

            // Step 5: Test clearing destinations
            $this->info("\n🧹 Step 5: Test clearing destinations");
            $clearResult = $posteService->setDestinations($email, []);
            if ($clearResult) {
                $this->info("✅ Clear successful");
            } else {
                $this->error("❌ Clear failed");
            }

            $clearedDestinations = $posteService->getDestinations($email);
            $this->line("Destinations after clear: " . json_encode($clearedDestinations, JSON_PRETTY_PRINT));

            if (empty($clearedDestinations)) {
                $this->info("✅ Destinations cleared successfully");
            } else {
                $this->error("❌ Destinations not cleared properly");
            }

            // Summary
            $this->info("\n📊 Test Summary:");
            $this->line("- Save operation: " . ($saveResult ? 'PASS' : 'FAIL'));
            $this->line("- Forward email persistence: " . ($forwardFound ? 'PASS' : 'FAIL'));
            $this->line("- User email exclusion: " . (!$userEmailFound ? 'PASS' : 'FAIL'));
            $this->line("- Clear operation: " . ($clearResult && empty($clearedDestinations) ? 'PASS' : 'FAIL'));

            $allPassed = $saveResult && $forwardFound && !$userEmailFound && $clearResult && empty($clearedDestinations);
            
            if ($allPassed) {
                $this->info("\n🎉 ALL TESTS PASSED! Forward save/load cycle works correctly.");
            } else {
                $this->error("\n❌ SOME TESTS FAILED! There are issues with the forward save/load cycle.");
            }

            return $allPassed ? 0 : 1;

        } catch (Exception $e) {
            $this->error("Error: " . $e->getMessage());
            $this->line("Stack trace: " . $e->getTraceAsString());
            return 1;
        }
    }
}
