<?php

namespace Exolog\Console\Commands;

use Exolog\Module\Mail\PosteApiService;
use Exolog\Module\Mail\PosteMailboxService;
use Exolog\Module\Users\UserMailboxService;
use Exolog\Module\Users\Model\User;
use Illuminate\Console\Command;

class TestPosteQuota extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:poste-quota {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Poste API quota endpoint for mailbox storage information';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        $this->info("Testing Poste API quota endpoint for: $email");
        $this->info("=".str_repeat("=", 50));
        
        try {
            // Test direct API service
            $this->info("1. Testing PosteApiService directly...");
            $this->info("GET request will be sent to: /boxes/$email/quota");
            
            $posteApiService = new PosteApiService();
            $quotaResponse = $posteApiService->getMailboxQuota($email);
            $this->info("PosteApiService quota response: " . json_encode($quotaResponse, JSON_PRETTY_PRINT));
            
            $this->info("");
            
            // Test mailbox service
            $this->info("2. Testing PosteMailboxService...");
            $posteMailboxService = new PosteMailboxService();
            $quotaResult = $posteMailboxService->getMailboxQuota($email);
            $this->info("PosteMailboxService quota result: " . json_encode($quotaResult, JSON_PRETTY_PRINT));
            
            $this->info("");
            
            // Test user mailbox service update
            $this->info("3. Testing UserMailboxService update...");
            $user = User::where('u_email', $email)->first();
            if ($user) {
                $this->info("Found user: {$user->u_id} - {$user->u_email}");
                
                $userMailboxService = new UserMailboxService();
                $userMailboxService->updateUserMailboxInfo($user);
                
                // Reload user to see updated values
                $user->refresh();
                $this->info("Updated user mailbox info:");
                $this->info("  u_ismailbox: {$user->u_ismailbox}");
                $this->info("  u_mailsize: '{$user->u_mailsize}'");
                $this->info("  u_maildest: '{$user->u_maildest}'");
            } else {
                $this->warn("No user found with email: $email");
            }
            
            $this->info("");
            $this->info("✅ Test completed successfully!");
            
        } catch (\Exception $e) {
            $this->error("❌ Test failed: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
            return 1;
        }
        
        return 0;
    }
}
