<?php

namespace Exolog\Console\Commands;

use Exolog\Module\Users\Model\User;
use Exolog\Module\Mail\PosteMailboxService;
use Illuminate\Console\Command;

class TestDualStorage extends Command
{
    protected $signature = 'test:dual-storage {email}';
    protected $description = 'Test dual storage (database + Poste API) for forward destinations';

    public function handle()
    {
        $email = $this->argument('email');
        
        $this->info("🔍 Testing dual storage for: $email");
        
        try {
            // Find user
            $user = User::where('u_email', $email)->first();
            if (!$user) {
                $this->error("User not found: $email");
                return 1;
            }
            
            $this->info("\n📊 Current State:");
            $this->line("User ID: {$user->u_id}");
            $this->line("Email: {$user->u_email}");
            $this->line("u_ismailbox: {$user->u_ismailbox}");
            $this->line("u_maildest: '{$user->u_maildest}'");
            
            // Parse database destinations
            $dbDestinations = [];
            if (!empty($user->u_maildest)) {
                $destinations = explode('||', trim($user->u_maildest, '|'));
                foreach ($destinations as $dest) {
                    if (!empty(trim($dest))) {
                        $dbDestinations[] = trim($dest);
                    }
                }
            }
            
            $this->info("\n📥 Database Destinations:");
            if (empty($dbDestinations)) {
                $this->line("  (none)");
            } else {
                foreach ($dbDestinations as $dest) {
                    $this->line("  - $dest");
                }
            }
            
            // Check Poste API
            $posteService = new PosteMailboxService();
            
            $this->info("\n📮 Poste API Status:");
            $mailboxExists = $posteService->mailboxExists($email);
            $this->line("Mailbox exists: " . ($mailboxExists ? 'YES' : 'NO'));
            
            $aliasExists = $posteService->aliasExists($email);
            $this->line("Alias exists: " . ($aliasExists ? 'YES' : 'NO'));
            
            if ($mailboxExists || $aliasExists) {
                $apiDestinations = $posteService->getDestinations($email);
                $this->info("\n📧 Poste API Destinations:");
                if (empty($apiDestinations)) {
                    $this->line("  (none)");
                } else {
                    foreach ($apiDestinations as $dest) {
                        $this->line("  - " . ($dest['name'] ?? 'INVALID'));
                    }
                }
                
                // Compare database vs API
                $this->info("\n🔄 Comparison:");
                $dbEmails = $dbDestinations;
                $apiEmails = array_column($apiDestinations, 'name');
                
                $this->line("Database count: " . count($dbEmails));
                $this->line("API count: " . count($apiEmails));
                
                $missing_in_api = array_diff($dbEmails, $apiEmails);
                $missing_in_db = array_diff($apiEmails, $dbEmails);
                
                if (!empty($missing_in_api)) {
                    $this->error("❌ Missing in API: " . implode(', ', $missing_in_api));
                }
                
                if (!empty($missing_in_db)) {
                    $this->error("❌ Missing in Database: " . implode(', ', $missing_in_db));
                }
                
                if (empty($missing_in_api) && empty($missing_in_db)) {
                    $this->info("✅ Database and API are in sync!");
                }
            }
            
        } catch (\Exception $e) {
            $this->error("Error: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
        }
    }
}
