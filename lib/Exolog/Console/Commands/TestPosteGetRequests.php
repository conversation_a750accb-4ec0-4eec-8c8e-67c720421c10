<?php

namespace Exolog\Console\Commands;

use Illuminate\Console\Command;
use Exolog\Module\Mail\PosteApiService;
use Exception;

class TestPosteGetRequests extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:poste-get-requests {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Poste API GET requests with email in URL path';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $email = $this->argument('email');
        [$userName, $domain] = explode('@', $email);

        $this->info("Testing Poste API GET requests for: {$email}");

        try {
            $posteApiService = new PosteApiService();

            // Test get domain
            $this->info("Testing getDomain with request body data...");
            try {
                $domainResponse = $posteApiService->getDomain($domain);
                $this->info("Domain GET response: " . json_encode($domainResponse));
            } catch (Exception $e) {
                $this->warn("Domain GET failed: " . $e->getMessage());
            }

            // Test get mailbox
            $this->info("Testing getMailbox with email in URL path...");
            try {
                $mailboxResponse = $posteApiService->getMailbox($email);
                $this->info("Mailbox GET response: " . json_encode($mailboxResponse));
            } catch (Exception $e) {
                $this->warn("Mailbox GET failed: " . $e->getMessage());
            }

            // Test alias exists
            $this->info("Testing aliasExists with email in URL path...");
            try {
                $aliasExists = $posteApiService->aliasExists($email);
                $this->info("Alias exists: " . ($aliasExists ? 'Yes' : 'No'));
            } catch (Exception $e) {
                $this->warn("Alias check failed: " . $e->getMessage());
            }

            $this->info("GET requests test completed!");
            return 0;

        } catch (Exception $e) {
            $this->error("GET requests test failed: " . $e->getMessage());
            return 1;
        }
    }
}
