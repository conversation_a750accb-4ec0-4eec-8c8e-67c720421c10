<?php

namespace Exolog\Console\Commands;

use Exolog\Module\Users\Model\User;
use Exolog\Module\Mail\PosteMailboxService;
use Illuminate\Console\Command;

class TestForwardSaveClear extends Command
{
    protected $signature = 'test:forward-save-clear {email}';
    protected $description = 'Test saving and clearing forward destinations in both database and Poste API';

    public function handle()
    {
        $email = $this->argument('email');
        
        $this->info("🔍 Testing forward save/clear for: $email");
        
        try {
            // Find user
            $user = User::where('u_email', $email)->first();
            if (!$user) {
                $this->error("User not found: $email");
                return 1;
            }
            
            $posteService = new PosteMailboxService();
            
            $this->info("\n📊 Initial State:");
            $this->showCurrentState($user, $posteService);
            
            // Test 1: Save some forward destinations
            $this->info("\n🧪 TEST 1: Saving forward destinations");
            $testDestinations = ['<EMAIL>', '<EMAIL>'];
            $this->saveForwardDestinations($user, $testDestinations, true);
            
            $this->info("\n📊 After Save:");
            $this->showCurrentState($user, $posteService);
            
            // Test 2: Clear forward destinations
            $this->info("\n🧪 TEST 2: Clearing forward destinations");
            $this->saveForwardDestinations($user, [], true);
            
            $this->info("\n📊 After Clear:");
            $this->showCurrentState($user, $posteService);
            
            // Test 3: Test alias mode (no mailbox)
            $this->info("\n🧪 TEST 3: Testing alias mode (no mailbox)");
            $this->saveForwardDestinations($user, ['<EMAIL>'], false);
            
            $this->info("\n📊 After Alias Save:");
            $this->showCurrentState($user, $posteService);
            
            // Test 4: Clear alias
            $this->info("\n🧪 TEST 4: Clearing alias");
            $this->saveForwardDestinations($user, [], false);
            
            $this->info("\n📊 Final State:");
            $this->showCurrentState($user, $posteService);
            
        } catch (\Exception $e) {
            $this->error("Error: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
        }
    }
    
    private function showCurrentState(User $user, PosteMailboxService $posteService)
    {
        // Refresh user data
        $user->refresh();
        
        $this->line("User ID: {$user->u_id}");
        $this->line("Email: {$user->u_email}");
        $this->line("u_ismailbox: {$user->u_ismailbox}");
        $this->line("u_maildest: '{$user->u_maildest}'");
        
        // Parse database destinations
        $dbDestinations = [];
        if (!empty($user->u_maildest)) {
            $destinations = explode('||', trim($user->u_maildest, '|'));
            foreach ($destinations as $dest) {
                if (!empty(trim($dest))) {
                    $dbDestinations[] = trim($dest);
                }
            }
        }
        
        $this->line("Database destinations: " . (empty($dbDestinations) ? '(none)' : implode(', ', $dbDestinations)));
        
        // Check Poste API
        $mailboxExists = $posteService->mailboxExists($user->u_email);
        $aliasExists = $posteService->aliasExists($user->u_email);
        
        $this->line("Poste mailbox exists: " . ($mailboxExists ? 'YES' : 'NO'));
        $this->line("Poste alias exists: " . ($aliasExists ? 'YES' : 'NO'));
        
        if ($mailboxExists || $aliasExists) {
            $apiDestinations = $posteService->getDestinations($user->u_email);
            $apiEmails = array_column($apiDestinations, 'name');
            $this->line("Poste destinations: " . (empty($apiEmails) ? '(none)' : implode(', ', $apiEmails)));
        }
    }
    
    private function saveForwardDestinations(User $user, array $destinations, bool $isMailbox)
    {
        $this->line("Saving destinations: " . (empty($destinations) ? '(none)' : implode(', ', $destinations)));
        $this->line("Is mailbox: " . ($isMailbox ? 'YES' : 'NO'));
        
        // Simulate the same logic as the controller
        $filteredDestinations = [];
        foreach ($destinations as $dest) {
            if ($dest !== 'maillisting' && strtolower($dest) !== strtolower($user->u_email)) {
                $filteredDestinations[] = $dest;
            }
        }

        // Format for database
        $u_maildest = '';
        if (!empty($filteredDestinations)) {
            $u_maildest = '|' . implode('||', $filteredDestinations) . '|';
        }

        // Update database
        $updateResult = $user->update([
            'u_ismailbox' => $isMailbox ? 1 : 0,
            'u_maildest' => $u_maildest
        ]);
        
        $this->line("Database update result: " . ($updateResult ? 'SUCCESS' : 'FAILED'));
        
        // Update Poste API
        $posteService = new PosteMailboxService();
        
        if ($isMailbox) {
            // For mailbox mode
            if (!$posteService->mailboxExists($user->u_email)) {
                $this->line("Creating mailbox...");
                $posteService->createMailbox($user->u_email, $user->u_name ?: $user->u_email, 'testpass123');
            }
            $posteResult = $posteService->setDestinations($user->u_email, $filteredDestinations);
        } else {
            // For alias mode
            if (!empty($filteredDestinations)) {
                if ($posteService->aliasExists($user->u_email)) {
                    $posteResult = $posteService->setDestinations($user->u_email, $filteredDestinations);
                } else {
                    $posteResult = $posteService->createAlias($user->u_email, $filteredDestinations);
                }
            } else {
                if ($posteService->aliasExists($user->u_email)) {
                    $posteResult = $posteService->deleteAlias($user->u_email);
                } else {
                    $posteResult = true; // Nothing to delete
                }
            }
        }
        
        $this->line("Poste API result: " . ($posteResult ? 'SUCCESS' : 'FAILED'));
    }
}
