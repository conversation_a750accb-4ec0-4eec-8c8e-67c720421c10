<?php

namespace Exolog\Console\Commands;

use Exolog\Module\Mail\PosteApiService;
use Exolog\Module\Mail\PosteMailboxService;
use Illuminate\Console\Command;

class DebugPosteResponse extends Command
{
    protected $signature = 'debug:poste-response {email}';
    protected $description = 'Debug Poste API response format for mailbox data';

    public function handle()
    {
        $email = $this->argument('email');
        
        $this->info("🔍 Debugging Poste API response for: $email");
        
        try {
            $posteApiService = new PosteApiService();
            $posteMailboxService = new PosteMailboxService();
            
            // 1. Check if mailbox exists
            $this->info("\n1️⃣ Checking mailbox existence...");
            $mailboxExists = $posteMailboxService->mailboxExists($email);
            $this->line("Mailbox exists: " . ($mailboxExists ? 'YES' : 'NO'));
            
            if ($mailboxExists) {
                // 2. Get raw mailbox data
                $this->info("\n2️⃣ Getting raw mailbox data...");
                $rawData = $posteApiService->getMailbox($email);
                $this->line("Raw mailbox data:");
                $this->line(json_encode($rawData, JSON_PRETTY_PRINT));
                
                // 3. Check redirectTo field specifically
                $this->info("\n3️⃣ Analyzing redirectTo field...");
                if (isset($rawData['redirectTo'])) {
                    $this->line("redirectTo field exists");
                    $this->line("Type: " . gettype($rawData['redirectTo']));
                    $this->line("Value: " . json_encode($rawData['redirectTo']));
                    
                    if (is_array($rawData['redirectTo'])) {
                        $this->line("Array count: " . count($rawData['redirectTo']));
                        foreach ($rawData['redirectTo'] as $index => $redirect) {
                            $this->line("  [$index]: $redirect");
                        }
                    }
                } else {
                    $this->line("redirectTo field does NOT exist");
                    $this->line("Available fields: " . implode(', ', array_keys($rawData)));
                }
                
                // 4. Test getDestinations method
                $this->info("\n4️⃣ Testing getDestinations method...");
                $destinations = $posteMailboxService->getDestinations($email);
                $this->line("Destinations from service:");
                $this->line(json_encode($destinations, JSON_PRETTY_PRINT));
                
            } else {
                // Check if it's an alias
                $this->info("\n2️⃣ Checking if it's an alias...");
                $aliasExists = $posteMailboxService->aliasExists($email);
                $this->line("Alias exists: " . ($aliasExists ? 'YES' : 'NO'));
                
                if ($aliasExists) {
                    $aliasData = $posteApiService->getAlias($email);
                    $this->line("Raw alias data:");
                    $this->line(json_encode($aliasData, JSON_PRETTY_PRINT));
                }
            }
            
        } catch (\Exception $e) {
            $this->error("Error: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
        }
    }
}
