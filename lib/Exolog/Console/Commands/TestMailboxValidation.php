<?php

namespace Exolog\Console\Commands;

use Exolog\Core\API\Controllers\UsersController;
use Exolog\Module\Users\Model\User;
use Illuminate\Console\Command;
use Exception;

class TestMailboxValidation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:mailbox-validation {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test mailbox validation rules (prevent self-forwarding)';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $email = $this->argument('email');

        $this->info("Testing mailbox validation for: $email");

        try {
            // Find user by email
            $user = User::where('u_email', $email)->first();
            
            if (!$user) {
                $this->error("User not found with email: $email");
                return 1;
            }

            $this->info("✅ User found: {$user->u_name} ({$user->u_email})");

            // Test case 1: Valid forward destination
            $this->info("\n🧪 Test 1: Valid forward destination");
            $validMailbox = [
                'is_popbox' => true,
                'password' => 'testpass123',
                'mailboxdest' => [
                    ['name' => '<EMAIL>']
                ],
                'is_maillisting_dest' => false
            ];

            $result1 = $this->testMailboxSave($user, $validMailbox);
            if ($result1['success']) {
                $this->info("✅ Valid forward destination accepted");
            } else {
                $this->warn("⚠️  Valid forward destination rejected: " . $result1['message']);
            }

            // Test case 2: Invalid forward destination (same as user email)
            $this->info("\n🧪 Test 2: Invalid forward destination (same as user email)");
            $invalidMailbox = [
                'is_popbox' => true,
                'password' => 'testpass123',
                'mailboxdest' => [
                    ['name' => $user->u_email]  // Same as user's email
                ],
                'is_maillisting_dest' => false
            ];

            $result2 = $this->testMailboxSave($user, $invalidMailbox);
            if (!$result2['success'] && strpos($result2['message'], 'Cannot forward to the same address') !== false) {
                $this->info("✅ Self-forwarding correctly rejected");
            } else {
                $this->error("❌ Self-forwarding validation failed");
                $this->line("Expected: Error message containing 'Cannot forward to the same address'");
                $this->line("Actual: " . ($result2['success'] ? 'Success' : $result2['message']));
            }

            // Test case 3: Mixed destinations (valid + invalid)
            $this->info("\n🧪 Test 3: Mixed destinations (valid + invalid)");
            $mixedMailbox = [
                'is_popbox' => true,
                'password' => 'testpass123',
                'mailboxdest' => [
                    ['name' => '<EMAIL>'],
                    ['name' => $user->u_email]  // Invalid
                ],
                'is_maillisting_dest' => false
            ];

            $result3 = $this->testMailboxSave($user, $mixedMailbox);
            if (!$result3['success'] && strpos($result3['message'], 'Cannot forward to the same address') !== false) {
                $this->info("✅ Mixed destinations correctly rejected");
            } else {
                $this->error("❌ Mixed destinations validation failed");
            }

            $this->info("\n📊 Test Summary:");
            $this->line("- Valid forward: " . ($result1['success'] ? '✅ PASS' : '❌ FAIL'));
            $this->line("- Self-forward rejection: " . ((!$result2['success'] && strpos($result2['message'], 'Cannot forward to the same address') !== false) ? '✅ PASS' : '❌ FAIL'));
            $this->line("- Mixed destinations: " . ((!$result3['success'] && strpos($result3['message'], 'Cannot forward to the same address') !== false) ? '✅ PASS' : '❌ FAIL'));

            return 0;

        } catch (Exception $e) {
            $this->error("Error: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * Test mailbox save operation
     */
    private function testMailboxSave(User $user, array $mailbox): array
    {
        try {
            // Create a mock request
            request()->merge([
                'u_id' => $user->u_id,
                'mailbox' => $mailbox
            ]);

            $controller = new UsersController();
            $response = $controller->saveUserMailbox();

            // Check if response indicates success or error
            if (method_exists($response, 'getData')) {
                $data = $response->getData(true);
                return [
                    'success' => isset($data['success']) && $data['success'],
                    'message' => $data['message'] ?? 'Unknown response'
                ];
            }

            return ['success' => true, 'message' => 'Operation completed'];

        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
}
