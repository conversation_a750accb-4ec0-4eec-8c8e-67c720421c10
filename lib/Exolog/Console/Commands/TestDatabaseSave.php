<?php

namespace Exolog\Console\Commands;

use Exolog\Module\Users\Model\User;
use Illuminate\Console\Command;

class TestDatabaseSave extends Command
{
    protected $signature = 'test:database-save {email}';
    protected $description = 'Test direct database save for u_maildest field';

    public function handle()
    {
        $email = $this->argument('email');
        
        $this->info("🔍 Testing database save for: $email");
        
        try {
            // Find user
            $user = User::where('u_email', $email)->first();
            if (!$user) {
                $this->error("User not found: $email");
                return 1;
            }
            
            $this->info("\n📊 Before Update:");
            $this->line("User ID: {$user->u_id}");
            $this->line("Email: {$user->u_email}");
            $this->line("u_ismailbox: {$user->u_ismailbox}");
            $this->line("u_maildest: '{$user->u_maildest}'");
            
            // Test data
            $testDestinations = ['<EMAIL>', '<EMAIL>'];
            $testMaildest = '|' . implode('||', $testDestinations) . '|';
            
            $this->info("\n💾 Attempting to save:");
            $this->line("u_ismailbox: 1");
            $this->line("u_maildest: '$testMaildest'");
            
            // Try to update
            $updateResult = $user->update([
                'u_ismailbox' => 1,
                'u_maildest' => $testMaildest
            ]);
            
            $this->line("Update result: " . ($updateResult ? 'SUCCESS' : 'FAILED'));
            
            // Refresh and check
            $user->refresh();
            
            $this->info("\n📊 After Update:");
            $this->line("u_ismailbox: {$user->u_ismailbox}");
            $this->line("u_maildest: '{$user->u_maildest}'");
            
            // Verify the data
            if ($user->u_ismailbox == 1 && $user->u_maildest === $testMaildest) {
                $this->info("\n✅ Database save SUCCESSFUL!");
            } else {
                $this->error("\n❌ Database save FAILED!");
                $this->error("Expected u_ismailbox: 1, Got: {$user->u_ismailbox}");
                $this->error("Expected u_maildest: '$testMaildest', Got: '{$user->u_maildest}'");
            }
            
            // Test clearing data
            $this->info("\n🗑️ Testing clear operation:");
            $clearResult = $user->update([
                'u_ismailbox' => 0,
                'u_maildest' => ''
            ]);
            
            $this->line("Clear result: " . ($clearResult ? 'SUCCESS' : 'FAILED'));
            
            $user->refresh();
            $this->line("After clear - u_ismailbox: {$user->u_ismailbox}");
            $this->line("After clear - u_maildest: '{$user->u_maildest}'");
            
        } catch (\Exception $e) {
            $this->error("Error: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
        }
    }
}
