<?php

namespace Exolog\Console\Commands;

use Exolog\Module\Mail\PosteApiService;
use Exolog\Module\Mail\PosteMailboxService;
use Illuminate\Console\Command;

class TestPosteSieve extends Command
{
    protected $signature = 'test:poste-sieve {email} {--get : Get current sieve script} {--set=* : Set redirect destinations} {--clear : Clear all redirects}';
    protected $description = 'Test Poste sieve API operations for mailbox redirects';

    public function handle()
    {
        $email = $this->argument('email');
        
        $this->info("🧪 Testing Poste Sieve API for: $email");
        $this->line('');

        try {
            $posteService = new PosteApiService();
            $mailboxService = new PosteMailboxService();

            // Get current sieve script
            if ($this->option('get') || (!$this->option('set') && !$this->option('clear'))) {
                $this->info('📥 Getting current sieve script...');
                
                $sieveData = $posteService->getMailboxSieve($email);
                
                if ($sieveData && isset($sieveData['script'])) {
                    $this->line('📜 Current sieve script:');
                    $this->line('```');
                    $this->line($sieveData['script']);
                    $this->line('```');
                    
                    // Parse and show extracted destinations
                    $destinations = $posteService->parseSieveScript($sieveData['script']);
                    if (!empty($destinations)) {
                        $this->line('');
                        $this->info('📧 Extracted redirect destinations:');
                        foreach ($destinations as $dest) {
                            $this->line("  • $dest");
                        }
                    } else {
                        $this->line('');
                        $this->comment('No redirect destinations found in script');
                    }
                } else {
                    $this->comment('No sieve script found for this mailbox');
                }
            }

            // Set new redirect destinations
            if ($this->option('set')) {
                $destinations = $this->option('set');
                $this->info('📤 Setting redirect destinations...');
                
                foreach ($destinations as $dest) {
                    $this->line("  • $dest");
                }
                
                $result = $posteService->updateMailboxRedirect($email, $destinations);
                
                if ($result) {
                    $this->info('✅ Redirect destinations updated successfully!');
                    
                    // Show generated script
                    $script = $this->generateSieveScript($destinations);
                    $this->line('');
                    $this->line('📜 Generated sieve script:');
                    $this->line('```');
                    $this->line($script);
                    $this->line('```');
                } else {
                    $this->error('❌ Failed to update redirect destinations');
                }
            }

            // Clear all redirects
            if ($this->option('clear')) {
                $this->info('🗑️ Clearing all redirect destinations...');
                
                $result = $posteService->updateMailboxRedirect($email, []);
                
                if ($result) {
                    $this->info('✅ All redirect destinations cleared!');
                } else {
                    $this->error('❌ Failed to clear redirect destinations');
                }
            }

        } catch (\Exception $e) {
            $this->error('❌ Error: ' . $e->getMessage());
            $this->line('');
            $this->line('Stack trace:');
            $this->line($e->getTraceAsString());
        }
    }

    private function generateSieveScript(array $redirectTo): string
    {
        if (empty($redirectTo)) {
            return '';
        }

        $script = "# poste.io redirect filter\nif true\n{\n";
        
        foreach ($redirectTo as $email) {
            $email = trim($email);
            if (!empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $script .= "      redirect \"$email\";\n";
            }
        }
        
        $script .= "}\n";

        return $script;
    }
}
