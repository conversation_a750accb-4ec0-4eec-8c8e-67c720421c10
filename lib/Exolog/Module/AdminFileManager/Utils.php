<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 12.02.2020
 * Time: 19:11
 */

namespace Exolog\Module\AdminFileManager;


class Utils
{

    /**
     * Cleanup filename
     *
     * @param string $str
     * @param bool $transliteration convert all spaces on files name and folders name with $replace_with variable
     * @param bool $convert_spaces convert all spaces on files name and folders name this value
     * @param string $replace_with
     * @param bool $is_folder
     *
     * @return string
     *
     */
    static public function fix_filename(
        $str,
        $transliteration = false,
        $convert_spaces = false,
        $replace_with = "_",
        $is_folder = false
    )
    {
        if ($convert_spaces) {
            $str = str_replace(' ', $replace_with, $str);
        }

        if ($transliteration) {
            if (!mb_detect_encoding($str, 'UTF-8', true)) {
                $str = utf8_encode($str);
            }
            if (function_exists('transliterator_transliterate')) {
                $str = transliterator_transliterate('Any-Latin; Latin-ASCII', $str);
            } else {
                $str = iconv('UTF-8', 'ASCII//TRANSLIT//IGNORE', $str);
            }

            $str = preg_replace("/[^a-zA-Z0-9\.\[\]_| -]/", '', $str);
        }

        $str = str_replace(array('"', "'", "/", "\\"), "", $str);
        $str = strip_tags($str);

        // Empty or incorrectly transliterated filename.
        // Here is a point: a good file UNKNOWN_LANGUAGE.jpg could become .jpg in previous code.
        // So we add that default 'file' name to fix that issue.
        if (strpos($str, '.') === 0 && $is_folder === false) {
            $str = 'file' . $str;
        }

        return trim($str);
    }

    static public function reArrayFiles(&$file_post)
    {

        $file_ary = array();
        $file_count = count($file_post['name']);
        $file_keys = array_keys($file_post);

        for ($i = 0; $i < $file_count; $i++) {
            foreach ($file_keys as $key) {
                $file_ary[$i][$key] = $file_post[$key][$i];
            }
        }

        return $file_ary;
    }
}
