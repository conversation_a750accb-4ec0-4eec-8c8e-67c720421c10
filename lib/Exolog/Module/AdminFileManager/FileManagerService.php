<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 11.02.2020
 * Time: 15:01
 */

namespace Exolog\Module\AdminFileManager;


use Exolog\Module\AdminFileManager\ConfigService\ConfigRepository;
use Exolog\Module\AdminFileManager\ConfigService\DefaultConfigRepository;
use Exolog\Module\AdminFileManager\Storage\StorageFileManager;
use Exolog\Module\Support\Facades\ExoFile;
use Illuminate\Support\Arr;
use RuntimeException;

class FileManagerService
{

    private ConfigRepository $configRepository;

    public function __construct(DefaultConfigRepository $configRepository)
    {
        $this->configRepository = $configRepository;
    }

    /**
     * @param $exolog
     * @param string $disk
     * @return FileManager
     */
    function getFileManager($disk)
    {
        if (empty($disk)) {
            throw new RuntimeException("'disk' is empty. Can't create FileManager");
        }
        return new StorageFileManager($disk);
    }

    private function getDisksConfig()
    {
        $config = [];
        foreach (StorageFileManager::availableDisks() as $disk) {
            $config[$disk] = ['driver' => 'local'];
        }
        if ($diskList = $this->configRepository->getDiskList()) {
            $config = Arr::where($config, function ($value, $key) use ($diskList) {
                return in_array($key, $diskList);
            });
        } else {
            $config = Arr::where($config, function ($value, $key) use ($diskList) {
                //TODO uncoment after migrate to disk
                return true;//$key !== 'DB';
            });
        }
        return $config;
    }

    public function getConfig(array $clientParams): array
    {
        $config = [
            "config" => [
                "leftDisk" => $this->configRepository->getLeftDisk(),
                "rightDisk" => $this->configRepository->getRightDisk(),
                "windowsConfig" => 2,
                "disks" => $this->getDisksConfig(),
                "lang" => "en",

            ]
        ];
        if ($clientParams['startDisk']) {
            $config['config']['leftDisk'] = $clientParams['startDisk'];
            if ($clientParams['startFolder']) {
                $config['config']['leftPath'] = $clientParams['startFolder'];
            }
        }
        if ($clientParams['startSelection']) {
            [$disk, $path] = ExoFile::parseFileId($clientParams['startSelection']);
            $config['config']['leftDisk'] = $disk;
            $config['config']['leftPath'] = pathinfo($path, PATHINFO_DIRNAME);
            $config['config']['selected'] = ['files' => [$path]];
        }
        return $config;
    }

}
