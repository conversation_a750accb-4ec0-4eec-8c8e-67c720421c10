<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 12.02.2020
 * Time: 13:21
 */

namespace Exolog\Module\AdminFileManager\Transfer;

abstract class Transfer
{
    public $disk;
    public $path;
    public $clipboard;

    /**
     * Transfer constructor.
     *
     * @param $disk
     * @param $path
     * @param $clipboard
     */
    public function __construct($disk, $path, $clipboard)
    {
        $this->disk = $disk;
        $this->path = $path;
        $this->clipboard = $clipboard;
    }

    /**
     * Transfer files and folders
     *
     */
    public function filesTransfer()
    {
        // determine the type of operation
        if ($this->clipboard['type'] === 'copy') {
            $this->copy();
        } elseif ($this->clipboard['type'] === 'cut') {
            $this->cut();
        }

    }

    abstract protected function copy();

    abstract protected function cut();
}
