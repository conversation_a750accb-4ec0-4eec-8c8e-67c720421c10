<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 12.02.2020
 * Time: 13:20
 */

namespace Exolog\Module\AdminFileManager\Transfer;


use RuntimeException;

class TransferService
{
    /**
     * @param $disk
     * @param $path
     * @param $clipboard
     *
     * @return Transfer
     */
    public static function build($disk, $path, $clipboard)
    {
        if ($disk === 'DB' && $clipboard['disk'] === 'DB') {
            return new DBTransfer($disk, $path, $clipboard);
        }

        if ($disk === 'DB' || $clipboard['disk'] === 'DB') {
            throw new RuntimeException('Not implemented yet!');
        }

        if ($disk === $clipboard['disk']) {
            return new LocalTransfer($disk, $path, $clipboard);
        }
        return new ExternalTransfer($disk, $path, $clipboard);
    }
}
