<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 11.02.2020
 * Time: 16:06
 */

namespace Exolog\Module\AdminFileManager;


abstract class FileManager
{

    abstract static public function availableDisks();

    protected $disk;

    /**
     * FileManager constructor.
     * @param $disk
     */
    public function __construct($disk)
    {
        $this->disk = $disk;
    }

    /**
     * Get directories for the tree (upper level)
     * @param $path
     * @return array
     */
    abstract public function tree($path);

    /**
     * Get directories/files for the tree (recursively)
     * @param $path
     * @return array
     */
    abstract public function treeContent($path);

    /**
     * Get content (files and folders)
     * @param $path
     * @return array
     */
    abstract public function content($path);

    /**
     * URL
     * @param $path
     */
    abstract public function url($path);

    /**
     * Get file to editing or showing
     * @param $path
     */
    abstract public function getFile($path);

    /**
     * Image thumbnails
     * @param $path
     * @return string
     */
    abstract public function thumbnails($path);

    /**
     * Image preview
     * @param $path
     * @return string
     */
    abstract public function preview($path);

    /**
     * Download file
     * @param $path
     */
    abstract public function download($path);

    /**
     * Create new file
     * @param $path
     * @param $name
     * @returns array
     */
    abstract public function createFile($name, $path);

    /**
     * Update file
     * @param $path
     * @param $file
     * @return
     */
    abstract public function updateFile($path, $file);

    /**
     * Create new directory
     * @param $name
     * @param $path
     * @return array
     */
    abstract public function createDirectory($name, $path);

    /**
     * Upload file
     * @param $path
     * @param $files
     * @param $fileURL
     * @param $overwrite
     */
    abstract public function upload($path, $files, $fileURL, $overwrite);

    /**
     * Delete selected items
     * @param $items array
     */
    abstract public function delete($items);

    /**
     * Rename file or folder
     * @param $newName
     * @param $oldName
     * @param $type
     * @return
     */
    abstract public function rename($newName, $oldName, $type);

    /**
     * Copy / Cut files and folders
     * @param $path
     * @param $clipboard
     * @return
     */
    abstract public function paste($path, $clipboard);

    /**
     * Zip
     * @param $path
     * @param $name
     * @param $elements array
     * @return
     */
    abstract public function zip($path, $name, $elements);

    /**
     * Unzip
     * @returns {*}
     * @param $data
     */
    abstract public function unzip($data);


    /**
     * File/Dir Properties
     * @param $path
     * @return
     */
    abstract public function props($path);

}
