<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 11.02.2020
 * Time: 16:17
 */

namespace Exolog\Module\AdminFileManager\Storage;

use Exolog\Module\AdminFileManager\FileManager;
use Exolog\Module\AdminFileManager\Storage\Traits\CheckTrait;
use Exolog\Module\AdminFileManager\Storage\Traits\ContentTrait;
use Exolog\Module\AdminFileManager\Storage\Traits\PathTrait;
use Exolog\Module\AdminFileManager\Transfer\TransferService;
use Exolog\Module\Http\Request;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;
use RuntimeException;

//use Intervention\Image\ImageManagerStatic as Image;


class StorageFileManager extends FileManager
{
    /**
     * @return  string[]
     */
    static public function availableDisks(): array
    {
        return array_merge(['media', 'protected', 'site'],
            array_keys(Config::get('site.disks', []))
        );
    }

    use PathTrait, ContentTrait, CheckTrait;

    /**
     * Initialize App
     *
     * @return array
     */
    public function initialize()
    {
        /* // if config not found
         if (!config()->has('file-manager')) {
             return [
                 'result' => [
                     'status'  => 'danger',
                     'message' => 'noConfig'
                 ],
             ];
         }

         $config = [
             'acl'           => $this->configRepository->getAcl(),
             'leftDisk'      => $this->configRepository->getLeftDisk(),
             'rightDisk'     => $this->configRepository->getRightDisk(),
             'leftPath'      => $this->configRepository->getLeftPath(),
             'rightPath'     => $this->configRepository->getRightPath(),
             'windowsConfig' => $this->configRepository->getWindowsConfig(),
             'hiddenFiles'   => $this->configRepository->getHiddenFiles(),
         ];

         // disk list
         foreach ($this->configRepository->getDiskList() as $disk) {
             if (array_key_exists($disk, config('filesystems.disks'))) {
                 $config['disks'][$disk] = Arr::only(
                     config('filesystems.disks')[$disk], ['driver']
                 );
             }
         }

         // get language
         $config['lang'] = app()->getLocale();

         return [
             'result' => [
                 'status'  => 'success',
                 'message' => null,
             ],
             'config' => $config,
         ];*/
    }

    /**
     * Get files and directories for the selected path and disk
     *
     * @param $path
     *
     * @return array
     */
    public function content($path)
    {
        // get content for the selected directory
        $content = $this->getContent($path);

        return [
            'result' => [
                'status' => 'success',
                'message' => null,
            ],
            'directories' => $content['directories'],
            'files' => $content['files'],
        ];
    }

    /**
     * Get part of the directory tree
     *
     * @param $path
     *
     * @return array
     */
    public function tree($path)
    {
        $directories = $this->getDirectoriesTree($path);

        return [
            'result' => [
                'status' => 'success',
                'message' => null,
            ],
            'directories' => $directories,
        ];
    }

    public function treeContent($path)
    {
        $directories = $this->getDirectoriesTreeContent($path);
        $content = $this->getContent($path);
        return [
            'result' => [
                'status' => 'success',
                'message' => null,
            ],
            'directories' => $directories,
            'files' => $content['files'],
        ];
    }

    /**
     * Upload files
     *
     * @param $path
     * @param $files
     * @param $fileURL
     * @param $overwrite
     *
     * @return array
     */
    public function upload($path, $files, $fileURL, $overwrite)
    {
        $fileNotUploaded = false;

        foreach ($files as $file) {
            // skip or overwrite files
            if (!$overwrite
                && Storage::disk($this->disk)
                    ->exists($path . '/' . $file->getClientOriginalName())
            ) {
                continue;
            }

            /* // check file size if need
             if ($this->configRepository->getMaxUploadFileSize()
                 && $file->getClientSize() / 1024 > $this->configRepository->getMaxUploadFileSize()
             ) {
                 $fileNotUploaded = true;
                 continue;
             }*/

            // check file type if need
            /*if ($this->configRepository->getAllowFileTypes()
                && !in_array(
                    $file->getClientOriginalExtension(),
                    $this->configRepository->getAllowFileTypes()
                )
            ) {
                $fileNotUploaded = true;
                continue;
            }*/

            // overwrite or save file
            Storage::disk($this->disk)->putFileAs(
                $path,
                $file,
                $file->getClientOriginalName()
            );
        }

        // If the some file was not uploaded
        if ($fileNotUploaded) {
            return [
                'result' => [
                    'status' => 'warning',
                    'message' => 'notAllUploaded',
                ],
            ];
        }

        return [
            'result' => [
                'status' => 'success',
                'message' => 'uploaded',
            ],
        ];
    }

    /**
     * Delete files and folders
     *
     * @param $items
     *
     * @return array
     */
    public function delete($items)
    {
        foreach ($items as $item) {
            // check all files and folders - exists or no
            if (!Storage::disk($this->disk)->exists($item['path'])) {
                continue;
            } else {
                if ($item['type'] === 'dir') {
                    // delete directory
                    Storage::disk($this->disk)->deleteDirectory($item['path']);
                } else {
                    // delete file
                    Storage::disk($this->disk)->delete($item['path']);
                }
            }
        }
        return [
            'result' => [
                'status' => 'success',
                'message' => 'deleted',
            ],
        ];
    }

    /**
     * Copy / Cut - Files and Directories
     *
     * @param $path
     * @param $clipboard
     *
     * @return array
     */
    public function paste($path, $clipboard)
    {
        // compare disk names
        if ($this->disk !== $clipboard['disk']) {

            if (!$this->checkDisk($clipboard['disk'])) {
                return $this->notFoundMessage();
            }
        }

        $transferService = TransferService::build($this->disk, $path, $clipboard);

        $transferService->filesTransfer();
    }

    /**
     * Rename file or folder
     *
     * @param $newName
     * @param $oldName
     * @param $type
     * @return array
     */
    public function rename($newName, $oldName, $type)
    {
        Storage::disk($this->disk)->move($oldName, $newName);

        $response = [];

        if ($type === 'dir') {
            // get directory properties
            $response['directory'] = $this->directoryProperties($newName);
        } else {
            $response['file'] = $this->fileProperties($newName);
        }

        $response['result'] = [
            'status' => 'success',
            'message' => 'renamed',
        ];
        return $response;
    }

    /**
     * Download selected file
     *
     * @param $path
     *
     * @return mixed
     */
    public function download($path)
    {
        // if file name not in ASCII format
        if (!preg_match('/^[\x20-\x7e]*$/', basename($path))) {
            $filename = Str::ascii(basename($path));
        } else {
            $filename = basename($path);
        }

        return Storage::disk($this->disk)->download($path, $filename);
    }

    /**
     * Create thumbnails
     *
     * @param $path
     *
     * @return Response|mixed
     * @throws FileNotFoundException
     */
    public function thumbnails($path)
    {
        //throw new \RuntimeException('Not implemented yet!');
        /* // create thumbnail
         if ($this->configRepository->getCache()) {
             $thumbnail = Image::cache(function ($image) use ($disk, $path) {
                 $image->make(Storage::disk($disk)->get($path))->fit(80);
             }, $this->configRepository->getCache());

             // output
             return response()->make(
                 $thumbnail,
                 200,
                 ['Content-Type' => Storage::disk($disk)->mimeType($path)]
             );
         }*/

        $thumbnail = Image::make(Storage::disk($this->disk)->get($path))->fit(80);

        return $thumbnail->response();
    }

    /**
     * Image preview
     *
     * @param $path
     *
     * @return mixed
     * @throws FileNotFoundException
     */
    public function preview($path)
    {
        // get image
        $preview = Image::make(Storage::disk($this->disk)->get($path));

        return $preview->response();
    }

    /**
     * Get file URL
     *
     * @param $path
     *
     * @return array
     */
    public function url($path)
    {
        return [
            'result' => [
                'status' => 'success',
                'message' => null,
            ],
            'url' => Storage::disk($this->disk)->url($path),
        ];
    }

    /**
     * Create new directory
     *
     * @param $path
     * @param $name
     *
     * @return array
     */
    public function createDirectory($name, $path)
    {
        // path for new directory
        $directoryName = $this->newPath($path, $name);

        // check - exist directory or no
        if (Storage::disk($this->disk)->exists($directoryName)) {
            return [
                'result' => [
                    'status' => 'warning',
                    'message' => 'dirExist',
                ],
            ];
        }

        // create new directory
        Storage::disk($this->disk)->makeDirectory($directoryName);

        // get directory properties
        $directoryProperties = $this->directoryProperties(
            $directoryName
        );

        // add directory properties for the tree module
        $tree = $directoryProperties;
        $tree['props'] = ['hasSubdirectories' => false];

        return [
            'result' => [
                'status' => 'success',
                'message' => 'dirCreated',
            ],
            'directory' => $directoryProperties,
            'tree' => [$tree],
        ];
    }

    /**
     * Create new file
     *
     * @param $path
     * @param $name
     *
     * @return array
     */
    public function createFile($name, $path)
    {
        // path for new file
        $path = $this->newPath($path, $name);

        // check - exist file or no
        if (Storage::disk($this->disk)->exists($path)) {
            return [
                'result' => [
                    'status' => 'warning',
                    'message' => 'fileExist',
                ],
            ];
        }

        // create new file
        Storage::disk($this->disk)->put($path, '');

        // get file properties
        $fileProperties = $this->fileProperties($path);

        return [
            'result' => [
                'status' => 'success',
                'message' => 'fileCreated',
            ],
            'file' => $fileProperties,
        ];
    }

    /**
     * Update file
     *
     * @param $path
     * @param $file
     *
     * @return array
     */
    public function updateFile($path, $file)
    {
        //fix to laravel file object
        $file = Request::capture()->file('file');

        // update file
        $res = Storage::disk($this->disk)->putFileAs(
            $path,
            $file,
            $file->getClientOriginalName()
        );
        if ($res === false) {
            throw new RuntimeException('Can`t update file. Please check permissions.');
        }

        // path for new file
        $filePath = $this->newPath($path, $file->getClientOriginalName());

        // get file properties
        $fileProperties = $this->fileProperties($filePath);

        return [
            'result' => [
                'status' => 'success',
                'message' => 'fileUpdated',
            ],
            'file' => $fileProperties,
        ];
    }

    /**
     * Stream file - for audio and video
     *
     * @param $path
     *
     * @return mixed
     */
    public function streamFile($path)
    {
        // if file name not in ASCII format
        if (!preg_match('/^[\x20-\x7e]*$/', basename($path))) {
            $filename = Str::ascii(basename($path));
        } else {
            $filename = basename($path);
        }

        return Storage::disk($this->disk)
            ->response($path, $filename,
                ['Accept-Ranges' => 'bytes']);
    }

    public function getFile($path)
    {
        return $this->streamFile($path);
    }

    public function zip($path, $name, $elements)
    {
        $zip = app(Zip::class);
        return $zip->create();
    }

    public function unzip($data)
    {
        $zip = app(Zip::class);
        return $zip->extract();
    }

    public function props($path)
    {
        $file = Storage::disk($this->disk)->getMetadata($path);
        $pathInfo = pathinfo($path);
        $file['basename'] = $pathInfo['basename'];
        $file['dirname'] = $pathInfo['dirname'] === '.' ? '' : $pathInfo['dirname'];
        $file['extension'] = $pathInfo['extension'] ?? '';
        $file['filename'] = $pathInfo['filename'];
        return $file;
    }
}
