<?php

namespace Exolog\Module\AdminFileManager\Storage\Traits;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Storage;

trait CheckTrait
{

    /**
     * Check disk name
     *
     * @param $name
     *
     * @return bool
     */
    public function checkDisk($name)
    {
        return in_array($name, static::availableDisks())
            && array_key_exists($name, Config::get('filesystems.disks'));
    }

    /**
     * Check Disk and Path
     *
     * @param $disk
     * @param $path
     *
     * @return bool
     */
    public function checkPath($path)
    {
        // check disk name
        if (!$this->checkDisk($this->disk)) {
            return false;
        }

        // check path
        if ($path && !Storage::disk($this->disk)->exists($path)) {
            return false;
        }

        return true;
    }

    /**
     * Disk/path not found message
     *
     * @return array
     */
    public function notFoundMessage()
    {
        return [
            'result' => [
                'status' => 'danger',
                'message' => 'notFound',
            ],
        ];
    }
}
