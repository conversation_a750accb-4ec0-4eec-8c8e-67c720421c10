<?php

namespace Exolog\Module\AdminFileManager\Storage\Traits;

use Alexusmai\LaravelFileManager\Services\ACLService\ACL;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use League\Flysystem\FileNotFoundException;

trait ContentTrait
{

    /**
     * Get content for the selected disk and path
     *
     * @param       $disk
     * @param null $path
     *
     * @return array
     */
    public function getContent($path = null)
    {
        $content = Storage::disk($this->disk)->listContents($path);

        // get a list of directories
        $directories = $this->filterDir($content);

        // get a list of files
        $files = $this->filterFile($content);

        return compact('directories', 'files');
    }

    /**
     * Get directories with properties
     *
     * @param       $disk
     * @param null $path
     *
     * @return array
     */
    public function directoriesWithProperties($path = null)
    {
        $content = Storage::disk($this->disk)->listContents($path);

        return $this->filterDir($content);
    }

    /**
     * Get files with properties
     *
     * @param       $disk
     * @param null $path
     *
     * @return array
     */
    public function filesWithProperties($path = null)
    {
        $content = Storage::disk($this->disk)->listContents($path);

        return $this->filterFile($content);
    }

    /**
     * Get directories for tree module
     *
     * @param $disk
     * @param $path
     *
     * @return array
     */
    public function getDirectoriesTree($path = null)
    {
        $directories = $this->directoriesWithProperties($path);

        foreach ($directories as $index => $dir) {
            $directories[$index]['props'] = [
                'hasSubdirectories' => (bool)Storage::disk($this->disk)->directories($dir['path']),
            ];
            $directories[$index]['directories'] = $this->getDirectoriesTree($dir['path']);
        }

        return $directories;
    }

    public function getDirectoriesTreeContent($path = null)
    {
        $directories = $this->directoriesWithProperties($path);

        foreach ($directories as $index => $dir) {
            $directories[$index]['props'] = [
                'hasSubdirectories' => (bool)Storage::disk($this->disk)->directories($dir['path']),
            ];
            if ($directories[$index]['props']['hasSubdirectories']) {
                $directories[$index]['directories'] = $this->getDirectoriesTreeContent($dir['path']);
            }
            $directories[$index]['files'] = $this->filesWithProperties($dir['path']);
        }

        return $directories;
    }

    /**
     * File properties
     *
     * @param       $disk
     * @param null $path
     *
     * @return mixed
     */
    public function fileProperties($path = null)
    {
        $file = Storage::disk($this->disk)->getMetadata($path);

        $pathInfo = pathinfo($path);

        $file['basename'] = $pathInfo['basename'];
        $file['dirname'] = $pathInfo['dirname'] === '.' ? '' : $pathInfo['dirname'];
        $file['extension'] = $pathInfo['extension'] ?? '';
        $file['filename'] = $pathInfo['filename'];


        return $file;
    }

    /**
     * Get properties for the selected directory
     *
     * @param null $path
     *
     * @return array|false
     * @throws FileNotFoundException
     */
    public function directoryProperties($path = null)
    {
        $directory = Storage::disk($this->disk)->getMetadata($path);

        $pathInfo = pathinfo($path);

        /**
         * S3 didn't return metadata for directories
         */
        if (!$directory) {
            $directory['path'] = $path;
            $directory['type'] = 'dir';
        }

        $directory['basename'] = $pathInfo['basename'];
        $directory['dirname'] = $pathInfo['dirname'] === '.' ? ''
            : $pathInfo['dirname'];


        return $directory;
    }

    /**
     * Get only directories
     *
     * @param $content
     *
     * @return array
     */
    protected function filterDir($content)
    {
        // select only dir
        $dirsList = Arr::where($content, function ($item) {
            return $item['type'] === 'dir';
        });

        // remove 'filename' param
        $dirs = array_map(function ($item) {
            return Arr::except($item, ['filename']);
        }, $dirsList);


        return array_values($dirs);
    }

    /**
     * Get only files
     *
     * @param $disk
     * @param $content
     *
     * @return array
     */
    protected function filterFile($content)
    {
        // select only files
        $files = Arr::where($content, function ($item) {
            return $item['type'] === 'file';
        });

        return array_values($files);
    }
}
