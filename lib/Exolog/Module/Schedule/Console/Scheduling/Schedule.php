<?php

namespace Exolog\Module\Schedule\Console\Scheduling;

use Exception;
use Exolog\Module\Schedule\Http\Services\ScheduleService;
use Exolog\Module\Site\Model\Site;
use Illuminate\Console\Scheduling\Schedule as BaseSchedule;
use Illuminate\Support\Facades\Log;

class Schedule
{
    private BaseSchedule $schedule;

    private $tasks;

    public function __construct(ScheduleService $scheduleService, BaseSchedule $schedule)
    {
        $this->tasks = $scheduleService->getActives();
        $this->schedule = $schedule;
    }

    /**
     * @throws Exception
     */
    public function execute()
    {
        foreach ($this->tasks as $task) {
            $this->dispatch($task);
        }
    }

    /**
     * @throws Exception
     */
    private function dispatch(\Exolog\Module\Schedule\Model\Schedule $task)
    {
        if ($task->command === 'custom') {
            $command = $task->command_custom;
            $event = $this->schedule->exec($command);
        } else {
            $command = $task->command;
            $event = $this->schedule->command(
                $command,
                array_values($task->getArguments()) + $task->getOptions()
            );
        }

        $event->cron($task->expression);

        //ensure output is being captured to write history
        $event->storeOutput();

        if ($task->environments) {
            $event->environments(explode(',', $task->environments));
        }

        if ($task->even_in_maintenance_mode) {
            $event->evenInMaintenanceMode();
        }

        if ($task->without_overlapping) {
            $event->withoutOverlapping();
        }

        if ($task->run_in_background) {
            $event->runInBackground();
        }

        if (!empty($task->webhook_before)) {
            $event->pingBefore($task->webhook_before);
        }

        if (!empty($task->webhook_after)) {
            $event->thenPing($task->webhook_after);
        }

        if (!empty($task->email_output)) {
            if ($task->sendmail_success) {
                $event->emailOutputTo($task->email_output);
            }

            if ($task->sendmail_error) {
                $event->emailOutputOnFailure($task->email_output);
            }
        }

        if (!empty($task->on_one_server)) {
            $event->onOneServer();
        }

        $event->onSuccess(
            function () use ($task, $event, $command) {
                $this->createLogFile($task, $event);
                if ($task->log_success) {
                    $this->createHistoryEntry($task, $event, $command);
                }
            }
        );

        $event->onFailure(
            function () use ($task, $event, $command) {
                $this->createLogFile($task, $event, 'critical');
                if ($task->log_error) {
                    $this->createHistoryEntry($task, $event, $command);
                }
            }
        );

        $event->after(function () use ($event) {
            unlink($event->output);
        });

        unset($event);
    }

    private function createLogFile(\Exolog\Module\Schedule\Model\Schedule $task, $event, $type = 'info')
    {
        if ($task->log_filename) {
            $filename = $task->log_filename;

            if ($task->site_id) {
                $site = Site::find($task->site_id);
                $filename = $site->site_alias . '_' . $filename;
            }

            $logChannel = Log::build([
                'driver' => 'single',
                'path' => storage_path('logs/' . $filename . '.log'),
            ]);
            Log::stack([$logChannel])->$type(file_get_contents($event->output));
        }
    }


    private function createHistoryEntry(\Exolog\Module\Schedule\Model\Schedule $task, $event, $command)
    {
        $task->histories()->create(
            [
                'command' => $command,
                'params' => $task->getArguments(),
                'options' => $task->getOptions(),
                'output' => file_get_contents($event->output)
            ]
        );
    }
}
