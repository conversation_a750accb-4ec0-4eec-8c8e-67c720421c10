<?php

namespace Exolog\Module\Schedule\Observer;

use Exolog\Module\Schedule\Http\Services\ScheduleService;
use Exolog\Module\Schedule\Model\Schedule;

class ScheduleObserver
{
    public function created()
    {
        $scheduleService = app(ScheduleService::class);
        $scheduleService->clearCache();
    }

    public function updated(Schedule $schedule)
    {
        $scheduleService = app(ScheduleService::class);
        $scheduleService->clearCache();
    }

    public function deleted(Schedule $schedule)
    {
        $scheduleService = app(ScheduleService::class);
        $scheduleService->clearCache();
    }

    public function saved(Schedule $schedule)
    {
        $scheduleService = app(ScheduleService::class);
        $scheduleService->clearCache();
    }
}
