<?php

namespace Exolog\Module\Schedule;

use Cron\CronExpression;
use Exolog\Module\Schedule\Commands\ScheduleClearCacheCommand;
use Exolog\Module\Schedule\Console\Scheduling\Schedule;
use Exolog\Module\Schedule\Observer\ScheduleObserver;
use Illuminate\Console\Scheduling\Schedule as BaseSchedule;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\ServiceProvider;

class DatabaseSchedulingServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Validator::extend('cron', function ($attribute, $value, $parameters, $validator) {
            return CronExpression::isValidExpression($value);
        });


        $config = $this->app['config'];

        if ($config->get('database-schedule.cache.enabled')) {
            $model = $config->get('database-schedule.model');
            $model::observe(ScheduleObserver::class);
        }


        $this->app->resolving(BaseSchedule::class, function ($schedule) {
            $schedule = app(Schedule::class, ['schedule' => $schedule]);
            return $schedule->execute();
        });

        $this->commands([
            ScheduleClearCacheCommand::class,
        ]);
    }
}
