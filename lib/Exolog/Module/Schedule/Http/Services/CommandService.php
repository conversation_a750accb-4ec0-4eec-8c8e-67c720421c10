<?php

namespace Exolog\Module\Schedule\Http\Services;

use Exolog\Module\Support\Concerns\HasMakeFactory;
use Exolog\Module\Support\Facades\Site;
use Illuminate\Contracts\Console\Kernel;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class CommandService
{
    use HasMakeFactory;

    public function get(): Collection
    {
        $commands = collect(app(Kernel::class)->all())->sortKeys();
        $commandsKeys = $commands->keys()->toArray();
        foreach (config('database-schedule.commands.exclude') as $exclude) {
            $commandsKeys = preg_grep("/^$exclude/", $commandsKeys, PREG_GREP_INVERT);
        }
        return $commands->only($commandsKeys)
            ->map(function ($command) {
                return (object)[
                    'name' => $command->getName(),
                    'description' => $command->getDescription(),
                    'signature' => $command->getSynopsis(),
                    'arguments' => $this->getArguments($command),
                    'options' => $this->getOptions($command),
                    'site_alias' => $this->extractSiteAlias($command->getName()),
                ];
            });
    }

    public function getForSite(): Collection
    {
        $all = $this->get();
        $alias = Site::alias();
        return $all->filter(function ($item) use ($alias) {
            return $item->site_alias === $alias || Str::startsWith($item->name, 'exolog:site');
        });
    }


    private function getArguments($command): array
    {
        $arguments = [];
        foreach ($command->getDefinition()->getArguments() as $argument) {
            $arguments[] = (object)[
                'name' => $argument->getName(),
                'default' => $argument->getDefault(),
                'required' => $argument->isRequired()
            ];
        }

        return $arguments;
    }

    private function getOptions($command): object
    {
        $options = (object)[
            'withValue' => [],
            'withoutValue' => [
                'verbose',
                'quiet',
                'ansi',
                'no-ansi',
            ]
        ];
        foreach ($command->getDefinition()->getOptions() as $option) {
            if ($option->acceptValue()) {
                $options->withValue[] = (object)[
                    'name' => $option->getName(),
                    'default' => $option->getDefault(),
                    'required' => $option->isValueRequired()
                ];
            } else {
                $options->withoutValue[] = $option->getName();
            }
        }

        return $options;
    }

    private function extractSiteAlias(string $command): ?string
    {
        if (Str::startsWith($command, 'site:')) {
            return Str::of($command)->explode(':')[1];
        }
        return null;
    }
}
