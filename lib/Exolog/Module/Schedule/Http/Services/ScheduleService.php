<?php


namespace Exolog\Module\Schedule\Http\Services;

use Exolog\Module\Schedule\Model\Schedule;
use Exolog\Module\Support\Concerns\HasMakeFactory;

class ScheduleService
{
    use HasMakeFactory;

    private $model;

    public function __construct()
    {
        $this->model = app(Schedule::class);
    }

    public function getActives()
    {
        if (config('database-schedule.cache.enabled')) {
            return $this->getFromCache();
        }
        return $this->model->active()->get();
    }

    public function clearCache()
    {
        $store = config('database-schedule.cache.store');
        $key = config('database-schedule.cache.key');

        cache()->store($store)->forget($key);
    }

    private function getFromCache()
    {
        $store = config('database-schedule.cache.store');
        $key = config('database-schedule.cache.key');

        return cache()->store($store)->rememberForever($key, function () {
            return $this->model->active()->get();
        });
    }
}
