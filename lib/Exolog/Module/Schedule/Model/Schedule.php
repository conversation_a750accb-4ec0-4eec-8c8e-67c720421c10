<?php

namespace Exolog\Module\Schedule\Model;

use Exolog\Module\Database\Scopes\SiteScopeSavingObserver;
use Exolog\Module\Support\Facades\Site;
use Illuminate\Console\Scheduling\ManagesFrequencies;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

/**
 * @property mixed $command
 * @property mixed $command_custom
 * @property string $expression
 * @property mixed $environments
 * @property mixed $even_in_maintenance_mode
 * @property mixed $without_overlapping
 * @property mixed $run_in_background
 * @property mixed $sendmail_success
 * @property mixed $sendmail_error
 * @property mixed $log_success
 * @property mixed $log_error
 * @property mixed $log_filename
 * @property mixed $site_id
 * @property string $status
 * @method static Builder onlySite()
 */
class Schedule extends Model
{
    use ManagesFrequencies;
    use SoftDeletes;

    public const STATUS_INACTIVE = '0';
    public const STATUS_ACTIVE = '1';
    public const SITE_FIELD = 'site_id';


    protected $fillable = [
        'command',
        'command_custom',
        'params',
        'options',
        'expression',
        'even_in_maintenance_mode',
        'without_overlapping',
        'on_one_server',
        'webhook_before',
        'webhook_after',
        'email_output',
        'sendmail_error',
        'sendmail_success',
        'log_success',
        'log_error',
        'status',
        'run_in_background',
        'log_filename',
        'groups',
        'environments',
    ];

    protected $attributes = [
        'expression' => '* * * * *',
        'params' => '{}',
        'options' => '{}',
    ];

    protected $casts = [
        'params' => 'array',
        'options' => 'array'
    ];

    public function histories()
    {
        return $this->hasMany(ScheduleHistory::class, 'schedule_id', 'id');
    }

    public function scopeInactive($query)
    {
        return $query->where('status', false);
    }

    public function scopeOnlySite($query)
    {
        return $query->where(static::SITE_FIELD, Site::id());
    }

    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    public function getArguments(): array
    {
        $arguments = [];

        foreach (($this->params ?? []) as $argument => $value) {
            if (empty($value['value'])) {
                continue;
            }
            if (isset($value["type"]) && $value['type'] === 'function') {
                eval('$arguments[$argument] = (string) ' . $value['value']);
            } else {
                $arguments[$argument] = $value['value'];
            }
        }

        return $arguments;
    }

    public function getOptions(): array
    {
        $options = [];
        foreach (($this->options ?? []) as $option => $value) {
            if (is_array($value) && ($value['value'] ?? null) === null) {
                continue;
            }
            $option = '--' . $option;
            if (is_array($value)) {
                if (isset($value["type"]) && $value['type'] === 'function') {
                    $options[$option] = (string)$value['value']();
                } else {
                    $options[$option] = $value['value'];
                }
            } else {
                $options[] = $option;
            }
        }
        if (Str::startsWith($this->command, ['exolog:site'])) {
            $options['--site_id'] = $this->site_id;
        }

        return $options;
    }

    public static function getGroups()
    {
        return static::whereNotNull('groups')
            ->groupBy('groups')
            ->get('groups')
            ->pluck('groups', 'groups');
    }

    public static function getEnvironments()
    {
        return static::whereNotNull('environments')
            ->groupBy('environments')
            ->get('environments')
            ->pluck('environments', 'environments');
    }

    public static function booted()
    {
        $key = static::SITE_FIELD;
        if (Site::isInit()) {
            static::saving(SiteScopeSavingObserver::apply($key));
        }
    }

    public function activate(): self
    {
        $this->status = $this::STATUS_ACTIVE;
        $this->save();
        return $this;

    }

    public function inactivate(): self
    {
        $this->status = $this::STATUS_INACTIVE;
        $this->save();
        return $this;
    }
}
