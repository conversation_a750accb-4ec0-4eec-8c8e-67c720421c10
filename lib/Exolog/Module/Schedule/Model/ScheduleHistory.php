<?php

namespace Exolog\Module\Schedule\Model;

use Illuminate\Database\Eloquent\Model;

class ScheduleHistory extends Model
{

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table;

    protected $fillable = [
        'command',
        'params',
        'output',
        'options'
    ];

    protected $casts = [
        'params' => 'array',
        'options' => 'array'
    ];

    /**
     * Creates a new instance of the model.
     *
     * @param array $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    public function command()
    {
        return $this->belongsTo(Schedule::class, 'schedule_id', 'id');
    }
}
