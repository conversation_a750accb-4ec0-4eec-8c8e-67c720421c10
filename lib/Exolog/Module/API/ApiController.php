<?php

namespace Exolog\Module\API;

use Exolog\Module\Routing\SystemController;
use Exolog\Module\Support\Facades\Auth;
use Illuminate\Auth\AuthenticationException;

class ApiController extends SystemController
{

    protected Api $api;

    public function __construct()
    {
        $this->api = resolve(Api::class);
    }

    /**
     * @param $params
     * @return true|void
     * @throws AuthenticationException
     * @deprecated
     */
    protected function checkAuth($params)
    {
        if (!isset($params['level'])) {
            $this->unauthenticated();
        }
        switch ($params['level']) {
            case 'user':
                if (Auth::check()) {
                    return true;
                }
                break;
            case 'admin':
                if (Auth::isAdmin()) {
                    return true;
                }
                break;
            case 'developer':
                if (Auth::isDeveloper()) {
                    return true;
                }
                break;

        }
        $this->unauthenticated();
    }

    /**
     * @throws AuthenticationException
     */
    protected function unauthenticated()
    {
        abort(403, 'Unauthorized.');
    }
}