<?php

namespace Exolog\Module\API;

use Exolog\Module\Routing\SystemController;
use Illuminate\Support\Str;

class ApiProxyController extends SystemController
{
    public function __invoke($controllerName, $methodName)
    {
        $controller = resolve('Exolog\\Core\API\\Controllers\\' . Str::ucfirst($controllerName) . 'Controller');
        $response = app()->call([$controller, $methodName]);
        if (!is_null($response)) {
            return $response;
        }
        return resolve(Api::class);
    }
}