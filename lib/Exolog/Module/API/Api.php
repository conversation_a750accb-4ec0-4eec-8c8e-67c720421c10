<?php

namespace Exolog\Module\API;

use Exolog\Module\Support\Facades\BuffLog;
use Illuminate\Contracts\Support\Responsable;
use Illuminate\Http\JsonResponse;

class Api implements Responsable
{

    public array $result = [
        'data' => [],
        'log' => [],
        'info' => [],
        'warning' => [],
        'success' => true
    ];

    public function errorMsg($params): JsonResponse
    {
        $response = [
            'error' => 'error',
            'code' => 400,
            'success' => false
        ];

        if (is_string($params)) {
            $response['error'] = $params;
        } else {

            $errorAlias = [
                'noexists' => ['error' => 'no such method error', 'code' => 404],
                'auth' => ['error' => 'you are not allowed to get data from api', 'code' => 401],
                'required' => ['error' => 'not all required parameters', 'code' => 400]
            ];

            if (array_key_exists($params['errorAlias'], $errorAlias)) {
                $response = array_merge($response, $errorAlias[$params['errorAlias']]);
            } elseif (isset($params['error'])) {
                $response['error'] = $params['error'];
                if (!empty($params['code'])) {
                    $response['code'] = $params['code'];
                }
            }
        }

        return new JsonResponse($response, $response['code']);
    }

    public function logMsg($params): Api
    {
        $this->result['log'] = $params;
        return $this;
    }

    public function logMsgPush($params): Api
    {
        if (is_array($params)) {
            if (empty($this->result['log'])) {
                $this->result['log'] = $params;
            } elseif (is_array($this->result['log'])) {
                $this->result['log'] = array_merge($this->result['log'], $params);
            } else {
                $this->result['log'] = array_merge([$this->result['log']], $params);
            }
        } else {
            $this->result['log'][] = $params;
        }
        return $this;
    }

    public function infoMsg($params): Api
    {
        $this->result['info'] = $params;
        return $this;
    }

    public function infoMsgPush($params): Api
    {
        $this->result['info'][] = $params;
        return $this;
    }

    public function warningMsg($params): Api
    {
        $this->result['warning'] = $params;
        return $this;
    }

    public function warningMsgPush($params): Api
    {
        $this->result['warning'][] = $params;
        return $this;
    }

    public function dataMsg($params): Api
    {
        $this->result['data'] = $params;
        return $this;
    }

    public function dataMsgPush($params): Api
    {
        $this->result['data'][] = $params;
        return $this;
    }

    public function clearData(): Api
    {
        $this->result['data'] = [];
        return $this;
    }

    public function emptyResult(): array
    {
        return ['data' => [], 'success' => true];
    }

    public function toResponse($request): JsonResponse
    {
        $this->logMsgPush(BuffLog::getRecords());

        return new JsonResponse($this->result);
    }

    /**
     * @param $data
     * @deprecated
     */
    public function result($data)
    {
        if (is_array($data)) {
            $this->result = array_merge($this->result, $data);
        }
        return $data;
    }
}