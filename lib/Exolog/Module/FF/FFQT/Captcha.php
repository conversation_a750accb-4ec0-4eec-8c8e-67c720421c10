<?php

namespace Exolog\Module\FF\FFQT;

use Exolog\Module\Support\Facades\Vars;
use Galahad\Aire\Elements\Element;
use RuntimeException;

class Captcha extends BaseResolver
{

    public function resolve(): Element
    {
        $element = new \Exolog\Module\FF\Elements\Captcha($this->aire, $this->form);
        $site_key = $this->fq->getCustomParam('site_key');
        if (empty($site_key)) {
            $site_key = Vars::edition('captcha_site_key')->getData();
        }
        if (empty($site_key)) {
            $site_key = Vars::site('captcha_site_key')->getData();
        }
        if (empty($site_key)) {
            throw new RuntimeException('Please set "site_key" in custom params of captcha field!');
        }
        $element->setSiteKey($site_key);
        return $element;
    }
}