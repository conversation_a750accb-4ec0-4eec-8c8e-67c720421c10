<?php

namespace Exolog\Module\FF\FFQT;

use Exolog\Module\FF\Elements\Select as SelectElement;
use Galahad\Aire\Elements\Element;

class Select extends BaseResolver
{

    public function resolve(): Element
    {
        $options = $this->fq->getValues();

        /** @var SelectElement $element */
        $element = $this->applyCommonAttributes(new SelectElement($this->aire, $options, $this->form));
        if ($this->fq->isMultiple()) {
            $element->multiple();
        }
        return $element;
    }
}