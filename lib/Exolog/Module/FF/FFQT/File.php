<?php

namespace Exolog\Module\FF\FFQT;

use Exolog\Module\FF\Elements\File as ElementFile;
use Exolog\Module\Support\Facades\Auth;
use Galahad\Aire\Elements\Attributes\ClassNames;
use Galahad\Aire\Elements\Button;
use Galahad\Aire\Elements\Element;

class File extends BaseResolver
{
    public function resolve(): Element
    {
        $is_filemanager = $this->fq->getCustomParam('filemanager') && Auth::isAdmin();

        /** @var ElementFile $element */
        $element = $this->applyCommonAttributes(new ElementFile($this->aire, $this->form));
        if ($is_filemanager) {
            $element->is_filemanager = true;

            $element->attributes['type'] = 'text';
            $element->attributes['readonly'] = true;

            $element->class((new ClassNames('input', $element))->all());
            $element->selectFileButton = new Button($this->aire, $this->form);
            $element->selectFileButton->label(trans('ff.common.select-library-file'));
            $element->selectFileButton->addClass('exo-select-file-btn');
            $element->selectFileButton->data('for', $element->attributes['id']);
            $element->selectFileButton->data('start-folder', $this->fq->getCustomParam('startFolder'));
            $element->selectFileButton->data('start-disk', $this->fq->getCustomParam('startDisk'));
            $element->selectFileButton->data('folder-back', $this->fq->getCustomParam('folderBack'));

            $element->clearFileButton = new Button($this->aire, $this->form);
            $element->clearFileButton->label(trans('ff.common.clear-library-file'));
            $element->clearFileButton->addClass('exo-clear-file-btn');
            $element->clearFileButton->data('for', $element->attributes['id']);


            $element->data('filemanager', 'image');
        } else {
            $element->clearFileButton = new Button($this->aire, $this->form);
            $element->clearFileButton->label(trans('ff.common.clear-library-file'));
            $element->clearFileButton->addClass('exo-clear-file-input-btn');
            $element->clearFileButton->data('for', $element->attributes['id']);
            $element->clearFileButton->data('value-input', '__' . $this->fq->fq_name);
        }
        return $element;
    }
}