<?php

namespace Exolog\Module\FF\FFQT;

use Exolog\Module\FF\Elements\Concerns\XeditableLabel;
use Exolog\Module\Forms\Model\FormQuestion;
use Galahad\Aire\Aire;
use Galahad\Aire\DTD\Input;
use Galahad\Aire\Elements\Element;
use Galahad\Aire\Elements\Form;

abstract class BaseResolver implements Resolver
{

    protected Aire $aire;
    protected Form $form;
    protected FormQuestion $fq;

    public function __construct(Aire $aire, Form $form, FormQuestion $fq)
    {
        $this->aire = $aire;
        $this->form = $form;
        $this->fq = $fq;
    }


    abstract public function resolve(): Element;

    protected function applyCommonAttributes(Element $element): Element
    {
        $element->setFQ($this->fq);
        $element->label((new XeditableLabel($this->fq))->build());
        $element->name($this->fq->fq_name);
        $element->helpText($this->fq->fq_info ?? '');

        if ($this->fq->fq_isrequired && (
                $element instanceof \Galahad\Aire\DTD\Textarea ||
                $element instanceof Input ||
                $element instanceof \Galahad\Aire\DTD\Select
            )) {
            $element->required();
        }

        $this->applyPlaceholder($element);
        return $element;
    }

    protected function applyPlaceholder(Element $element): Element
    {
        $placeholder = data_get($this->fq->getCustomParams(), 'placeholder');
        if ($placeholder && (
                $element instanceof Input ||
                $element instanceof \Galahad\Aire\DTD\Textarea
            )) {
            $element->placeholder($placeholder);
        }
        return $element;
    }
}