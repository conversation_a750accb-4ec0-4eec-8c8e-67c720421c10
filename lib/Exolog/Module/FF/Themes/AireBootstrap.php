<?php

namespace Exolog\Module\FF\Themes;

use Exolog\Module\FF\FFService;
use Galahad\Aire\Elements\Attributes\ClassNames;
use Galahad\Aire\Elements\Checkbox;
use Galahad\Aire\Elements\Input;

class AireBootstrap implements AireTheme
{

    public function registerMutators()
    {
        $checkboxMutator = function (Checkbox $checkbox) {
            $checkbox->group->addClass('custom-control custom-checkbox');
        };
        Checkbox::registerElementMutator($checkboxMutator);
        \Exolog\Module\FF\Elements\Checkbox::registerElementMutator($checkboxMutator);

        $inputMutator = function (Input $input) {

            $input->attributes->registerMutator('class', function (ClassNames $classNames) use ($input) {

                if ('file' === $input->attributes->get('type')) {
                    $classNames
                        ->remove('form-control')
                        ->add('custom-file-input');

                    //$input
                    //->groupAddClass('custom-file');
                    //->groupRemoveClass('form-group');

                    $input->group->label
                        ->addClass('custom-file-label')
                        ->removeClass('cursor-pointer');
                }

                if ('range' === $input->attributes->get('type')) {
                    $classNames
                        ->remove('form-control')
                        ->add('form-control-range');
                }

                return $classNames;
            });
        };
        Input::registerElementMutator($inputMutator);
        \Exolog\Module\FF\Elements\Input::registerElementMutator($inputMutator);
    }

    public function apply(FFService $aire)
    {
        $this->registerMutators();
    }
}