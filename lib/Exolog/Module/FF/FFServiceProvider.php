<?php

namespace Exolog\Module\FF;

use Exolog\Module\FF\Elements\ExoForm;
use Galahad\Aire\Elements\Element;
use Galahad\Aire\Elements\Form;
use Galahad\Aire\Support\AireServiceProvider;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;

class FFServiceProvider extends ServiceProvider
{
    /**
     * Resolved path to internal config file
     *
     * @var string
     */
    protected $config_path;


    /**
     * Resolved path to internal translations
     *
     * @var string
     */
    protected $translations_directory;

    /**
     * Resolved path to the built JS directory
     *
     * @var string
     */
    protected $js_dist_directory;

    public function __construct(Application $app)
    {
        parent::__construct($app);

        //$base_path = dirname(__DIR__, 2);

        //$this->config_path = "$base_path/config/aire.php";
        //$this->translations_directory = "$base_path/translations";
    }

    /**
     * Perform post-registration booting of services.
     *
     * @return void
     */
    public function boot()
    {
        //require_once __DIR__.'/helpers.php';

        //$this->bootConfig();
        $this->bootViews();
        //$this->bootBladeComponents();
        //$this->bootTranslations();
        //$this->bootPublicAssets();
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        //$this->mergeConfigFrom($this->config_path, 'aire');

        $this->app->singleton('ffaire', function (Application $app) {
            return new FFService(
                $app['view'],
                $app['session.store'],
                $app['ffaire.form.resolver'],
                $app['config']['aire']
            );
        });


        $this->app->alias('ffaire', FFService::class);

        //Base form to use inline view
        $this->app->bind('galahad.aire.form', function (Application $app) {
            return new Form(
                $app['ffaire'],
                $app['url'],
                $app->bound('router') ? $app['router'] : null,
                $app->bound('session.store') ? $app['session.store'] : null
            );
        });
        $this->app->alias('galahad.aire.form', Form::class);

        //FF form to build form by react or exolog form
        $this->app->bind('ffaire.form', function (Application $app, $parameters) {
            return new ExoForm(
                $app['ffaire'],
                $app['url'],
                $app->bound('router') ? $app['router'] : null,
                $app->bound('session.store') ? $app['session.store'] : null,
                $parameters[0],
                $parameters[1],
            );
        });

        $this->app->alias('ffaire.form', ExoForm::class);


        $this->app->singleton('ffaire.form.resolver', function (Application $app) {
            return function () use ($app) {
                return $app->make(Form::class);
            };
        });

        Element::macro('getFQ', function () {
            return $this->view_data['fq'];
        });

        Element::macro('setFQ', function ($fq) {
            $this->view_data['fq'] = $fq;
        });

    }

    /**
     * Boot our views
     *
     * @return $this
     */
    protected function bootViews(): self
    {
        $path = resource_path('views/vendor/aire-tailwind');
        //null - laravel will add exolog view path with prefix /vendor/namespace
        //the same exolog will do for site view path
        $this->loadViewsFrom($path, 'aire-tailwind');


        $path = resource_path('views/vendor/aire-bootstrap');
        $this->loadViewsFrom($path, 'aire-bootstrap');

        /*if (method_exists($this->app, 'resourcePath')) {
            $this->publishes([
                $this->view_directory => $this->app->resourcePath('views/vendor/aire-tailwind'),
            ], 'aire-views');
        }*/

        return $this;
    }

    protected function bootBladeComponents(): self
    {
        if (version_compare($this->app->version(), '8.0.0', '>=')) {
            Blade::componentNamespace('Galahad\\Aire\\Components', 'aire');
        }

        return $this;
    }

    /**
     * Boot the configuration
     *
     * @return AireServiceProvider
     */
    protected function bootConfig(): self
    {
        if (method_exists($this->app, 'configPath')) {
            $this->publishes([
                $this->config_path => $this->app->configPath('aire.php'),
            ], 'aire-config');
        }

        return $this;
    }

    /**
     * Boot the translations
     *
     * @return AireServiceProvider
     */
    protected function bootTranslations(): self
    {
        $this->loadTranslationsFrom($this->translations_directory, 'aire');

        if (method_exists($this->app, 'resourcePath')) {
            $this->publishes([
                $this->translations_directory => $this->app->resourcePath('lang/vendor/aire'),
            ], 'aire-translations');
        }

        return $this;
    }

    /**
     * Publish public assets (JS/etc)
     *
     * @return AireServiceProvider
     */
    protected function bootPublicAssets(): self
    {
        if (method_exists($this->app, 'publicPath')) {
            $this->publishes([
                $this->js_dist_directory => $this->app->publicPath() . '/vendor/aire/js',
            ], 'aire-public-assets');
        }

        return $this;
    }
}
