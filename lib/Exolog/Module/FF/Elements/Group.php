<?php

namespace Exolog\Module\FF\Elements;


use Illuminate\Contracts\Support\Htmlable;

class Group extends \Galahad\Aire\Elements\Group
{

    /**
     * @var ExoForm
     */
    protected $form;

    protected function viewData(): array
    {
        return array_merge(parent::viewData(), [
            'error_view' => $this->aire->applyThemeInherit('_error'),
            'fq' => $this->getElementFQ(),
            'form_id' => $this->form->formModel->form_id
        ]);
    }

    /**
     * Set the group's label
     *
     * @param string|Htmlable $text
     * @return \Galahad\Aire\Elements\Group
     */
    public function label($text): self
    {
        // TODO: Is this necessary any more or can we just use attributes?
        // TODO: Might make sense to have a special innerHTML attribute that doesn't get rendered to the key=value list

        $this->label = (new Label($this->aire, $this))->text($text);

        return $this;
    }

    public function getElementFQ()
    {
        return $this->element->getFQ();
    }
}