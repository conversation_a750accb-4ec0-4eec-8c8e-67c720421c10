<?php

namespace Exolog\Module\FF\Elements;

use Galahad\Aire\Aire;
use Galahad\Aire\Elements\Button;

class Submit extends Button
{
    public $name = 'submit';

    public function __construct(Aire $aire, ExoForm $form)
    {
        parent::__construct($aire, $form);
        $this->view_data['exoForm'] = $form;
        $this->view_data['form_id'] = $form->formModel->form_id;
    }

    public function label(string $label, bool $overwriteEditable = true): self
    {
        if ($overwriteEditable) {
            $this->view_data['overwriteEditable'] = true;
        }
        return parent::label($label);
    }

    public function labelHtml(string $html, bool $overwriteEditable = true): self
    {
        if ($overwriteEditable) {
            $this->view_data['overwriteEditable'] = true;
        }
        return parent::labelHtml($html);
    }
}