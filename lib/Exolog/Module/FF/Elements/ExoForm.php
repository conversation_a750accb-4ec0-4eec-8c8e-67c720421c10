<?php

namespace Exolog\Module\FF\Elements;

use ArrayAccess;
use Exolog\Core\Forms\React;
use Exolog\Module\Contracts\XEditable;
use Exolog\Module\FF\FFQT\Hidden;
use Exolog\Module\FF\FFQT\Resolver;
use Exolog\Module\FF\FFService;
use Exolog\Module\Forms\Model\Form;
use Exolog\Module\Forms\Model\FormQuestion;
use Exolog\Module\Support\Facades\Vars;
use Galahad\Aire\Elements\Button;
use Galahad\Aire\Elements\Element;
use Galahad\Aire\Elements\Form as FormElement;
use Illuminate\Routing\Router;
use Illuminate\Routing\UrlGenerator;
use Illuminate\Session\Store;
use Illuminate\Support\HtmlString;
use Illuminate\Support\MessageBag;
use Illuminate\Support\ViewErrorBag;
use InvalidArgumentException;
use ReturnTypeWillChange;
use RuntimeException;
use function array_key_exists;

/**
 *
 */
class ExoForm extends FormElement implements ArrayAccess, XEditable
{
    private static array $form_ids = [];
    private array $formconf;

    public Form $formModel;

    /**
     * @var Element[]
     */
    public array $fields;
    public Button $submit;
    public \Galahad\Aire\Elements\Summary $summary;

    public function __construct(
        FFService    $aire,
        UrlGenerator $url,
        Router       $router = null,
        Store        $session_store = null,
        Form         $formModel = null,
                     $formconf = []
    )
    {
        parent::__construct($aire, $url, $router, $session_store);
        if (!$formModel instanceof Form) {
            throw new InvalidArgumentException('Incorrect param form model passed to constructor!');
        }
        $this->formModel = $formModel;

        $this->setFormConf($formconf);

        $this->route('forms.react.submit', $formModel->form_id);

        $this->resolveFields();

        $this->encType('multipart/form-data');

        $this->submit = $this->submit(__('ff.common.submit'));
        $this->summary = $this->summary(false);


        $this->addClass('ff-exo-form');
        $this->data('form_id', $this->formModel->form_id);
        $this->data('form_name', $this->formModel->form_name);
        $this->id($this->getFormDomId());
    }

    public function submit(string $label = ''): Submit
    {
        $button = new Submit($this->aire, $this);

        if ($label) {
            $button->label($label, false);
        }
        $button->type('Submit');

        return $button;
    }

    public function summary(?bool $verbose = null): Summary
    {
        $summary = new Summary($this->aire, $this);

        if (null !== $verbose) {
            $summary->verbose($verbose);
        }

        return $summary;
    }

    private function resolveFields(): void
    {
        $form_questions = $this->formModel->form_questions;

        if (array_key_exists('order', $this->formconf)) {
            $sorted = $form_questions->sortBy(function (FormQuestion $fq) {
                $order = array_search($fq->fq_name, $this->formconf['order'], true);
                if ($order === false) {
                    $order = (int)$fq->fq_order + 1000;
                }
                return $order;
            });
            $form_questions = collect($sorted->values()->all());
        }

        if (array_key_exists('show', $this->formconf)) {
            $form_questions = $form_questions->filter(function (FormQuestion $fq) {
                return in_array($fq->fq_name, $this->formconf['show'], true);
            });
        }

        if (array_key_exists('hide', $this->formconf)) {
            $form_questions = $form_questions->filter(function (FormQuestion $fq) {
                return !in_array($fq->fq_name, $this->formconf['hide'], true);
            });
        }

        $form_questions->each(
            function (FormQuestion $fq) {

                $ffqtClass = $fq->getECO('FFQT');
                if (empty($ffqtClass)) {
                    throw new InvalidArgumentException('FFQT class is not defined for form question type = ' . $fq->fq_type);
                }


                if (array_key_exists('hidden', $this->formconf)) {
                    if (in_array($fq->fq_name, $this->formconf['hidden'], true)) {
                        $ffqtClass = Hidden::class;
                    }
                }
                /** @var Resolver $ffqt */
                $ffqt = app($ffqtClass, [
                    'aire' => $this->aire,
                    'form' => $this,
                    'fq' => $fq,
                ]);
                $this->fields[$fq->fq_name] = $ffqt->resolve();
            });
    }

    private function getFormDomId(): string
    {
        if (!array_key_exists($this->formModel->form_id, static::$form_ids)) {
            static::$form_ids[$this->formModel->form_id] = 0;
            return 'form_' . $this->formModel->form_id;
        }
        static::$form_ids[$this->formModel->form_id]++;
        return 'form_' . $this->formModel->form_id . '_' . static::$form_ids[$this->formModel->form_id];
    }

    protected function viewData(): array
    {
        $this->view_data['fields'] = $this->fields;
        $this->view_data['submit'] = $this->submit;
        $this->view_data['summary'] = $this->summary;
        $this->view_data['_formconf'] = encrypt($this->formconf);
        $this->view_data['errorBag'] = $this->formconf['errorBag'];
        $this->view_data['scripts'] = new FFScripts();
        $this->view_data['form_id'] = $this->formModel->form_id;

        return array_merge(parent::viewData(), $this->validationData(),);
    }

    /**
     * Render the Element to HTML
     *
     * @return string
     */
    public function render(): string
    {
        $this->formconf['fields'] = array_keys($this->fields);

        $this->aire->setPrefix($this->formconf['prefix'] ?? 'exolog_form');

        $this->setAttribute($this->getExeditAttrName(), $this->getExeditAttrValue());

        $result = $this->aire->render(
            $this->name,
            $this->viewData()
        );

        $this->aire->setPrefix('');

        return $result;
    }

    public function open(): FormElement
    {
        throw new RuntimeException('You should not call this method for ExoForm');
    }


    public function close(): FormElement
    {
        throw new RuntimeException('You should not call this method for ExoForm');
    }

    /**
     * Render to HTML, including group if appropriate
     *
     * @return string
     */
    public function toHtml(): string
    {
        return $this->grouped && $this->group
            ? $this->group->render()
            : $this->render();
    }

    public function bindReact(React $react): self
    {
        $this->bind($react);
        return $this;
    }

    /**
     * Bind data to the form
     *
     * This data will automatically be used to determine an Element's
     * value if a value is not set, and no old input exists
     *
     * @param $bound_data
     * @param bool $merge
     * @return ExoForm
     */
    public function bind($bound_data, bool $merge = false): self
    {
        if ($bound_data instanceof React) {
            if ($merge) {
                throw new InvalidArgumentException('You can`t merge React instance to current data!');
            }

            if ($bound_data->getReactForm()->form_id !== $this->formModel->form_id) {
                throw new InvalidArgumentException("This form was initiated by a different form_id=" .
                    $bound_data->getReactForm()->form_id .
                    " from the one to which this React belongs form_id=" . $this->formModel->form_id);
            }
            $this->formconf['react_id'] = $bound_data['react_id'];
        }

        if ($merge) {
            if (is_null($this->bound_data)) {
                $this->bound_data = [];
            }
            foreach ($bound_data as $key => $bound_datum) {
                $this->bound_data[$key] = $bound_datum;
            }
        } else {
            $this->bound_data = $bound_data;
        }

        return $this;
    }

    #[ReturnTypeWillChange]
    public function offsetExists($offset)
    {
        return array_key_exists($offset, $this->fields);
    }

    #[ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->fields[$offset];
    }

    #[ReturnTypeWillChange]
    public function offsetSet($offset, $value): void
    {
        if (null === $offset) {
            $this->fields[] = $value;
        } else {
            $this->fields[$offset] = $value;
        }
    }

    #[ReturnTypeWillChange]
    public function offsetUnset($offset): void
    {
        unset($this->fields[$offset]);
    }

    public function setFormConf(array $formconf = []): self
    {
        $this->formconf = array_merge(
            ['errorBag' => 'form_' . $this->formModel->form_id],
            $formconf,
            ['form_id' => $this->formModel->form_id]
        );

        if ($formconf['react_id']) {
            $react = react()->find($formconf['react_id']);
            if (is_null($react)) {
                throw new InvalidArgumentException("You pass incorrect 'react_id' to formconf. Can't find react.");
            }
            $this->bindReact($react);
        } else {
            $this->bind(new React($this->formModel));
        }
        return $this;
    }

    /**
     * Get any validation errors associated with an Element
     *
     * @param string|null $name
     * @return array
     */
    public function getErrors(string $name = null): array
    {
        $errors = $this->getErrorBag();

        if (!$errors instanceof ViewErrorBag && !$errors instanceof MessageBag) {
            return [];
        }

        // If no name provided, return all errors
        if ($name === null) {
            return $errors->all();
        }

        if (!$errors->has($name)) {
            return [];
        }

        return $errors->get($name);
    }

    public function getErrorBag()
    {
        if (!$errors = $this->session_store->get('errors')) {
            return null;
        }

        if (!$errors instanceof ViewErrorBag) {
            return null;
        }

        return $errors->getBag($this->getErrorBagName());
    }

    protected function getErrorBagName()
    {
        return $this->formconf['errorBag'];
    }

    //TODO get old values by errorBag?
    //public function getBoundValue($name, $default = null)


    private function getExeditAttrValue()
    {
        $form_response = $this->formconf['response'] ?? 'response';

        $response_var = Vars::form('container_' . $form_response, $this->formModel->form_id);

        if (empty($response_var->getId())) {
            $response_var->save();
        }
        return $response_var->getContainer()->getContainerId();

    }

    public function getExeditAttr($exedit = []): HtmlString
    {
        return new HtmlString(sprintf('%s="%s"', $this->getExeditAttrName(), $this->getExeditAttrValue()));
    }

    private function getExeditAttrName(): string
    {
        return 'data-form-cont';
    }
}