<?php

namespace Exolog\Module\FF\Elements;

use Exolog\Module\FF\Elements\Concerns\InheritGroupable;
use Galahad\Aire\Elements\Button;
use Galahad\Aire\Elements\Input;

class File extends Input
{
    use InheritGroupable;

    public $name = 'file';
    //protected $grouped = false;

    public bool $is_filemanager = false;
    protected $default_attributes = [
        'type' => 'file',
    ];
    public ?Button $selectFileButton = null;
    public ?Button $clearFileButton = null;


    protected function viewData(): array
    {
        $this->view_data['is_filemanager'] = $this->is_filemanager;
        $this->view_data['selectFileButton'] = $this->selectFileButton;
        $this->view_data['clearFileButton'] = $this->clearFileButton;
        return parent::ViewData();
    }


}