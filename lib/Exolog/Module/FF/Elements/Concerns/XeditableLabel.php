<?php

namespace Exolog\Module\FF\Elements\Concerns;

use Exolog\Module\Forms\Model\FormQuestion;
use Exolog\Module\View\XEditableValue;

class XeditableLabel
{
    use XEditableValue;

    private FormQuestion $fq;

    public function __construct(FormQuestion $fq)
    {
        $this->fq = $fq;
    }

    public function build(): string
    {
        return sprintf('<span %s>%s</span>', $this->getExeditAttr(), $this->fq->fq_displayname);
    }

    protected function buildExedit($exedit = [])
    {
        return [
            'type' => 'form',
            'id' => $this->fq->fq_form,
            'field' => $this->fq->fq_name,
            'contenttype' => 'tinymce',
            'toolbar' => 'custom',
            'label' => true
        ];

    }
}