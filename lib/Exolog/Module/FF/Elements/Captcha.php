<?php

namespace Exolog\Module\FF\Elements;

use Galahad\Aire\Aire;
use Galahad\Aire\Contracts\NonInput;
use Galahad\Aire\Elements\Element;
use Galahad\Aire\Elements\Form;

class Captcha extends Element implements NonInput
{
    public $name = 'captcha';

    protected $grouped = false;

    public function __construct(Aire $aire, Form $form = null)
    {
        parent::__construct($aire, $form);

        $this->attributes->set('class', 'g-recaptcha');
    }

    public function setSiteKey($site_key): void
    {
        $this->site_key = $site_key;
        $this->attributes->set('data-site-key', $site_key);
    }
}