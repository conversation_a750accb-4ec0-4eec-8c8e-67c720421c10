<?php

namespace Exolog\Module\FF;

use Closure;
use Exolog\Core\Forms\React;
use Exolog\Module\FF\Elements\ExoForm;
use Exolog\Module\FF\Themes\AireTheme;
use Exolog\Module\Forms\Model\Form;
use Galahad\Aire\Aire;
use Illuminate\Session\Store;
use Illuminate\Support\Str;
use Illuminate\View\Factory;
use InvalidArgumentException;

/**
 * @inherit
 */
class FFService extends Aire
{

    private static function getThemeConfig($namespace): array
    {
        return config("${namespace}-theme");
    }

    public function react(React $react, $formconf = []): ExoForm
    {
        $form = $react->getReactForm();
        return $this->exoForm($form, $formconf)->bindReact($react);
    }

    public function exoForm($form, $formconf = []): ExoForm
    {
        if (is_string($form)) {
            $formModel = Form::findByName($form);
        } elseif ($form instanceof Form) {
            $formModel = $form;
        } else {
            throw new InvalidArgumentException('Parameter "form" should be form name or form model');
        }

        //TODO remove link to form from Aire
        $this->form = app()->make(ExoForm::class, [$formModel, $formconf]);

        $this->form->onClose(function () {
            $this->form = null;
        });

        return $this->form;
    }

    public function setPrefix($prefix = null)
    {
        $this->view_prefix = $prefix;
    }

    public function setTheme($namespace = null, $prefix = null, array $config = null): Aire
    {
        if (is_null($config)) {
            $config = $this::getThemeConfig($namespace);
        }
        if (empty($config)) {
            throw new InvalidArgumentException('Config is null. Please specify correct theme/namespace or set custom');
        }

        parent::setTheme($namespace, $prefix, $config); // TODO: Change the autogenerated stub

        $theme = app('Exolog\Module\FF\Themes\\' . Str::of($namespace)->camel()->ucfirst());
        if ($theme instanceof AireTheme) {
            $theme->apply($this);
        }
        return $this;
    }

    public function __construct(Factory $view_factory, Store $session_store, Closure $form_resolver, array $config)
    {
        parent::__construct($view_factory, $session_store, $form_resolver, $config);
    }

    public function resetTheme(): self
    {
        $this->setTheme(config('aire.default_theme'));

        return $this;
    }

    public function render($view, array $data = [], array $merge_data = []): string
    {
        return $this->view_factory->first($this->applyThemeInherit($view), $data, $merge_data)->render();
    }

    public function renderFirst(array $views, array $data = [], array $merge_data = []): string
    {
        $theme_views = $this->applyThemeInherit($views);
        return $this->view_factory->first($theme_views, $data, $merge_data)->render();
    }

    public function applyThemeInherit($views)
    {
        $result = [];
        if ($this->view_prefix) {
            $result = collect($views)->map(function ($view) {
                return "{$this->view_namespace}::{$this->view_prefix}.{$view}";
            });
        }
        return collect($result)->concat(collect($views)->map(function ($view) {
            return "{$this->view_namespace}::{$view}";
        }))->toArray();
    }
}