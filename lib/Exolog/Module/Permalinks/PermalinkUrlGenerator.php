<?php

namespace Exolog\Module\Permalinks;

use Closure;
use Exolog\Core\Forms\React;
use Exolog\Module\Container\BaseContainer;
use Exolog\Module\Forms\FQT\Unit\Unit;
use Exolog\Module\Forms\Model\FormQuestion;
use Exolog\Module\Support\Concerns\HasMakeFactory;
use Exolog\Module\Support\Facades\Markers;
use Illuminate\Support\Str;

class PermalinkUrlGenerator
{
    use HasMakeFactory;

    public function generate(ResolvedPermalink $resolvedPermalink): string
    {
        $form_permalink = $resolvedPermalink->getFormPermalink();

        $template = $form_permalink->fp_uri;

        $keys = $this->prepareKeys($resolvedPermalink);

        $url = $this->buildURL($template, $keys);

        if (!Str::startsWith($url, ['http://', 'https://'])) {
            $url = $this->makeUnique($url, $resolvedPermalink->react);
        }

        return $url;
    }

    /**
     * Replaces occurrences in template (ex. {key}) from passed arrays
     * in a first attempt looking for keys
     * @param string $template ex '{alias}/any/{name}'
     * @param array $keys ex. array('alias'=>'page_1','name'=>'title')
     * @return string ex. 'page_1/any/title'
     */
    public function buildURL(string $template, array $keys = []): string
    {
        $link = $template;
        $link = Markers::apply($link, ['keys' => $keys]);
        if (preg_match_all("/(\{.+?\})/", $template, $matches)) {
            $in_keys = array_unique($matches[0]);
            foreach ($in_keys as $match) {
                $key = trim($match, '{}');
                $value = '';
                if (array_key_exists($key, $keys)) {
                    $value = Str::slug($keys[$key]);
                }
                if (Str::startsWith($key, 'container_tree_')) {
                    if ($keys['container_tree'] instanceof Closure) {
                        $value = $keys['container_tree'](str_replace('container_tree_', '', $key));
                    }
                }

                $link = str_replace($match, $value, $link);
            }
        }
        if (!Str::startsWith($link, ['http://', 'https://'])) {
            //cleanup link
            $link = str_replace('//', '/', $link);
        }
        return $link;
    }

    private function prepareKeys(ResolvedPermalink $resolvedPermalink): array
    {

        $keys = $resolvedPermalink->react->toArray();

        //Replace select/radio values for display value
        //also use custom param for option to extract multi lang values
        //check custom param option for "lang" if exist add as field_lang
        $fqs = $resolvedPermalink->react->getReactForm()->form_questions;
        foreach ($fqs as $fq) {
            /** @var FormQuestion $fq */
            $type = $fq->getTypeName();
            if ($type === 'select' || $type === 'radio') {
                $fqvs = $fq->values;
                foreach ($fqvs as $fqv) {
                    // if this value selected
                    if ($resolvedPermalink->react[$fq['fq_name']] === $fqv['fqv_value']) {
                        //use Display value instead of code
                        $keys[$fq['fq_name']] = $fqv['fqv_displayvalue'];
                    }
                }
            }
        }

        //Add react's form keys
        $form = $resolvedPermalink->react->getReactForm()->toArray();
        $form_keys = add_keys_prefix($form, 'form_');
        $keys = array_merge($form_keys, $keys);


        if (!empty($resolvedPermalink->context['container'])) {

            //Add container(page|react) keys
            /** @var BaseContainer $container */
            $container = $resolvedPermalink->context['container'];
            $parent = (array)$container->getHolder();
            $parent_keys = [];

            if ($container->getHolderType() === 'unit') {
                /** @var Unit $unit */
                $unit = $container->getHolder();
                if ($unit->getHolderType() === $unit::HOLDER_TYPE_REACT) {
                    $parent_keys = $unit->getHolder()->toArray();
                }
            } else {
                $parent_keys = $parent;
            }

            $parent_keys = add_keys_prefix($parent_keys, 'parent_');
            $keys = array_merge($parent_keys, $keys);

            $keys['container_tree'] = static function ($key) use ($resolvedPermalink, $container) {
                $tree = $container->getReactsTree();
                $stack = [];
                $react_id = $resolvedPermalink->react->id();

                while ($react_id) {
                    $parent_tree_react = find_parent_react_in_tree($tree, $react_id);
                    if ($parent_tree_react) {
                        $stack[] = Str::slug($parent_tree_react[$key]);
                        $react_id = $parent_tree_react->id();
                    } else {
                        $react_id = null;
                    }
                }
                return implode('/', array_reverse($stack));
            };
        }
        return $keys;
    }

    private function makeUnique(string $permalink, React $react, $postfix = null): string
    {
        if ($postfix !== null) {
            $parts = parse_url($permalink);
            $new_path = rtrim($parts['path'], '/') . '-' . $postfix;
            $parts['path'] = substr($parts['path'], -1) === '/' ? $new_path . '/' : $new_path;
            $permalink_unique = build_url($parts);
        } else {
            $permalink_unique = $permalink;
        }
        $exists = react()
            ->permalink($permalink_unique)
            ->edition($react->getReactRelatedEditionId())
            ->where('react_id', '!=', $react->id())
            ->exists();
        if ($exists) {
            return $this->makeUnique($permalink, $react, $postfix + 1);
        }
        return $permalink_unique;
    }


}