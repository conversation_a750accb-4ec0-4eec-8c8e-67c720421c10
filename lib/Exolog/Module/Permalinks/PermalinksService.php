<?php

namespace Exolog\Module\Permalinks;

use Exception;
use Exolog\Core\Forms\React;
use Exolog\Module\Forms\Model\Form;
use Exolog\Module\Permalinks\Model\Permalink;
use Exolog\Module\Support\Concerns\HasMakeFactory;
use Exolog\Module\Support\Facades\Vars;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Psr\Log\LoggerInterface;

class PermalinksService
{
    use HasMakeFactory;

    public LoggerInterface $logger;

    public function __construct(?LoggerInterface $logger = null)
    {
        $this->logger = $logger;
    }

    public function updateReactPermalinks(React $react, array $options = []): void
    {
        if (!$react->hasField('permalink')) {
            return;
        }

        $resolver = new PermalinkResolver($react);

        $resolvedPermalinks = $resolver->resolve();
        $homepage_var = Vars::edition('homepage_react_id', $react->getReactRelatedEditionId());

        if ($options['use_as_homepage']) {
            $react_old_homepage_id = $homepage_var->getValueAsInt();
            if ($react_old_homepage_id !== $react->id()) {
                $homepage_var->setValue($react->id())->save();
                if ($react_old_homepage = react()->find($react_old_homepage_id)) {
                    $this->updateReactPermalinks($react_old_homepage);
                }
            }
        }

        $is_homepage = $homepage_var->getValueAsInt() === $react->id();
        $resolvedIds = $resolvedPermalinks->each(function (ResolvedPermalink $resolvedPermalink) use ($is_homepage) {
            if ($resolvedPermalink->type === Permalink::TYPE_PRIMARY && $is_homepage) {
                $resolvedPermalink->url = Permalink::HOME;
            } else {
                $resolvedPermalink->generatePermalinkUrl();
            }
        })->filter(function (ResolvedPermalink $resolvedPermalink) {
            return $resolvedPermalink->url !== '';
        })->each(function (ResolvedPermalink $resolvedPermalink) {
            $resolvedPermalink->sync();
        })->map(function (ResolvedPermalink $resolvedPermalink) {
            if ($resolvedPermalink->permalink === null) {
                return null;
            }
            return $resolvedPermalink->permalink->p_id;
        })->filter();

        //Delete old permalinks which are not resolved in this run
        Permalink::query()
            ->where('p_react_id', $react->id())
            ->whereIn('p_type', [Permalink::TYPE_PRIMARY, Permalink::TYPE_ALTERNATE])
            ->whereNotIn('p_id', $resolvedIds)
            ->delete();

        return;
    }

    public function permalinksGenerate(): Collection
    {

        $forms = Form::query()->whereHas('form_questions', function (Builder $q) {
            $q->where('fq_type', ECO('forms.fqt.permalink.fqt_id'));
        })->pluck('form_id');

        return react()->form($forms)->get()->each(function (React $react) {
            try {
                $this->updateReactPermalinks($react);
            } catch (Exception $exception) {
                //throw $exception;
                $this->logger->error($exception->getMessage(), ['error' => $exception]);
            }
        });
    }

    public function getCustomErrorPage($code)
    {
        $react = react()->edition(getCurrentEdition())
            ->where('alias', $code)
            ->first();

        if (empty($react)) {
            $react = react()->edition(getDefaultEdition())
                ->where('alias', $code)
                ->first();
        }

        if (empty($react)) {
            $page404 = Vars::edition('page_' . $code)->getValue();
            if (empty($page404)) {
                $page404 = Vars::edition('page_' . $code, getDefaultEdition())->getValue();
            }

            if (!empty($page404) && is_numeric(trim($page404))) {
                $react = react()->find($page404);
            }
        }


        if (!empty($react)) {
            $page404 = $react->getPermalink()->url;
        }

        if (empty($page404)) {
            return null;
        }


        $arrContextOptions = [
            "ssl" => [
                "verify_peer" => false,
                "verify_peer_name" => false,
            ],
        ];
        return file_get_contents($page404, false, stream_context_create($arrContextOptions));
    }

}
