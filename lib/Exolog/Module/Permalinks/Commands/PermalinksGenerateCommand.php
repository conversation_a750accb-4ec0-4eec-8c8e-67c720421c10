<?php

namespace Exolog\Module\Permalinks\Commands;

use Exception;
use Exolog\Module\Console\SystemCommand;
use Exolog\Module\Log\BufferLogger;
use Exolog\Module\Permalinks\Model\Permalink;
use Exolog\Module\Permalinks\PermalinksService;
use Exolog\Module\Support\Facades\Site;
use Illuminate\Support\Facades\Artisan;

class PermalinksGenerateCommand extends SystemCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'exolog:permalinks-generate {--site_id=}';


    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Regenerate and update all permalinks';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     * @throws Exception
     */
    public function handle()
    {
        if ($this->option('site_id')) {
            $this->updateOneSite();
        } else {
            $this->updateAllSite();
        }
        return 0;
    }

    private function updateOneSite(): void
    {
        $this->resolveSite();

        $this->info('Start generate permalinks for site: "' . Site::alias() . '"');

        Permalink::query()
            ->whereIn('p_type', [Permalink::TYPE_PRIMARY, Permalink::TYPE_ALTERNATE])
            ->delete();

        $reacts = PermalinksService::make(['logger' => $logger = BufferLogger::make()])->permalinksGenerate();
        $this->info("{$reacts->count()} reacts processed.");
        if ($errorCount = count($logger->getRecords())) {
            $this->error("Errors: $errorCount");
        }
        foreach ($logger->getRecords() as $record) {
            $this->error($record['formatted'], 'vv');
        }
    }

    private function updateAllSite(): void
    {
        $sites = \Exolog\Module\Site\Model\Site::all()->pluck('site_id');

        $sites->each(function ($site_id) {
            $this->info('Send the job to queue for site:' . $site_id);
            Artisan::queue('exolog:permalinks-generate', ['--site_id' => $site_id]);
        });

    }
}
