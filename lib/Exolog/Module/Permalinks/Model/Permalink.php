<?php


namespace Exolog\Module\Permalinks\Model;


use Exolog\Module\Database\Concerns\HasSiteScope;
use Exolog\Module\Forms\Model\FormPermalink;
use Exolog\Module\Support\PHPDoc\PHPDoc_Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

/**
 * @property string $p_url
 * @property string $p_redirect
 * @property int $p_fp_id
 * @property FormPermalink $form_permalink
 * @property array $p_context
 * @property string $p_type
 * @property int $p_id
 *
 * @mixin PHPDoc_Builder
 */
class Permalink extends Model
{
    use HasSiteScope;
    use HasPermalinkSaveFrom;

    public const SITE_FIELD = 'p_site_id';

    public const TYPE_PRIMARY = 'PRIMARY';
    public const TYPE_HISTORY = 'HISTORY';
    public const TYPE_ALTERNATE = 'ALTERNATE';
    public const TYPE_MANUAL = 'MANUAL';

    public const HOME = '/';

    protected $table = 'permalinks';
    protected $primaryKey = 'p_id';
    //public $timestamps = true;
    protected $with = [];

    protected $fillable = [
        'p_react_id',
        'p_type',
        'p_url',
        'p_fp_id',
        'p_context',
        'p_redirect',
    ];

    protected $casts = [
        'p_fp_id' => 'int',
        'p_react_id' => 'int',
        'p_site_id' => 'int',
        'p_context' => 'json',
    ];


    protected static function booted()
    {
        static::saving(static function (Permalink $permalink) {
            if ($permalink->p_type === static::TYPE_HISTORY && $permalink->p_redirect === null) {
                $permalink->p_redirect = static::TYPE_PRIMARY;
            }
        });
    }


    public function form_permalink(): BelongsTo
    {
        return $this->belongsTo(FormPermalink::class, 'p_fp_id');
    }

    public function setPUrlAttribute($value)
    {
        if (!Str::startsWith($value, ['https://', 'mailto:', '#'])) {
            $value = Str::start($value, '/');
        }
        $this->attributes['p_url'] = $value;
    }
}