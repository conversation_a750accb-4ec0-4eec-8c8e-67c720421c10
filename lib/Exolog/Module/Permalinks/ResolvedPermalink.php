<?php


namespace Exolog\Module\Permalinks;


use Exolog\Core\Forms\React;
use Exolog\Module\Container\BaseContainer;
use Exolog\Module\Forms\Model\FormPermalink;
use Exolog\Module\Permalinks\Model\Permalink;
use Exolog\Module\Support\Facades\Site;
use RuntimeException;

class ResolvedPermalink
{
    public React $react;
    public array $context;
    public int $form_permalink_id;
    public string $type;
    public string $url;
    public ?Permalink $permalink = null;

    public function __construct(string $type, React $react, int $form_permalink_id, array $context)
    {
        $this->react = $react;
        $this->context = $context;
        $this->form_permalink_id = $form_permalink_id;
        $this->type = $type;
    }

    public function generatePermalinkUrl(): void
    {
        $this->url = PermalinkUrlGenerator::make()->generate($this);
    }

    public function getFormPermalink(): FormPermalink
    {
        return FormPermalink::find($this->form_permalink_id);
    }

    public function sync(): void
    {
        //delete from history with this url
        Permalink::query()
            ->where('p_react_id', $this->react->id())
            ->whereIn('p_type', [Permalink::TYPE_HISTORY])
            ->where('p_url', $this->url)
            ->delete();

        if ($this->type === Permalink::TYPE_PRIMARY) {
            //delete alternate with this url
            Permalink::query()
                ->where('p_react_id', $this->react->id())
                ->whereIn('p_type', [Permalink::TYPE_ALTERNATE])
                ->where('p_url', $this->url)
                ->delete();

            //looking for current primary
            /** @var Permalink $permalink */
            $permalink = Permalink::query()
                ->where('p_react_id', $this->react->id())
                ->where('p_type', Permalink::TYPE_PRIMARY)
                ->first();

            //if exists update and push to history
            if ($permalink) {
                //compare with current
                if ($this->isChanged($permalink)) {
                    if ($permalink->p_url !== Permalink::HOME) {
                        //move to history
                        if ($this->getFormPermalink()->fp_history_active) {
                            Permalink::query()
                                ->where('p_react_id', $this->react->id())
                                ->where('p_type', Permalink::TYPE_HISTORY)
                                ->where('p_url', $permalink->p_url)
                                ->delete();
                            $permalink->p_type = Permalink::TYPE_HISTORY;
                            $permalink->save();
                        }
                    }
                    $this->permalink = Permalink::create($this->toArray());
                } else {
                    $permalink->fill($this->toArray());
                    $permalink->save();
                    $this->permalink = $permalink;
                }

            } else {
                //if primary permalink doesn't exist, create new
                $this->permalink = Permalink::create($this->toArray());
            }
        } elseif ($this->type === Permalink::TYPE_ALTERNATE) {

            //Skip this alternate permalink if primary permalink exists with this url
            $permalink = Permalink::query()
                ->where('p_react_id', $this->react->id())
                ->where('p_type', Permalink::TYPE_PRIMARY)
                ->where('p_url', $this->url)
                ->first();

            if ($permalink) {
                return;
            }

            //delete alterante with the same URL and other context
            Permalink::query()
                ->where('p_react_id', $this->react->id())
                ->where('p_type', Permalink::TYPE_ALTERNATE)
                ->where('p_url', $this->url)
                ->where('p_context->container', '<>', $this->context['container']->getContainerId())
                ->delete();

            // Check if this exact permalink already exists
            $this->permalink = Permalink::query()
                ->where('p_react_id', $this->react->id())
                ->where('p_type', Permalink::TYPE_ALTERNATE)
                ->where('p_context->container', $this->context['container']->getContainerId())
                ->first();

            // If it doesn't exist, check if creating it would violate unique constraint
            if (!$this->permalink) {
                $currentSiteId = Site::id();
                $existingPermalink = Permalink::query()
                    ->where('p_url', $this->url)
                    ->where('p_site_id', $currentSiteId)
                    ->first();

                if ($existingPermalink) {
                    // Skip creating alternate permalink if it would violate unique constraint
                    // Debug info for troubleshooting
                    dump("Skipping ALTERNATE permalink creation - constraint violation would occur:");
                    dump("URL: " . $this->url);
                    dump("Site ID: " . $currentSiteId);
                    dump("Existing permalink ID: " . $existingPermalink->p_id);
                    return;
                }

                // Safe to create new permalink
                $this->permalink = new Permalink();
            }

            $data = $this->toArray();

            try {
                $this->permalink->fill($data)->save();
            } catch (\Illuminate\Database\QueryException $e) {
                // Check if this is a duplicate entry constraint violation
                if ($e->getCode() === '23000' && strpos($e->getMessage(), 'permalinks_p_url_p_site_id_unique') !== false) {
                    // Skip this permalink creation due to duplicate constraint
                    return;
                }
                // Re-throw if it's a different error
                throw $e;
            }
        } else {
            throw new RuntimeException('Not supported a permalink type!');
        }
    }

    private function toArray(): array
    {
        $context = collect($this->context)->map(function ($item) {
            if ($item instanceof BaseContainer) {
                return $item->getContainerId();
            }
            return $item;
        })->toArray();

        return [
            'p_type' => $this->type,
            'p_react_id' => $this->react->id(),
            'p_url' => $this->url,
            'p_fp_id' => $this->form_permalink_id,
            'p_context' => $context
        ];
    }

    private function isChanged(Permalink $permalink): bool
    {
        $current = $this->toArray();
        return $permalink->p_url !== $current['p_url'];
        //|| $permalink->p_fp_id !== $current['p_fp_id']
        //|| !array_is_equal($permalink->p_context, $current['p_context']);
    }

}