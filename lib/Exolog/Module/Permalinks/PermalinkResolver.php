<?php


namespace Exolog\Module\Permalinks;


use Exolog\Core\Forms\React;
use Exolog\Module\Permalinks\Model\Permalink;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use RuntimeException;

class PermalinkResolver
{

    private React $react;

    public function __construct(React $react)
    {
        $this->react = $react;
    }

    /**
     * Collect available permalink template (form_permalink) for React
     *
     * @return Collection<ResolvedPermalink>
     */
    public function resolve(): Collection
    {
        $links = collect();
        $is_primary_found = false;

        //1. Primary permalinks from react level.
        //Get form_permalink(template) from react or form as default
        if ($this->react['react_form_permalink_id']) {
            $context = [];
            $links->push(new ResolvedPermalink(
                    Permalink::TYPE_PRIMARY,
                    $this->react,
                    $this->react['react_form_permalink_id'],
                    $context)
            );
            $is_primary_found = true;
        }

        //2. Parent container level.
        //Get parent container with this react's form in config and available form_permalink
        $count_primary_container = 0;
        foreach ($this->react->parentContainers() as $container) {
            $config = Arr::first($container->getConfig()['forms'], function ($value) {
                return (int)$value['form_id'] === $this->react->getReactFormId();
            });
            if (!empty($config['form_permalink_id'])) {
                $context['container'] = $container;

                if ($config['use_as_primary']) {
                    $count_primary_container++;
                    if ($count_primary_container > 1) {
                        throw new RuntimeException("Can't resolve primary permalink! React (id={$this->react->id()}) belongs more than one container with primary form_permalink in form settings.");
                    }
                    if (!$is_primary_found) {
                        $links->push(new ResolvedPermalink(
                                Permalink::TYPE_PRIMARY,
                                $this->react,
                                $config['form_permalink_id'],
                                $context)
                        );
                        $is_primary_found = true;
                    }

                } else {
                    $links->push(new ResolvedPermalink(
                            Permalink::TYPE_ALTERNATE,
                            $this->react,
                            $config['form_permalink_id'],
                            $context)
                    );
                }
            }
        }
        //3d level. Get primary permalink from as default
        if (!$is_primary_found && $this->react->getReactForm()->form_permalink_id) {
            $links->push(new ResolvedPermalink(
                    Permalink::TYPE_PRIMARY,
                    $this->react,
                    $this->react->getReactForm()->form_permalink_id,
                    [])
            );
        }

        return $links;
    }
}