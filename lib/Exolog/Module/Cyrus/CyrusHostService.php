<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 23.04.2020
 * Time: 17:25
 */

namespace Exolog\Module\Cyrus;


use Exception;
use Illuminate\Database\ConnectionInterface;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;

class CyrusHostService
{

    private ConnectionInterface $mailDB;
    private $prefix;

    /**
     * constructor.
     * @param $prefix
     * @throws Exception
     */
    public function __construct($prefix)
    {
        if (empty($prefix)) {
            throw new InvalidArgumentException('Prefix is empty!');
        }
        $this->prefix = $prefix;
        $this->mailDB = DB::connection('mail');
    }

    /**
     * @param $mailhost
     * @throws Exception
     */
    public function deleteHost($mailhost)
    {
        $cyrusService = new CyrusService();
        $cyrusService->deleteBoxesByHost($mailhost);

        $this->mailDB->table('domain')
            ->where('prefix', $this->prefix)
            ->where('domain_name', $mailhost)
            ->delete();
    }

    public function softDeleteHost($mailhost)
    {
        $cyrusService = new CyrusService();
        $cyrusService->softDeleteBoxesByHost($mailhost);

    }

    public function softUndeleteHost($mailhost)
    {
        $cyrusService = new CyrusService();
        $cyrusService->softUndeleteBoxesByHost($mailhost);

    }

    public function saveHost($mailhost, $maxaccounts, $quota)
    {
        if ($this->isHostExists($mailhost)) {
            $this->mailDB->table('domain')
                ->where('domain_name', $mailhost)
                ->update([
                    'maxaccounts' => $maxaccounts,
                    'quota' => $quota,
                    'status' => 1,
                    'prefix' => $this->prefix
                ]);
        } else {
            $this->mailDB->table('domain')->insert([
                'domain_name' => $mailhost,
                'prefix' => $this->prefix,
                'maxaccounts' => $maxaccounts,
                'quota' => $quota,
                'status' => 1
            ]);
        }
    }

    private function isHostExists($mailhost)
    {
        return $this->mailDB->table('domain')
            ->where('domain_name', $mailhost)
            ->count();

    }
}