<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 23.04.2020
 * Time: 15:49
 */

namespace Exolog\Module\Cyrus;

use Exolog\Module\Database\Legacy\Database;

class MailDB
{
    /**
     * @var Database
     */
    static private $instance;

    /**
     * MailDB constructor.
     */
    public function __construct()
    {

    }

    public static function getInstance()
    {
        if (self::$instance == null) {
            self::$instance = new Database([
                'pdo' => $resolver->connection()->getPdo()
            ]);
        }

        return self::$instance;
    }

}
