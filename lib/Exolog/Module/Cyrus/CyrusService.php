<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 23.04.2020
 * Time: 15:49
 */

namespace Exolog\Module\Cyrus;

use Exception;
use Illuminate\Database\ConnectionInterface;
use Illuminate\Support\Facades\DB;
use RuntimeException;

class CyrusService
{
    private $mbox;
    private $host;
    private $admin_user;
    public string $cyrus_prefix = 'user/';
    public bool $connectionStatus = false;
    // init list folders that we need to create in new box
    private array $create_folders = [
        'Drafts',
        'Sent',
        'Trash',
        'Spam'
    ];

    private ConnectionInterface $mailDB;

    /**
     * @throws Exception
     */
    public function __construct()
    {
        $CYRUS_HOST = config('services.cyrus.host');
        $CYRUS_PORT = config('services.cyrus.port');
        $CYRUS_USERNAME = config('services.cyrus.username');
        $CYRUS_PASSWORD = config('services.cyrus.password');

        $this->mbox = @imap_open("{" . $CYRUS_HOST . ":" . $CYRUS_PORT . "/imap/notls}", $CYRUS_USERNAME,
            $CYRUS_PASSWORD, OP_HALFOPEN);

        if ($this->mbox === false) {
            throw new RuntimeException(imap_last_error());
        }
        // check for connection
        if (!empty($this->mbox)) {
            $this->mailDB = DB::connection('mail');

            $this->host = $CYRUS_HOST;
            $this->admin_user = $CYRUS_USERNAME;
            $this->connectionStatus = true;
        }
    }

    function is_exist_mbox($box_name)
    {
        $list = imap_getmailboxes($this->mbox, '{' . $this->host . '}', $this->cyrus_prefix . $box_name . '');
        return is_array($list) ? true : false;
    }

    function is_exist_alias($alias)
    {
        return $this->mailDB->table('virtual')
            ->where('alias', $alias)
            ->count();
    }

    function list_mailboxes($mask)
    {
        $list = imap_list($this->mbox, '{' . $this->host . '}', $mask);
        $mailboxes = [];
        if (is_array($list)) {
            foreach ($list as $key => $val) {
                $mailboxes[] = imap_utf7_decode($val);
            }
            return $mailboxes;
        }
    }

    function create_mailbox($boxname, $alias, $pass, $desc)
    {

        list($userName, $mailDomain) = explode("@", $alias);
        if (imap_createmailbox($this->mbox,
            imap_utf7_encode('{' . $this->host . '}' . $this->cyrus_prefix . $boxname))) {
            imap_setacl($this->mbox, $this->cyrus_prefix . $boxname, $this->admin_user,
                "lrswipcda") or die('Could not setacl on mailbox');
            imap_setacl($this->mbox, $this->cyrus_prefix . $boxname, $boxname,
                "lrswipcda") or die('Could not setacl on mailbox');

            $prefix = $this->mailDB->table('domain')
                ->select('prefix')
                ->where('domain_name', 'LIKE', $mailDomain)
                ->value('prefix');

            $this->mailDB->table('accountuser')->insert([
                'username' => $boxname,
                'password' => $pass,
                'prefix' => $prefix,
                'domain_name' => $mailDomain,
                'status' => 1
            ]);

            $this->set_dest($boxname, $desc);
            $this->create_standart_subfolders($boxname);
        }
    }

    function create_standart_subfolders($boxname)
    {
        // creation sub folders in mailbox
        foreach ($this->create_folders as $subfolder) {
            $this->create_subfolder($boxname, $subfolder);
        }
    }

    function get_user_pass($boxname)
    {
        return $this->mailDB->table('accountuser')
            ->select('password')
            ->where('username', $boxname)
            ->limit(1)
            ->value('password');
    }

    // Create sub folder in $user root folder
    // Used for Spam box creation etc
    function create_subfolder($boxname, $subfolder)
    {
        list($userName, $mailDomain) = explode("@", $boxname);
        imap_createmailbox($this->mbox,
            imap_utf7_encode('{' . $this->host . '}' . $this->cyrus_prefix . $userName . '/' . $subfolder . '@' . $mailDomain));
        imap_subscribe($this->mbox,
            imap_utf7_encode('{' . $this->host . '}' . $this->cyrus_prefix . $userName . '/' . $subfolder . '@' . $mailDomain));
    }


    function get_subfolders($boxname)
    {
        list($userName, $mailDomain) = explode("@", $boxname);
        $result = [];
        $folders = imap_getmailboxes($this->mbox, imap_utf7_encode('{' . $this->host . '}'),
            sprintf('user/%s/*@%s', $userName, $mailDomain));
        if (!empty($folders)) {
            foreach ($folders as $folder) {
                if (preg_match('#/([^\/@]+)@#', $folder->name, $pok)) {
                    $result[] = $pok[1];
                }
            }
        }
        return $result;
    }

    /**
     * rename complete mailbox.
     * @param $currentbox
     * @param $newbox
     */
    function rename_mailbox($currentbox, $newbox)
    {
        imap_renamemailbox($this->mbox,
            imap_utf7_encode('{' . $this->host . '}' . $this->cyrus_prefix . $currentbox),
            imap_utf7_encode('{' . $this->host . '}' . $this->cyrus_prefix . $newbox));
    }

    function create_alias($boxname, $desc)
    {
        // fix for non array param
        if (!is_array($desc)) {
            $desc = (array)$desc;
        }

        $this->mailDB->table('virtual')->insert([
            'alias' => $boxname,
            'dest' => implode(' ', $desc),
            'username' => '',
            'status' => 1,
            'filter' => 'OK'
        ]);
    }

    function delete_alias($boxname)
    {
        $this->mailDB->table('virtual')
            ->where('alias', $boxname)
            ->delete();
    }

    /**
     * Delete imap box + mysql data + clean al forwards to deleted addres on current mail server
     * @param string $boxname
     */
    function delete_mailbox($boxname)
    {
        $this->mailDB->table('accountuser')
            ->where('username', $boxname)
            ->delete();

        $this->mailDB->table('virtual')
            ->where('alias', $boxname)
            ->delete();

        $this->mailDB->table('virtual')
            ->whereRaw("SUBSTRING_INDEX('$boxname', '@', -1) = SUBSTRING_INDEX(alias, '@', -1)")
            ->update([
                'dest' => DB::raw("TRIM(REPLACE(REPLACE(dest, '$boxname', ''), '  ', ' '))")
            ]);

        imap_deletemailbox($this->mbox, '{' . $this->host . '}' . $this->cyrus_prefix . $boxname);
    }

    /**
     * Soft delete mailbox is like delete_mailbox above but without actually deleting anything.
     * This will set mysql data to status 0 and leave the mailbox on IMAP untouched.
     * @param $boxname
     */
    function soft_delete_mailbox($boxname)
    {
        $this->mailDB->table('accountuser')
            ->where('username', $boxname)
            ->update([
                'password' => DB::raw("concat(password, '--deleted')"),
                'status' => 0
            ]);

        $this->mailDB->table('virtual')
            ->where('alias', $boxname)
            ->update(['status' => 0]);
    }

    function soft_undelete_mailbox($boxname)
    {
        $this->mailDB->table('accountuser')
            ->where('username', $boxname)
            ->update([
                'password' => DB::raw("replace(password, '--deleted', '')"),
                'status' => 1
            ]);

        $this->mailDB->table('virtual')
            ->where('alias', $boxname)
            ->update(['status' => 1]);
    }

    function delete_mailbox_from_trash($boxname)
    {
        $this->mailDB->table('accountuser')->where('username', $boxname)->delete();
        $this->mailDB->table('virtual')->where('alias', $boxname)->delete();
        $this->mailDB->table('virtual')
            ->update([
                'dest' => DB::raw("TRIM(REPLACE(REPLACE(dest, '$boxname', ''), '  ', ' '))")
            ]);

        imap_deletemailbox($this->mbox, '{' . $this->host . '}' . $this->cyrus_prefix . $boxname);
    }

    function set_pass($boxname, $pass)
    {
        $this->mailDB->table('accountuser')
            ->where('username', $boxname)
            ->update(['password' => $pass]);
    }

    function set_dest($boxname, $desc)
    {
        foreach ($desc as $key => $item) {
            if ($item === 'maillisting') {
                $desc[$key] = $this->mailListingDestName();
            }
        }

        $count = $this->mailDB->table('virtual')
            ->where('alias', $boxname)
            ->count();
        if ($count) {
            $this->mailDB->table('virtual')
                ->where('alias', $boxname)
                ->update(['dest' => trim(implode(' ', $desc))]);
        } else {
            $this->mailDB->table('virtual')
                ->insert([
                    'alias' => $boxname,
                    'dest' => trim(implode(' ', $desc)),
                    'username' => $boxname,
                    'status' => 1,
                    'filter' => 'OK',
                ]);
        }
    }

    function set_quota($boxname, $quotaval)
    {
        if ($setquota = @imap_set_quota($this->mbox, $this->cyrus_prefix . $boxname, $quotaval)) {
            return true;
        }
    }

    function get_quota($boxname)
    {
        if ($usrquota = @imap_get_quota($this->mbox, $this->cyrus_prefix . $boxname . '')) {
            return $usrquota;
        }
    }

    private function get_dest_array($boxname)
    {
        $result = [];

        $res = $this->mailDB->table('virtual')
            ->where('alias', $boxname)
            ->value('dest');
        if ($res) {
            foreach (explode(' ', $res) as $value) {
                $result[] = $value;
            }
        }
        return $result;
    }

    function is_maillisting_dest($boxname)
    {
        return in_array($this->mailListingDestName(), $this->get_dest_array($boxname));
    }

    function get_dest($boxname, $filter = true)
    {
        $mailListing = $this->mailListingDestName();
        $array_dest = [];

        foreach ($this->get_dest_array($boxname) as $value) {
            if (!in_array($value, [$boxname, $mailListing]) || !$filter) {
                $array_dest[] = ['name' => $value];
            }
        }
        return $array_dest;
    }

    function get_all_aliases_on_site($email)
    {
        $rows_alias = [];
        list($user_name, $domain) = explode('@', $email);

        $res = $this->mailDB->table('virtual')
            ->select('alias')
            ->where('alias', 'like', '%@' . $domain)
            ->where('alias', '!=', $email)
            ->get();

        foreach ($res as $row) {
            $rows_alias[] = ['name' => $row['alias']];
        }
        return $rows_alias;
    }

    private function getMailboxesByHost($mailhost): array
    {
        return $this->mailDB->table('accountuser')
            ->select('username')
            ->where('domain_name', $mailhost)
            ->get()
            ->toArray();
    }

    public function deleteBoxesByHost($mailhost): void
    {
        $boxes = $this->getMailboxesByHost($mailhost);
        foreach ($boxes as $box) {
            $this->delete_mailbox_from_trash($box['username']);
        }
        CyrusAdmin::reloadSystemService();
    }

    public function softDeleteBoxesByHost($mailhost): void
    {
        $boxes = $this->getMailboxesByHost($mailhost);

        foreach ($boxes as $box) {
            $this->soft_delete_mailbox($box['username']);
        }
        CyrusAdmin::reloadSystemService();
    }

    public function softUndeleteBoxesByHost($mailhost): void
    {
        $boxes = $this->getMailboxesByHost($mailhost);

        foreach ($boxes as $box) {
            $this->soft_undelete_mailbox($box['username']);
        }
        CyrusAdmin::reloadSystemService();
    }

    private function mailListingDestName(): string
    {
        return 'maillisting' . config('exolog.server_name');
    }
}
