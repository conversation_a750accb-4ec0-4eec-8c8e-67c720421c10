<?php

namespace Exolog\Module\Cyrus\Commands;

use Exception;
use Exolog\Module\Console\SystemCommand;
use Exolog\Module\Support\Facades\Site;
use Exolog\Module\Users\UserMailboxService;
use Illuminate\Support\Facades\Artisan;

class MailboxInfoUpdateCommand extends SystemCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'exolog:mailbox-info-update {--site_id=}';


    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update user mailbox information (statistics)';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     * @throws Exception
     */
    public function handle()
    {
        if ($this->option('site_id')) {
            $this->updateOneSite();
        } else {
            $this->updateAllSite();
        }
        return 0;
    }

    private function updateOneSite(): void
    {
        $this->resolveSite();

        $this->info('Start update user mailboxes information for site: "' . Site::alias() . '"');
        $count = UserMailboxService::make()->updateUsersMailboxInfo();
        $this->info("Updated $count mailboxes");
    }


    private function updateAllSite(): void
    {
        $sites = \Exolog\Module\Site\Model\Site::all()->pluck('site_id');

        $sites->each(function ($site_id) {
            $this->info('Send the job to queue for site:' . $site_id);
            Artisan::queue('exolog:mailbox-info-update', ['--site_id' => $site_id]);
        });

    }
}
