<?php

namespace Exolog\Module\Mail;

use Exception;
use Exolog\Module\DNS\DnsProviderFactory;
use Exolog\Module\DNS\DNSProvider;
use Exolog\Module\Support\Concerns\HasMakeFactory;
use Illuminate\Support\Facades\Log;
use RuntimeException;

class PosteDnsManager
{
    use HasMakeFactory;

    private PosteApiService $posteService;

    public function __construct()
    {
        $this->posteService = new PosteApiService();
    }

    /**
     * Add all mail-related DNS records for a domain
     *
     * @param string $domainName
     * @return bool
     * @throws Exception
     */
    public function addMailDnsRecords(string $domainName): bool
    {
        try {
            Log::info('Adding mail DNS records for domain', ['domain' => $domainName]);

            $dnsProvider = DnsProviderFactory::make($domainName);
            if (!$dnsProvider) {
                throw new RuntimeException("No DNS provider found for domain: $domainName");
            }

            $errors = [];

            // Add A record for mail subdomain
            try {
                $this->addMailSubdomainRecord($dnsProvider, $domainName);
            } catch (Exception $e) {
                $errors[] = "Mail subdomain A record: " . $e->getMessage();
                Log::warning('Failed to add mail subdomain A record, continuing with other records', [
                    'domain' => $domainName,
                    'error' => $e->getMessage()
                ]);
            }

            // Add MX record
            try {
                $this->addMxRecord($dnsProvider, $domainName);
            } catch (Exception $e) {
                $errors[] = "MX record: " . $e->getMessage();
                Log::warning('Failed to add MX record, continuing with other records', [
                    'domain' => $domainName,
                    'error' => $e->getMessage()
                ]);
            }

            // Add SPF record
            try {
                $this->addSpfRecord($dnsProvider, $domainName);
            } catch (Exception $e) {
                $errors[] = "SPF record: " . $e->getMessage();
                Log::warning('Failed to add SPF record, continuing with other records', [
                    'domain' => $domainName,
                    'error' => $e->getMessage()
                ]);
            }

            // Add DMARC record
            try {
                $this->addDmarcRecord($dnsProvider, $domainName);
            } catch (Exception $e) {
                $errors[] = "DMARC record: " . $e->getMessage();
                Log::warning('Failed to add DMARC record, continuing with other records', [
                    'domain' => $domainName,
                    'error' => $e->getMessage()
                ]);
            }

            // Add DKIM record
            try {
                $this->addDkimRecord($dnsProvider, $domainName);
            } catch (Exception $e) {
                $errors[] = "DKIM record: " . $e->getMessage();
                Log::warning('Failed to add DKIM record, continuing with other records', [
                    'domain' => $domainName,
                    'error' => $e->getMessage()
                ]);
            }

            // Log summary
            if (!empty($errors)) {
                Log::warning('Some DNS records failed to create', [
                    'domain' => $domainName,
                    'errors' => $errors,
                    'total_errors' => count($errors)
                ]);
            }

            Log::info('All mail DNS records added successfully', ['domain' => $domainName]);
            return true;

        } catch (Exception $e) {
            Log::error('Failed to add mail DNS records', [
                'domain' => $domainName,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Remove all mail-related DNS records for a domain
     *
     * @param string $domainName
     * @return bool
     * @throws Exception
     */
    public function removeMailDnsRecords(string $domainName): bool
    {
        try {
            Log::info('Removing mail DNS records for domain', ['domain' => $domainName]);

            $dnsProvider = DnsProviderFactory::make($domainName);
            if (!$dnsProvider) {
                throw new RuntimeException("No DNS provider found for domain: $domainName");
            }

            // Remove mail subdomain A record
            $this->removeMailSubdomainRecord($dnsProvider, $domainName);

            // Remove MX record
            $this->removeMxRecord($dnsProvider, $domainName);

            // Remove SPF record
            $this->removeSpfRecord($dnsProvider, $domainName);

            // Remove DMARC record
            $this->removeDmarcRecord($dnsProvider, $domainName);

            // Remove DKIM record
            $this->removeDkimRecord($dnsProvider, $domainName);

            Log::info('All mail DNS records removed successfully', ['domain' => $domainName]);
            return true;

        } catch (Exception $e) {
            Log::error('Failed to remove mail DNS records', [
                'domain' => $domainName,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Add A record for mail subdomain (mail.domain.tld)
     *
     * @param DNSProvider $dnsProvider
     * @param string $domainName
     * @throws Exception
     */
    private function addMailSubdomainRecord(DNSProvider $dnsProvider, string $domainName): void
    {
        try {
            // Try to resolve IP from existing Cloudflare A records first
            $resolvedIp = $this->resolveIpFromCloudflare($dnsProvider, $domainName);
            $ipSource = 'cloudflare_resolved';

            // Fallback to config if Cloudflare resolution fails
            if (empty($resolvedIp)) {
                $resolvedIp = config('mail.server_ip');
                $ipSource = 'config';
                Log::info('Using fallback IP from config', [
                    'domain' => $domainName,
                    'fallback_ip' => $resolvedIp
                ]);
            }

            $mailServerIp = $resolvedIp;

            if (empty($mailServerIp)) {
                Log::error('No mail server IP available', [
                    'domain' => $domainName,
                    'cloudflare_resolved' => false,
                    'config_value' => config('mail.server_ip'),
                    'suggestion' => 'Add MAIL_SERVER_IP=************** to .env file or ensure domain has A records in Cloudflare'
                ]);
                throw new RuntimeException('No mail server IP available. Please add MAIL_SERVER_IP to .env file or ensure domain has A records in Cloudflare');
            }

            $record = [
                'name' => "mail.$domainName",
                'ttl' => '900',
                'type' => 'A',
                'value' => $mailServerIp
            ];

            $dnsProvider->updateRecord($record);
            Log::info('Mail subdomain A record added', [
                'domain' => $domainName,
                'subdomain' => "mail.$domainName",
                'ip' => $mailServerIp,
                'ip_source' => $ipSource
            ]);

        } catch (Exception $e) {
            Log::error('Failed to add mail subdomain A record', [
                'domain' => $domainName,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Add MX record for the domain
     *
     * @param DNSProvider $dnsProvider
     * @param string $domainName
     * @throws Exception
     */
    private function addMxRecord(DNSProvider $dnsProvider, string $domainName): void
    {
        try {
            $record = [
                'name' => $domainName,
                'ttl' => '900',
                'type' => 'MX',
                'priority' => 10,
                'value' => config('services.poste.mail_domain')
            ];

            $dnsProvider->updateRecord($record);
            Log::info('MX record added', ['domain' => $domainName, 'record' => $record]);

        } catch (Exception $e) {
            Log::error('Failed to add MX record', [
                'domain' => $domainName,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Add SPF record for the domain
     *
     * @param DNSProvider $dnsProvider
     * @param string $domainName
     * @throws Exception
     */
    private function addSpfRecord(DNSProvider $dnsProvider, string $domainName): void
    {
        try {
            $record = [
                'name' => $domainName,
                'ttl' => '900',
                'type' => 'TXT',
                'value' => '"v=spf1 a mx -all"'
            ];

            $dnsProvider->updateRecord($record);
            Log::info('SPF record added', ['domain' => $domainName, 'record' => $record]);

        } catch (Exception $e) {
            Log::error('Failed to add SPF record', [
                'domain' => $domainName,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Add DMARC record for the domain
     *
     * @param DNSProvider $dnsProvider
     * @param string $domainName
     * @throws Exception
     */
    private function addDmarcRecord(DNSProvider $dnsProvider, string $domainName): void
    {
        try {
            $record = [
                'name' => "_dmarc.$domainName",
                'ttl' => '900',
                'type' => 'TXT',
                'value' => '"v=DMARC1; p=none"'
            ];

            $dnsProvider->updateRecord($record);
            Log::info('DMARC record added', ['domain' => $domainName, 'record' => $record]);

        } catch (Exception $e) {
            Log::error('Failed to add DMARC record', [
                'domain' => $domainName,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Add DKIM record for the domain
     *
     * @param DNSProvider $dnsProvider
     * @param string $domainName
     * @throws Exception
     */
    private function addDkimRecord(DNSProvider $dnsProvider, string $domainName): void
    {
        try {
            // First try to get existing DKIM information from Poste
            $dkimInfo = $this->posteService->getDkimRecord($domainName);

            // If no DKIM key exists, generate one
            if (empty($dkimInfo) || !isset($dkimInfo['public'])) {
                Log::info('No DKIM key found, generating new one', ['domain' => $domainName]);

                try {
                    $this->posteService->generateDkimKey($domainName);
                    // Get the newly generated DKIM information
                    $dkimInfo = $this->posteService->getDkimRecord($domainName);
                } catch (Exception $e) {
                    Log::error('Failed to generate DKIM key', [
                        'domain' => $domainName,
                        'error' => $e->getMessage()
                    ]);
                    return;
                }
            }

            if (empty($dkimInfo) || !isset($dkimInfo['selector']) || !isset($dkimInfo['public'])) {
                Log::warning('DKIM information still not available after generation', [
                    'domain' => $domainName,
                    'dkim_info' => $dkimInfo
                ]);
                return;
            }

            // Format DKIM public key into proper TXT record format
            $publicKey = $dkimInfo['public'];

            // Remove PEM headers and format for DNS TXT record
            $publicKey = str_replace(['-----BEGIN PUBLIC KEY-----', '-----END PUBLIC KEY-----'], '', $publicKey);
            $publicKey = preg_replace('/\s+/', '', $publicKey); // Remove all whitespace

            // Create proper DKIM TXT record format
            $dkimRecord = "v=DKIM1; k=rsa; p=" . $publicKey;

            // Quote the entire record for Cloudflare
            $dkimPublicKey = '"' . $dkimRecord . '"';

            $record = [
                'name' => "{$dkimInfo['selector']}._domainkey.$domainName",
                'ttl' => '900',
                'type' => 'TXT',
                'value' => $dkimPublicKey
            ];

            $dnsProvider->updateRecord($record);
            Log::info('DKIM record added', [
                'domain' => $domainName,
                'selector' => $dkimInfo['selector'],
                'record_name' => $record['name'],
                'record_format' => 'v=DKIM1; k=rsa; p=...',
                'public_key_length' => strlen($publicKey),
                'full_record_length' => strlen($record['value']),
                'is_properly_formatted' => str_contains($record['value'], 'v=DKIM1')
            ]);

        } catch (Exception $e) {
            Log::error('Failed to add DKIM record', [
                'domain' => $domainName,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Remove A record for mail subdomain (mail.domain.tld)
     *
     * @param DNSProvider $dnsProvider
     * @param string $domainName
     * @throws Exception
     */
    private function removeMailSubdomainRecord(DNSProvider $dnsProvider, string $domainName): void
    {
        try {
            $record = [
                'name' => "mail.$domainName",
                'type' => 'A'
            ];

            $dnsProvider->deleteRecord($record);
            Log::info('Mail subdomain A record removed', [
                'domain' => $domainName,
                'subdomain' => "mail.$domainName"
            ]);

        } catch (Exception $e) {
            Log::warning('Failed to remove mail subdomain A record (may not exist)', [
                'domain' => $domainName,
                'subdomain' => "mail.$domainName",
                'error' => $e->getMessage()
            ]);
            // Don't throw - this is not critical for mail disabling
        }
    }

    /**
     * Remove MX record for the domain
     *
     * @param DNSProvider $dnsProvider
     * @param string $domainName
     * @throws Exception
     */
    private function removeMxRecord(DNSProvider $dnsProvider, string $domainName): void
    {
        try {
            $record = [
                'name' => $domainName,
                'type' => 'MX'
            ];

            $dnsProvider->deleteRecord($record);
            Log::info('MX record removed', ['domain' => $domainName]);

        } catch (Exception $e) {
            Log::warning('Failed to remove MX record (may not exist)', [
                'domain' => $domainName,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Remove SPF record for the domain
     *
     * @param DNSProvider $dnsProvider
     * @param string $domainName
     * @throws Exception
     */
    private function removeSpfRecord(DNSProvider $dnsProvider, string $domainName): void
    {
        try {
            $record = [
                'name' => $domainName,
                'type' => 'TXT',
                'value' => 'v=spf1 a mx -all'
            ];

            $dnsProvider->deleteRecord($record);
            Log::info('SPF record removed', ['domain' => $domainName]);

        } catch (Exception $e) {
            Log::warning('Failed to remove SPF record (may not exist)', [
                'domain' => $domainName,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Remove DMARC record for the domain
     *
     * @param DNSProvider $dnsProvider
     * @param string $domainName
     * @throws Exception
     */
    private function removeDmarcRecord(DNSProvider $dnsProvider, string $domainName): void
    {
        try {
            $record = [
                'name' => "_dmarc.$domainName",
                'type' => 'TXT'
            ];

            $dnsProvider->deleteRecord($record);
            Log::info('DMARC record removed', ['domain' => $domainName]);

        } catch (Exception $e) {
            Log::warning('Failed to remove DMARC record (may not exist)', [
                'domain' => $domainName,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Remove DKIM record for the domain
     *
     * @param DNSProvider $dnsProvider
     * @param string $domainName
     * @throws Exception
     */
    private function removeDkimRecord(DNSProvider $dnsProvider, string $domainName): void
    {
        try {
            // We need to get the DKIM selector to know which record to remove
            $dkimInfo = $this->posteService->getDkimRecord($domainName);

            if (empty($dkimInfo) || !isset($dkimInfo['selector'])) {
                Log::warning('Cannot remove DKIM record - selector not available', [
                    'domain' => $domainName
                ]);
                return;
            }

            $record = [
                'name' => "{$dkimInfo['selector']}._domainkey.$domainName",
                'type' => 'TXT'
            ];

            $dnsProvider->deleteRecord($record);
            Log::info('DKIM record removed', ['domain' => $domainName]);

        } catch (Exception $e) {
            Log::warning('Failed to remove DKIM record (may not exist)', [
                'domain' => $domainName,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Update DKIM record for a domain (useful when DKIM keys are rotated)
     *
     * @param string $domainName
     * @return bool
     * @throws Exception
     */
    public function updateDkimRecord(string $domainName): bool
    {
        try {
            Log::info('Updating DKIM record for domain', ['domain' => $domainName]);

            $dnsProvider = DnsProviderFactory::make($domainName);
            if (!$dnsProvider) {
                throw new RuntimeException("No DNS provider found for domain: $domainName");
            }

            // Remove old DKIM record and add new one
            $this->removeDkimRecord($dnsProvider, $domainName);
            $this->addDkimRecord($dnsProvider, $domainName);

            Log::info('DKIM record updated successfully', ['domain' => $domainName]);
            return true;

        } catch (Exception $e) {
            Log::error('Failed to update DKIM record', [
                'domain' => $domainName,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Resolve IP address from existing Cloudflare A records
     * Looks for A records pointing to the domain and uses that IP for mail subdomain
     *
     * @param DNSProvider $dnsProvider
     * @param string $domainName
     * @return string|null
     */
    private function resolveIpFromCloudflare(DNSProvider $dnsProvider, string $domainName): ?string
    {
        try {
            Log::info('Attempting to resolve IP from Cloudflare DNS records', [
                'domain' => $domainName
            ]);

            // Get all DNS records for the domain
            $records = $dnsProvider->getRecords();

            if (empty($records)) {
                Log::info('No DNS records found in Cloudflare', [
                    'domain' => $domainName
                ]);
                return null;
            }

            // Look for A records that point to the main domain or www subdomain
            $candidateIps = [];

            foreach ($records as $record) {
                if ($record['type'] === 'A') {
                    $recordName = $record['name'];
                    $recordValue = $record['value'] ?? $record['content'] ?? null;

                    // Check if this is the main domain or www subdomain
                    if ($recordName === $domainName || $recordName === "www.$domainName" || $recordName === "@") {
                        $candidateIps[] = [
                            'name' => $recordName,
                            'ip' => $recordValue,
                            'priority' => ($recordName === $domainName) ? 1 : 2 // Prefer main domain over www
                        ];

                        Log::info('Found candidate A record', [
                            'domain' => $domainName,
                            'record_name' => $recordName,
                            'ip' => $recordValue
                        ]);
                    }
                }
            }

            if (empty($candidateIps)) {
                Log::info('No suitable A records found for IP resolution', [
                    'domain' => $domainName,
                    'total_records' => count($records),
                    'suggestion' => 'Add A record for main domain or www subdomain'
                ]);
                return null;
            }

            // Sort by priority (main domain first, then www)
            usort($candidateIps, function($a, $b) {
                return $a['priority'] <=> $b['priority'];
            });

            $selectedIp = $candidateIps[0]['ip'];

            Log::info('Successfully resolved IP from Cloudflare', [
                'domain' => $domainName,
                'resolved_ip' => $selectedIp,
                'source_record' => $candidateIps[0]['name'],
                'total_candidates' => count($candidateIps)
            ]);

            return $selectedIp;

        } catch (Exception $e) {
            Log::warning('Failed to resolve IP from Cloudflare', [
                'domain' => $domainName,
                'error' => $e->getMessage(),
                'fallback' => 'Will use config IP instead'
            ]);
            return null;
        }
    }
}
