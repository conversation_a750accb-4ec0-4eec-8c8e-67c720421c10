<?php

namespace Exolog\Module\Mail;

use Exception;
use Exolog\Module\Support\Concerns\HasMakeFactory;
use Illuminate\Support\Facades\Log;
use RuntimeException;

class PosteApiService
{
    use HasMakeFactory;

    private PosteApiClient $client;

    public function __construct()
    {
        $this->client = new PosteApiClient(
            config('services.poste.url'),
            config('services.poste.user'),
            config('services.poste.password')
        );
    }

    /**
     * Add a domain to Poste
     *
     * @param string $domainName
     * @return array
     * @throws Exception
     */
    public function addDomain(string $domainName): array
    {
        try {
            Log::info('Adding domain to Poste', ['domain' => $domainName]);
            
            $response = $this->client->post('domains', [
                'name' => $domainName
            ]);
            
            Log::info('Domain added successfully', ['domain' => $domainName]);
            return $response;
            
        } catch (Exception $e) {
            Log::error('Failed to add domain to Poste', [
                'domain' => $domainName,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Remove a domain from Poste
     *
     * @param string $domainName
     * @return array
     * @throws Exception
     */
    public function removeDomain(string $domainName): array
    {
        try {
            Log::info('Removing domain from Poste', ['domain' => $domainName]);

            $response = $this->client->delete("domains/$domainName");

            Log::info('Domain removed successfully', ['domain' => $domainName]);
            return $response;

        } catch (Exception $e) {
            // If domain doesn't exist (404 or 400 with "not found"), treat as success
            if (strpos($e->getMessage(), 'was not found') !== false ||
                strpos($e->getMessage(), '404') !== false) {
                Log::info('Domain was already removed or does not exist', [
                    'domain' => $domainName,
                    'message' => $e->getMessage()
                ]);
                return [];
            }

            Log::error('Failed to remove domain from Poste', [
                'domain' => $domainName,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get domain information from Poste
     *
     * @param string $domainName
     * @return array
     * @throws Exception
     */
    public function getDomain(string $domainName): array
    {
        try {
            // Pass domain name in request body like POST method
            return $this->client->get("domains/$domainName", [], ['name' => $domainName]);
        } catch (Exception $e) {
            Log::error('Failed to get domain from Poste', [
                'domain' => $domainName,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * List all domains in Poste
     *
     * @return array
     * @throws Exception
     */
    public function listDomains(): array
    {
        try {
            return $this->client->get('domains');
        } catch (Exception $e) {
            Log::error('Failed to list domains from Poste', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Add a mailbox to Poste
     *
     * @param string $email
     * @param string $password
     * @param string $name
     * @param array $options Additional options like quota, redirectTo, etc.
     * @return array
     * @throws Exception
     */
    public function addMailbox(string $email, string $password, string $name, array $options = []): array
    {
        try {
            Log::info('Adding mailbox to Poste', ['email' => $email, 'name' => $name, 'options' => $options]);

            $data = array_merge([
                'email' => $email,
                'passwordPlaintext' => $password,
                'name' => $name
            ], $options);

            // Handle redirectTo field - ensure it's an array if provided
            if (isset($data['redirectTo']) && !is_array($data['redirectTo'])) {
                if (is_string($data['redirectTo']) && !empty($data['redirectTo'])) {
                    // Split comma-separated emails into array
                    $data['redirectTo'] = array_map('trim', explode(',', $data['redirectTo']));
                } else {
                    unset($data['redirectTo']);
                }
            }

            Log::info('📤 About to send POST request to Poste API', [
                'email' => $email,
                'endpoint' => 'boxes',
                'full_data' => $data,
                'has_redirectTo' => isset($data['redirectTo']),
                'redirectTo_value' => $data['redirectTo'] ?? null,
                'redirectTo_type' => isset($data['redirectTo']) ? gettype($data['redirectTo']) : 'not_set'
            ]);

            $response = $this->client->post('boxes', $data);

            Log::info('Mailbox added successfully', ['email' => $email, 'redirectTo' => $data['redirectTo'] ?? null]);
            return $response;

        } catch (Exception $e) {
            Log::error('Failed to add mailbox to Poste', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Remove a mailbox from Poste
     *
     * @param string $email
     * @return array
     * @throws Exception
     */
    public function removeMailbox(string $email): array
    {
        try {
            Log::info('Removing mailbox from Poste', ['email' => $email]);
            
            $response = $this->client->delete("boxes/$email");
            
            Log::info('Mailbox removed successfully', ['email' => $email]);
            return $response;
            
        } catch (Exception $e) {
            Log::error('Failed to remove mailbox from Poste', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Update mailbox password
     *
     * @param string $email
     * @param string $password
     * @return array
     * @throws Exception
     */
    public function updateMailboxPassword(string $email, string $password): array
    {
        try {
            Log::info('Updating mailbox password in Poste', ['email' => $email]);

            $response = $this->client->patch("boxes/$email", [
                'passwordPlaintext' => $password
            ]);

            Log::info('Mailbox password updated successfully', ['email' => $email]);
            return $response;

        } catch (Exception $e) {
            Log::error('Failed to update mailbox password in Poste', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Update mailbox redirect settings
     *
     * @param string $email
     * @param array $redirectTo Array of email addresses to redirect to
     * @return array
     * @throws Exception
     */
    public function updateMailboxRedirect(string $email, array $redirectTo = []): array
    {
        try {
            Log::info('🔄 Updating mailbox redirect using sieve API', [
                'email' => $email,
                'redirectTo' => $redirectTo,
                'redirectTo_count' => count($redirectTo)
            ]);

            // Generate sieve script for redirects
            $script = $this->generateSieveScript($redirectTo);

            $requestData = ['script' => $script];

            Log::info('📤 Sending PATCH request to Poste sieve API', [
                'email' => $email,
                'endpoint' => "boxes/$email/sieve",
                'script' => $script,
                'request_data' => $requestData
            ]);

            $response = $this->client->patch("boxes/$email/sieve", $requestData);

            Log::info('✅ Mailbox sieve script updated successfully in Poste API', [
                'email' => $email,
                'redirectTo' => $redirectTo,
                'script' => $script,
                'response' => $response
            ]);

            return $response;

        } catch (Exception $e) {
            Log::error('❌ Failed to update mailbox sieve script in Poste API', [
                'email' => $email,
                'redirectTo' => $redirectTo,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Get mailbox information
     *
     * @param string $email
     * @return array
     * @throws Exception
     */
    public function getMailbox(string $email): array
    {
        try {
            Log::info('📥 Getting mailbox from Poste API', ['email' => $email]);

            $response = $this->client->get("boxes/$email");

            Log::info('✅ Mailbox data retrieved from Poste API', [
                'email' => $email,
                'response' => $response,
                'has_redirectTo' => isset($response['redirectTo']),
                'redirectTo' => $response['redirectTo'] ?? null
            ]);

            return $response;
        } catch (Exception $e) {
            Log::error('❌ Failed to get mailbox from Poste API', [
                'email' => $email,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Get mailbox sieve script
     * @param string $email
     * @return array
     * @throws Exception
     */
    public function getMailboxSieve(string $email): array
    {
        try {
            Log::info('📥 Getting mailbox sieve script from Poste API', [
                'email' => $email,
                'endpoint' => "boxes/$email/sieve"
            ]);

            $response = $this->client->get("boxes/$email/sieve");

            Log::info('✅ Mailbox sieve script retrieved successfully', [
                'email' => $email,
                'response' => $response
            ]);

            return $response;

        } catch (Exception $e) {
            Log::error('❌ Failed to get mailbox sieve script from Poste API', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Generate sieve script for redirect destinations
     * @param array $redirectTo Array of email addresses
     * @return string
     */
    private function generateSieveScript(array $redirectTo): string
    {
        if (empty($redirectTo)) {
            return '';
        }

        $script = "# poste.io redirect filter\nif true\n{\n";

        foreach ($redirectTo as $email) {
            $email = trim($email);
            if (!empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $script .= "      redirect \"$email\";\n";
            }
        }

        $script .= "}\n";

        return $script;
    }

    /**
     * Parse sieve script to extract redirect email addresses
     * @param string $script
     * @return array
     */
    public function parseSieveScript(string $script): array
    {
        $redirectDestinations = [];

        // Extract email addresses from redirect "<EMAIL>"; lines
        if (preg_match_all('/redirect\s+"([^"]+)";/', $script, $matches)) {
            $redirectDestinations = array_filter($matches[1], function($dest) {
                return !empty(trim($dest)) && filter_var(trim($dest), FILTER_VALIDATE_EMAIL);
            });
        }

        return array_values($redirectDestinations);
    }

    /**
     * Generate DKIM key for domain
     * @param string $domain
     * @return array
     * @throws Exception
     */
    public function generateDkimKey(string $domain): array
    {
        try {
            Log::info('🔐 Generating DKIM key for domain', [
                'domain' => $domain,
                'endpoint' => "domains/$domain/dkim"
            ]);

            $response = $this->client->put("domains/$domain/dkim");

            Log::info('✅ DKIM key generated successfully', [
                'domain' => $domain,
                'response' => $response
            ]);

            return $response;

        } catch (Exception $e) {
            Log::error('❌ Failed to generate DKIM key', [
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get DKIM key for domain
     * @param string $domain
     * @return array
     * @throws Exception
     */
    public function getDkimKey(string $domain): array
    {
        try {
            Log::info('🔐 Getting DKIM key for domain', [
                'domain' => $domain,
                'endpoint' => "domains/$domain/dkim"
            ]);

            $response = $this->client->get("domains/$domain/dkim");

            Log::info('✅ DKIM key retrieved successfully', [
                'domain' => $domain,
                'has_public_key' => isset($response['public_key'])
            ]);

            return $response;

        } catch (Exception $e) {
            Log::error('❌ Failed to get DKIM key', [
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get mailbox quota information
     *
     * @param string $email
     * @return array
     * @throws Exception
     */
    public function getMailboxQuota(string $email): array
    {
        try {
            Log::info('Getting mailbox quota from Poste', ['email' => $email]);

            $response = $this->client->get("boxes/$email/quota");

            Log::info('Mailbox quota retrieved successfully', [
                'email' => $email,
                'storage_usage' => $response['storage_usage'] ?? 0,
                'storage_limit' => $response['storage_limit'] ?? 0
            ]);

            return $response;
        } catch (Exception $e) {
            Log::error('Failed to get mailbox quota from Poste', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * List all mailboxes for a domain
     *
     * @param string $domainName
     * @return array
     * @throws Exception
     */
    public function listMailboxes(string $domainName = ''): array
    {
        try {
            $endpoint = 'boxes';
            if (!empty($domainName)) {
                $endpoint .= "?domain=$domainName";
            }
            return $this->client->get($endpoint);
        } catch (Exception $e) {
            Log::error('Failed to list mailboxes from Poste', [
                'domain' => $domainName,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get DKIM record for a domain
     *
     * @param string $domainName
     * @return array
     * @throws Exception
     */
    public function getDkimRecord(string $domainName): array
    {
        try {
            Log::info('Getting DKIM record from Poste', ['domain' => $domainName]);
            
            $response = $this->client->get("domains/$domainName/dkim");
            
            Log::info('DKIM record retrieved successfully', ['domain' => $domainName]);
            return $response;
            
        } catch (Exception $e) {
            Log::error('Failed to get DKIM record from Poste', [
                'domain' => $domainName,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Check if a domain exists in Poste
     *
     * @param string $domainName
     * @return bool
     */
    public function domainExists(string $domainName): bool
    {
        try {
            $this->getDomain($domainName);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Check if a mailbox exists in Poste
     *
     * @param string $email
     * @return bool
     */
    public function mailboxExists(string $email): bool
    {
        try {
            $this->getMailbox($email);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get a specific alias
     *
     * @param string $email
     * @return array
     * @throws Exception
     */
    public function getAlias(string $email): array
    {
        try {
            Log::info('Getting alias from Poste', ['email' => $email]);
            return $this->client->get("aliases/$email");
        } catch (Exception $e) {
            Log::error('Failed to get alias from Poste', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get aliases for an email
     *
     * @param string $email
     * @return array
     * @throws Exception
     */
    public function getAliases(string $email): array
    {
        try {
            Log::info('Getting aliases from Poste', ['email' => $email]);

            // Get all aliases and filter by destination
            $response = $this->client->get("aliases");

            $aliases = [];
            if (isset($response['aliases'])) {
                foreach ($response['aliases'] as $alias) {
                    if (isset($alias['destination']) && $alias['destination'] === $email) {
                        $aliases[] = $alias;
                    }
                }
            }

            Log::info('Aliases retrieved successfully', [
                'email' => $email,
                'count' => count($aliases)
            ]);
            return $aliases;

        } catch (Exception $e) {
            Log::error('Failed to get aliases from Poste', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Check if alias exists
     *
     * @param string $email
     * @return bool
     * @throws Exception
     */
    public function aliasExists(string $email): bool
    {
        try {
            Log::info('Checking if alias exists in Poste', ['email' => $email]);

            $response = $this->client->get("aliases/$email");

            Log::info('Alias existence checked', ['email' => $email, 'exists' => true]);
            return true;

        } catch (Exception $e) {
            if (strpos($e->getMessage(), '404') !== false || strpos($e->getMessage(), 'not found') !== false) {
                Log::info('Alias does not exist', ['email' => $email]);
                return false;
            }

            Log::error('Failed to check alias existence in Poste', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
