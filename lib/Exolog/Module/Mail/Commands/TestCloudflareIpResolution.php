<?php

namespace Exolog\Module\Mail\Commands;

use Exception;
use Exolog\Module\DNS\DnsProviderFactory;
use Exolog\Module\Mail\PosteDnsManager;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use ReflectionClass;
use ReflectionMethod;

class TestCloudflareIpResolution extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:cloudflare-ip-resolution {domain : Domain to test IP resolution for}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test IP resolution from Cloudflare DNS records for mail subdomain creation';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $domain = $this->argument('domain');
        
        $this->info("Testing IP resolution for domain: $domain");
        $this->line('');

        try {
            // Create DNS provider
            $dnsProvider = DnsProviderFactory::create($domain);
            
            if (!$dnsProvider || !$dnsProvider->exists()) {
                $this->error("Domain '$domain' not found in DNS provider (Cloudflare)");
                return 1;
            }

            $this->info("✓ Domain found in Cloudflare");

            // Get all DNS records
            $records = $dnsProvider->getRecords();
            $this->info("✓ Found " . count($records) . " DNS records");
            
            // Show A records
            $aRecords = array_filter($records, function($record) {
                return $record['type'] === 'A';
            });
            
            $this->line('');
            $this->info('Current A Records:');
            foreach ($aRecords as $record) {
                $name = $record['name'];
                $ip = $record['value'] ?? $record['content'] ?? 'unknown';
                $this->line("  - $name → $ip");
            }

            // Test IP resolution using PosteDnsManager
            $this->line('');
            $this->info('Testing IP resolution...');
            
            $dnsManager = new PosteDnsManager();
            
            // Use reflection to access private method
            $reflection = new ReflectionClass($dnsManager);
            $method = $reflection->getMethod('resolveIpFromCloudflare');
            $method->setAccessible(true);
            
            $resolvedIp = $method->invoke($dnsManager, $dnsProvider, $domain);
            
            if ($resolvedIp) {
                $this->info("✓ Successfully resolved IP from Cloudflare: $resolvedIp");
            } else {
                $this->warn("⚠ Could not resolve IP from Cloudflare");
                $fallbackIp = config('mail.server_ip');
                $this->info("  Fallback IP from config: " . ($fallbackIp ?: 'NOT SET'));
            }

            // Show what would be created
            $this->line('');
            $this->info('Mail subdomain record that would be created:');
            $finalIp = $resolvedIp ?: config('mail.server_ip');
            if ($finalIp) {
                $this->line("  mail.$domain → $finalIp");
                $this->info("✓ Ready to create mail subdomain");
            } else {
                $this->error("✗ No IP available for mail subdomain creation");
                $this->line("  Please either:");
                $this->line("  1. Add A record for '$domain' or 'www.$domain' in Cloudflare");
                $this->line("  2. Set MAIL_SERVER_IP in .env file");
            }

        } catch (Exception $e) {
            $this->error("Error testing IP resolution: " . $e->getMessage());
            Log::error('TestCloudflareIpResolution error', [
                'domain' => $domain,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }

        return 0;
    }
}
