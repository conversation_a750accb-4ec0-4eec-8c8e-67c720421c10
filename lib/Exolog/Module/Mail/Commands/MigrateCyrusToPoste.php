<?php

namespace Exolog\Module\Mail\Commands;

use Exception;
use Exolog\Module\Cyrus\CyrusService;
use Exolog\Module\Domains\Model\Domain;
use Exolog\Module\Mail\PosteApiService;
use Exolog\Module\Mail\PosteDnsManager;
use Exolog\Module\Mail\PosteMailboxService;
use Exolog\Module\Users\Model\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MigrateCyrusToPoste extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mail:migrate-to-poste 
                            {--domain= : Migrate only specific domain}
                            {--dry-run : Show what would be migrated without actually doing it}
                            {--force : Skip confirmation prompts}
                            {--skip-dns : Skip DNS record creation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate existing mailboxes from Cyrus to Poste API';

    private PosteApiService $posteService;
    private PosteMailboxService $posteMailboxService;
    private PosteDnsManager $dnsManager;
    private CyrusService $cyrusService;
    private bool $dryRun = false;
    private bool $skipDns = false;

    /**
     * Create a new command instance.
     */
    public function __construct()
    {
        parent::__construct();
        $this->posteService = new PosteApiService();
        $this->posteMailboxService = new PosteMailboxService();
        $this->dnsManager = new PosteDnsManager();
        $this->cyrusService = new CyrusService();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $this->dryRun = $this->option('dry-run');
        $this->skipDns = $this->option('skip-dns');
        $force = $this->option('force');
        $specificDomain = $this->option('domain');

        // Check if Poste API is configured
        if (!config('services.poste.url')) {
            $this->error('Poste API is not configured. Please set POSTE_API_URL in your environment.');
            return 1;
        }

        $this->info('Cyrus to Poste Migration Tool');
        $this->line('================================');

        if ($this->dryRun) {
            $this->warn('DRY RUN MODE - No actual changes will be made');
        }

        if ($this->skipDns) {
            $this->warn('DNS records will be skipped');
        }

        // Get mail domains
        $domains = $this->getMailDomains($specificDomain);
        
        if ($domains->isEmpty()) {
            $this->error('No mail domains found to migrate');
            return 1;
        }

        $this->info("Found {$domains->count()} mail domain(s) to migrate:");
        foreach ($domains as $domain) {
            $this->line("  - {$domain->domain_name}");
        }

        // Show confirmation unless force flag is used
        if (!$force && !$this->dryRun) {
            if (!$this->confirm('Do you want to proceed with the migration?')) {
                $this->info('Migration cancelled');
                return 0;
            }
        }

        $totalMigrated = 0;
        $totalErrors = 0;

        foreach ($domains as $domain) {
            $result = $this->migrateDomain($domain);
            $totalMigrated += $result['migrated'];
            $totalErrors += $result['errors'];
        }

        $this->line('');
        $this->info('Migration Summary:');
        $this->info("  Domains processed: {$domains->count()}");
        $this->info("  Mailboxes migrated: $totalMigrated");
        
        if ($totalErrors > 0) {
            $this->warn("  Errors encountered: $totalErrors");
            $this->warn('  Check the logs for detailed error information');
        }

        if ($this->dryRun) {
            $this->warn('This was a dry run - no actual changes were made');
        }

        return $totalErrors > 0 ? 1 : 0;
    }

    /**
     * Get mail domains to migrate
     */
    private function getMailDomains(?string $specificDomain)
    {
        $query = Domain::where('domain_ismail', 1);
        
        if ($specificDomain) {
            $query->where('domain_name', $specificDomain);
        }
        
        return $query->get();
    }

    /**
     * Migrate a single domain
     */
    private function migrateDomain(Domain $domain): array
    {
        $this->line('');
        $this->info("Migrating domain: {$domain->domain_name}");
        
        $migrated = 0;
        $errors = 0;

        try {
            // Step 1: Create domain in Poste
            if (!$this->dryRun) {
                if (!$this->posteService->domainExists($domain->domain_name)) {
                    $this->posteService->addDomain($domain->domain_name);
                    $this->info("  ✓ Domain created in Poste");
                } else {
                    $this->info("  ✓ Domain already exists in Poste");
                }
            } else {
                $this->info("  [DRY RUN] Would create domain in Poste");
            }

            // Step 2: Add DNS records
            if (!$this->skipDns) {
                if (!$this->dryRun) {
                    $this->dnsManager->addMailDnsRecords($domain->domain_name);
                    $this->info("  ✓ DNS records added");
                } else {
                    $this->info("  [DRY RUN] Would add DNS records");
                }
            }

            // Step 3: Migrate mailboxes
            $mailboxes = $this->getCyrusMailboxes($domain->domain_name);
            
            if (empty($mailboxes)) {
                $this->info("  No mailboxes found for {$domain->domain_name}");
                return ['migrated' => $migrated, 'errors' => $errors];
            }

            $this->info("  Found " . count($mailboxes) . " mailbox(es) to migrate");

            foreach ($mailboxes as $mailbox) {
                try {
                    if ($this->migrateMailbox($mailbox, $domain->domain_name)) {
                        $migrated++;
                        $this->info("    ✓ Migrated: {$mailbox['username']}");
                    }
                } catch (Exception $e) {
                    $errors++;
                    $this->error("    ✗ Failed to migrate: {$mailbox['username']} - {$e->getMessage()}");
                    Log::error('Mailbox migration failed', [
                        'mailbox' => $mailbox['username'],
                        'domain' => $domain->domain_name,
                        'error' => $e->getMessage()
                    ]);
                }
            }

        } catch (Exception $e) {
            $errors++;
            $this->error("  ✗ Domain migration failed: {$e->getMessage()}");
            Log::error('Domain migration failed', [
                'domain' => $domain->domain_name,
                'error' => $e->getMessage()
            ]);
        }

        return ['migrated' => $migrated, 'errors' => $errors];
    }

    /**
     * Get mailboxes from Cyrus for a domain
     */
    private function getCyrusMailboxes(string $domain): array
    {
        try {
            // Get mailboxes from the mail database
            $mailboxes = DB::connection('mail')
                ->table('accountuser')
                ->where('domain_name', $domain)
                ->get()
                ->toArray();

            return array_map(function($mailbox) {
                return (array) $mailbox;
            }, $mailboxes);

        } catch (Exception $e) {
            Log::error('Failed to get Cyrus mailboxes', [
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Migrate a single mailbox
     */
    private function migrateMailbox(array $mailbox, string $domain): bool
    {
        if ($this->dryRun) {
            return true; // Simulate success in dry run
        }

        $email = $mailbox['username'];
        
        // Check if mailbox already exists in Poste
        if ($this->posteService->mailboxExists($email)) {
            Log::info('Mailbox already exists in Poste, skipping', ['email' => $email]);
            return true;
        }

        // Create mailbox in Poste
        // Note: We can't migrate the actual password, so we'll set a temporary one
        $tempPassword = 'TempPass' . rand(1000, 9999) . '!';
        
        $this->posteService->addMailbox($email, $tempPassword, $email);
        
        Log::info('Mailbox migrated to Poste', [
            'email' => $email,
            'domain' => $domain,
            'temp_password' => $tempPassword
        ]);

        return true;
    }
}
