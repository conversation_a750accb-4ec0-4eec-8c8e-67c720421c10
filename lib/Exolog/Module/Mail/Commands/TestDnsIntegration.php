<?php

namespace Exolog\Module\Mail\Commands;

use Exception;
use Exolog\Module\DNS\DnsProviderFactory;
use Exolog\Module\Mail\PosteDnsManager;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * Test command for DNS integration functionality
 */
class TestDnsIntegration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mail:test-dns {domain} {--dry-run : Show what would be done without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test DNS integration for mail domains';

    private PosteDnsManager $dnsManager;

    public function __construct()
    {
        parent::__construct();
        $this->dnsManager = new PosteDnsManager();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $domain = $this->argument('domain');
        $dryRun = $this->option('dry-run');

        $this->info("Testing DNS integration for domain: $domain");
        $this->line('');

        try {
            // Test DNS provider detection
            $this->info('1. Testing DNS provider detection...');
            $dnsProvider = DnsProviderFactory::make($domain);
            
            if (!$dnsProvider) {
                $this->error("✗ No DNS provider found for domain: $domain");
                $this->line('Make sure the domain is configured in Cloudflare or OpenProvider');
                return 1;
            }
            
            $providerClass = get_class($dnsProvider);
            $this->info("✓ DNS provider found: $providerClass");
            $this->line('');

            // Test configuration
            $this->info('2. Testing configuration...');
            $mailDomain = config('services.poste.mail_domain');
            if (!$mailDomain) {
                $this->error('✗ EXOLOG_MAIL_DOMAIN not configured');
                return 1;
            }
            $this->info("✓ Mail domain configured: $mailDomain");
            $this->line('');

            // Show what records would be created
            $this->info('3. DNS records that would be created:');
            $this->line("  MX:    $domain → $mailDomain (priority 10)");
            $this->line("  SPF:   $domain → \"v=spf1 a mx -all\"");
            $this->line("  DMARC: _dmarc.$domain → \"v=DMARC1; p=none\"");
            $this->line("  DKIM:  [selector]._domainkey.$domain → [public key from Poste]");
            $this->line('');

            if ($dryRun) {
                $this->info('✓ Dry run completed - no changes made');
                return 0;
            }

            // Test actual DNS record creation
            if ($this->confirm("Create DNS records for $domain?")) {
                $this->info('4. Creating DNS records...');
                
                $success = $this->dnsManager->addMailDnsRecords($domain);
                
                if ($success) {
                    $this->info("✓ DNS records created successfully for $domain");
                    $this->line('');
                    $this->line('Note: DNS propagation may take up to 24 hours');
                    
                    // Offer to remove records
                    if ($this->confirm("Remove the test DNS records?")) {
                        $this->info('5. Removing DNS records...');
                        $this->dnsManager->removeMailDnsRecords($domain);
                        $this->info("✓ DNS records removed for $domain");
                    }
                } else {
                    $this->error("✗ Failed to create DNS records for $domain");
                    return 1;
                }
            }

            return 0;

        } catch (Exception $e) {
            $this->error("Test failed: " . $e->getMessage());
            Log::error('DNS integration test failed', [
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);
            return 1;
        }
    }
}
