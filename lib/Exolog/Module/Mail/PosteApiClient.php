<?php

namespace Exolog\Module\Mail;

use Exception;
use <PERSON><PERSON><PERSON><PERSON>ttp\Client as GuzzleClient;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;
use RuntimeException;

class PosteApiClient
{
    private GuzzleClient $client;
    private string $baseUrl;
    private string $username;
    private string $password;

    public function __construct(string $baseUrl, string $username, string $password)
    {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->username = $username;
        $this->password = $password;
        
        $this->client = new GuzzleClient([
            'base_uri' => $this->baseUrl,
            'timeout' => 30,
            'verify' => false, // For development - should be true in production
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ]
        ]);
    }

    /**
     * Make a GET request to the Poste API
     *
     * @param string $endpoint
     * @param array $params Query parameters for URL
     * @param array $data Optional data to send in request body (non-standard but some APIs require it)
     * @return array
     * @throws Exception
     */
    public function get(string $endpoint, array $params = [], array $data = []): array
    {
        try {
            $url = $this->buildUrl($endpoint, $params);

            $requestOptions = [
                'auth' => [$this->username, $this->password]
            ];

            // If data is provided, send it in the request body (non-standard for GET)
            if (!empty($data)) {
                $requestOptions['json'] = $data;
                Log::info('Poste API GET request', ['url' => $url, 'data' => $data]);
            } else {
                Log::info('Poste API GET request', ['url' => $url]);
            }

            $response = $this->client->get($url, $requestOptions);

            $data = json_decode($response->getBody()->getContents(), true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new RuntimeException('Invalid JSON response from Poste API');
            }

            Log::info('Poste API GET response', ['status' => $response->getStatusCode()]);

            return $data;
            
        } catch (GuzzleException $e) {
            Log::error('Poste API GET error', [
                'endpoint' => $endpoint,
                'error' => $e->getMessage(),
                'code' => $e->getCode()
            ]);
            throw new RuntimeException("Poste API GET request failed: " . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * Make a POST request to the Poste API
     *
     * @param string $endpoint
     * @param array $data
     * @return array
     * @throws Exception
     */
    public function post(string $endpoint, array $data = []): array
    {
        try {
            $url = $this->buildUrl($endpoint);

            Log::info('Poste API POST request', ['url' => $url, 'data' => $data]);

            $response = $this->client->post($url, [
                'auth' => [$this->username, $this->password],
                'json' => $data
            ]);

            $responseBody = $response->getBody()->getContents();

            // Handle empty or non-JSON responses
            if (empty($responseBody)) {
                Log::info('Poste API POST response (empty body)', ['status' => $response->getStatusCode()]);
                return [];
            }

            $responseData = json_decode($responseBody, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::warning('Poste API returned non-JSON response', [
                    'status' => $response->getStatusCode(),
                    'body' => $responseBody,
                    'json_error' => json_last_error_msg()
                ]);
                // Return success if status code indicates success
                if ($response->getStatusCode() >= 200 && $response->getStatusCode() < 300) {
                    return ['success' => true, 'raw_response' => $responseBody];
                }
                throw new RuntimeException('Invalid JSON response from Poste API: ' . $responseBody);
            }

            Log::info('Poste API POST response', ['status' => $response->getStatusCode()]);

            return $responseData;

        } catch (GuzzleException $e) {
            Log::error('Poste API POST error', [
                'endpoint' => $endpoint,
                'data' => $data,
                'error' => $e->getMessage(),
                'code' => $e->getCode()
            ]);
            throw new RuntimeException("Poste API POST request failed: " . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * Make a PUT request to the Poste API
     *
     * @param string $endpoint
     * @param array $data
     * @return array
     * @throws Exception
     */
    public function put(string $endpoint, array $data = []): array
    {
        try {
            $url = $this->buildUrl($endpoint);

            Log::info('Poste API PUT request', ['url' => $url, 'data' => $data]);

            $response = $this->client->put($url, [
                'auth' => [$this->username, $this->password],
                'json' => $data
            ]);

            $responseBody = $response->getBody()->getContents();

            // Handle empty responses
            if (empty($responseBody)) {
                Log::info('Poste API PUT response (empty body)', ['status' => $response->getStatusCode()]);
                return [];
            }

            $responseData = json_decode($responseBody, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::warning('Poste API PUT returned non-JSON response', [
                    'status' => $response->getStatusCode(),
                    'body' => $responseBody,
                    'json_error' => json_last_error_msg()
                ]);
                // Return success if status code indicates success
                if ($response->getStatusCode() >= 200 && $response->getStatusCode() < 300) {
                    return ['success' => true, 'raw_response' => $responseBody];
                }
                throw new RuntimeException('Invalid JSON response from Poste API: ' . $responseBody);
            }

            Log::info('Poste API PUT response', ['status' => $response->getStatusCode()]);

            return $responseData;

        } catch (GuzzleException $e) {
            Log::error('Poste API PUT error', [
                'endpoint' => $endpoint,
                'data' => $data,
                'error' => $e->getMessage(),
                'code' => $e->getCode()
            ]);
            throw new RuntimeException("Poste API PUT request failed: " . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * Make a PATCH request to the Poste API
     *
     * @param string $endpoint
     * @param array $data
     * @return array
     * @throws Exception
     */
    public function patch(string $endpoint, array $data = []): array
    {
        try {
            $url = $this->buildUrl($endpoint);

            Log::info('Poste API PATCH request', ['url' => $url, 'data' => $data]);

            $response = $this->client->patch($url, [
                'auth' => [$this->username, $this->password],
                'json' => $data
            ]);

            $responseBody = $response->getBody()->getContents();

            // Handle empty responses
            if (empty($responseBody)) {
                Log::info('Poste API PATCH response (empty body)', ['status' => $response->getStatusCode()]);
                return [];
            }

            $decodedResponse = json_decode($responseBody, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::warning('Poste API PATCH response is not valid JSON', [
                    'response' => $responseBody,
                    'json_error' => json_last_error_msg()
                ]);
                return [];
            }

            Log::info('Poste API PATCH response', ['response' => $decodedResponse]);
            return $decodedResponse;

        } catch (Exception $e) {
            Log::error('Poste API PATCH request failed', [
                'url' => $url ?? $endpoint,
                'data' => $data,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Make a DELETE request to the Poste API
     *
     * @param string $endpoint
     * @return array
     * @throws Exception
     */
    public function delete(string $endpoint): array
    {
        try {
            $url = $this->buildUrl($endpoint);

            Log::info('Poste API DELETE request', ['url' => $url]);

            $response = $this->client->delete($url, [
                'auth' => [$this->username, $this->password]
            ]);

            $responseBody = $response->getBody()->getContents();

            // Handle empty responses (common for DELETE requests)
            if (empty($responseBody)) {
                Log::info('Poste API DELETE response (empty body)', ['status' => $response->getStatusCode()]);
                return [];
            }

            $responseData = json_decode($responseBody, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::warning('Poste API DELETE returned non-JSON response', [
                    'status' => $response->getStatusCode(),
                    'body' => $responseBody,
                    'json_error' => json_last_error_msg()
                ]);
                // Return success if status code indicates success
                if ($response->getStatusCode() >= 200 && $response->getStatusCode() < 300) {
                    return ['success' => true, 'raw_response' => $responseBody];
                }
                return [];
            }

            Log::info('Poste API DELETE response', ['status' => $response->getStatusCode()]);

            return $responseData;

        } catch (GuzzleException $e) {
            Log::error('Poste API DELETE error', [
                'endpoint' => $endpoint,
                'error' => $e->getMessage(),
                'code' => $e->getCode()
            ]);
            throw new RuntimeException("Poste API DELETE request failed: " . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * Build the full URL for the API endpoint
     *
     * @param string $endpoint
     * @param array $params
     * @return string
     */
    private function buildUrl(string $endpoint, array $params = []): string
    {
        $url = $this->baseUrl . '/' . ltrim($endpoint, '/');
        
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        
        return $url;
    }
}
