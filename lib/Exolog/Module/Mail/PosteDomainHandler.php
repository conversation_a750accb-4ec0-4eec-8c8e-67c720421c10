<?php

namespace Exolog\Module\Mail;

use Exception;
use Exolog\Dealer\Exception\DealerException;
use Exolog\Module\Support\Concerns\HasMakeFactory;
use Illuminate\Support\Facades\Log;

class PosteDomainHandler
{
    use HasMakeFactory;

    private int $site_id;
    private PosteApiService $posteService;
    private PosteDnsManager $dnsManager;

    /**
     * @throws DealerException
     */
    public function __construct($site_id)
    {
        if (empty($site_id)) {
            throw new DealerException('Site id is not defined!');
        }

        $this->site_id = $site_id;
        $this->posteService = new PosteApiService();
        $this->dnsManager = new PosteDnsManager();
    }

    /**
     * Delete a mail domain from Poste
     *
     * @param string $mailhost
     * @throws Exception
     */
    public function deleteHost(string $mailhost): void
    {
        try {
            Log::info('Deleting mail domain from Poste', [
                'domain' => $mailhost,
                'site_id' => $this->site_id
            ]);

            // Check if domain exists before trying to delete
            if (!$this->posteService->domainExists($mailhost)) {
                Log::info('Mail domain does not exist in Poste, skipping deletion', [
                    'domain' => $mailhost,
                    'site_id' => $this->site_id
                ]);
                return;
            }

            // First, get all mailboxes for this domain and delete them
            $mailboxes = $this->posteService->listMailboxes($mailhost);

            if (!empty($mailboxes)) {
                foreach ($mailboxes as $mailbox) {
                    if (isset($mailbox['email'])) {
                        $this->posteService->removeMailbox($mailbox['email']);
                        Log::info('Deleted mailbox during domain deletion', [
                            'email' => $mailbox['email'],
                            'domain' => $mailhost
                        ]);
                    }
                }
            }

            // Then delete the domain
            $this->posteService->removeDomain($mailhost);

            // Remove DNS records for the domain
            try {
                $this->dnsManager->removeMailDnsRecords($mailhost);
                Log::info('DNS records removed for mail domain', [
                    'domain' => $mailhost,
                    'site_id' => $this->site_id
                ]);

                // Execute shell script to remove Caddy reverse proxy for mail subdomain
                $this->executeMailServerRemovalScript($mailhost);

            } catch (Exception $dnsException) {
                Log::warning('Failed to remove DNS records for mail domain', [
                    'domain' => $mailhost,
                    'site_id' => $this->site_id,
                    'dns_error' => $dnsException->getMessage()
                ]);
                // Don't throw here - domain deletion succeeded, DNS is secondary
            }

            Log::info('Mail domain deleted successfully from Poste', [
                'domain' => $mailhost,
                'site_id' => $this->site_id
            ]);

        } catch (Exception $e) {
            Log::error('Failed to delete mail domain from Poste', [
                'domain' => $mailhost,
                'site_id' => $this->site_id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Soft delete a mail domain (disable mailboxes but keep domain)
     * Note: Poste doesn't have soft delete, so we'll disable all mailboxes
     *
     * @param string $mailhost
     * @throws Exception
     */
    public function softDeleteHost(string $mailhost): void
    {
        try {
            Log::info('Soft deleting mail domain in Poste', [
                'domain' => $mailhost,
                'site_id' => $this->site_id
            ]);

            // Get all mailboxes for this domain
            $mailboxes = $this->posteService->listMailboxes($mailhost);
            
            if (!empty($mailboxes)) {
                foreach ($mailboxes as $mailbox) {
                    if (isset($mailbox['email'])) {
                        // For soft delete, we could disable the mailbox or change password
                        // Since Poste doesn't have disable, we'll log this for manual handling
                        Log::warning('Soft delete requested for mailbox - manual intervention may be required', [
                            'email' => $mailbox['email'],
                            'domain' => $mailhost,
                            'action' => 'soft_delete'
                        ]);
                    }
                }
            }

            Log::info('Mail domain soft delete completed', [
                'domain' => $mailhost,
                'site_id' => $this->site_id
            ]);

        } catch (Exception $e) {
            Log::error('Failed to soft delete mail domain', [
                'domain' => $mailhost,
                'site_id' => $this->site_id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Soft undelete a mail domain (re-enable mailboxes)
     * Note: This is a placeholder since Poste doesn't have soft delete/undelete
     *
     * @param string $mailhost
     * @throws Exception
     */
    public function softUndeleteHost(string $mailhost): void
    {
        try {
            Log::info('Soft undeleting mail domain in Poste', [
                'domain' => $mailhost,
                'site_id' => $this->site_id
            ]);

            // Since we don't actually soft delete in Poste, this is mostly a no-op
            // But we log it for consistency
            Log::info('Mail domain soft undelete completed', [
                'domain' => $mailhost,
                'site_id' => $this->site_id,
                'note' => 'No action needed - Poste does not support soft delete'
            ]);

        } catch (Exception $e) {
            Log::error('Failed to soft undelete mail domain', [
                'domain' => $mailhost,
                'site_id' => $this->site_id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Save/create a mail domain in Poste
     *
     * @param string $mailhost
     * @throws Exception
     */
    public function saveHost(string $mailhost): void
    {
        try {
            Log::info('Saving mail domain to Poste', [
                'domain' => $mailhost,
                'site_id' => $this->site_id
            ]);

            $domainCreated = false;

            // Check if domain already exists
            if (!$this->posteService->domainExists($mailhost)) {
                // Create the domain in Poste
                $this->posteService->addDomain($mailhost);
                $domainCreated = true;

                Log::info('Mail domain created successfully in Poste', [
                    'domain' => $mailhost,
                    'site_id' => $this->site_id
                ]);
            } else {
                Log::info('Mail domain already exists in Poste', [
                    'domain' => $mailhost,
                    'site_id' => $this->site_id
                ]);
            }

            // Generate DKIM key for the domain (both new and existing domains)
            try {
                Log::info('Generating DKIM key for domain', [
                    'domain' => $mailhost,
                    'site_id' => $this->site_id
                ]);

                $dkimData = $this->posteService->generateDkimKey($mailhost);

                Log::info('DKIM key generated successfully', [
                    'domain' => $mailhost,
                    'site_id' => $this->site_id,
                    'has_public_key' => isset($dkimData['public'])
                ]);

            } catch (Exception $dkimException) {
                Log::warning('Failed to generate DKIM key for domain', [
                    'domain' => $mailhost,
                    'site_id' => $this->site_id,
                    'dkim_error' => $dkimException->getMessage()
                ]);
                // Don't throw here - domain creation succeeded, DKIM is secondary
            }

            // Add DNS records for the domain (both new and existing domains)
            try {
                $this->dnsManager->addMailDnsRecords($mailhost);
                Log::info('DNS records added for mail domain', [
                    'domain' => $mailhost,
                    'site_id' => $this->site_id,
                    'domain_created' => $domainCreated
                ]);

                // Execute shell script to configure Caddy reverse proxy for mail subdomain
                $this->executeMailServerScript($mailhost);

            } catch (Exception $dnsException) {
                Log::warning('Failed to add DNS records for mail domain', [
                    'domain' => $mailhost,
                    'site_id' => $this->site_id,
                    'dns_error' => $dnsException->getMessage()
                ]);
                // Don't throw here - domain creation succeeded, DNS is secondary
            }

        } catch (Exception $e) {
            Log::error('Failed to save mail domain to Poste', [
                'domain' => $mailhost,
                'site_id' => $this->site_id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get the prefix for this site (similar to original MailhostHandler)
     *
     * @return string
     */
    private function getPrefix(): string
    {
        return "ex-" . config('exolog.server_name') . "-" . $this->site_id;
    }

    /**
     * Check if a domain exists in Poste
     *
     * @param string $mailhost
     * @return bool
     */
    public function hostExists(string $mailhost): bool
    {
        try {
            return $this->posteService->domainExists($mailhost);
        } catch (Exception $e) {
            Log::error('Failed to check if domain exists in Poste', [
                'domain' => $mailhost,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get domain information from Poste
     *
     * @param string $mailhost
     * @return array|null
     */
    public function getHostInfo(string $mailhost): ?array
    {
        try {
            return $this->posteService->getDomain($mailhost);
        } catch (Exception $e) {
            Log::error('Failed to get domain info from Poste', [
                'domain' => $mailhost,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * List all mailboxes for a domain
     *
     * @param string $mailhost
     * @return array
     */
    public function getMailboxes(string $mailhost): array
    {
        try {
            return $this->posteService->listMailboxes($mailhost);
        } catch (Exception $e) {
            Log::error('Failed to get mailboxes for domain from Poste', [
                'domain' => $mailhost,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Execute shell script to configure Caddy reverse proxy for mail subdomain
     * Following the same pattern as DomainHandler::execHandler
     *
     * @param string $mailhost
     */
    private function executeMailServerScript(string $mailhost): void
    {
        try {
            // Extract the mail subdomain (mail.domain.com)
            $mailSubdomain = "mail.{$mailhost}";

            // Get script path from config (similar to DomainHandler pattern)
            $handlerScript = '/app/exologadmin.webkracht.nl/htdocs/scripts/update_site_domain.sh';

            // Check if script exists
            if (!file_exists($handlerScript)) {
                Log::warning('Mail server script not found', [
                    'script_path' => $handlerScript,
                    'domain' => $mailhost,
                    'mail_subdomain' => $mailSubdomain
                ]);
                return;
            }

            // Following DomainHandler pattern: exec("$handlerScript $certPath $host $use_ssl $use_le $force", $output, $result_code);
            // For mail server: exec("$handlerScript mailserver $mailSubdomain", $output, $result_code);

            Log::info('Executing mail server script', [
                'script' => $handlerScript,
                'target' => 'mailserver',
                'domain' => $mailSubdomain
            ]);

            exec("$handlerScript mailserver $mailSubdomain", $output, $result_code);

            if ($result_code === 0) {
                Log::info('Mail server script executed successfully', [
                    'domain' => $mailhost,
                    'mail_subdomain' => $mailSubdomain,
                    'result_code' => $result_code,
                    'output' => $output
                ]);
            } else {
                Log::error('Mail server script execution failed', [
                    'domain' => $mailhost,
                    'mail_subdomain' => $mailSubdomain,
                    'result_code' => $result_code,
                    'output' => $output
                ]);
            }

        } catch (Exception $e) {
            Log::error('Exception during mail server script execution', [
                'domain' => $mailhost,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Execute script to remove Caddy reverse proxy configuration for mail subdomain
     * Following the same pattern as DomainHandler::execHandler
     * Note: Current script doesn't have removal functionality, logs for manual cleanup
     *
     * @param string $mailhost
     */
    private function executeMailServerRemovalScript(string $mailhost): void
    {
        try {
            // Extract the mail subdomain (mail.domain.com)
            $mailSubdomain = "mail.{$mailhost}";

            Log::info('Mail subdomain proxy removal needed', [
                'domain' => $mailhost,
                'mail_subdomain' => $mailSubdomain,
                'note' => 'Manual Caddy configuration cleanup may be required',
                'suggestion' => 'Consider adding removal functionality to update_site_domain.sh script'
            ]);

            // TODO: When removal functionality is added to the script, use this pattern:
            // $handlerScript = '/app/exologadmin.webkracht.nl/htdocs/scripts/update_site_domain.sh';
            // exec("$handlerScript remove-mailserver $mailSubdomain", $output, $result_code);

        } catch (Exception $e) {
            Log::error('Exception during mail server removal script execution', [
                'domain' => $mailhost,
                'error' => $e->getMessage()
            ]);
        }
    }
}
