<?php

namespace Exolog\Module\Mail;

use Exception;
use Exolog\Module\Support\Concerns\HasMakeFactory;
use Illuminate\Support\Facades\Log;

class PosteMailboxService
{
    use HasMakeFactory;

    private PosteApiService $posteService;

    public function __construct()
    {
        $this->posteService = new PosteApiService();
    }

    /**
     * Check if mailbox exists in Poste
     *
     * @param string $email
     * @return bool
     */
    public function mailboxExists(string $email): bool
    {
        try {
            Log::info('Checking if mailbox exists in Poste', ['email' => $email]);

            return $this->posteService->mailboxExists($email);

        } catch (Exception $e) {
            Log::error('Failed to check mailbox existence in Poste', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Create a new mailbox
     *
     * @param string $email
     * @param string $name
     * @param string $password
     * @param array $destinations Additional email destinations (for forwarding)
     * @param array $options Additional options like quota
     * @return bool
     * @throws Exception
     */
    public function createMailbox(string $email, string $name, string $password, array $destinations = [], array $options = []): bool
    {
        try {
            Log::info('🏗️ PosteMailboxService.createMailbox called', [
                'email' => $email,
                'name' => $name,
                'password' => '***',
                'destinations' => $destinations,
                'destinations_type' => gettype($destinations),
                'destinations_count' => is_array($destinations) ? count($destinations) : 'not_array',
                'options' => $options
            ]);

            // Prepare redirectTo for mailbox creation if destinations are provided
            $redirectEmails = [];
            if (!empty($destinations)) {
                foreach ($destinations as $destination) {
                    if (is_array($destination) && isset($destination['name'])) {
                        $redirectEmails[] = $destination['name'];
                    } elseif (is_string($destination)) {
                        $redirectEmails[] = $destination;
                    }
                }
                if (!empty($redirectEmails)) {
                    $options['redirectTo'] = $redirectEmails;
                    Log::info('📧 Including redirectTo in mailbox creation', [
                        'email' => $email,
                        'redirectTo' => $redirectEmails
                    ]);
                } else {
                    Log::info('📧 No valid redirect destinations for mailbox creation', ['email' => $email]);
                }
            } else {
                Log::info('📧 No redirect destinations provided for mailbox creation', ['email' => $email]);
            }

            // Create the mailbox in Poste with redirect options
            $this->posteService->addMailbox($email, $password, $name, $options);

            Log::info('Mailbox created successfully in Poste', ['email' => $email]);
            return true;

        } catch (Exception $e) {
            Log::error('Failed to create mailbox in Poste', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Delete a mailbox
     *
     * @param string $email
     * @return bool
     * @throws Exception
     */
    public function deleteMailbox(string $email): bool
    {
        try {
            Log::info('Deleting mailbox from Poste', ['email' => $email]);

            $this->posteService->removeMailbox($email);

            Log::info('Mailbox deleted successfully from Poste', ['email' => $email]);
            return true;

        } catch (Exception $e) {
            Log::error('Failed to delete mailbox from Poste', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Check if a mailbox exists
     *
     * @param string $email
     * @return bool
     */
    public function isExistMailbox(string $email): bool
    {
        try {
            return $this->posteService->mailboxExists($email);
        } catch (Exception $e) {
            Log::error('Failed to check if mailbox exists in Poste', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Set/update mailbox password
     *
     * @param string $email
     * @param string $password
     * @return bool
     * @throws Exception
     */
    public function setPassword(string $email, string $password): bool
    {
        try {
            Log::info('Setting password for mailbox in Poste', ['email' => $email]);

            $this->posteService->updateMailboxPassword($email, $password);

            Log::info('Password set successfully for mailbox in Poste', ['email' => $email]);
            return true;

        } catch (Exception $e) {
            Log::error('Failed to set password for mailbox in Poste', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get mailbox information
     *
     * @param string $email
     * @return array|null
     */
    public function getMailboxInfo(string $email): ?array
    {
        try {
            return $this->posteService->getMailbox($email);
        } catch (Exception $e) {
            Log::error('Failed to get mailbox info from Poste', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get mailbox quota information
     *
     * @param string $email
     * @return array|null
     */
    public function getMailboxQuota(string $email): ?array
    {
        try {
            Log::info('Getting mailbox quota from Poste', ['email' => $email]);

            return $this->posteService->getMailboxQuota($email);

        } catch (Exception $e) {
            Log::error('Failed to get mailbox quota from Poste', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Update mailbox redirect settings
     *
     * @param string $email
     * @param array $redirectEmails Array of email addresses to redirect to
     * @return bool
     */
    public function updateMailboxRedirect(string $email, array $redirectEmails = []): bool
    {
        try {
            Log::info('Updating mailbox redirect in Poste', [
                'email' => $email,
                'redirectTo' => $redirectEmails
            ]);

            $this->posteService->updateMailboxRedirect($email, $redirectEmails);

            Log::info('Mailbox redirect updated successfully', [
                'email' => $email,
                'redirectTo' => $redirectEmails
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('Failed to update mailbox redirect', [
                'email' => $email,
                'redirectTo' => $redirectEmails,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get mailbox sieve script
     * @param string $email
     * @return array|null
     */
    public function getMailboxSieve(string $email): ?array
    {
        try {
            Log::info('Getting mailbox sieve script from Poste', ['email' => $email]);

            $sieveData = $this->posteService->getMailboxSieve($email);

            Log::info('Mailbox sieve script retrieved successfully', [
                'email' => $email,
                'has_script' => isset($sieveData['script']) && !empty($sieveData['script'])
            ]);

            return $sieveData;

        } catch (Exception $e) {
            Log::error('Failed to get mailbox sieve script', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Parse sieve script to extract redirect destinations
     * @param string $script
     * @return array
     */
    public function parseSieveScript(string $script): array
    {
        return $this->posteService->parseSieveScript($script);
    }

    /**
     * Generate DKIM key for domain
     * @param string $domain
     * @return array|null
     */
    public function generateDkimKey(string $domain): ?array
    {
        try {
            Log::info('Generating DKIM key for domain', ['domain' => $domain]);

            $dkimData = $this->posteService->generateDkimKey($domain);

            Log::info('DKIM key generated successfully', [
                'domain' => $domain,
                'has_public_key' => isset($dkimData['public'])
            ]);

            return $dkimData;

        } catch (Exception $e) {
            Log::error('Failed to generate DKIM key', [
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get DKIM key for domain
     * @param string $domain
     * @return array|null
     */
    public function getDkimKey(string $domain): ?array
    {
        try {
            Log::info('Getting DKIM key for domain', ['domain' => $domain]);

            $dkimData = $this->posteService->getDkimKey($domain);

            Log::info('DKIM key retrieved successfully', [
                'domain' => $domain,
                'has_public_key' => isset($dkimData['public'])
            ]);

            return $dkimData;

        } catch (Exception $e) {
            Log::error('Failed to get DKIM key', [
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Set destinations for an email (forwarding)
     * Note: This is a simplified implementation - Poste handles aliases differently
     *
     * @param string $email
     * @param array $destinations
     * @return bool
     */
    public function setDestinations(string $email, array $destinations): bool
    {
        try {
            Log::info('💾 Setting destinations for email in Poste', [
                'email' => $email,
                'destinations' => $destinations,
                'destinations_count' => count($destinations)
            ]);

            // Convert destinations to email addresses for Poste redirectTo field
            $redirectEmails = [];
            foreach ($destinations as $index => $destination) {
                Log::info('🔄 Processing destination', [
                    'email' => $email,
                    'index' => $index,
                    'destination' => $destination,
                    'destination_type' => gettype($destination)
                ]);

                if (is_array($destination) && isset($destination['name'])) {
                    $redirectEmails[] = $destination['name'];
                    Log::info('✅ Added array destination', [
                        'email' => $email,
                        'destination_name' => $destination['name']
                    ]);
                } elseif (is_string($destination)) {
                    $redirectEmails[] = $destination;
                    Log::info('✅ Added string destination', [
                        'email' => $email,
                        'destination' => $destination
                    ]);
                } else {
                    Log::warning('⚠️ Skipped invalid destination format', [
                        'email' => $email,
                        'destination' => $destination,
                        'type' => gettype($destination)
                    ]);
                }
            }

            Log::info('📧 Final redirect emails to save', [
                'email' => $email,
                'redirectEmails' => $redirectEmails,
                'count' => count($redirectEmails)
            ]);

            // Update mailbox redirect settings in Poste
            if (!empty($redirectEmails)) {
                Log::info('📮 Updating mailbox redirect in Poste API', [
                    'email' => $email,
                    'redirectTo' => $redirectEmails
                ]);

                $this->posteService->updateMailboxRedirect($email, $redirectEmails);

                Log::info('✅ Mailbox redirect updated successfully in Poste', [
                    'email' => $email,
                    'redirectTo' => $redirectEmails
                ]);
            } else {
                Log::info('🗑️ Clearing mailbox redirect (no destinations)', ['email' => $email]);

                // Clear redirects if no destinations
                $this->posteService->updateMailboxRedirect($email, []);

                Log::info('✅ Mailbox redirect cleared in Poste', ['email' => $email]);
            }

            return true;

        } catch (Exception $e) {
            Log::error('Failed to set destinations in Poste', [
                'email' => $email,
                'destinations' => $destinations,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get destinations for an email
     *
     * @param string $email
     * @param bool $includeMailbox
     * @return array
     */
    public function getDestinations(string $email, bool $includeMailbox = true): array
    {
        try {
            Log::info('🔍 Getting destinations for email from Poste', [
                'email' => $email,
                'include_mailbox' => $includeMailbox
            ]);

            $destinations = [];

            // Check if mailbox exists and get its redirect destinations
            $mailboxExists = $this->mailboxExists($email);
            Log::info('📮 Mailbox existence check', [
                'email' => $email,
                'exists' => $mailboxExists
            ]);

            if ($mailboxExists) {
                $mailboxInfo = $this->posteService->getMailbox($email);

                Log::info('📥 Raw mailbox info from Poste API', [
                    'email' => $email,
                    'mailboxInfo' => $mailboxInfo
                ]);

                if ($mailboxInfo && isset($mailboxInfo['redirectTo']) && is_array($mailboxInfo['redirectTo'])) {
                    Log::info('📧 Processing redirectTo array from mailbox', [
                        'email' => $email,
                        'redirectTo' => $mailboxInfo['redirectTo']
                    ]);

                    // Add redirect destinations (excluding the user's own email)
                    foreach ($mailboxInfo['redirectTo'] as $redirectEmail) {
                        if (strtolower($redirectEmail) !== strtolower($email)) {
                            $destinations[] = ['name' => $redirectEmail];
                            Log::info('✅ Added redirect destination', [
                                'email' => $email,
                                'destination' => $redirectEmail
                            ]);
                        } else {
                            Log::info('🚫 Skipped user\'s own email from redirectTo', [
                                'email' => $email,
                                'skipped' => $redirectEmail
                            ]);
                        }
                    }
                } else {
                    Log::info('⚠️ No redirectTo array found in mailbox info', [
                        'email' => $email,
                        'mailboxInfo_keys' => $mailboxInfo ? array_keys($mailboxInfo) : 'null'
                    ]);
                }

                // Only include the mailbox itself if explicitly requested and no redirects exist
                // This prevents the user's own email from appearing in the forward list
                if ($includeMailbox && empty($destinations)) {
                    Log::info('📮 Not including user\'s own email in destinations (correct behavior)', [
                        'email' => $email
                    ]);
                    // Don't include the user's own email in destinations to prevent confusion
                    // The mailbox existence is handled separately by is_popbox
                }
            } else {
                // Check if it's an alias
                $aliasExists = $this->aliasExists($email);
                Log::info('📧 Alias existence check', [
                    'email' => $email,
                    'exists' => $aliasExists
                ]);

                if ($aliasExists) {
                    $aliasInfo = $this->posteService->getAlias($email);

                    Log::info('📥 Raw alias info from Poste API', [
                        'email' => $email,
                        'aliasInfo' => $aliasInfo
                    ]);

                    if ($aliasInfo && isset($aliasInfo['destinations']) && is_array($aliasInfo['destinations'])) {
                        Log::info('📧 Processing destinations array from alias', [
                            'email' => $email,
                            'destinations' => $aliasInfo['destinations']
                        ]);

                        foreach ($aliasInfo['destinations'] as $destination) {
                            if (strtolower($destination) !== strtolower($email)) {
                                $destinations[] = ['name' => $destination];
                                Log::info('✅ Added alias destination', [
                                    'email' => $email,
                                    'destination' => $destination
                                ]);
                            } else {
                                Log::info('🚫 Skipped user\'s own email from alias destinations', [
                                    'email' => $email,
                                    'skipped' => $destination
                                ]);
                            }
                        }
                    } else {
                        Log::info('⚠️ No destinations array found in alias info', [
                            'email' => $email,
                            'aliasInfo_keys' => $aliasInfo ? array_keys($aliasInfo) : 'null'
                        ]);
                    }
                }
            }

            Log::info('✅ Final destinations retrieved from Poste', [
                'email' => $email,
                'destinations_count' => count($destinations),
                'destinations' => $destinations
            ]);

            return $destinations;

        } catch (Exception $e) {
            Log::error('Failed to get destinations from Poste', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Create an alias
     *
     * @param string $email
     * @param array $destinations
     * @return bool
     */
    public function createAlias(string $email, array $destinations): bool
    {
        try {
            Log::info('Creating alias in Poste', [
                'email' => $email,
                'destinations' => $destinations
            ]);

            // This would need to be implemented based on Poste's alias API
            Log::warning('Alias creation not fully implemented for Poste', [
                'email' => $email,
                'destinations' => $destinations,
                'note' => 'Manual configuration may be required'
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('Failed to create alias in Poste', [
                'email' => $email,
                'destinations' => $destinations,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Delete an alias
     *
     * @param string $email
     * @return bool
     */
    public function deleteAlias(string $email): bool
    {
        try {
            Log::info('Deleting alias from Poste', ['email' => $email]);

            // This would need to be implemented based on Poste's alias API
            Log::warning('Alias deletion not fully implemented for Poste', [
                'email' => $email,
                'note' => 'Manual configuration may be required'
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('Failed to delete alias from Poste', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Check if an alias exists
     *
     * @param string $email
     * @return bool
     */
    public function isExistAlias(string $email): bool
    {
        try {
            // This would need to be implemented based on Poste's alias API
            return false;
        } catch (Exception $e) {
            Log::error('Failed to check if alias exists in Poste', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Soft delete mailboxes by host (disable them)
     *
     * @param string $mailhost
     * @return bool
     */
    public function softDeleteBoxesByHost(string $mailhost): bool
    {
        try {
            Log::info('Soft deleting mailboxes by host in Poste', ['mailhost' => $mailhost]);

            $mailboxes = $this->posteService->listMailboxes($mailhost);
            
            foreach ($mailboxes as $mailbox) {
                if (isset($mailbox['email'])) {
                    // Log for manual intervention since Poste doesn't have soft delete
                    Log::warning('Soft delete requested for mailbox', [
                        'email' => $mailbox['email'],
                        'mailhost' => $mailhost,
                        'note' => 'Manual intervention may be required'
                    ]);
                }
            }

            return true;

        } catch (Exception $e) {
            Log::error('Failed to soft delete mailboxes by host in Poste', [
                'mailhost' => $mailhost,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Soft undelete mailboxes by host (re-enable them)
     *
     * @param string $mailhost
     * @return bool
     */
    public function softUndeleteBoxesByHost(string $mailhost): bool
    {
        try {
            Log::info('Soft undeleting mailboxes by host in Poste', ['mailhost' => $mailhost]);

            // Since Poste doesn't have soft delete, this is mostly a no-op
            Log::info('Soft undelete completed for mailhost', [
                'mailhost' => $mailhost,
                'note' => 'No action needed - Poste does not support soft delete'
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('Failed to soft undelete mailboxes by host in Poste', [
                'mailhost' => $mailhost,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Check if email is a maillisting destination
     *
     * @param string $email
     * @return bool
     */
    public function isMaillistingDest(string $email): bool
    {
        try {
            // Check if this email is configured as a mailing list destination
            // This would need to be implemented based on your mailing list logic
            Log::info('Checking if email is maillisting dest in Poste', ['email' => $email]);

            // For now, return false - implement based on your mailing list requirements
            return false;

        } catch (Exception $e) {
            Log::error('Failed to check maillisting dest in Poste', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get all aliases for an email
     *
     * @param string $email
     * @return array
     */
    public function getAliases(string $email): array
    {
        try {
            Log::info('Getting aliases for email from Poste', ['email' => $email]);

            // Get aliases from Poste API
            $response = $this->posteService->getAliases($email);

            Log::info('Retrieved aliases from Poste', [
                'email' => $email,
                'aliases_count' => count($response)
            ]);

            return $response;

        } catch (Exception $e) {
            Log::error('Failed to get aliases from Poste', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Check if alias exists
     *
     * @param string $email
     * @return bool
     */
    public function aliasExists(string $email): bool
    {
        try {
            Log::info('Checking if alias exists in Poste', ['email' => $email]);

            return $this->posteService->aliasExists($email);

        } catch (Exception $e) {
            Log::error('Failed to check alias existence in Poste', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Update mailbox password
     *
     * @param string $email
     * @param string $password
     * @return bool
     */
    public function updateMailboxPassword(string $email, string $password): bool
    {
        try {
            Log::info('Updating mailbox password in Poste', ['email' => $email]);

            $this->posteService->updateMailboxPassword($email, $password);

            Log::info('Mailbox password updated successfully in Poste', ['email' => $email]);
            return true;

        } catch (Exception $e) {
            Log::error('Failed to update mailbox password in Poste', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
