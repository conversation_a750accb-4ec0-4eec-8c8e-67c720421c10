<?php

namespace Exolog\Module\Users\Model\Traits;

use Exolog\Module\Support\Facades\Site;
use Exolog\Module\Support\Facades\Validator;
use Exolog\Module\Users\Model\User;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;
use Illuminate\Validation\Rules\Unique;

trait HasUserSaveFrom
{
    public static function saveFromArray(array $data): User
    {
        $userValue = Validator::validateWithAttributes(
            $data,
            [
                'u_id' => 'nullable|numeric',
                'u_email' => [
                    tap(Rule::unique(User::class), static function (Unique $rule) use ($data) {
                        if (is_numeric($data['u_id'])) {
                            $rule->ignore($data['u_id'], 'u_id');
                        }
                        $rule->where('u_site', Site::id());
                    }),
                    'email'
                ],
                'u_lang' => 'nullable|numeric',
                'u_password' => ['nullable', Password::min(8)],
                'u_first_name' => 'nullable',
                'u_between_name' => 'nullable',
                'u_last_name' => 'nullable',
                'u_sex' => 'nullable',
                'u_address' => 'nullable',
                'u_zipcode' => 'nullable',
                'u_city' => 'nullable',
                'u_country' => 'nullable',
                'u_state' => 'nullable',
                'u_telephone' => 'nullable',
                'u_info' => 'nullable',
                'u_alt_email' => 'nullable',
                'usergroups' => 'nullable|array',
            ]);

        $user = User::updateOrCreate([
            'u_id' => $userValue['u_id']
        ], $userValue);

        if (array_key_exists('usergroups', $userValue)) {
            $ids = Arr::pluck($userValue['usergroups'], 'ug_id');
            $user->usergroups()->sync($ids);
        }

        return $user->refresh();
    }
}
