<?php

namespace Exolog\Module\Users\Model\Traits;

use Exolog\Module\Domains\Model\Domain;
use Exolog\Module\Mails\Mail\UserPasswordChangeLink;
use Illuminate\Support\Facades\Mail;

trait Authenticatable
{
    /**
     * The column name of the "remember me" token.
     *
     * @var string
     */
    protected $rememberTokenName = 'u_remember_token';

    /**
     * Get the name of the unique identifier for the user.
     *
     * @return string
     */
    public function getAuthIdentifierName()
    {
        return $this->getKeyName();
    }

    /**
     * Get the unique identifier for the user.
     *
     * @return mixed
     */
    public function getAuthIdentifier()
    {
        return $this->{$this->getAuthIdentifierName()};
    }

    /**
     * Get the unique broadcast identifier for the user.
     *
     * @return mixed
     */
    public function getAuthIdentifierForBroadcasting()
    {
        return $this->getAuthIdentifier();
    }

    /**
     * Get the password for the user.
     *
     * @return string
     */
    public function getAuthPassword()
    {
        return $this->u_password;
    }

    /**
     * Get the token value for the "remember me" session.
     *
     * @return string|null
     */
    public function getRememberToken()
    {
        if (!empty($this->getRememberTokenName())) {
            return (string)$this->{$this->getRememberTokenName()};
        }
    }

    /**
     * Set the token value for the "remember me" session.
     *
     * @param string $value
     * @return void
     */
    public function setRememberToken($value)
    {
        if (!empty($this->getRememberTokenName())) {
            $this->{$this->getRememberTokenName()} = $value;
        }
    }

    /**
     * Get the column name for the "remember me" token.
     *
     * @return string
     */
    public function getRememberTokenName()
    {
        return $this->rememberTokenName;
    }

    /**
     * @param (array) $options (optional)
     *              (int) $ttl - link valid hours (optional) defaults to 24
     *              (string) $path - path to page where change password. Default 'change-password'
     *              (redirect) $redir - true redirect/false abs link  (optional) default - false
     * @return string
     */
    public function genUserChangePasswordLink(array $options = []): string
    {
        if (empty($options['ttl']) || !is_numeric($options['ttl']) || ($options['ttl'] <= 0)) {
            $ttl = 24;
        } else {
            $ttl = (int)$options['ttl'];
        }

        $path = data_get($options, 'path', 'change-password');

        $hash = [
            'u_id' => $this->u_id,
            'u_email' => $this->u_email,
            'u_site' => $this->u_site,
            'time' => time(),
            'ttl' => $ttl
        ];

        if (!empty($options['redir'])) {
            $hash['redir'] = $options['redir'];
        }

        $hash = encrypt($hash);

        return url_query($path, [], ['hash' => $hash]);
    }

    /**
     * @param (array) $options (optional)
     *              (int) $ttl - link valid hours (optional) defaults to 24
     *              (string) $path - path to page where change password. Default 'change-password'
     *              (redirect) $redir - true redirect/false abs link  (optional) default - false
     *              (relative) $relative - relative link or absolute link (optional) default - true
     */
    public function sendPasswordChangeLink(array $options = []): void
    {
        $link = $this->genUserChangePasswordLink($options);
        Mail::to($this->u_email)->send(new UserPasswordChangeLink($this, $link));
    }

    /**
     * Generate backend login hash string that can be used by Auth::loginUsingHash(),
     * without authorized current user
     * Used by dealer login
     */
    public function getLoginHash(): string
    {
        return encrypt(['t' => time(), 'u' => $this['u_id']]);
    }

    public function genUserLoginLink($options = []): string
    {

        $relative = false;
        if (!empty($options['relative'])) {
            $relative = true;
        }
        $redir = '/';
        if (!empty($options['redir'])) {
            $redir = $options['redir'];
        }

        $hash = $this->getLoginHash();

        if (!empty($options['host']) && is_string($options['host'])) {
            $host = $options['host'];
        } else {
            $host = Domain::getMainDomain()->domain_name;
        }

        $baseUrl = '/api/auth/backendHashLogin?hash=';

        $protocol = 'https://';

        $link = $baseUrl . $hash . '&redir=' . $redir;

        if (!$relative) {
            $link = $protocol . $host . $link;
        }

        return $link;
    }
}
