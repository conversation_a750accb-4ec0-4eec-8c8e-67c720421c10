<?php


namespace Exolog\Module\Users\Export;


use Exolog\Module\Users\Model\User;
use JeroenDes<PERSON>overe\VCard\VCard;

class ExportVCFService
{
    static function downloadUserCard($u_id)
    {
        $user = User::findOrFail($u_id);

        $vcard = new VCard();
        $prefix = '';
        $suffix = '';

        $vcard->addName($user['u_last_name'], $user['u_first_name'], $user['u_between_name'], $prefix, $suffix);

        $vcard->addEmail($user['u_email'], 'PREF;WORK');
        $vcard->addEmail($user['u_alt_email'], 'HOME');
        $vcard->addPhoneNumber($user['u_telephone'], 'PREF;WORK');
        $vcard->addAddress(null, null, $user['u_address'], $user['u_city'], $user['u_state'], $user['u_zipcode'],
            $user['u_country']);
        $vcard->addNote($user['u_info']);
        $vcard->addURL($user['u_url']);

        $vcard->download();
    }
}