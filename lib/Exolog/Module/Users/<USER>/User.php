<?php


namespace Exolog\Module\Users\Model;


use Exolog\Module\Database\Concerns\HasSiteScope;
use Exolog\Module\Support\PHPDoc\PHPDoc_Builder;
use Exolog\Module\Users\Model\Traits\Authenticatable;
use Exolog\Module\Users\Model\Traits\HasUserSaveFrom;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;


/**
 * @property Collection<Usergroup> $usergroups
 * @property string $u_newsletter_confirmstring
 * @property \Carbon\Carbon $u_deleted_at
 * @property mixed $u_password
 * @property mixed $u_id
 * @property string|null $u_two_factor_secret
 * @property string $u_email
 * @property integer $u_site
 * @property Carbon $u_date_added
 * @property Carbon|mixed $u_date_changed
 *
 * @mixin PHPDoc_Builder
 *
 */
class User extends Model implements AuthenticatableContract
{
    use HasSiteScope;
    use SoftDeletes;
    use Authenticatable;
    use HasUserSaveFrom;

    public const SITE_FIELD = 'u_site';
    public const DELETED_AT = 'u_deleted_at';

    protected $table = 'user';
    protected $primaryKey = 'u_id';
    public $timestamps = false;
    protected $fillable = [
        'u_email',
        'u_lang',
        'u_alias',
        'u_password',
        'u_two_factor_secret',
        'u_first_name',
        'u_between_name',
        'u_last_name',
        'u_sex',
        'u_address',
        'u_zipcode',
        'u_city',
        'u_country',
        'u_state',
        'u_telephone',
        'u_info',
        'u_url',
        'u_alt_email',
        'u_ismailbox',
        'u_mailsize',
        'u_maildest',
        'u_did',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [
        'u_two_factor_secret',
        'u_password',
        'u_remember_token',
        'u_newsletter_confirmstring'
    ];

    protected $casts = [
        'u_date_added' => 'datetime',
        'u_date_changed' => 'datetime',
        'u_site' => 'int',
        'u_ismailbox' => 'int',
    ];

    protected $appends = [
        //'u_isadmin',
        //'u_isdeveloper',
        'is_2fa'
    ];


    protected static function booted()
    {
        static::creating(static function (User $user) {
            $user->u_date_added = now();
            $user->u_newsletter_confirmstring = $user->u_newsletter_confirmstring ?? Str::random(15);

            if (empty($user->u_password)) {
                $user->u_password = Str::random(10);
            }
        });

        static::saving(static function (User $user) {
            $user->u_date_changed = now();
        });
    }

    public function usergroups(): BelongsToMany
    {
        return $this->belongsToMany(Usergroup::class, 'user_usergroup', 'uug_user', 'uug_usergroup');
    }

    /**
     * @param string $email
     * @return User|null
     */
    public static function findByEmail(string $email): ?User
    {
        $query = static::query();
        $email = strtolower($email);
        return $query->whereRaw('lower(u_email) = ?', [$email])->first();
    }


    public static function findByEmailOrFail(string $email): User
    {
        if (!is_null($model = static::findByEmail($email))) {
            return $model;
        }
        throw (new ModelNotFoundException)->setModel(static::class);
    }

    public static function findByConfirmString(string $confirm_string): ?User
    {
        /** @var User */
        return static::query()->whereRaw('upper(u_newsletter_confirmstring) = ?', [$confirm_string])->first();
    }


    /**
     * @param Usergroup|int|string $ug
     */
    public function addToGroup($ug): User
    {
        $ug = Usergroup::resolveOrFail($ug);
        $this->usergroups()->attach($ug->ug_id);
        $this->refresh();
        return $this;
    }

    /**
     * @param Usergroup|int|string $ug
     */
    public function removeFromGroup($ug): User
    {
        $ug = Usergroup::resolveOrFail($ug);
        $this->usergroups()->detach($ug->ug_id);
        $this->refresh();
        return $this;
    }

    /**
     * @param Usergroup|int|string $ug
     */
    public function memberOf($ug): bool
    {
        $ug = Usergroup::resolve($ug);
        return $this->usergroups->contains($ug);
    }

    public function isAdmin(): bool
    {
        return $this->usergroups->contains(function ($ug) {
            return $ug->ug_isadmin === 1;
        });
    }

    public function isDeveloper(): bool
    {
        return $this->usergroups->contains(function ($ug) {
            return $ug->ug_isdeveloper === 1;
        });
    }

    public function softDelete(): ?bool
    {
        return $this->delete(); // TODO: Change the autogenerated stub
    }

    public function getUIsadminAttribute()
    {
        return (int)$this->isAdmin();
    }

    public function getUIsdeveloperAttribute()
    {
        return (int)$this->isDeveloper();
    }

    public function getIs2faAttribute()
    {
        return !empty($this->u_two_factor_secret);
    }

    public function setUPasswordAttribute($value)
    {
        $this->attributes['u_password'] = Hash::make($value);
    }

    /**
     * @param User|int|string $user
     */
    public static function resolve($user): ?User
    {
        if ($user instanceof static) {
            return $user;
        }
        if (is_numeric($user)) {
            return static::find($user);
        }
        return static::findByEmail($user);
    }


    /**
     * @param User|int|string $user
     */
    public static function resolveOrFail($user): User
    {
        if (!is_null($model = static::resolve($user))) {
            return $model;
        }

        throw (new ModelNotFoundException)->setModel(static::class);
    }
}
