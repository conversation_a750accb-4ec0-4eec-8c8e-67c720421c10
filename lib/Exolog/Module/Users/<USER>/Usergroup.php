<?php


namespace Exolog\Module\Users\Model;


use Exolog\Module\Database\Concerns\HasSiteScope;
use Exolog\Module\Support\PHPDoc\PHPDoc_Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

/**
 * @property string $ug_name
 * @property string $ug_alias
 * @property Collection<User> $users
 * @property integer $ug_id
 *
 * @mixin PHPDoc_Builder
 */
class Usergroup extends Model
{
    use HasSiteScope;

    public const SITE_FIELD = 'ug_site';

    protected $table = 'usergroup';
    protected $primaryKey = 'ug_id';
    public $timestamps = false;
    protected $fillable = [
        'ug_name',
        'ug_alias',
        'ug_info',
        'ug_isadmin',
        'ug_parent',
        'ug_isnewsletter',
        'ug_isdeveloper',
    ];
    protected $casts = [
        'ug_parent' => 'int',
        'ug_isadmin' => 'int',
        'ug_isnewsletter' => 'int',
        'ug_isdeveloper' => 'int'
    ];


    public static function findByName($ug_name, $withUsers = false): ?Usergroup
    {
        return static::query()->when($withUsers, function ($query) {
            $query->with('users');
        })->where('ug_name', $ug_name)->first();
    }

    public static function findByNameOrFail($ug_name, $withUsers = false): Usergroup
    {
        if (!is_null($model = static::findByName($ug_name, $withUsers))) {
            return $model;
        }

        throw (new ModelNotFoundException)->setModel(static::class);
    }


    protected static function booted()
    {
        static::creating(static function (Usergroup $ug) {
            if (empty($ug->ug_alias)) {
                $ug->ug_alias = Str::slug($ug->ug_name);
            }
        });
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_usergroup', 'uug_usergroup', 'uug_user');
    }

    public function parent(): HasOne
    {
        return $this->hasOne(static::class, 'ug_id', 'ug_parent');
    }

    /**
     * @param User|int|string $user
     * @return $this
     */
    public function addUser($user): Usergroup
    {
        $user = User::resolveOrFail($user);
        $this->users()->attach($user->u_id);
        $this->refresh();
        return $this;
    }

    /**
     * @param User|int|string $user
     * @return $this
     */
    public function removeUser($user): Usergroup
    {
        $user = User::resolveOrFail($user);
        $this->users()->detach($user->u_id);
        $this->refresh();
        return $this;
    }

    /**
     * @param User|int|string $user
     * @return bool
     */
    public function hasUser($user): bool
    {
        $user = User::resolve($user);
        $this->load('users');
        return $this->users->contains($user);
    }

    /**
     * @param Usergroup|int|string $usergroup
     */
    public static function resolve($usergroup): ?Usergroup
    {
        if ($usergroup instanceof static) {
            return $usergroup;
        }
        if (is_numeric($usergroup)) {
            return static::find($usergroup);
        }
        return static::findByName($usergroup);
    }

    public static function resolveOrFail($usergroup): Usergroup
    {
        if (!is_null($model = static::resolve($usergroup))) {
            return $model;
        }

        throw (new ModelNotFoundException)->setModel(static::class);
    }
}