<?php

namespace Exolog\Module\Users;

use Exolog\Module\Cyrus\CyrusService;
use Exolog\Module\Domains\Model\Domain;
use Exolog\Module\Mail\PosteMailboxService;
use Exolog\Module\Support\Concerns\HasMakeFactory;
use Exolog\Module\Users\Model\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserMailboxService
{

    use HasMakeFactory;

    public function updateUserMailboxInfo(User $user): void
    {
        // Check if user's domain uses Poste or Cyrus
        [$userName, $mailDomain] = explode("@", $user['u_email']);

        if ($this->isPosteDomain($mailDomain)) {
            $this->updateUserMailboxInfoPoste($user);
        } else {
            $this->updateUserMailboxInfoCyrus($user);
        }
    }

    /**
     * Check if domain uses Poste API
     */
    private function isPosteDomain(string $domain): bool
    {
        // For now, assume all mail domains use Poste
        // You can add logic here to determine which domains use <PERSON><PERSON> vs Cyrus
        return in_array($domain, Domain::mailhosts()->pluck('domain_name')->toArray(), true);
    }

    /**
     * Update user mailbox info using Poste API
     */
    private function updateUserMailboxInfoPoste(User $user): void
    {
        try {
            $posteService = new PosteMailboxService();

            $update = [
                'u_ismailbox' => 0,
                'u_mailsize' => '',
                'u_maildest' => ''
            ];

            // Check if mailbox exists in Poste
            if ($posteService->mailboxExists($user['u_email'])) {
                $update['u_ismailbox'] = 1;

                // Note: We'll get sieve data separately below for forward destinations

                // Get mailbox quota from Poste
                $quotaInfo = $posteService->getMailboxQuota($user['u_email']);

                if ($quotaInfo && isset($quotaInfo['storage_usage'])) {
                    $storageUsage = $quotaInfo['storage_usage'];
                    if ($storageUsage > 1073741824) { // > 1GB
                        $update['u_mailsize'] = round($storageUsage / 1073741824, 1) . " GB";
                    } elseif ($storageUsage > 1048576) { // > 1MB
                        $update['u_mailsize'] = round($storageUsage / 1048576, 1) . " MB";
                    } elseif ($storageUsage > 0) {
                        $update['u_mailsize'] = round($storageUsage / 1024, 1) . " KB";
                    } else {
                        $update['u_mailsize'] = "0 KB";
                    }
                }

                // Get forward destinations from Poste mailbox sieve script
                try {
                    $sieveData = $posteService->getMailboxSieve($user['u_email']);

                    if ($sieveData && isset($sieveData['script']) && !empty($sieveData['script'])) {
                        // Parse Sieve script to extract redirect email addresses
                        $script = $sieveData['script'];
                        $redirectDestinations = $posteService->parseSieveScript($script);

                        if (!empty($redirectDestinations)) {
                            $update['u_maildest'] = "|" . implode('||', $redirectDestinations) . "|";
                            \Log::info('📧 Updated u_maildest from Poste sieve script', [
                                'user_email' => $user['u_email'],
                                'script' => $script,
                                'extracted_destinations' => $redirectDestinations,
                                'u_maildest' => $update['u_maildest']
                            ]);
                        } else {
                            $update['u_maildest'] = '';
                            \Log::info('📧 No valid redirect destinations found in Poste sieve script', [
                                'user_email' => $user['u_email'],
                                'script' => $script
                            ]);
                        }
                    } else {
                        $update['u_maildest'] = '';
                        \Log::info('📧 No sieve script found for mailbox', [
                            'user_email' => $user['u_email'],
                            'sieve_data' => $sieveData
                        ]);
                    }
                } catch (\Exception $e) {
                    $update['u_maildest'] = '';
                    \Log::warning('⚠️ Failed to get sieve script for mailbox', [
                        'user_email' => $user['u_email'],
                        'error' => $e->getMessage()
                    ]);
                }
            } else {
                // Mailbox doesn't exist, clear forward destinations
                $update['u_maildest'] = '';
                if ($update['u_ismailbox'] == 0) {
                    $update['u_mailsize'] = 'forward';
                }
            }

            $user->update($update);

            Log::info('Updated user mailbox info using Poste', [
                'email' => $user['u_email'],
                'is_mailbox' => $update['u_ismailbox'],
                'mail_size' => $update['u_mailsize']
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update user mailbox info using Poste', [
                'email' => $user['u_email'],
                'error' => $e->getMessage()
            ]);

            // Fallback to basic update
            $user->update([
                'u_ismailbox' => 0,
                'u_mailsize' => '',
                'u_maildest' => ''
            ]);
        }
    }

    /**
     * Update user mailbox info using Cyrus (legacy)
     */
    private function updateUserMailboxInfoCyrus(User $user): void
    {
        $cyrus = new CyrusService();

        $update = [
            'u_ismailbox' => 0,
            'u_mailsize' => '',
            'u_maildest' => ''
        ];
        $dest = $cyrus->get_dest($user['u_email'], false);
        if (!empty($dest)) {
            foreach ($dest as $k => $v) {
                $dest[$k] = $v['name'];
            }
        }
        if ($cyrus->is_exist_mbox($user['u_email'])) {
            $update['u_ismailbox'] = 1;
            $cur_user_mailbox_quota = $cyrus->get_quota($user['u_email']);
            if (empty($cur_user_mailbox_quota)) {
                // We set quota here
                if ($cyrus->set_quota($user['u_email'], 10000000)) {
                    // now lets check again
                    $cur_user_mailbox_quota = $cyrus->get_quota($user['u_email']);
                }
            }
            if ($cur_user_mailbox_quota) {
                if ($cur_user_mailbox_quota['usage'] > "1024" && $cur_user_mailbox_quota['usage'] < "1073741824") {
                    $cur_user_mailbox_quota = round($cur_user_mailbox_quota['usage'] / 1024,
                            1) . "  MB"; // display in MegaBytes
                } else {
                    if ($cur_user_mailbox_quota['usage'] > "1048576") {
                        $cur_user_mailbox_quota = round($cur_user_mailbox_quota['usage'] / 1048576,
                                1) . "  GB"; // display in GigaBytes
                    } else {
                        $cur_user_mailbox_quota = round($cur_user_mailbox_quota['usage'],
                                1) . " KB"; // display in KiloBytes
                    }
                }
            }
        } else {
            $cur_user_mailbox_quota = '';
            if (!empty($dest)) {
                $cur_user_mailbox_quota = 'forward';
            }
        }
        if (!empty($dest)) {
            $update['u_maildest'] = "|" . implode('||', $dest) . "|";
        }
        $update['u_mailsize'] = $cur_user_mailbox_quota;

        $user->update($update);
    }

    public function updateUsersMailboxInfo(): int
    {
        $mailhosts = Domain::mailhosts()->pluck('domain_name')->toArray();

        $mailhostRegexp = '@' . implode('|@', $mailhosts) . '$';

        return User::query()->whereRaw(DB::raw(sprintf('u_email REGEXP "%s"',
            $mailhostRegexp)))->get()->each(function (User $user) {
            $this->updateUserMailboxInfo($user);
        })->count();

    }
}