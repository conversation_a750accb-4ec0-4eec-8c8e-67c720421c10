<?php


namespace Exolog\Module\Sitemap;


use Exception;
use Exolog\Module\Forms\Model\FormPermalink;
use Exolog\Module\Sitemap\Events\BeforeImageAppend;
use Exolog\Module\Sitemap\Events\BeforeUrlAppend;
use Exolog\Module\Support\Facades\Vars;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use RuntimeException;

class SitemapIndexGenerator implements SitemapGenerator
{
    private array $params;

    /**
     * Local sitemap cache
     * @var array|null
     */
    private ?array $sitemap = null;

    public function __construct(array $params)
    {

        if (empty($params['edition_id'])) {
            throw new RuntimeException('Edition do not defined!');
        }
        //normalize params to build cache key
        $params = Arr::only($params, ['index', 'namespace', 'skip_images', 'edition_id']);
        $params['edition_id'] = (int)$params['edition_id'];
        $params['index'] = $params['index'] ?? null;
        $params['namespace'] = (bool)$params['namespace'];
        $params['skip_images'] = (bool)$params['skip_images'];
        ksort($params);

        $this->params = $params;

    }

    public static function cacheBuilder()
    {
        return new SitemapIndexCacheBuilder();
    }

    protected function getEditionId()
    {
        return $this->params['edition_id'];
    }


    public function getBaseHost()
    {
        return exolog()->getEditionDefaultUrl($this->getEditionId());
    }

    public function getSitemap()
    {

        if (Vars::site(Helper::VAR_SITEMAP_CRON())->getValue()) {
            if (Cache::has($this->getCacheKey())) {
                return Cache::get($this->getCacheKey());
            }
        }

        return $this->generate();
    }

    /**
     * @return array|string
     * @throws Exception
     */
    public function generate()
    {
        if ($this->params['index']) {
            return $this->generateUrlset();
        }

        return $this->generateSitemapIndex();
    }


    /**
     * @return array|string
     * @throws Exception
     */
    private function generateSitemapIndex()
    {
        $sitemaps = array_map(function ($item) {
            return sprintf('%s/%s-sitemap.xml', $this->getBaseHost(), $item);
        }, $this->getSitemapIndexList());
        return view('system.sitemap.sitemapindex', [
            'sitemaps' => $sitemaps,
        ])->toHtml();

    }

    /**
     * @return array|string
     * @throws Exception
     */
    private function generateUrlset()
    {
        $sitemaps = $this->getSitemapArray();
        if (!array_key_exists($this->params['index'], $sitemaps)) {
            throw new SitemapNotFoundException(sprintf('Index "%s" not found!', $this->params['index']));
        }
        return view('system.sitemap.urlset', [
            'urlset' => $sitemaps[$this->params['index']],
        ])->toHtml();
    }

    /**
     * @throws Exception
     */
    public function getSitemapIndexList(): array
    {
        return array_keys($this->getSitemapArray());
    }

    /**
     * @return array
     * @throws Exception
     */
    public function getSitemapArray(): ?array
    {
        if (!is_null($this->sitemap)) {
            return $this->sitemap;
        }

        $namespace = $this->params['namespace'];
        $skip_images_global = $this->params['skip_images'];
        $editionId = $this->getEditionId();

        $is_default_edition = getDefaultEdition() == $editionId;

        $sitemap = [];


        $reacts = PermalinkReactsLocator::make()->collect();

        //TODO getBaseHost from react
        $host = $this->getBaseHost();

        foreach ($reacts as $react) {
            //get permalinks for editionId or if empty, and editionId is default
            if ($react['edition_id'] != $editionId && (!empty($react['edition_id']) || !$is_default_edition)) {
                continue;
            }

            $index_name = $this->getIndexName($react['p_fp_id']);


            if ($index_name === null) {
                continue;
            }

            if (!empty($this->params['index']) && $index_name !== $this->params['index']) {
                continue;
            }

            $permalink = $react['permalink'];
            $loc = $host . $permalink;

            $loc = htmlspecialchars($loc);
            $url = ['loc' => $loc];
            if (!empty($react['react_date_changed'])) {
                $url['lastmod'] = gmdate('Y-m-d\TH:i:sP', strtotime($react['react_date_changed']));
            }
            if ($namespace) {
                $url['identifier:identifier'] = $react['react_id'];
            }

            if (!$skip_images_global) {
                $image_section = SitemapImage::getReactPicture($react);

                ['react' => $react, 'image:image' => $image_section] = BeforeImageAppend::dispatch(
                    [
                        'react' => $react,
                        'image:image' => $image_section
                    ]
                );

                if (!empty($image_section)) {
                    //append host if empty
                    foreach ($image_section as $key => $item) {
                        if (!Str::startsWith($item['image:loc'], 'http')) {
                            $image_section[$key]['image:loc'] = $host . $item['image:loc'];
                        }
                    }
                    $url['image:image'] = $image_section;
                }
            }

            ['sitemap' => $sitemap, 'url' => $url, 'react' => $react] = BeforeUrlAppend::dispatch([
                    'sitemap' => $sitemap,
                    'url' => $url,
                    'react' => $react
                ]
            );

            if (!empty($url)) {
                $sitemap[$index_name][] = $url;
            }
        }

        $this->sitemap = $sitemap;

        return $sitemap;
    }

    private function getCacheKey(): string
    {
        return sprintf('sitemap_%s', md5(serialize($this->params)));
    }

    /**
     * @throws Exception
     */
    public function generateAndPutToCache(): void
    {
        $xml = $this->generate();
        Cache::forever($this->getCacheKey(), $xml);
    }

    private function getIndexName($fp_id): ?string
    {
        $fp = FormPermalink::findOrFail($fp_id);
        if ($fp->fp_sitemap_active) {
            return $fp->fp_sitemap_name;
        }
        return null;
    }
}