<?php


namespace Exolog\Module\Sitemap;


use Exolog\Module\Editions\Model\Edition;
use Exolog\Module\Sitemap\Events\SitemapCacheUpdated;
use Illuminate\Database\Eloquent\Collection;

class SitemapService
{

    public static function getSitemap(array $params): string
    {
        return self::getGenerator($params)->getSitemap();
    }

    public static function getSitemapArray(array $params): array
    {
        return self::getGenerator($params)->getSitemapArray();
    }

    public static function buildCache()
    {
        SitemapGeneratorFactory::makeCacheBuilder()->build();
        SitemapCacheUpdated::dispatch();
    }

    /**
     * Return list of edition with sitemap
     * @return Edition[]|Collection
     */
    public static function getEditions()
    {
        return Edition::all()->filter(function ($edition) {
            return !empty(exolog()->getEditionDefaultUrl($edition['e_id'], true));
        });
    }

    private static function getGenerator(array $params = []): SitemapGenerator
    {
        return SitemapGeneratorFactory::make($params);
    }


}