<?php


namespace Exolog\Module\Sitemap;

use Exolog\Module\Routing\SystemController;
use Exolog\Module\Support\Facades\Defaults;

class SitemapController extends SystemController
{
    public function sitemapindex()
    {
        //get sitemap index
        return $this->getSitemapXML(null);
    }

    public function sitemap($index)
    {
        //get sitemap
        return $this->getSitemapXML($index);
    }

    private function getSitemapXML($index)
    {
        $params = request()->only(['namespace', 'skip_images', 'edition_id']);

        if (empty($params['edition_id'])) {
            $params['edition_id'] = Defaults::get('edition');
        }

        $params['index'] = $index;

        try {
            $xml = SitemapService::getSitemap($params);
        } catch (SitemapNotFoundException $e) {
            throw404();
        }

        if (empty($xml)) {
            throw404();
        }

        return response()->make($xml)->header('Content-Type', 'text/xml');
    }


}