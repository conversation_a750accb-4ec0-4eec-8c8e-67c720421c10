<?php


namespace Exolog\Module\Sitemap;


class Helper
{
    public static function VAR_SITEMAP_CRON()
    {
        return ECO('variables.site.sitemap_cron.name');
    }

    public static function array_to_xml(array $array, $xml = '')
    {
        foreach ($array as $node => $value) {
            if (is_array($value)) {
                $xml .= self::array_to_nodes($value, $node);
            } else {
                $xml .= sprintf('<%s>%s</%1$s>', $node, $value) . PHP_EOL;
            }
        }
        return $xml;
    }

    private static function array_to_nodes(array $rows, $node = 'row'): string
    {
        $xml = '';
        foreach ($rows as $value) {
            $xml .= sprintf('<%s>%s</%1$s>', $node, self::array_to_xml($value));
        }
        return $xml;
    }
}