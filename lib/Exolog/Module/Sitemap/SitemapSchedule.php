<?php


namespace Exolog\Module\Sitemap;


use Exolog\Module\Schedule\Model\Schedule;
use RuntimeException;

class SitemapSchedule
{
    public function activate(): void
    {
        $schedule = $this->findOrCreateSchedule();
        $schedule->activate();
    }

    public function inactivate(): void
    {
        $schedule = $this->findOrCreateSchedule();
        $schedule->inactivate();
    }

    protected function findOrCreateSchedule(): Schedule
    {
        $schedules = Schedule::onlySite()->where('command', 'exolog:site:generate-sitemap')->get();
        if ($schedules->count() > 1) {
            throw new RuntimeException('More than 1 schedule was found for generating sitemap.');
        }
        if ($schedules->count() === 0) {
            return Schedule::create([
                'command' => 'exolog:site:generate-sitemap',
                'options' => ['site_id' => ['type' => 'string', 'value' => 'auto']],
                'expression' => '0 4 * * *',
                'even_in_maintenance_mode' => 0,
                'without_overlapping' => 1,
                'on_one_server' => 1,
                'sendmail_error' => 0,
                'sendmail_success' => 0,
                'log_success' => 0,
                'log_error' => 1,
                'status' => 1,
                'run_in_background' => 1,
            ]);
        }
        return $schedules->first();
    }

}