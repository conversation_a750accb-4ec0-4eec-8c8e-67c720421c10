<?php


namespace Exolog\Module\Sitemap;


use Exception;


class SitemapIndexCacheBuilder implements CacheBuilder
{
    /**
     * @throws Exception
     */
    public function build()
    {
        foreach (SitemapService::getEditions() as $edition) {
            $params = ['edition_id' => $edition['e_id']];
            $generator = new SitemapIndexGenerator($params);
            $generator->generateAndPutToCache();
            foreach ($generator->getSitemapIndexList() as $index) {
                $indexGenerator = new SitemapIndexGenerator(array_merge($params, ['index' => $index]));
                $indexGenerator->generateAndPutToCache();
            }
        }
    }
}