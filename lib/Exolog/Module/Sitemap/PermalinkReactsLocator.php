<?php


namespace Exolog\Module\Sitemap;


use Exolog\Module\Support\Concerns\HasMakeFactory;
use Exolog\Module\Support\Facades\Site;
use Illuminate\Support\Facades\DB;

class PermalinkReactsLocator
{
    use HasMakeFactory;

    /**
     * Collect all permalink for site
     * @return array Permalinks
     */
    public function collect(): array
    {
        $sql = "SELECT
                           r.react_id,
                           r.react_subject,
                           f.form_name,
                           r.react_date_changed,
                           JSON_UNQUOTE(r.react_jdoc->'$.publish') publish,
                           p.p_url permalink,
                           p.p_fp_id,
                           r.react_edition_id edition_id 
                    FROM react r
                        JOIN form f ON (r.react_form = f.form_id)  
                        LEFT JOIN formquestion fqp ON (f.form_id = fqp.fq_form AND fqp.fq_name = 'publish')
                        /*https://gitlab.fidela.nl/fidela/exolog-reactJson/-/issues/169#note_1040
                            As a first simple step:
                            When a form doesn't have the field no_sitemap
                            The permalinks of the reacts of this form can never be in sitemap.xml
                            */
                        JOIN formquestion fqn ON (f.form_id = fqn.fq_form AND fqn.fq_name = 'no_sitemap')
                        JOIN permalinks p on (r.react_id = p.p_react_id AND p.p_type = 'PRIMARY')
                    WHERE
                          r.react_isdeleted = 0 AND
                          f.form_site_id = :site_id AND
                          (JSON_UNQUOTE(r.react_jdoc->'$.no_sitemap') is null or JSON_UNQUOTE(r.react_jdoc->'$.no_sitemap') = 0) AND 
                          /*Bug #6614 Field publish should be taken into account for sitemap.xml*/
                          (fqp.fq_id IS NULL OR (fqp.fq_id IS NOT NULL AND JSON_UNQUOTE(r.react_jdoc->'$.publish')=1))
                    ORDER BY permalink";

        return DB::select($sql, [':site_id' => Site::id()]);
    }
}