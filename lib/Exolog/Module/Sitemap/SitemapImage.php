<?php


namespace Exolog\Module\Sitemap;


use Exolog\Module\Forms\FQT\Picture\Picture;
use Illuminate\Support\Facades\DB;

class SitemapImage
{
    public static function getReactPicture($react)
    {
        //todo move this query to repository and cache
        $sql = "SELECT 
                    r.react_id,
                    fq.fq_name 
                FROM react r,
                     form f,
                     formquestion fq
                where r.react_form = f.form_id
                  and r.react_id = :react_id
                  and fq.fq_type = :fqt_id
                  and f.form_id = fq.fq_form";

        $picture_fileds = DB::select($sql, [
            ':react_id' => $react['react_id'],
            ':fqt_id' => ECO('forms.fqt.picture.fqt_id')
        ]);
        $image_section = [];
        $react = react()->find($react['react_id']);

        foreach ($picture_fileds as $picture_filed) {
            /**@var $picture Picture */
            $picture = $react->getFieldValue($picture_filed['fq_name']);
            if ($picture->getSitemap()) {
                $url = $picture->getURL();
                if ($url) {
                    $image = ['image:loc' => $url];
                    $caption = $picture->getAlt();
                    if ($caption) {
                        $image['image:caption'] = $caption;
                    }
                    $image_section[] = $image;
                }
            }
        }
        return $image_section;
    }
}