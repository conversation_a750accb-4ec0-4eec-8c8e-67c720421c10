<?php


namespace Exolog\Module\Sitemap\Listeners;


use Exolog\Module\Sitemap\Helper;
use Exolog\Module\Sitemap\SitemapSchedule;
use Exolog\Module\Vars\Events\VariableDeleted;
use Exolog\Module\Vars\Events\VariableEvent;
use Exolog\Module\Vars\Events\VariableRemoved;
use Exolog\Module\Vars\Events\VariableSaved;
use Illuminate\Events\Dispatcher;


class SitemapVariableEventSubscriber
{

    /**
     * @var SitemapSchedule
     */
    private SitemapSchedule $sitemapScheduler;

    public function __construct(SitemapSchedule $sitemapSchedule)
    {
        $this->sitemapScheduler = $sitemapSchedule;
    }

    /**
     * Register the listeners for the subscriber.
     *
     * @param Dispatcher $events
     * @return void
     */
    public function subscribe($events)
    {
        $events->listen(VariableSaved::class, function (VariableEvent $event) {
            if ($event->variable->getName() !== Helper::VAR_SITEMAP_CRON()
                || $event->variable->getType() !== 'site') {
                return;
            }
            if (!empty($event->variable->getValue())) {
                $this->sitemapScheduler->activate();
            } else {
                $this->sitemapScheduler->inactivate();
            }

        });

        $events->listen([VariableDeleted::class, VariableRemoved::class], function (VariableEvent $event) {
            if ($event->variable->getName() !== Helper::VAR_SITEMAP_CRON() ||
                $event->variable->getType() !== 'site') {
                return;
            }
            $this->sitemapScheduler->inactivate();
        });

    }


}