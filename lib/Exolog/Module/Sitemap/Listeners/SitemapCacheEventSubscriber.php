<?php


namespace Exolog\Module\Sitemap\Listeners;


use Exolog\Module\Sitemap\Events\SitemapCacheUpdated;
use Exolog\Module\Sitemap\SitemapService;
use Illuminate\Events\Dispatcher as DispatcherAlias;
use Illuminate\Support\Facades\Http;

class SitemapCacheEventSubscriber
{

    /**
     * Register the listeners for the subscriber.
     *
     * @param DispatcherAlias $events
     * @return void
     */
    public function subscribe(DispatcherAlias $events): void
    {
        $events->listen(SitemapCacheUpdated::class, function () {
            $this->pingGoogle();
        });
    }

    private function pingGoogle(): void
    {
        foreach (SitemapService::getEditions() as $edition) {
            $url = sprintf('http://www.google.com/ping?sitemap=%s/sitemap.xml',
                exolog()->getEditionDefaultUrl($edition['e_id']));
            $response = Http::get($url);
        }
    }
}