<?php


namespace Exolog\Module\Sitemap;


use Exolog\Module\Support\Concerns\HasMakeFactory;
use Exolog\Module\Support\Facades\Site;
use Illuminate\Support\Facades\DB;

class SitemapManager
{
    use HasMakeFactory;

    public function getReacts($params = []): array
    {
        $sql = "SELECT
                           r.react_id,
                           f.form_name react_form_name,
                           r.react_date_changed,
                           IF(JSON_UNQUOTE(r.react_jdoc->'$.menu_title') IS NULL 
                               or JSON_UNQUOTE(r.react_jdoc->'$.menu_title') = '',r.react_subject,
                               JSON_UNQUOTE(r.react_jdoc->'$.menu_title')) react_title,
                           JSON_UNQUOTE(r.react_jdoc->'$.publish') publish,
                           p.p_url permalink,
                           JSON_UNQUOTE(r.react_jdoc->'$.no_sitemap') no_sitemap, 
                           if(fq_publish.fq_id is null or fq_publish.fq_id = '', 0, 1) have_publish, 
                           if(fq_no_sitemap.fq_id is null or fq_no_sitemap.fq_id = '', 0, 1) have_no_sitemap,
                           r.react_edition_id
                    FROM react r
                        JOIN form f ON (r.react_form = f.form_id)  
                        JOIN formquestion fq_permalink ON (f.form_id = fq_permalink.fq_form AND fq_permalink.fq_name = 'permalink')
                        LEFT JOIN formquestion fq_publish ON (f.form_id = fq_publish.fq_form AND fq_publish.fq_name = 'publish')
                        JOIN formquestion fq_no_sitemap ON (f.form_id = fq_no_sitemap.fq_form AND fq_no_sitemap.fq_name = 'no_sitemap')
                        JOIN permalinks p on (r.react_id = p.p_react_id AND p.p_type = 'PRIMARY')
                    WHERE
                          r.react_isdeleted = 0 
                          AND f.form_site_id = :site_id
                          /*select by edition or for default select with empty edition relation*/
                          AND ((:e_id IS NOT NULL AND (r.react_edition_id = :e_id 
                                                            OR (r.react_edition_id IS NULL AND 
                                                                EXISTS (SELECT 1 
                                                                        FROM edition e 
                                                                        WHERE e.e_id = :e_id AND e.e_isdefault = 1)
                                                                )
                                                        )
                                ) OR :e_id IS NULL)
                    ORDER BY permalink";

        $values = [
            ':site_id' => Site::id(),
            ':e_id' => $params['e_id']
        ];

        $result = DB::select($sql, $values);
        foreach ($result as $key => $item) {
            $result[$key]['react_title'] = strip_tags($item['react_title']);
        }
        return $result;
    }
}