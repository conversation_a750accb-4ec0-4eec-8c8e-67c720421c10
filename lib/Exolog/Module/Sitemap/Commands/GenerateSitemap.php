<?php

namespace Exolog\Module\Sitemap\Commands;

use Exolog\Module\Console\SiteCommand;
use Exolog\Module\Sitemap\SitemapService;
use Exolog\Module\Support\Facades\Site;

class GenerateSitemap extends SiteCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'exolog:site:generate-sitemap {--site_id=auto}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate sitemap and put it to the cache.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        SitemapService::buildCache();
        $alias = Site::alias();
        $this->info("Sitemap was generated for site '$alias'");

        return 0;
    }
}