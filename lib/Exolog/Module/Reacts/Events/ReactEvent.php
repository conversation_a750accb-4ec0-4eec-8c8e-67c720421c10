<?php


namespace Exolog\Module\Reacts\Events;

/*
ReactCreating : before a record has been created.
ReactCreated : after a record has been created.
ReactUpdating : before a record is updated.
ReactUpdated : after a record has been updated.
ReactSaving : before a record is saved (either created or updated).
ReactSaved : after a record has been saved (either created or updated).
ReactDeleting : before a record is deleted or soft-deleted.
ReactDeleted : after a record has been deleted or soft-deleted.
*/

use Exolog\Module\Events\FluentExoEvent;

abstract class ReactEvent extends FluentExoEvent
{

}