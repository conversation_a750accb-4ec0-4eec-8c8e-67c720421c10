<?php

namespace Exolog\Module\Reacts\Query;

use Exolog\Core\Forms\React;
use Exolog\Module\Exceptions\ReactNotFoundException;
use Exolog\Module\Reacts\ReactSearchBuilder;
use Exolog\Module\Support\Facades\Site;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Database\Concerns\BuildsQueries;
use Illuminate\Database\Connection;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\HigherOrderBuilderProxy;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Traits\ForwardsCalls;
use InvalidArgumentException;
use RuntimeException;

/**
 * @property-read HigherOrderBuilderProxy $orWhere
 *
 * @mixin JdocQueryBuilder
 */
class ReactQueryBuilder
{
    use ForwardsCalls,
        BuildsQueries {
        sole as baseSole;
    }

    private JdocQueryBuilder $query;

    /**
     * The methods that should be returned from query builder.
     *
     * @var string[]
     */
    protected $passthru = [
        'average',
        'avg',
        'count',
        'dd',
        'doesntExist',
        'dump',
        'exists',
        'getBindings',
        'getConnection',
        'getGrammar',
        'insert',
        'insertGetId',
        'insertOrIgnore',
        'insertUsing',
        'max',
        'min',
        'raw',
        'sum',
        'toSql',
    ];
    private bool $formSet = false;

    public function __construct($isdeletet = 0)
    {
        /**
         * @var Connection $connection
         */
        $connection = app('db.connection');
        $query = new JdocQueryBuilder($connection, $connection->getQueryGrammar(), $connection->getPostProcessor());
        $query->select([
            'react.*',
            'form.*',
            DB::raw('form.form_name react_form_name'),
            DB::raw("IF(JSON_UNQUOTE(react.react_jdoc->'$.menu_title') IS NULL 
                           or JSON_UNQUOTE(react.react_jdoc->'$.menu_title') = '',react.react_subject,
                         JSON_UNQUOTE(react.react_jdoc->'$.menu_title')) react_title")
        ]);
        $query->from('react');

        if (!is_null($isdeletet)) {
            $query->where('react_isdeleted', $isdeletet);
        }

        $query->join('form', 'form.form_id', '=', 'react.react_form');
        $query->where('form.form_site_id', Site::id());
        $this->query = $query;
    }


    /**
     * @param string|integer|array|\Illuminate\Support\Collection $forms form name, form id, array of form name or array of id
     */
    public function form($forms): ReactQueryBuilder
    {
        if ($this->formSet) {
            throw new RuntimeException('You can set forms only once! Use an array to set multiple forms.');
        }
        $field = 'form.form_name';

        if ($forms instanceof \Illuminate\Support\Collection) {
            $forms = $forms->toArray();
        }
        if (collect(Arr::wrap($forms))->every(function ($val) {
            return is_numeric($val);
        })) {
            $field = 'form.form_id';
        }

        if (is_array($forms)) {
            $this->query->whereIn($field, $forms);
        } else {
            $this->query->where($field, $forms);
        }
        $this->formSet = true;
        return $this;
    }

    public function published(): ReactQueryBuilder
    {
        $this->query->where('react_vr_publish', 1);
        return $this;
    }

    public function permalink($permalink): ReactQueryBuilder
    {

        $this
            ->addSelect('p.p_id')
            ->addSelect('p.p_url')
            ->addSelect('p.p_type')
            ->addSelect('p.p_fp_id');

        // Only select p_redirect if the column exists
        if (Schema::hasColumn('permalinks', 'p_redirect')) {
            $this->addSelect('p.p_redirect');
        }

        $this->join('permalinks as p', function (JoinClause $join) use ($permalink) {
                $join->on('react.react_id', '=', 'p.p_react_id')
                    ->where('p_url', $permalink);
            });

        /*    $this->addSelect(
                DB::raw("(SELECT p_id FROM permalinks p WHERE p.p_url = ? AND p.p_react_id = react_id) permalink_id",),
            );

            $this->query->whereRaw('react_id in (SELECT p_react_id FROM permalinks p WHERE p.p_url = ? AND p.p_site_id = react_site)',
                [$permalink]);*/
        return $this;
    }

    public function edition($edition_id): ReactQueryBuilder
    {
        $this->query->whereRaw('(react_edition_id = ? 
                                       OR (react_edition_id IS NULL AND 
                                            EXISTS (SELECT 1 FROM edition e WHERE e.e_id = ? AND e.e_isdefault = 1)
                                           )
                                     )', [$edition_id, $edition_id]);
        return $this;
    }

    public function find($react_id): ?React
    {
        /** @var React */
        return $this->where('react_id', $react_id)->first();
    }

    public function findOrFail($id): React
    {
        $result = $this->find($id);

        $id = $id instanceof Arrayable ? $id->toArray() : $id;

        if (is_array($id)) {
            if (count($result) === count(array_unique($id))) {
                return $result;
            }
        } elseif (!is_null($result)) {
            return $result;
        }

        throw (new ReactNotFoundException)->setIds($id);
    }

    public function firstOrFail($columns = ['*']): ?React
    {
        if (!is_null($model = $this->first($columns))) {
            return $model;
        }
        throw new ReactNotFoundException();
    }

    public function first(): ?React
    {
        return $this->take(1)->get()->first();
    }

    public function get(array $react_options = []): \Illuminate\Support\Collection
    {
        return $this->query->get()->map(function ($value) use ($react_options) {
            $json = json_decode($value['react_jdoc'], true);
            $item = array_merge($json, $value);
            //todo move to React Factory
            $item['react_title'] = strip_tags(dv($item['menu_title'], $item['react_subject']));
            unset($item['react_jdoc']);
            $item['react_layout'] = json_decode($value['react_layout'], true);
            //$item['react_title'] = strip_tags($item['react_title']);
            return new React($item['react_form'], $item, $react_options);
        });
    }

    /**
     * Dynamically handle calls into the query instance.
     *
     * @param string $method
     * @param array $parameters
     * @return mixed
     */
    public function __call($method, $parameters)
    {

        if (in_array($method, $this->passthru)) {
            return $this->query->{$method}(...$parameters);
        }

        $this->forwardCallTo($this->query, $method, $parameters);

        return $this;
    }

    public function withPublishState(): ReactQueryBuilder
    {
        $this->addSelect(
            DB::raw("IF(fqp.fq_id IS NOT NULL, react_vr_publish, -1) AS react_publish"),
        )->leftJoin('formquestion as fqp', function (JoinClause $join) {
            $join->on('form.form_id', '=', 'fqp.fq_form')
                ->where('fq_name', 'publish');
        });
        return $this;
    }

    public function search(array $params): ReactQueryBuilder
    {
        if (count($params) === 0) {
            return $this;
        }
        /** @var ReactSearchBuilder $rsb */
        $rsb = resolve(ReactSearchBuilder::class);
        //todo mutate current query instead return new
        return $rsb->build($this, $params);
    }


    /**
     * Add a generic "order by" clause if the query doesn't already have one.
     *
     * @return void
     */
    protected function enforceOrderBy()
    {
        if (empty($this->query->orders) && empty($this->query->unionOrders)) {
            $this->orderBy('react_id');
        }
    }

    /**
     * Paginate the given query.
     *
     * @param int|null $perPage
     * @param array $columns
     * @param string $pageName
     * @param int|null $page
     * @return LengthAwarePaginator
     *
     * @throws InvalidArgumentException
     */
    public function paginate($perPage = 15, $pageName = 'page', $page = null)
    {
        $page = $page ?: Paginator::resolveCurrentPage($pageName);

        $results = ($total = $this->query->getCountForPagination())
            ? $this->forPage($page, $perPage)->get()
            : new Collection();

        return $this->paginator($results, $total, $perPage, $page, [
            'path' => Paginator::resolveCurrentPath(),
            'pageName' => $pageName,
        ]);
    }

    /**
     * Paginate the given query into a simple paginator.
     *
     * @param int|null $perPage
     * @param array $columns
     * @param string $pageName
     * @param int|null $page
     * @return \Illuminate\Contracts\Pagination\Paginator
     */
    public function simplePaginate($perPage = 15, $pageName = 'page', $page = null)
    {
        $page = $page ?: Paginator::resolveCurrentPage($pageName);

        // Next we will set the limit and offset for this query so that when we get the
        // results we get the proper section of results. Then, we'll create the full
        // paginator instances for these results with the given page and per page.
        $this->skip(($page - 1) * $perPage)->take($perPage + 1);

        return $this->simplePaginator($this->get(), $perPage, $page, [
            'path' => Paginator::resolveCurrentPath(),
            'pageName' => $pageName,
        ]);
    }

    public function exoPaginate(array $params): LengthAwarePaginator
    {
        $way = isset($params['way']) ? strtolower($params['way']) : "asc";
        $order = $params['order'] ?? 'react_id';

        $this->orderBy($order, $way);

        $page = isset($params['page']) ? (int)$params['page'] : 1;
        return $this->paginate($params['limit'], 'page', $page);
    }

    public function applyQBRules(array $qbRules): ReactQueryBuilder
    {
        (new QBRuleBuilder($this))->applyQBRules($qbRules);
        return $this;
    }

    /**
     *
     * The purpose of this method is to wrap the current query of the builder
     * in a subQuery with an alias. Ex: `select * from (query) as "as"`
     * This allows you to filter data by fields selected in the clause.
     */
    public function wrapQuery(string $as): ReactQueryBuilder
    {
        $this->query = $this->query->newQuery()->fromSub($this->query, $as);
        return $this;
    }
}