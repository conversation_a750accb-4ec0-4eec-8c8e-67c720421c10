<?php

namespace Exolog\Module\Reacts\Query;

use Exolog\Core\Forms\React;

trait Queryable
{
    private static function newQuery($isdeleted)
    {
        return new ReactQueryBuilder($isdeleted);
    }

    public static function query(): ReactQueryBuilder
    {
        return static::newQuery(0);
    }

    public static function find($react_id): ?React
    {
        return static::query()->find($react_id);
    }

    public static function findOrFail($react_id): React
    {
        return static::query()->findOrFail($react_id);
    }

    public static function first(): ?React
    {
        return static::query()->first();
    }

    public static function firstOrFail(): React
    {
        return static::query()->firstOrFail();
    }

    public static function form($forms): ReactQueryBuilder
    {
        return static::query()->form($forms);
    }

    public static function withTrashed()
    {
        return static::newQuery(null);
    }

    public static function onlyTrashed()
    {
        return static::newQuery(1);
    }


}