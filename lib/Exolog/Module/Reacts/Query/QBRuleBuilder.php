<?php

namespace Exolog\Module\Reacts\Query;

use Exolog\Module\Permalinks\Model\Permalink;

class QBRuleBuilder
{

    private ReactQueryBuilder $reactQueryBuilder;

    public function __construct(ReactQueryBuilder $reactQueryBuilder)
    {
        $this->reactQueryBuilder = $reactQueryBuilder;
    }

    private function parseQBRule(JdocQueryBuilder $query, array $rule, string $method): void
    {
        if ($rule['query']['operand'] === 'permalink') {
            $existsMethod = $method === 'where' ? 'whereExists' : 'orWhereExists';
            $query->{$existsMethod}(function (JdocQueryBuilder $q) use ($rule) {
                $q->select('p_id')
                    ->from('permalinks')
                    ->whereNotIn('permalinks.p_type', [Permalink::TYPE_HISTORY])
                    ->whereColumn('permalinks.p_react_id', 'react_id');

                if (in_array($rule['query']['operator'], ['EMPTY', 'NOT EMPTY', 'IN', 'NOT IN'])) {
                    throw new \RuntimeException('Not implemented yet!');
                }
                $q->where('permalinks.p_url', $rule['query']['operator'], $rule['query']['value']);
            });
        } else {
            if (in_array($rule['query']['operator'], ['EMPTY', 'NOT EMPTY'])) {
                $query->{$method}(function (JdocQueryBuilder $q) use ($rule) {
                    if ($rule['query']['operator'] === 'EMPTY') {
                        $q->where($rule['query']['operand'], '');
                        $q->orWhereNull($rule['query']['operand']);
                    } else {
                        $q->where($rule['query']['operand'], '<>', '');
                        $q->orWhereNotNull($rule['query']['operand']);
                    }
                });
                return;
            }
            if (in_array($rule['query']['operator'], ['IN', 'NOT IN'])) {
                $query->{$method}(function (JdocQueryBuilder $q) use ($rule) {
                    $values = explode(',', $rule['query']['value']);
                    $methodIn = $rule['query']['operator'] === 'IN' ? 'whereIn' : 'whereNotIn';
                    $q->{$methodIn}($rule['query']['operand'], $values);
                });
                return;
            }
            $query->{$method}($rule['query']['operand'], $rule['query']['operator'], $rule['query']['value']);
        }

    }

    public function applyQBRules(array $qbRules): self
    {
        if (empty($qbRules)) {
            return $this;
        }

        $group = [];
        $group['query'] = ['children' => $qbRules['children']];
        $group['query']['logicalOperator'] = $qbRules['logicalOperator'];
        $method = $qbRules['logicalOperator'] === 'AND' ? 'where' : 'orWhere';

        $this->reactQueryBuilder->where(function (JdocQueryBuilder $query) use ($method, $group) {
            $this->parseQBGroup($query, $group, $method);
        });
        return $this;
    }

    private function parseQBGroup(JdocQueryBuilder $query, array $group, string $method = 'where'): void
    {
        $query->{$method}(function (JdocQueryBuilder $subQuery) use ($group) {
            $sub_method = $group['query']['logicalOperator'] === 'AND' ? 'where' : 'orWhere';
            foreach ($group['query']['children'] as $child) {
                if ($child['type'] === 'query-builder-group') {
                    $this->parseQBGroup($subQuery, $child, $sub_method);
                } else {
                    $this->parseQBRule($subQuery, $child, $sub_method);
                }
            }
        });
    }
}