<?php

namespace Exolog\Module\Reacts\Query;

use Closure;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

/**
 * @method search(array $params)
 */
class JdocQueryBuilder extends Builder
{
    public function orWhere($column, $operator = null, $value = null)
    {
        return parent::orWhere($this->parseColumn($column), $operator, $value);
    }

    public function orWhereColumn($first, $operator = null, $second = null)
    {
        return parent::orWhereColumn($this->parseColumn($first), $operator, $this->parseColumn($second));
    }

    public function orWhereIn($column, $values)
    {
        return parent::orWhereIn($this->parseColumn($column), $values);
    }

    public function orWhereNotIn($column, $values)
    {
        return parent::orWhereNotIn($this->parseColumn($column), $values);
    }

    public function orWhereIntegerInRaw($column, $values)
    {
        return parent::orWhereIntegerInRaw($this->parseColumn($column), $values);
    }

    public function orWhereIntegerNotInRaw($column, $values)
    {
        return parent::orWhereIntegerNotInRaw($this->parseColumn($column), $values);
    }

    public function orWhereNull($column)
    {
        return parent::orWhereNull($this->parseColumn($column));
    }

    public function orWhereBetween($column, array $values)
    {
        return parent::orWhereBetween($this->parseColumn($column), $values);
    }

    public function orWhereBetweenColumns($column, array $values)
    {
        return parent::orWhereBetweenColumns($this->parseColumn($column), $values);
    }

    public function orWhereNotBetween($column, array $values)
    {
        return parent::orWhereNotBetween($this->parseColumn($column), $values);
    }

    public function orWhereNotBetweenColumns($column, array $values)
    {
        return parent::orWhereNotBetweenColumns($this->parseColumn($column), $values);
    }

    public function orWhereNotNull($column)
    {
        return parent::orWhereNotNull($this->parseColumn($column));
    }

    public function orWhereDate($column, $operator, $value = null)
    {
        return parent::orWhereDate($this->parseColumn($column), $operator, $value);
    }

    public function orWhereTime($column, $operator, $value = null)
    {
        return parent::orWhereTime($this->parseColumn($column), $operator, $value);
    }

    public function orWhereDay($column, $operator, $value = null)
    {
        return parent::orWhereDay($this->parseColumn($column), $operator, $value);
    }

    public function orWhereMonth($column, $operator, $value = null)
    {
        return parent::orWhereMonth($this->parseColumn($column), $operator, $value);
    }

    public function orWhereYear($column, $operator, $value = null)
    {
        return parent::orWhereYear($this->parseColumn($column), $operator, $value);
    }

    public function orWhereRowValues($columns, $operator, $values)
    {
        return parent::orWhereRowValues($this->parseColumns($columns), $operator, $values);
    }

    public function orWhereJsonContains($column, $value)
    {
        return parent::orWhereJsonContains($this->parseColumn($column), $value);
    }

    public function orWhereJsonDoesntContain($column, $value)
    {
        return parent::orWhereJsonDoesntContain($this->parseColumn($column), $value);
    }

    public function orWhereJsonLength($column, $operator, $value = null)
    {
        return parent::orWhereJsonLength($this->parseColumn($column), $operator, $value);
    }

    public function where($column, $operator = null, $value = null, $boolean = 'and')
    {
        return parent::where($this->parseColumn($column), $operator, $value, $boolean);
    }

    public function whereColumn($first, $operator = null, $second = null, $boolean = 'and')
    {
        return parent::whereColumn($this->parseColumn($first), $operator, $this->parseColumn($second), $boolean);
    }

    public function whereIn($column, $values, $boolean = 'and', $not = false)
    {
        return parent::whereIn($this->parseColumn($column), $values, $boolean, $not);
    }

    public function whereNotIn($column, $values, $boolean = 'and')
    {
        return parent::whereNotIn($this->parseColumn($column), $values, $boolean);
    }

    public function whereIntegerInRaw($column, $values, $boolean = 'and', $not = false)
    {
        return parent::whereIntegerInRaw($this->parseColumn($column), $values, $boolean, $not);
    }

    public function whereIntegerNotInRaw($column, $values, $boolean = 'and')
    {
        return parent::whereIntegerNotInRaw($this->parseColumn($column), $values, $boolean);
    }

    public function whereNull($columns, $boolean = 'and', $not = false)
    {
        return parent::whereNull($this->parseColumns($columns), $boolean, $not);
    }

    public function whereNotNull($columns, $boolean = 'and')
    {
        return parent::whereNotNull($this->parseColumns($columns), $boolean);
    }

    public function whereBetween($column, array $values, $boolean = 'and', $not = false)
    {
        return parent::whereBetween($this->parseColumn($column), $values, $boolean, $not);
    }

    public function whereBetweenColumns($column, array $values, $boolean = 'and', $not = false)
    {
        return parent::whereBetweenColumns($this->parseColumn($column), $values, $boolean, $not);
    }

    public function whereNotBetween($column, array $values, $boolean = 'and')
    {
        return parent::whereNotBetween($this->parseColumn($column), $values, $boolean);
    }

    public function whereNotBetweenColumns($column, array $values, $boolean = 'and')
    {
        return parent::whereNotBetweenColumns($this->parseColumn($column), $values, $boolean);
    }

    public function whereDate($column, $operator, $value = null, $boolean = 'and')
    {
        return parent::whereDate($this->parseColumn($column), $operator, $value, $boolean);
    }

    public function whereTime($column, $operator, $value = null, $boolean = 'and')
    {
        return parent::whereTime($this->parseColumn($column), $operator, $value, $boolean);
    }

    public function whereDay($column, $operator, $value = null, $boolean = 'and')
    {
        return parent::whereDay($this->parseColumn($column), $operator, $value, $boolean);
    }

    public function whereMonth($column, $operator, $value = null, $boolean = 'and')
    {
        return parent::whereMonth($this->parseColumn($column), $operator, $value, $boolean);
    }

    public function whereYear($column, $operator, $value = null, $boolean = 'and')
    {
        return parent::whereYear($this->parseColumn($column), $operator, $value, $boolean);
    }

    protected function whereSub($column, $operator, Closure $callback, $boolean)
    {
        return parent::whereSub($this->parseColumn($column), $operator, $callback, $boolean);
    }

    public function whereRowValues($columns, $operator, $values, $boolean = 'and')
    {
        return parent::whereRowValues($this->parseColumns($columns), $operator, $values, $boolean);
    }

    public function whereJsonContains($column, $value, $boolean = 'and', $not = false)
    {
        return parent::whereJsonContains($this->parseColumn($column), $value, $boolean, $not);
    }

    public function whereJsonDoesntContain($column, $value, $boolean = 'and')
    {
        return parent::whereJsonDoesntContain($this->parseColumn($column), $value, $boolean);
    }

    public function whereJsonLength($column, $operator, $value = null, $boolean = 'and')
    {
        return parent::whereJsonLength($this->parseColumn($column), $operator, $value, $boolean);
    }


    private function parseColumn($column)
    {
        if (!is_string($column)) {
            return $column;
        }
        if (Str::contains($column, '.')) {
            return $column;
        }
        if (Str::startsWith($column, '!')) {
            return Str::after($column, '!');
        }
        return Str::startsWith($column, 'react_') ? $column : 'react_jdoc->' . $column;
    }

    private function parseColumns($columns)
    {
        $result = [];
        foreach (Arr::wrap($columns) as $column) {
            $result[] = $this->parseColumn($column);
        }
        return $result;
    }

    public function orderBy($column, $direction = 'asc')
    {
        return parent::orderBy($this->parseColumn($column), $direction);
    }

    public function orderByDesc($column)
    {
        return parent::orderByDesc($this->parseColumn($column));
    }

    public function min($column)
    {
        return parent::min($this->parseColumn($column));
    }

    public function max($column)
    {
        return parent::max($this->parseColumn($column));
    }

    public function sum($column)
    {
        return parent::sum($this->parseColumn($column));
    }

    public function avg($column)
    {
        return parent::avg($this->parseColumn($column));
    }
}