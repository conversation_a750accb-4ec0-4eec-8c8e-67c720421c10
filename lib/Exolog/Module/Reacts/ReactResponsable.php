<?php

namespace Exolog\Module\Reacts;

use Illuminate\Http\Response;
use RuntimeException;
use Throwable;

trait ReactResponsable
{
    /**
     * @throws Throwable
     */
    public function render(): string
    {
        if ($this->isMailable()) {
            return $this->previewMailView()->render();
        }
        if (view()->exists('page')) {
            return view('page', ['page' => $this])->render();
        }
        throw new RuntimeException('Can not find default view "page" for render react!');
    }

    abstract public function isMailable(): bool;

    abstract public function previewMailView();

    public function toResponse($request)
    {
        return new Response($this->render(), 200, ['Content-Type' => 'text/html']);
    }
}