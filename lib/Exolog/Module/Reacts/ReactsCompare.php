<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 05.06.2020
 * Time: 17:16
 */

namespace Exolog\Module\Reacts;


use Exolog\Core\Forms\React;
use Exolog\Module\Forms\Model\Form;

class ReactsCompare
{
    /**
     * @param $reactA
     * @param $reactB
     * @return bool
     */
    public static function compare($reactA, $reactB): bool
    {
        if ($reactA === $reactB) {
            return true;
        }
        if (empty($reactA) && !empty($reactB)) {
            return false;
        }
        if (empty($reactB) && !empty($reactA)) {
            return false;
        }

        $res = array_diff_assoc(self::normalizeReact($reactA), self::normalizeReact($reactB));
        if (!empty($res)) {
            return false;
        }
        $res = array_diff_assoc(self::normalizeReact($reactB), self::normalizeReact($reactA));
        return empty($res);
    }

    /**
     * @param $react
     * @return array
     */
    public static function normalizeReact($react): array
    {

        if ($react instanceof React) {
            $react = $react->toArray();
        } else {
            $react = (array)$react;
        }

        $cleanReact = [];
        $form_id = $react['react_form'];
        $fqs = Form::findOrFail($form_id)->form_questions;
        foreach ($fqs as $fq) {
            $cleanReact[$fq['fq_name']] = $react[$fq['fq_name']];
        }
        return $cleanReact;
    }
}