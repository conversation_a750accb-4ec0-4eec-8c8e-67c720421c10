<?php

namespace Exolog\Module\Reacts;

use Exolog\Module\Reacts\Query\ReactQueryBuilder;
use InvalidArgumentException;

class ReactSearchBuilder
{

    public function build(ReactQueryBuilder $query, array $params): ReactQueryBuilder
    {
        $params['query'] = trim($params['query']);

        if (($params['form_questions'] === false) && !empty($params['query'])) {
            throw new InvalidArgumentException('"query" param should be empty for non-query request!');
        }

        $query
            ->withPublishState()
            ->addSelect(
                'u.u_email as react_user_email',
            )
            ->leftJoin('user as u', 'react_user', '=', 'u.u_id')
            ->when($params['form_questions'], function (ReactQueryBuilder $query) use ($params) {
                if (isset($params['query']) && $params['query'] !== '') {
                    $query->where(function ($query) use ($params) {
                        $query_str = '%' . $params['query'] . '%';
                        foreach ($params['form_questions'] as $fq) {
                            $query->orWhere(function ($query) use ($query_str, $fq) {
                                $query->where($fq['fq_name'], 'LIKE', $query_str);
                                $query->where('form.form_id', $fq['fq_form']);
                            });
                        }
                    });
                } else {
                    $query->whereIn('form.form_id',
                        collect($params['form_questions'])->pluck('fq_form')->unique()->toArray()
                    );
                }
            });

        return $query->wrapQuery('base')
            ->when($params['filter'], function (ReactQueryBuilder $query) use ($params) {
                foreach ($params['filter'] as $key => $value) {
                    if (!is_null($value) && $value !== '') {
                        if (in_array($key, ['react_form_name', 'react_edition_id'])) {
                            $query->where($key, $value);
                        } else {
                            $query->where($key, 'like', $value . '%');
                        }
                    }
                }
            });
    }
}