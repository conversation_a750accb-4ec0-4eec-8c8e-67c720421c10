<?php


namespace Exolog\Module\Editions\Model;


use Carbon\Carbon;
use Exolog\Module\Database\Concerns\HasSiteScope;
use Exolog\Module\Domains\Model\Domain;
use Exolog\Module\Site\Model\Site;
use Exolog\Module\Support\PHPDoc\PHPDoc_Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use RuntimeException;

/**
 * @property mixed e_id
 * @property string e_isdeleted
 * @property string e_info
 * @property string e_title
 * @property string e_isdefault
 * @property Carbon $e_date
 * @property boolean e_isclosed
 *
 * @mixin PHPDoc_Builder
 */
class Edition extends Model
{

    use HasSiteScope;
    use SoftDeletes;

    public const SITE_FIELD = 'e_site';
    public const DELETED_AT = 'e_deleted_at';

    protected $table = 'edition';
    protected $primaryKey = 'e_id';
    public $timestamps = false;
    protected $fillable = [
        'e_info',
        'e_title',
        'e_alias',
        'e_isdefault',
        'e_isclosed',
    ];

    protected $casts = [
        'e_date' => 'datetime',
        'e_site' => 'int',
        'e_isclosed' => 'bool',
    ];

    public static function getDefaultOrFail(): Edition
    {
        if (!is_null($model = static::getDefault())) {
            return $model;
        }
        throw (new ModelNotFoundException('Default edition not found!'));
    }


    protected static function booted()
    {
        static::creating(static function (Edition $edition) {
            $edition->e_date = now();
        });

        static::deleting(static function (Edition $edition) {
            if ($edition->e_isdefault) {
                throw new RuntimeException('The "default" edition cannot be deleted.');
            }
        });
    }

    public function site(): BelongsTo
    {
        return $this->belongsTo(Site::class, 'e_site');
    }

    public function setDefault(): void
    {
        static::query()
            ->where('e_id', '!=', $this['e_id'])
            ->update(['e_isdefault' => 0]);

        $this['e_isdefault'] = 1;
        $this->save();
    }

    public static function getDefault(): ?Edition
    {
        /** @var Edition */
        return static::query()->where('e_isdefault', 1)->first();
    }

    public function getDefaultDomain(): ?Domain
    {
        /** @var Domain */
        return Domain::query()
            ->where('domain_edition_id', $this->e_id)
            ->where('domain_isdefault', 1)
            ->first();
    }
}