<?php

namespace Exolog\Module\Console;

use Illuminate\Console\Command;
use InvalidArgumentException;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class SiteCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'exolog:site:* {--site_id=}';


    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $site_id = $this->option('site_id');
        if (empty($site_id)) {
            throw new InvalidArgumentException("The site command must have the argument '--site_id'.");
        }
        coreInit()->resolveSite($site_id);

        return parent::execute($input, $output);
    }

}