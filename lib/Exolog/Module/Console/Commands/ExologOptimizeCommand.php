<?php

namespace Exolog\Module\Console\Commands;

use Illuminate\Console\Command;

class ExologOptimizeCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'exolog:optimize';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cache the framework bootstrap files';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $this->call('config:cache');
        //TODO fix cache:route
        //$this->call('route:cache');
        $this->call('exolog:eco:cache');

        $this->info('Files cached successfully!');
    }
}
