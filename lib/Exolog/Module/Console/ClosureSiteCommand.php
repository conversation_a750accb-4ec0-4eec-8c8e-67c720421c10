<?php

namespace Exolog\Module\Console;

use Illuminate\Foundation\Console\ClosureCommand;
use Illuminate\Support\Str;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ClosureSiteCommand extends ClosureCommand
{

    protected function getSiteAlias()
    {
        return Str::of($this->signature)->explode(':')[1];
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $site_alias = $this->getSiteAlias();
        coreInit()->resolveSite($site_alias);

        return parent::execute($input, $output);
    }

}