<?php

namespace Exolog\Module\Console;

use Closure;
use Exolog\Module\Base\Bootstrap\HandleExceptions;
use Exolog\Module\Base\Bootstrap\RegisterProviders;
use Exolog\Module\Http\UseExologSubApplication;
use Exolog\Module\Site\Model\Site;
use Illuminate\Console\Application as Artisan;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Bootstrap\BootProviders;
use Illuminate\Foundation\Bootstrap\LoadConfiguration;
use Illuminate\Foundation\Bootstrap\LoadEnvironmentVariables;
use Illuminate\Foundation\Bootstrap\RegisterFacades;
use Illuminate\Foundation\Bootstrap\SetRequestForConsole;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use ReflectionException;
use RuntimeException;

class Kernel extends ConsoleKernel
{
    use UseExologSubApplication;

    protected $bootstrappers = [
        LoadEnvironmentVariables::class,
        LoadConfiguration::class,
        HandleExceptions::class,
        RegisterFacades::class,
        SetRequestForConsole::class,
        RegisterProviders::class,
        BootProviders::class,
    ];
    protected ?string $currentPushSiteAlias = null;

    /**
     * Define the application's command schedule.
     *
     * @param Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        //$schedule->command('every_1min')->everyMinute();
    }

    public function handle($input, $output = null)
    {
        //skip load commands on migration
        if (in_array($input->getFirstArgument(), ['migrate', 'optimize'], true)) {
            $this->commandsLoaded = true;
        }
        return parent::handle($input, $output);
    }

    public function queue($command, array $parameters = [])
    {
        return QueuedCommand::dispatch(func_get_args());
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        //$this->load(__DIR__.'/Commands');
        //$this->load(base_path('exologadmin.webkracht.nl/class/Console/Commands'));

        $this->includeSitesCommands();
        require base_path('routes/console.php');
    }

    /**
     * @throws ReflectionException
     */
    /*protected function loadSite($site_alias)
    {
        $paths = base_path("homepages.webkracht.nl/htdocs/sites/$site_alias/class/Commands");

        $paths = array_unique(Arr::wrap($paths));

        $paths = array_filter($paths, function($path) {
            return is_dir($path);
        });

        if (empty($paths)) {
            return;
        }

        $namespace = $this->app->getNamespace();

        foreach ((new Finder)->in($paths)->files() as $command) {
            $command = $namespace . str_replace(
                    ['/', '.php'],
                    ['\\', ''],
                    Str::after($command->getRealPath(),
                        realpath(base_path("homepages.webkracht.nl/htdocs/sites/$site_alias/class")) . DIRECTORY_SEPARATOR)
                );

            if (is_subclass_of($command, Command::class) &&
                !(new ReflectionClass($command))->isAbstract()) {
                Artisan::starting(function($artisan) use ($command) {
                    $artisan->resolve($command);
                });
            }
        }
    }*/

    public function commandSite($signature, Closure $callback): ClosureSiteCommand
    {
        if (!$this->currentPushSiteAlias) {
            throw new RuntimeException('You use method "commandSite" in incorrect context!');
        }
        $signature = 'site:' . $this->currentPushSiteAlias . ':' . $signature;
        $command = new ClosureSiteCommand($signature, $callback);

        Artisan::starting(function ($artisan) use ($command) {
            $artisan->add($command);
        });

        return $command;
    }

    protected function includeSitesCommands(): void
    {
        Site::all()->each(function (Site $site) {
            $alias = $site->site_alias;
            $this->currentPushSiteAlias = $site->site_alias;
            if ($path = realpath(base_path("homepages.webkracht.nl/htdocs/sites/$alias/config/routes.console.php"))) {
                require $path;
            }
        });
        $this->currentPushSiteAlias = null;
    }
}
