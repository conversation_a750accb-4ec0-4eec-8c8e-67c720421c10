<?php


namespace Exolog\Module\Http;


use Exolog\Dealer\Routing\Middleware\AuthenticateSiteOwner;
use Exolog\Dealer\Routing\Middleware\AuthenticateSuper;
use Exolog\Dealer\Routing\Middleware\DealerInitSite;
use Exolog\Module\Base\Application;
use Exolog\Module\Base\Bootstrap\HandleExceptions;
use Exolog\Module\Base\Bootstrap\RegisterProviders;
use Exolog\Module\Http\Middleware\Debug;
use Exolog\Module\Http\Middleware\DecryptParameters;
use Exolog\Module\Http\Middleware\EditionClosed;
use Exolog\Module\Http\Middleware\PermalinkRedirect;
use Exolog\Module\Http\Middleware\TrimStrings;
use Exolog\Module\Routing\Middleware\Authenticate;
use Exolog\Module\Routing\Middleware\LegacyPdfMode;
use Exolog\Module\Routing\Router;
use Illuminate\Auth\Middleware\Authorize;
use Illuminate\Contracts\Auth\Middleware\AuthenticatesRequests;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Bootstrap\BootProviders;
use Illuminate\Foundation\Bootstrap\LoadConfiguration;
use Illuminate\Foundation\Bootstrap\LoadEnvironmentVariables;
use Illuminate\Foundation\Bootstrap\RegisterFacades;
use Illuminate\Foundation\Http\Kernel as KernelFoundation;
use Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull;
use Illuminate\Foundation\Http\Middleware\ValidatePostSize;
use Illuminate\Http\Middleware\SetCacheHeaders;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Routing\Middleware\ThrottleRequests;
use Illuminate\Routing\Pipeline;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Facade;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Spatie\ResponseCache\Middlewares\CacheResponse;
use Spatie\ResponseCache\Middlewares\DoNotCacheResponse;


class Kernel extends KernelFoundation
{
    use UseExologSubApplication;

    /**
     * The bootstrap classes for the application.
     *
     * @var array
     */
    protected $bootstrappers = [
        LoadEnvironmentVariables::class,
        LoadConfiguration::class,
        HandleExceptions::class,
        RegisterFacades::class,
        RegisterProviders::class,
        BootProviders::class,
    ];

    /**
     * The application's middleware stack.
     *
     * @var array
     */
    protected $middleware = [
        PermalinkRedirect::class,
        Debug::class,
        DecryptParameters::class,
        TrimStrings::class,
        ConvertEmptyStringsToNull::class,
        ValidatePostSize::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [
        //TODO Create common web api groups
        //web
        //   exolog-web
        //      system-web
        //      system-site-web
        //         site-web
        //   dealer-web
        //api
        //   exolog-api
        //      system-api
        //      system-site-api
        //         site-api
        //   dealer-api
        //
        'system-dealer' => [
            \Exolog\Module\Routing\Middleware\EncryptCookies::class,
            AddQueuedCookiesToResponse::class,
            StartSession::class,
            ShareErrorsFromSession::class,
            SubstituteBindings::class,
        ],
        'system-web' => [
            \Exolog\Module\Routing\Middleware\EncryptCookies::class,
            AddQueuedCookiesToResponse::class,
            StartSession::class,
            ShareErrorsFromSession::class,
            SubstituteBindings::class,
        ],
        'system-api' => [
            \Exolog\Module\Routing\Middleware\EncryptCookies::class,
            AddQueuedCookiesToResponse::class,
            StartSession::class,
            ShareErrorsFromSession::class,
            SubstituteBindings::class,
        ],
        'system-site-web' => [
            \Exolog\Module\Routing\Middleware\EncryptCookies::class,
            AddQueuedCookiesToResponse::class,
            StartSession::class,
            ShareErrorsFromSession::class,
            SubstituteBindings::class,
            EditionClosed::class,
            LegacyPdfMode::class
        ],
        'system-site-api' => [
            \Exolog\Module\Routing\Middleware\EncryptCookies::class,
            AddQueuedCookiesToResponse::class,
            StartSession::class,
            ShareErrorsFromSession::class,
            SubstituteBindings::class,
        ]
    ];

    /**
     * The application's route middleware.
     *
     * @var array
     */
    protected $routeMiddleware = [
        'auth-dealer' => \Exolog\Dealer\Routing\Middleware\Authenticate::class,
        'auth-super-dealer' => AuthenticateSuper::class,
        'dealer-set-site' => DealerInitSite::class,
        'site-owner-dealer' => AuthenticateSiteOwner::class,

        'auth' => Authenticate::class,

        'cache.headers' => SetCacheHeaders::class,
        'doNotCacheResponse' => DoNotCacheResponse::class,
        'cacheResponse' => CacheResponse::class,
    ];

    /**
     * The priority-sorted list of middleware.
     *
     * Forces non-global middleware to always be in the given order.
     *
     * @var array
     */
    protected $middlewarePriority = [
        EncryptCookies::class,
        StartSession::class,
        ShareErrorsFromSession::class,
        AuthenticatesRequests::class,
        ThrottleRequests::class,
        AuthenticateSession::class,
        SubstituteBindings::class,
        Authorize::class,
    ];


    /**
     * Create a new HTTP kernel instance.
     * @param \Illuminate\Contracts\Foundation\Application $app
     * @param Router $router
     */
    public function __construct(Application $app, Router $router)
    {
        parent::__construct($app, $router);
    }

    protected function sendRequestThroughRouter($request)
    {
        $this->app->instance('request', $request);

        Facade::clearResolvedInstance('request');

        $this->bootstrap();
        //TODo make Kernel for dealer? and move it bootstrap
        if (defined('APP_DEALER')) {
            $this->initDealer();
        } else {
            $this->resolveSiteByRequest($request);
        }

        return (new Pipeline($this->app))
            ->send($request)
            ->through($this->app->shouldSkipMiddleware() ? [] : $this->middleware)
            ->then($this->dispatchToRouter());
    }


}