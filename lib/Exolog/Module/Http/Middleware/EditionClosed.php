<?php


namespace Exolog\Module\Http\Middleware;


use Exolog\Module\Http\Request;
use Exolog\Module\Support\Facades\Auth;
use Exolog\Module\Support\Facades\Defaults;

class EditionClosed
{
    public function handle(Request $request, $next)
    {
        if (($request->path() !== '404') && !defined('APP_DEALER') && Defaults::getEdition()->e_isclosed && !Auth::isAdmin()) {
            throw404();
        }
        return $next($request);
    }
}