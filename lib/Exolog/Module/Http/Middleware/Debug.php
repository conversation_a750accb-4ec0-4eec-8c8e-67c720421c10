<?php


namespace Exolog\Module\Http\Middleware;


use Illuminate\Filesystem\Filesystem;
use RuntimeException;

class Debug
{
    /**
     * @var Filesystem
     */
    private Filesystem $files;

    /**
     * @param Filesystem $files
     */
    public function __construct(Filesystem $files)
    {
        $this->files = $files;
    }

    public function handle($request, $next)
    {
        if ($request->input('debug')) {
            $this->viewsClear();
        }
        return $next($request);
    }

    private function viewsClear()
    {
        $path = app('config')['view.compiled'];

        if (!$path) {
            throw new RuntimeException('View path not found.');
        }

        foreach ($this->files->glob("{$path}/*") as $view) {
            $this->files->delete($view);
        }
    }
}