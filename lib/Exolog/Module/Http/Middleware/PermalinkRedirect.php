<?php


namespace Exolog\Module\Http\Middleware;


use Exolog\Module\Permalinks\Model\Permalink;
use Exolog\Module\Support\Facades\Defaults;

class PermalinkRedirect
{

    public function handle($request, $next)
    {
        $page = Defaults::getCurrentReact();
        if ($page && $page['p_redirect']) {
            if ($page['p_redirect'] === Permalink::TYPE_PRIMARY) {
                if ($primary = $page->getPermalink()->primary()) {
                    return redirect($primary->getURL(), 301);
                }
                throw404();
            } else {
                return redirect($page['p_redirect'], 301);
            }
        }
        return $next($request);
    }
}