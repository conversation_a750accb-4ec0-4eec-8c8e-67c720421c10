<?php

namespace Exolog\Module\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\ParameterBag;

class DecryptParameters
{
    protected array $parameters = [
        '_formconf'
    ];

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $this->decrypt($request);

        return $next($request);
    }

    private function decrypt(Request $request)
    {
        $this->decryptParameterBag($request->request);
    }


    /**
     * Clean the data in the parameter bag.
     *
     * @param ParameterBag $bag
     * @return void
     */
    protected function decryptParameterBag(ParameterBag $bag)
    {
        foreach ($this->parameters as $parameter) {
            if ($bag->has($parameter)) {
                $bag->set(trim($parameter, '_'), decrypt($bag->get($parameter)));
            }
        }
    }

}