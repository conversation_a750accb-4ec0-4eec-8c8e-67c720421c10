<?php


namespace Exolog\Module\Http;

use Illuminate\Http\Request as IlluminateRequest;
use Illuminate\Translation\Translator;

class Request extends IlluminateRequest
{
    public function validateWithAttributes(array $rules, $messages = [], ...$params): array
    {
        /** @var Translator $translator */
        $translator = trans();
        $messages = array_merge($messages, $translator->get('validation-attributes'));
        return $this->validate($rules, $messages, ...$params);
    }
}