<?php

namespace Exolog\Module\Http;

use Exolog\Dealer\Dealer;
use Exolog\Module\Base\Bootstrap\LoadSiteConfiguration;
use Exolog\Module\Base\Bootstrap\RegisterSiteProviders;
use Exolog\Module\Routing\RouteServiceProvider;
use Illuminate\Foundation\Bootstrap\RegisterFacades;

trait UseExologSubApplication
{
    /**
     * The bootstrap classes for the site.
     *
     * @var array
     */
    protected $siteBootstrappers = [
        LoadSiteConfiguration::class,
        RegisterFacades::class,
        //After application boot we can only register provider with auto boot
        RegisterSiteProviders::class,
    ];

    /**
     * Add middleware to the router's middleware stack.
     *
     * @param array $routeMiddleware
     */
    protected function appendRouteMiddleware(array $routeMiddleware)
    {
        $this->routeMiddleware = array_merge($this->routeMiddleware, $routeMiddleware);
        $this->syncMiddlewareToRouter();
    }

    public function registrSiteMiddleware()
    {
        if (!empty($this->app->config['site.middleware'])) {
            foreach ($this->app->config['site.middleware'] as $middleware) {
                $this->pushMiddleware($middleware);
            }
        }

        if (!empty($this->app->config['site.routeMiddleware'])) {
            $this->appendRouteMiddleware($this->app->config['site.routeMiddleware']);
        }

        if (!empty($this->app->config['site.middlewareGroups'])) {
            foreach ($this->app->config['site.middlewareGroups'] as $group => $middlewareGroup) {
                if (!isset($this->middlewareGroups[$group])) {
                    $this->middlewareGroups[$group] = [];
                }
                foreach ($middlewareGroup as $middleware) {
                    $this->appendMiddlewareToGroup($group, $middleware);
                }
            }
        }
    }

    protected function bootstrapSubApp()
    {
        //TODO Fix this (Rework bootstrap process)
        if (!defined('APP_DEALER')) {
            $this->app->bootstrapWith($this->siteBootstrappers);
            $this->registrSiteMiddleware();
        }
        $this->app->register(new RouteServiceProvider($this->app), true);
    }

    public function initSite()
    {
        $this->bootstrapSubApp();
        $this->app->initSite();
    }

    public function initDealer()
    {
        app()->singleton(Dealer::class);
        app()->alias(Dealer::class, 'dealer');

        $this->bootstrapSubApp();
    }

    protected function resolveSiteByRequest(Request $request)
    {
        //Detect and define current site/edition/page
        coreInit()->resolveSiteByRequest($request);
    }
}