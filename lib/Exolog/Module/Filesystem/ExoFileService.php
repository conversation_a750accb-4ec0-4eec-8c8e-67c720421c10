<?php

namespace Exolog\Module\Filesystem;

use Exolog\Module\Support\Encrypter;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;
use InvalidArgumentException;

class ExoFileService
{

    public const DISK_DELIMITER = '::';
    private Encrypter $encrypter;

    public function __construct(Encrypter $encrypter)
    {
        $this->encrypter = $encrypter;
    }

    /**
     * Extract base file name from fileId
     * @retun string 'picture.jpg'
     */
    public function basename($fileId): string
    {
        [$disk, $path] = $this->parseFileId($fileId);
        if ($disk === 'DB') {
            throw new InvalidArgumentException('DB files not supported!');
        }
        return pathinfo($path, PATHINFO_BASENAME);
    }

    /**
     * Return real path to file
     * @param $fileId
     * @return string|null
     */
    public function path($fileId): ?string
    {
        [$disk, $path] = $this->parseFileId($fileId);
        if ($disk === 'DB') {
            throw new InvalidArgumentException('DB files not supported!');
        }
        return Storage::disk($disk)->path($path);
    }

    /**
     * Parse fileId and return array with DB and path to file
     * @param $fileId
     * @return array
     */
    public function parseFileId($fileId): array
    {
        if (Str::contains($fileId, static::DISK_DELIMITER)) {
            return [
                (string)Str::of($fileId)->before(static::DISK_DELIMITER),
                (string)Str::of($fileId)->after(static::DISK_DELIMITER)
            ];
        }

        return ['DB', $fileId];
    }

    /**
     *
     * Compose fileId from disk,path and filename
     * @param $disk
     * @param $path
     * @param $filename
     * @return string
     */
    public function composeFileId($disk, $path, $filename = null): string
    {
        $fileId = $disk . static::DISK_DELIMITER . $path;
        if ($filename) {
            $fileId = Str::finish($fileId, '/');
            $fileId .= $filename;
        }
        return $fileId;
    }


    /**
     * @param array $params
     * @return string
     */
    public function encodeFileId(array $params): string
    {
        return urlencode($this->encrypter->encrypt($params));
    }

    /**
     * @param $hash
     * @return array|false|string
     */
    public function decodeFileId($hash)
    {
        $params = $this->encrypter->decrypt($hash);
        $params['t'] = $this->decodeTransform($params['t']);
        return $params;
    }

    public function decodeTransform($src)
    {
        if (is_string($src)) {
            $t = [];
            preg_match_all("/(\d*)([a-z])/m", $src, $match, PREG_SET_ORDER);
            foreach ($match as $item) {
                if (isset($item[1])) {
                    $t[$item[2]] = $item[1];
                }
            }
            return $t;
        }
        return $src;
    }


    /**
     * Create a streamed download response for a given file.
     * */
    public function download($fileId, $name = null, array $headers = [])
    {
        [$disk, $path] = $this->parseFileId($fileId);
        if ($disk === 'DB') {
            throw new InvalidArgumentException('DB files not supported!');
        }
        return Storage::disk($disk)->download($path, $name, $headers);
    }

    public function url($fileId): string
    {
        return URL::route('media.file',
            ['hash' => $this->encodeFileId(['f' => $fileId]), 'path' => $this->basename($fileId)]);
    }
}