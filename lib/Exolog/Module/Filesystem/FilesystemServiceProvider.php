<?php


namespace Exolog\Module\Filesystem;


use Exolog\Module\Support\Facades\Path;
use Illuminate\Support\Facades\Config;

class FilesystemServiceProvider extends \Illuminate\Filesystem\FilesystemServiceProvider
{
    public function register()
    {
        parent::register();
    }

    public function initSite()
    {
        Config::set('filesystems.disks.site', [
            'driver' => 'local',
            'root' => Path::to('site'),
            'url' => Path::web('site'),
        ]);

        Config::set('filesystems.disks.media', [
            'driver' => 'local',
            'root' => Path::to('site', 'media'),
            'url' => Path::web('media'),
            'public' => true
        ]);

        Config::set('filesystems.disks.protected', [
            'driver' => 'local',
            'root' => Path::to('site', 'protected'),
        ]);

        $disks = Config::get('site.disks', []);

        foreach ($disks as $key => $disk) {
            Config::set("filesystems.disks.$key", $disk);
        }
    }

    public function boot()
    {
        /* Storage::extend('dbfile', function ($app, $config) {
             return new Filesystem(new DBFileAdapter());
         });*/
    }
}