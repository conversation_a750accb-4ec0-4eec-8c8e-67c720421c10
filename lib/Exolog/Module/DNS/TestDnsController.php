<?php

namespace Exolog\Module\DNS;

use Exolog\Module\DNS\DnsProviderFactory;
use Exolog\Module\Mail\PosteDnsManager;
use Exolog\Module\Mail\PosteApiService;
use Exolog\Module\Routing\SystemController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Exception;

class TestDnsController extends SystemController
{
    private PosteDnsManager $dnsManager;
    private PosteApiService $posteService;

    public function __construct(PosteApiService $posteService)
    {
        $this->posteService = $posteService;
        $this->dnsManager = new PosteDnsManager($posteService);
    }

    /**
     * Test DNS record operations
     * 
     * Usage:
     * GET /test-dns/add/example.com - Add all mail DNS records
     * GET /test-dns/remove/example.com - Remove all mail DNS records
     * GET /test-dns/single/add/example.com?type=MX&value=mail.dc2.exolog.tech - Add single record
     * GET /test-dns/single/remove/example.com?type=MX&name=example.com - Remove single record
     */
    public function testDnsOperation(Request $request, string $action, string $domain)
    {
        try {
            Log::info('Testing DNS operation', [
                'action' => $action,
                'domain' => $domain,
                'request_params' => $request->all()
            ]);

            $result = [];
            
            switch ($action) {
                case 'add':
                    $result = $this->addAllMailRecords($domain);
                    break;
                    
                case 'remove':
                    $result = $this->removeAllMailRecords($domain);
                    break;
                    
                default:
                    return response()->json([
                        'error' => 'Invalid action. Use: add, remove',
                        'usage' => [
                            'add_all' => '/test-dns/add/example.com',
                            'remove_all' => '/test-dns/remove/example.com',
                            'single_add' => '/test-dns/single/add/example.com?type=MX&value=mail.dc2.exolog.tech',
                            'single_remove' => '/test-dns/single/remove/example.com?type=MX&name=example.com'
                        ]
                    ], 400);
            }

            return response()->json([
                'success' => true,
                'action' => $action,
                'domain' => $domain,
                'result' => $result,
                'message' => "DNS operation '$action' completed for domain '$domain'"
            ]);

        } catch (Exception $e) {
            Log::error('DNS test operation failed', [
                'action' => $action,
                'domain' => $domain,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'action' => $action,
                'domain' => $domain,
                'error' => $e->getMessage(),
                'message' => "DNS operation '$action' failed for domain '$domain'"
            ], 500);
        }
    }

    /**
     * Test single DNS record operations
     */
    public function testSingleRecord(Request $request, string $action, string $domain)
    {
        try {
            $type = $request->get('type', 'TXT');
            $name = $request->get('name', $domain);
            $value = $request->get('value', 'test-record-value');
            $ttl = $request->get('ttl', 900);

            Log::info('Testing single DNS record operation', [
                'action' => $action,
                'domain' => $domain,
                'type' => $type,
                'name' => $name,
                'value' => $value,
                'ttl' => $ttl
            ]);

            $dnsProvider = DnsProviderFactory::create($domain);
            
            if (!$dnsProvider->exists()) {
                return response()->json([
                    'success' => false,
                    'error' => "Domain '$domain' not found in DNS provider",
                    'message' => "Make sure the domain is added to Cloudflare"
                ], 404);
            }

            $record = [
                'name' => $name,
                'type' => $type,
                'value' => $value,
                'ttl' => $ttl
            ];

            $result = null;
            
            switch ($action) {
                case 'add':
                    $result = $dnsProvider->createRecord($record);
                    break;
                    
                case 'update':
                    $result = $dnsProvider->updateRecord($record);
                    break;
                    
                case 'remove':
                    $result = $dnsProvider->deleteRecord($record);
                    break;
                    
                default:
                    return response()->json([
                        'error' => 'Invalid action. Use: add, update, remove'
                    ], 400);
            }

            return response()->json([
                'success' => true,
                'action' => $action,
                'domain' => $domain,
                'record' => $record,
                'result' => $result,
                'message' => "Single DNS record operation '$action' completed"
            ]);

        } catch (Exception $e) {
            Log::error('Single DNS record test failed', [
                'action' => $action,
                'domain' => $domain,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'action' => $action,
                'domain' => $domain,
                'error' => $e->getMessage(),
                'message' => "Single DNS record operation '$action' failed"
            ], 500);
        }
    }

    /**
     * List all DNS records for a domain
     */
    public function listRecords(string $domain)
    {
        try {
            Log::info('Listing DNS records', ['domain' => $domain]);

            $dnsProvider = DnsProviderFactory::create($domain);
            
            if (!$dnsProvider->exists()) {
                return response()->json([
                    'success' => false,
                    'error' => "Domain '$domain' not found in DNS provider",
                    'message' => "Make sure the domain is added to Cloudflare"
                ], 404);
            }

            $records = $dnsProvider->getRecords();

            return response()->json([
                'success' => true,
                'domain' => $domain,
                'total_records' => count($records),
                'records' => $records,
                'message' => "Retrieved " . count($records) . " DNS records for domain '$domain'"
            ]);

        } catch (Exception $e) {
            Log::error('Failed to list DNS records', [
                'domain' => $domain,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'domain' => $domain,
                'error' => $e->getMessage(),
                'message' => "Failed to list DNS records for domain '$domain'"
            ], 500);
        }
    }

    /**
     * Add all mail DNS records for a domain
     */
    private function addAllMailRecords(string $domain): array
    {
        $success = $this->dnsManager->addMailDnsRecords($domain);
        
        return [
            'operation' => 'add_all_mail_records',
            'success' => $success,
            'records_added' => [
                'MX' => "$domain → " . config('services.poste.mail_domain'),
                'SPF' => "$domain → v=spf1 a mx -all",
                'DMARC' => "_dmarc.$domain → v=DMARC1; p=none",
                'DKIM' => "[selector]._domainkey.$domain → [public key from Poste]"
            ]
        ];
    }

    /**
     * Remove all mail DNS records for a domain
     */
    private function removeAllMailRecords(string $domain): array
    {
        $success = $this->dnsManager->removeMailDnsRecords($domain);
        
        return [
            'operation' => 'remove_all_mail_records',
            'success' => $success,
            'records_removed' => [
                'MX' => $domain,
                'SPF' => $domain,
                'DMARC' => "_dmarc.$domain",
                'DKIM' => "[selector]._domainkey.$domain"
            ]
        ];
    }
}
