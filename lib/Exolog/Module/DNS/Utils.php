<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 18.12.2019
 * Time: 14:08
 */

namespace Exolog\Module\DNS;


class Utils
{
    public static function extractTLD($host)
    {
        $myhost = strtolower(trim($host));
        $count = substr_count($myhost, '.');
        if ($count === 2) {
            if (strlen(explode('.', $myhost)[1]) > 3) {
                $myhost = explode('.', $myhost, 2)[1];
            }
        } else {
            if ($count > 2) {
                $myhost = self::extractTLD(explode('.', $myhost, 2)[1]);
            }
        }
        return $myhost;
    }

    public static function getParentDomain($host)
    {
        $myhost = strtolower(trim($host));
        $count = substr_count($myhost, '.');
        if ($count === 2) {
            if (strlen(explode('.', $myhost)[1]) > 3) {
                $myhost = explode('.', $myhost, 2)[1];
            }
        } else {
            if ($count > 2) {
                $myhost = explode('.', $myhost, 2)[1];
            }
        }
        return $myhost;
    }

    public static function getHostAddr($domain)
    {
        $lines = [];
        $command = 'dig +short ' . ($domain) . ' @*******';
        exec($command, $lines);
        if (empty($lines)) {
            return false;
        }
        return end($lines);
    }

    /**
     * Check if system allowed to execute commands. Return true or string with error message
     * @return bool|string
     */
    private static function execEnabled()
    {
        if (!function_exists('exec')) {
            return 'missing_function_exec';
        }
        $disabled = explode(',', ini_get('disable_functions'));
        if (in_array('exec', $disabled)) {
            return 'disabled_function_exec';
        }
        $lines = [];
        $command = 'dig -v 2>&1 | grep "not found" ';
        exec($command, $lines);
        if (!empty($lines)) {
            return 'dig not found';
        }
        return true;
    }
}
