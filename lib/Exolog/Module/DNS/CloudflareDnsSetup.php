<?php

namespace Exolog\Module\DNS;

use Cloudflare\API\Adapter\Guzzle;
use Cloudflare\API\Auth\APIToken;
use Cloudflare\API\Endpoints\Zones;
use Exception;
use Illuminate\Support\Facades\Log;
use RuntimeException;

/**
 * Cloudflare DNS Setup Service
 * Handles complete DNS setup: A record + www CNAME (both with proxy enabled)
 * Based on working Python implementation
 */
class CloudflareDnsSetup
{
    private string $domain_name;
    private Zones $cfZones;
    private string $zoneID;
    private Guzzle $adapter;

    public function __construct(string $domain_name)
    {
        Log::info('🔧 CloudflareDnsSetup: Initializing', ['domain' => $domain_name]);
        
        $this->domain_name = $domain_name;
        
        // Use the working API token
        $tokenValue = config('services.cloudflare.token') ?: '****************************************';
        $token = new APIToken($tokenValue);
        $this->adapter = new Guzzle($token);
        $this->cfZones = new Zones($this->adapter);
        
        Log::info('🔧 CloudflareDnsSetup: Using API token', [
            'domain' => $domain_name,
            'token_length' => strlen($tokenValue)
        ]);
        
        // Get zone ID for the domain
        Log::info('🔧 CloudflareDnsSetup: Getting zone ID', ['domain' => $domain_name]);
        
        $response = $this->cfZones->listZones($domain_name);
        
        Log::info('🔧 CloudflareDnsSetup: Zone lookup response', [
            'domain' => $domain_name,
            'total_count' => $response->result_info->total_count ?? 0
        ]);
        
        if ($response->result_info->total_count > 0) {
            foreach ($response->result as $zone) {
                Log::info('🔧 CloudflareDnsSetup: Checking zone', [
                    'domain' => $domain_name,
                    'zone_name' => $zone->name,
                    'zone_id' => $zone->id
                ]);
                
                if ($zone->name === $domain_name) {
                    $this->zoneID = $zone->id;
                    Log::info('🔧 CloudflareDnsSetup: Found matching zone', [
                        'domain' => $domain_name,
                        'zone_id' => $this->zoneID
                    ]);
                    break;
                }
            }
        }
        
        if (empty($this->zoneID)) {
            Log::error('🔧 CloudflareDnsSetup: Domain not found in Cloudflare', ['domain' => $domain_name]);
            throw new RuntimeException("Domain '{$domain_name}' not found in Cloudflare");
        }
        
        Log::info('🔧 CloudflareDnsSetup: Initialization complete', [
            'domain' => $domain_name,
            'zone_id' => $this->zoneID
        ]);
    }

    /**
     * Complete DNS setup: A record + www CNAME (both with proxy enabled)
     */
    public function setupDnsRecords(string $serverIp): array
    {
        Log::info('🔧 CloudflareDnsSetup: Starting complete DNS setup', [
            'domain' => $this->domain_name,
            'server_ip' => $serverIp
        ]);
        
        $results = [];
        
        try {
            // Step 1: Setup main domain A record with proxy
            Log::info('🔧 CloudflareDnsSetup: Step 1 - Setting up main A record', [
                'domain' => $this->domain_name,
                'ip' => $serverIp
            ]);
            
            if ($this->setupMainARecord($serverIp)) {
                $results[] = "Main A record created/updated with proxy enabled";
                Log::info('✅ Main A record setup successful', ['domain' => $this->domain_name]);
            } else {
                $results[] = "Warning: Failed to setup main A record";
                Log::warning('❌ Main A record setup failed', ['domain' => $this->domain_name]);
            }
            
            // Step 2: Setup www CNAME with proxy
            Log::info('🔧 CloudflareDnsSetup: Step 2 - Setting up www CNAME', [
                'domain' => $this->domain_name
            ]);
            
            if ($this->setupWwwCname()) {
                $results[] = "www CNAME record created/updated with proxy enabled";
                Log::info('✅ www CNAME setup successful', ['domain' => $this->domain_name]);
            } else {
                $results[] = "Warning: Failed to setup www CNAME";
                Log::warning('❌ www CNAME setup failed', ['domain' => $this->domain_name]);
            }
            
            Log::info('🔧 CloudflareDnsSetup: Complete DNS setup finished', [
                'domain' => $this->domain_name,
                'results' => $results
            ]);
            
            return $results;
            
        } catch (Exception $e) {
            Log::error('🔧 CloudflareDnsSetup: Exception during DNS setup', [
                'domain' => $this->domain_name,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            $results[] = "Error: " . $e->getMessage();
            return $results;
        }
    }

    /**
     * Setup main domain A record with proxy enabled
     */
    private function setupMainARecord(string $serverIp): bool
    {
        try {
            $dnsRecords = $this->getDnsRecords();
            $mainRecord = null;

            // Find existing A record for main domain
            foreach ($dnsRecords as $record) {
                if ($record['name'] === $this->domain_name && $record['type'] === 'A') {
                    $mainRecord = $record;
                    break;
                }
            }

            if ($mainRecord) {
                // Update existing A record with proxy
                Log::info('🔧 Updating existing A record', [
                    'domain' => $this->domain_name,
                    'old_ip' => $mainRecord['content'],
                    'new_ip' => $serverIp,
                    'proxied' => $mainRecord['proxied']
                ]);

                // Use the existing record's name format for updates
                return $this->updateDnsRecord($mainRecord['id'], 'A', $this->domain_name, $serverIp, true);
            } else {
                // Create new A record with proxy - use full domain name like working test
                Log::info('🔧 Creating new A record', [
                    'domain' => $this->domain_name,
                    'ip' => $serverIp
                ]);

                // Use full domain name like working test
                return $this->createDnsRecord('A', $this->domain_name, $serverIp, true);
            }

        } catch (Exception $e) {
            Log::error('Failed to setup main A record', [
                'domain' => $this->domain_name,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Setup www CNAME with proxy enabled
     */
    private function setupWwwCname(): bool
    {
        try {
            $dnsRecords = $this->getDnsRecords();
            $wwwRecord = null;

            // Find existing www record
            foreach ($dnsRecords as $record) {
                if ($record['name'] === "www.{$this->domain_name}") {
                    $wwwRecord = $record;
                    break;
                }
            }

            if ($wwwRecord) {
                // Update existing www record with proxy
                Log::info('🔧 Updating existing www record', [
                    'domain' => $this->domain_name,
                    'type' => $wwwRecord['type'],
                    'content' => $wwwRecord['content'],
                    'proxied' => $wwwRecord['proxied']
                ]);

                // Use existing record's name format for updates
                return $this->updateDnsRecord($wwwRecord['id'], $wwwRecord['type'], $wwwRecord['name'], $wwwRecord['content'], true);
            } else {
                // Create new www CNAME with proxy - use full www domain name like working test
                Log::info('🔧 Creating new www CNAME', [
                    'domain' => $this->domain_name
                ]);

                // Use full www domain name like working test
                return $this->createDnsRecord('CNAME', "www.{$this->domain_name}", $this->domain_name, true);
            }

        } catch (Exception $e) {
            Log::error('Failed to setup www CNAME', [
                'domain' => $this->domain_name,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get DNS records for the zone
     */
    private function getDnsRecords(): array
    {
        $url = "https://api.cloudflare.com/client/v4/zones/{$this->zoneID}/dns_records";
        
        $response = $this->adapter->get($url);
        $data = json_decode($response->getBody()->getContents(), true);
        
        if (!$data['success']) {
            throw new RuntimeException('Failed to fetch DNS records: ' . json_encode($data['errors']));
        }
        
        return $data['result'] ?? [];
    }

    /**
     * Create DNS record using direct cURL (exactly like working test)
     */
    private function createDnsRecord(string $type, string $name, string $content, bool $proxied): bool
    {
        try {
            $data = [
                'type' => $type,
                'name' => $name,
                'content' => $content,
                'ttl' => 1,
                'proxied' => $proxied
            ];

            Log::info('🔧 Creating DNS record', [
                'domain' => $this->domain_name,
                'type' => $type,
                'name' => $name,
                'content' => $content,
                'proxied' => $proxied
            ]);

            // Use direct cURL like working test
            $result = $this->makeRequest('POST', "https://api.cloudflare.com/client/v4/zones/{$this->zoneID}/dns_records", $data);

            if ($result['status_code'] === 200 && ($result['data']['success'] ?? false)) {
                Log::info('✅ DNS record created successfully', [
                    'domain' => $this->domain_name,
                    'type' => $type,
                    'name' => $name
                ]);
                return true;
            } else {
                Log::error('❌ Failed to create DNS record', [
                    'domain' => $this->domain_name,
                    'status_code' => $result['status_code'],
                    'errors' => $result['data']['errors'] ?? 'Unknown error'
                ]);
                return false;
            }

        } catch (Exception $e) {
            Log::error('❌ Exception creating DNS record', [
                'domain' => $this->domain_name,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Update DNS record using direct cURL (exactly like working test)
     */
    private function updateDnsRecord(string $recordId, string $type, string $name, string $content, bool $proxied): bool
    {
        try {
            $data = [
                'type' => $type,
                'name' => $name,
                'content' => $content,
                'ttl' => 1,
                'proxied' => $proxied
            ];

            Log::info('🔧 Updating DNS record', [
                'domain' => $this->domain_name,
                'record_id' => $recordId,
                'type' => $type,
                'name' => $name,
                'content' => $content,
                'proxied' => $proxied
            ]);

            // Use direct cURL like working test
            $result = $this->makeRequest('PUT', "https://api.cloudflare.com/client/v4/zones/{$this->zoneID}/dns_records/{$recordId}", $data);

            if ($result['status_code'] === 200 && ($result['data']['success'] ?? false)) {
                Log::info('✅ DNS record updated successfully', [
                    'domain' => $this->domain_name,
                    'record_id' => $recordId,
                    'type' => $type
                ]);
                return true;
            } else {
                Log::error('❌ Failed to update DNS record', [
                    'domain' => $this->domain_name,
                    'record_id' => $recordId,
                    'status_code' => $result['status_code'],
                    'errors' => $result['data']['errors'] ?? 'Unknown error'
                ]);
                return false;
            }

        } catch (Exception $e) {
            Log::error('❌ Exception updating DNS record', [
                'domain' => $this->domain_name,
                'record_id' => $recordId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Setup subdomain A record with proxy enabled
     * This method creates A records for subdomains (e.g., mail.example.com, api.example.com)
     */
    public function setupSubdomainARecord(string $subdomainName, string $serverIp): bool
    {
        try {
            Log::info('🔧 CloudflareDnsSetup: Setting up subdomain A record', [
                'subdomain' => $subdomainName,
                'parent_domain' => $this->domain_name,
                'server_ip' => $serverIp
            ]);

            $dnsRecords = $this->getDnsRecords();
            $subdomainRecord = null;

            // Find existing A record for subdomain
            foreach ($dnsRecords as $record) {
                if ($record['name'] === $subdomainName && $record['type'] === 'A') {
                    $subdomainRecord = $record;
                    break;
                }
            }

            if ($subdomainRecord) {
                // Update existing subdomain A record with proxy
                Log::info('🔧 Updating existing subdomain A record', [
                    'subdomain' => $subdomainName,
                    'old_ip' => $subdomainRecord['content'],
                    'new_ip' => $serverIp,
                    'proxied' => $subdomainRecord['proxied']
                ]);

                return $this->updateDnsRecord($subdomainRecord['id'], 'A', $subdomainName, $serverIp, true);
            } else {
                // Create new subdomain A record with proxy
                Log::info('🔧 Creating new subdomain A record', [
                    'subdomain' => $subdomainName,
                    'ip' => $serverIp
                ]);

                return $this->createDnsRecord('A', $subdomainName, $serverIp, true);
            }

        } catch (Exception $e) {
            Log::error('Failed to setup subdomain A record', [
                'subdomain' => $subdomainName,
                'parent_domain' => $this->domain_name,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Make HTTP request using cURL (copied from working test)
     */
    private function makeRequest(string $method, string $url, array $data = null): array
    {
        $tokenValue = config('services.cloudflare.token') ?: '****************************************';

        $headers = [
            "Authorization: Bearer {$tokenValue}",
            "Content-Type: application/json"
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);

        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return [
            'status_code' => $httpCode,
            'data' => json_decode($response, true)
        ];
    }
}
