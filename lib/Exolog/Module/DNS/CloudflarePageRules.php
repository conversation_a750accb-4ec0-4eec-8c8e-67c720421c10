<?php

namespace Exolog\Module\DNS;

use Cloudflare\API\Adapter\Guzzle;
use Cloudflare\API\Auth\APIToken;
use Cloudflare\API\Endpoints\Zones;
use Exception;
use Illuminate\Support\Facades\Log;
use RuntimeException;

/**
 * Cloudflare Page Rules API Service
 * Handles creation and management of Cloudflare Page Rules for WWW redirects
 */
class CloudflarePageRules
{
    private string $domain_name;
    private Zones $cfZones;
    private string $zoneID;
    private Guzzle $adapter;

    public function __construct(string $domain_name)
    {
        Log::info('🔧 CloudflarePageRules: Initializing', ['domain' => $domain_name]);

        $this->domain_name = $domain_name;

        // Use the working API token
        $tokenValue = config('services.cloudflare.token') ?: '****************************************';
        $token = new APIToken($tokenValue);
        $this->adapter = new Guzzle($token);
        $this->cfZones = new Zones($this->adapter);

        Log::info('🔧 CloudflarePageRules: Using API token', [
            'domain' => $domain_name,
            'token_length' => strlen($tokenValue)
        ]);

        Log::info('🔧 CloudflarePageRules: Getting zone ID', ['domain' => $domain_name]);

        // Get zone ID for the domain
        $response = $this->cfZones->listZones($domain_name);

        Log::info('🔧 CloudflarePageRules: Zone lookup response', [
            'domain' => $domain_name,
            'total_count' => $response->result_info->total_count ?? 0
        ]);

        if ($response->result_info->total_count > 0) {
            foreach ($response->result as $zone) {
                Log::info('🔧 CloudflarePageRules: Checking zone', [
                    'domain' => $domain_name,
                    'zone_name' => $zone->name,
                    'zone_id' => $zone->id
                ]);

                if ($zone->name === $domain_name) {
                    $this->zoneID = $zone->id;
                    Log::info('🔧 CloudflarePageRules: Found matching zone', [
                        'domain' => $domain_name,
                        'zone_id' => $this->zoneID
                    ]);
                    break;
                }
            }
        }

        if (empty($this->zoneID)) {
            Log::error('🔧 CloudflarePageRules: Domain not found in Cloudflare', ['domain' => $domain_name]);
            throw new RuntimeException("Domain '{$domain_name}' not found in Cloudflare");
        }

        Log::info('🔧 CloudflarePageRules: Initialization complete', [
            'domain' => $domain_name,
            'zone_id' => $this->zoneID
        ]);
    }

    /**
     * Check if WWW Page Rule already exists
     */
    public function wwwPageRuleExists(): bool
    {
        try {
            $pageRules = $this->getPageRules();
            
            foreach ($pageRules as $rule) {
                // Check if this is a WWW redirect rule
                if ($this->isWwwRedirectRule($rule)) {
                    Log::info('Found existing WWW Page Rule', [
                        'domain' => $this->domain_name,
                        'rule_id' => $rule['id'],
                        'pattern' => $rule['targets'][0]['constraint']['value'] ?? 'unknown'
                    ]);
                    return true;
                }
            }
            
            return false;
        } catch (Exception $e) {
            Log::error('Failed to check WWW Page Rule existence', [
                'domain' => $this->domain_name,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Create WWW setup: CNAME record + Proxy + Page Rule redirect
     * Based on working Python implementation
     */
    public function createWwwRedirectRule(): bool
    {
        Log::info('🔧 CloudflarePageRules: createWwwRedirectRule called', ['domain' => $this->domain_name]);

        try {
            // Step 1: Ensure WWW CNAME record exists with proxy enabled
            Log::info('🔧 CloudflarePageRules: Step 1 - Ensuring WWW CNAME record', ['domain' => $this->domain_name]);
            $this->ensureWwwCname();

            // Step 2: Enable proxy for main domain
            Log::info('🔧 CloudflarePageRules: Step 2 - Enabling proxy for main domain', ['domain' => $this->domain_name]);
            $this->enableProxyForDomain();

            // Step 3: Check if Page Rule already exists
            Log::info('🔧 CloudflarePageRules: Step 3 - Checking if WWW Page Rule already exists', ['domain' => $this->domain_name]);

            if ($this->wwwPageRuleExists()) {
                Log::info('🔧 CloudflarePageRules: WWW Page Rule already exists, skipping creation', [
                    'domain' => $this->domain_name
                ]);
                return true;
            }

            // Step 4: Create WWW → non-WWW Page Rule
            Log::info('🔧 CloudflarePageRules: Step 4 - Creating WWW Page Rule', ['domain' => $this->domain_name]);

            $ruleData = [
                'targets' => [
                    [
                        'target' => 'url',
                        'constraint' => [
                            'operator' => 'matches',
                            'value' => "www.{$this->domain_name}/*"
                        ]
                    ]
                ],
                'actions' => [
                    [
                        'id' => 'forwarding_url',
                        'value' => [
                            'url' => "https://{$this->domain_name}/\$1",
                            'status_code' => 301
                        ]
                    ]
                ],
                'priority' => 1,
                'status' => 'active'
            ];

            Log::info('🔧 CloudflarePageRules: Creating WWW Page Rule', [
                'domain' => $this->domain_name,
                'zone_id' => $this->zoneID,
                'rule_data' => $ruleData
            ]);

            $response = $this->createPageRule($ruleData);

            Log::info('🔧 CloudflarePageRules: Page Rule creation response', [
                'domain' => $this->domain_name,
                'response' => $response
            ]);

            if ($response && isset($response['result']['id'])) {
                Log::info('✅ CloudflarePageRules: WWW Page Rule created successfully', [
                    'domain' => $this->domain_name,
                    'rule_id' => $response['result']['id'],
                    'pattern' => "www.{$this->domain_name}/*",
                    'redirect_to' => "https://{$this->domain_name}/\$1"
                ]);
                return true;
            }

            Log::error('❌ CloudflarePageRules: Failed to create WWW Page Rule', [
                'domain' => $this->domain_name,
                'response' => $response
            ]);
            return false;

        } catch (Exception $e) {
            Log::error('❌ CloudflarePageRules: Exception while creating WWW Page Rule', [
                'domain' => $this->domain_name,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Get all Page Rules for the zone using direct cURL
     */
    private function getPageRules(): array
    {
        try {
            $result = $this->makeRequest('GET', "https://api.cloudflare.com/client/v4/zones/{$this->zoneID}/pagerules");

            if ($result['status_code'] === 200 && ($result['data']['success'] ?? false)) {
                return $result['data']['result'] ?? [];
            } else {
                throw new RuntimeException('Failed to fetch Page Rules: ' . json_encode($result['data']['errors'] ?? 'Unknown error'));
            }

        } catch (Exception $e) {
            Log::error('🔧 CloudflarePageRules: Exception in getPageRules', [
                'domain' => $this->domain_name,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Create a new Page Rule using direct cURL (like working test)
     */
    private function createPageRule(array $ruleData): array
    {
        try {
            Log::info('🔧 CloudflarePageRules: Making direct cURL request for Page Rule', [
                'domain' => $this->domain_name,
                'zone_id' => $this->zoneID,
                'rule_data' => $ruleData
            ]);

            // Use direct cURL like working test
            $result = $this->makeRequest('POST', "https://api.cloudflare.com/client/v4/zones/{$this->zoneID}/pagerules", $ruleData);

            Log::info('🔧 CloudflarePageRules: Direct cURL response', [
                'domain' => $this->domain_name,
                'status_code' => $result['status_code'],
                'response_data' => $result['data']
            ]);

            if ($result['status_code'] === 200 && ($result['data']['success'] ?? false)) {
                return $result['data'];
            } else {
                throw new RuntimeException('Failed to create Page Rule: ' . json_encode($result['data']['errors'] ?? 'Unknown error'));
            }

        } catch (Exception $e) {
            Log::error('🔧 CloudflarePageRules: Exception in createPageRule', [
                'domain' => $this->domain_name,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Ensure WWW CNAME record exists with proxy enabled
     * Based on Python: ensure_www_cname()
     */
    private function ensureWwwCname(): bool
    {
        try {
            $dnsRecords = $this->getDnsRecords();
            $wwwExists = false;
            $wwwRecord = null;

            // Check if www record already exists
            foreach ($dnsRecords as $record) {
                if ($record['name'] === "www.{$this->domain_name}") {
                    $wwwExists = true;
                    $wwwRecord = $record;
                    break;
                }
            }

            if (!$wwwExists) {
                // Create CNAME www -> domain with proxy enabled
                Log::info('🌍 Creating CNAME www -> domain', ['domain' => $this->domain_name]);
                return $this->createDnsRecord('CNAME', "www.{$this->domain_name}", $this->domain_name, true);
            } else {
                // Enable proxy for existing www record
                if ($wwwRecord && in_array($wwwRecord['type'], ['A', 'CNAME', 'AAAA'])) {
                    Log::info('✅ Enabling proxy for existing www record', [
                        'domain' => $this->domain_name,
                        'type' => $wwwRecord['type']
                    ]);
                    return $this->updateDnsProxy($wwwRecord['id'], $wwwRecord['type'], $wwwRecord['name'], $wwwRecord['content']);
                }
            }

            return true;
        } catch (Exception $e) {
            Log::error('Failed to ensure WWW CNAME', [
                'domain' => $this->domain_name,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Enable proxy for main domain A/AAAA records
     * Based on Python: enable_proxy_for_domain()
     */
    private function enableProxyForDomain(): bool
    {
        try {
            $dnsRecords = $this->getDnsRecords();

            foreach ($dnsRecords as $record) {
                if ($record['name'] === $this->domain_name && in_array($record['type'], ['A', 'AAAA'])) {
                    Log::info('✅ Enabling proxy for main domain', [
                        'domain' => $this->domain_name,
                        'type' => $record['type']
                    ]);
                    $this->updateDnsProxy($record['id'], $record['type'], $record['name'], $record['content']);
                }
            }

            return true;
        } catch (Exception $e) {
            Log::error('Failed to enable proxy for domain', [
                'domain' => $this->domain_name,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get DNS records for the zone
     */
    private function getDnsRecords(): array
    {
        $url = "https://api.cloudflare.com/client/v4/zones/{$this->zoneID}/dns_records";

        $response = $this->adapter->get($url);
        $data = json_decode($response->getBody()->getContents(), true);

        if (!$data['success']) {
            throw new RuntimeException('Failed to fetch DNS records: ' . json_encode($data['errors']));
        }

        return $data['result'] ?? [];
    }

    /**
     * Create DNS record
     */
    private function createDnsRecord(string $type, string $name, string $content, bool $proxied): bool
    {
        $url = "https://api.cloudflare.com/client/v4/zones/{$this->zoneID}/dns_records";

        $data = [
            'type' => $type,
            'name' => $name,
            'content' => $content,
            'ttl' => 1, // Auto TTL
            'proxied' => $proxied
        ];

        $response = $this->adapter->post($url, [
            'json' => $data
        ]);

        $result = json_decode($response->getBody()->getContents(), true);

        return $result['success'] ?? false;
    }

    /**
     * Update DNS record proxy setting
     */
    private function updateDnsProxy(string $recordId, string $type, string $name, string $content): bool
    {
        $url = "https://api.cloudflare.com/client/v4/zones/{$this->zoneID}/dns_records/{$recordId}";

        $data = [
            'type' => $type,
            'name' => $name,
            'content' => $content,
            'ttl' => 1, // Auto TTL
            'proxied' => true
        ];

        $response = $this->adapter->put($url, [
            'json' => $data
        ]);

        $result = json_decode($response->getBody()->getContents(), true);

        return $result['success'] ?? false;
    }

    /**
     * Check if a Page Rule is a WWW redirect rule
     */
    private function isWwwRedirectRule(array $rule): bool
    {
        // Check if this rule targets www.domain.com
        if (!isset($rule['targets'][0]['constraint']['value'])) {
            return false;
        }

        $pattern = $rule['targets'][0]['constraint']['value'];
        $expectedPattern = "www.{$this->domain_name}/*";

        // Check if pattern matches WWW redirect pattern
        if ($pattern !== $expectedPattern) {
            return false;
        }

        // Check if action is forwarding_url
        if (!isset($rule['actions'][0]['id']) || $rule['actions'][0]['id'] !== 'forwarding_url') {
            return false;
        }

        // Check if it redirects to non-WWW version
        $redirectUrl = $rule['actions'][0]['value']['url'] ?? '';
        $expectedRedirect = "https://{$this->domain_name}/\$1";

        return $redirectUrl === $expectedRedirect;
    }

    /**
     * Make HTTP request using cURL (copied from working test)
     */
    private function makeRequest(string $method, string $url, array $data = null): array
    {
        $tokenValue = config('services.cloudflare.token') ?: '****************************************';

        $headers = [
            "Authorization: Bearer {$tokenValue}",
            "Content-Type: application/json"
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);

        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return [
            'status_code' => $httpCode,
            'data' => json_decode($response, true)
        ];
    }
}
