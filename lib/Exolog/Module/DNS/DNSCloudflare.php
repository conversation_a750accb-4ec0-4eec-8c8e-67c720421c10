<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 16.12.2019
 * Time: 16:27
 */

namespace Exolog\Module\DNS;

use Cloudflare\API\Adapter\Guzzle;
use Cloudflare\API\Auth\APIToken;
use Cloudflare\API\Endpoints\DNS;
use Cloudflare\API\Endpoints\Zones;
use Exception;
use Illuminate\Support\Facades\Log;
use RuntimeException;

class DNSCloudflare implements DNSProvider
{
    private $domain_name;
    private Zones $cfZones;
    private DNS $cfDNS;
    private string $zoneID;

    /**
     * DNSOpenProvider constructor.
     * @param $domain_name
     */
    public function __construct($domain_name)
    {
        $this->domain_name = $domain_name;
        $token = new APIToken(config('services.cloudflare.token'));
        $adapter = new Guzzle($token);
        $this->cfDNS = new DNS($adapter);
        $this->cfZones = new Zones($adapter);
        $response = $this->cfZones->listZones($domain_name);

        // Find exact match for the domain name (not substring match)
        if ($response->result_info->total_count > 0) {
            foreach ($response->result as $zone) {
                if ($zone->name === $domain_name) {
                    $this->zoneID = $zone->id;
                    break;
                }
            }
        }
    }

    public function exists(): bool
    {
        return !empty($this->zoneID);
    }

    public function getRecords(): array
    {
        $response = $this->cfDNS->listRecords($this->zoneID);
        $records = json_decode(json_encode($response->result), true);
        return collect($records)->map(function ($item) {
            $item['value'] = $item['content'];
            return $item;
        })->toArray();
    }

    /**
     * @throws Exception
     */
    public function createRecord(array $record)
    {
        Log::info('Creating DNS record via Cloudflare API', [
            'domain' => $this->domain_name,
            'zone_id' => $this->zoneID,
            'record' => $record
        ]);

        // Prepare parameters for Cloudflare API
        $ttl = isset($record['ttl']) ? (int)$record['ttl'] : 1; // Default TTL of 1 means "automatic"
        $proxied = false; // Mail records should not be proxied

        // For MX records, we need to pass priority as a separate parameter
        if ($record['type'] === 'MX' && isset($record['priority'])) {
            $response = $this->cfDNS->addRecord(
                $this->zoneID,
                $record['type'],
                $record['name'],
                $record['value'],
                $ttl,
                $proxied,
                $record['priority']  // Priority for MX records
            );
        } else {
            $response = $this->cfDNS->addRecord(
                $this->zoneID,
                $record['type'],
                $record['name'],
                $record['value'],
                $ttl,
                $proxied
            );
        }

        if (!$response) {
            $errorBody = $this->cfDNS->getBody();
            Log::error('Cloudflare DNS record creation failed', [
                'domain' => $this->domain_name,
                'zone_id' => $this->zoneID,
                'record' => $record,
                'response_body' => $errorBody,
                'response_json' => json_decode($errorBody, true)
            ]);
            throw new RuntimeException('DNS record create error! Response:' . json_encode($errorBody));
        }

        // Log successful response
        $responseData = json_decode(json_encode($response), true);
        Log::info('Cloudflare DNS record created successfully', [
            'domain' => $this->domain_name,
            'zone_id' => $this->zoneID,
            'record' => $record,
            'response' => $responseData,
            'record_id' => $responseData['result']['id'] ?? 'unknown'
        ]);

        return true;
    }

    /**
     * @throws Exception
     */
    public function updateRecord(array $record, $upsert = true): bool
    {
        Log::info('Updating DNS record via Cloudflare API', [
            'domain' => $this->domain_name,
            'zone_id' => $this->zoneID,
            'record' => $record,
            'upsert' => $upsert
        ]);

        $recordId = $this->cfDNS->getRecordID($this->zoneID, $record['type'], $record['name']);

        if ($recordId) {
            Log::info('Found existing DNS record, updating', [
                'domain' => $this->domain_name,
                'record_id' => $recordId,
                'record_type' => $record['type'],
                'record_name' => $record['name']
            ]);

            // Prepare update data
            $updateData = [
                'type' => $record['type'],
                'name' => $record['name'],
                'content' => $record['value'],
                'ttl' => isset($record['ttl']) ? (int)$record['ttl'] : 1,
            ];

            // Add priority for MX records
            if ($record['type'] === 'MX' && isset($record['priority'])) {
                $updateData['priority'] = (int)$record['priority'];
            }

            $response = $this->cfDNS->updateRecordDetails($this->zoneID, $recordId, $updateData);

            if (isset($response->result->id)) {
                $responseData = json_decode(json_encode($response), true);
                Log::info('Cloudflare DNS record updated successfully', [
                    'domain' => $this->domain_name,
                    'zone_id' => $this->zoneID,
                    'record_id' => $recordId,
                    'record' => $record,
                    'response' => $responseData
                ]);
                return true;
            }

            $responseData = json_decode(json_encode($response), true);
            Log::error('Cloudflare DNS record update failed', [
                'domain' => $this->domain_name,
                'zone_id' => $this->zoneID,
                'record_id' => $recordId,
                'record' => $record,
                'response' => $responseData
            ]);
            throw new RuntimeException('DNS record update error! Response:' . json_encode($response));
        }

        Log::info('DNS record not found, attempting upsert', [
            'domain' => $this->domain_name,
            'record_type' => $record['type'],
            'record_name' => $record['name'],
            'upsert' => $upsert
        ]);

        if ($upsert) {
            return $this->createRecord($record);
        }

        Log::warning('DNS record not found and upsert disabled', [
            'domain' => $this->domain_name,
            'record' => $record
        ]);

        return false;
    }

    public function deleteRecord(array $record): bool
    {
        Log::info('Deleting DNS record via Cloudflare API', [
            'domain' => $this->domain_name,
            'zone_id' => $this->zoneID,
            'record' => $record
        ]);

        $recordId = $this->cfDNS->getRecordID($this->zoneID, $record['type'], $record['name']);

        if (!$recordId) {
            Log::warning('DNS record not found for deletion', [
                'domain' => $this->domain_name,
                'zone_id' => $this->zoneID,
                'record' => $record
            ]);
            return false;
        }

        Log::info('Found DNS record for deletion', [
            'domain' => $this->domain_name,
            'record_id' => $recordId,
            'record_type' => $record['type'],
            'record_name' => $record['name']
        ]);

        $result = $this->cfDNS->deleteRecord($this->zoneID, $recordId);

        if ($result) {
            Log::info('Cloudflare DNS record deleted successfully', [
                'domain' => $this->domain_name,
                'zone_id' => $this->zoneID,
                'record_id' => $recordId,
                'record' => $record
            ]);
        } else {
            Log::error('Cloudflare DNS record deletion failed', [
                'domain' => $this->domain_name,
                'zone_id' => $this->zoneID,
                'record_id' => $recordId,
                'record' => $record
            ]);
        }

        return $result;
    }
}
