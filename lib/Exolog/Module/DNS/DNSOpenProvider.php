<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 16.12.2019
 * Time: 16:27
 */

namespace Exolog\Module\DNS;

use Exception;
use Exolog\Module\OpenProvider\OP\ApiException;
use Exolog\Module\OpenProvider\OpenProvider;
use RuntimeException;

class DNSOpenProvider implements DNSProvider
{
    private $openProvider;
    private $domain_name;

    /**
     * DNSOpenProvider constructor.
     * @param $domain_name
     */
    public function __construct($domain_name)
    {
        $this->domain_name = $domain_name;
        $this->openProvider = new OpenProvider();
    }

    public function getRecords()
    {
        $request = $this->openProvider->getDns(['domain' => $this->domain_name, 'records' => true]);
        $dns = $request->getValue();
        return $dns['records'];
    }

    /**
     * @throws Exception
     */
    public function createRecord(array $record)
    {
        $request = $this->openProvider->getDns(['domain' => $this->domain_name, 'records' => true]);
        $dns = $request->getValue();

        foreach ($dns['records'] as $rec) {
            if ($rec['name'] === $record['name'] && $rec['type'] === $record['type']) {
                throw new RuntimeException('DNS record exists with this "name" and "type"! ' . json_encode($record));
            }
        }

        $dns['records'][] = $record;

        $this->openProvider->updateDns($dns);
    }

    /**
     * @throws Exception
     */
    public function updateRecord(array $record, $upsert = true): bool
    {
        $request = $this->openProvider->getDns(['domain' => $this->domain_name, 'records' => true]);
        $dns = $request->getValue();
        $found = false;

        foreach ($dns['records'] as $key => $rec) {
            if ($rec['name'] === $record['name'] && $rec['type'] === $record['type']) {
                $dns['records'][$key] = $record;
                $found = true;
                break;
            }
        }
        if (!$found) {
            $dns['records'][] = $record;
        } else {
            return false;
        }

        $this->openProvider->updateDns($dns);

        return true;
    }

    public function deleteRecord(array $record)
    {
        throw new RuntimeException('Not implemented yet!');
    }

    /**
     * @throws ApiException
     */
    public function exists(): bool
    {
        try {
            $records = $this->getRecords();
            return !empty($records);
        } catch (ApiException $exception) {
            if ((int)$exception->getCode() === 872) {
                return false;
            }
            throw $exception;
        }
    }
}
