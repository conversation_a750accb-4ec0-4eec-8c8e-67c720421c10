<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 16.12.2019
 * Time: 16:27
 */

namespace Exolog\Module\DNS;

class DnsProviderFactory
{
    public static function make($domain): ?DNSProvider
    {
        $tld = Utils::extractTLD($domain);
        $provider = new DNSCloudflare($tld);
        if ($provider->exists()) {
            return $provider;
        }
        $provider = new DNSOpenProvider($tld);
        if ($provider->exists()) {
            return $provider;
        }
        return null;
    }
}
