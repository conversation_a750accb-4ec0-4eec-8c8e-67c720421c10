<?php


namespace Exolog\Module\Routing\Middleware;


use Closure;
use Exolog\Module\Http\Request;
use Exolog\Module\Support\Facades\Auth;
use Exolog\Module\View\RenderPDFService;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Http\Response;

class LegacyPdfMode
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @param mixed ...$groups
     * @return mixed
     * @throws AuthenticationException
     */
    public function handle(Request $request, Closure $next)
    {
        /** @var \Exolog\Module\Http\Response $response */
        $response = $next($request);

        if ($request->has('mode') && $request['mode'] === 'pdf') {
            if (!Auth::isAdmin()) {
                throw new AuthenticationException();
            }
            $pdf = RenderPDFService::make()->htmlToPdf($response->content(), $request->all());

            return new Response(
                $pdf,
                200,
                [
                    'Content-Type' => 'application/pdf',
                    'Content-Disposition' => 'inline; filename="' . 'page.pdf'
                ]
            );
        }
        return $response;
    }

}