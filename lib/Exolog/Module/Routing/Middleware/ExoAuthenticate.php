<?php


namespace Exolog\Module\Routing\Middleware;


use Closure;
use Exolog\Module\Http\Request;
use Exolog\Module\Support\Facades\Auth;
use Exolog\Module\Users\Model\Usergroup;
use Illuminate\Auth\AuthenticationException;

class ExoAuthenticate
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @param mixed ...$groups
     * @return mixed
     * @throws AuthenticationException
     */
    public function handle(Request $request, Closure $next, ...$groups)
    {

        if (Auth::check()) {
            if (empty($groups)) {
                return $next($request);
            }

            $user = Auth::user();

            foreach ($groups as $group) {
                if ($group === 'admin' && $user->isAdmin()) {
                    return $next($request);
                }
                if ($group === 'developer' && $user->isDeveloper()) {
                    return $next($request);
                }
                if ($user->memberOf(Usergroup::findByName($group))) {
                    return $next($request);
                }
            }
        }
        $this->unauthenticated($request, $groups);
    }


    /**
     * Handle an unauthenticated user.
     *
     * @param \Illuminate\Http\Request $request
     * @param array $groups
     * @return void
     *
     * @throws AuthenticationException
     */
    protected function unauthenticated($request, array $groups)
    {
        throw new AuthenticationException(
            'Unauthenticated.', $groups, $this->redirectTo($request)
        );
    }

    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param \Illuminate\Http\Request $request
     * @return string|null
     */
    protected function redirectTo($request)
    {
        //
    }
}