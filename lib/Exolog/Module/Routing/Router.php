<?php

namespace Exolog\Module\Routing;


use Exolog\Module\Support\Facades\Defaults;

class Router extends \Illuminate\Routing\Router
{
    public function group(array $attributes, $routes)
    {

        if (!empty($attributes['edition'])) {
            $e_id = getCurrentEdition();
            if (empty($e_id)) {
                return;
            }
            $edition = Defaults::getEdition();
            if ($attributes['edition'] != $edition->e_id
                && $attributes['edition'] !== $edition->e_title) {
                return;
            }
        }


        if (!empty($attributes['form'])
            || !empty($attributes['form_permalink'])
            || !empty($attributes['mailable'])) {

            $currentReact = CurrentReact::detectCurrentReact();

            if ($currentReact === null) {
                return;
            }

            if (!empty($attributes['form'])) {
                $form = $currentReact->getReactForm();
                if ($attributes['form'] != $form->form_id && $attributes['form'] !== $form->form_name) {
                    return;
                }
            }

            if (!empty($attributes['mailable'])) {
                if (!$currentReact->isMailable()) {
                    return;
                }
            }
        }

        parent::group($attributes, $routes);
    }

    public function form($form, $routes)
    {
        $attributes = ['form' => $form];

        $this->group($attributes, $routes);
    }

    public function edition($edition, $routes)
    {
        $attributes = ['edition' => $edition];

        $this->group($attributes, $routes);
    }

    public function form_permalink($form_permalink, $routes)
    {
        $attributes = ['form_permalink' => $form_permalink];

        $this->group($attributes, $routes);
    }

    public function mailable($routes)
    {
        $attributes = ['mailable' => true];

        $this->group($attributes, $routes);
    }

    /*    public static function toResponse($request, $response)
        {
            if ($response instanceof Responsable) {
                $response = $response->toResponse($request);
            }
            if ($response instanceof View) {
                $response = new ViewResponse($response);
            } elseif (!$response instanceof SymfonyResponse &&
                ($response instanceof Arrayable ||
                    $response instanceof Jsonable ||
                    $response instanceof ArrayObject ||
                    $response instanceof JsonSerializable ||
                    is_array($response))) {

                $response = new JsonResponse($response);
            } elseif (!$response instanceof SymfonyResponse) {
                $response = new Response($response, 200, ['Content-Type' => 'text/html']);
            }

            return parent::toResponse($request, $response);
        }*/

    public function fallback($action)
    {
        $placeholder = 'fallbackPlaceholder';

        return $this->addRoute(
            static::$verbs, "{{$placeholder}}", $action
        )->where($placeholder, '.*')->fallback();
    }
}