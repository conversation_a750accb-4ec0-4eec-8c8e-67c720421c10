<?php

namespace Exolog\Module\Routing;

use Closure;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Contracts\Routing\UrlGenerator;
use Illuminate\Http\Request;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Traits\ForwardsCalls;

/**
 * @mixin Router
 */
class RouteServiceProvider extends ServiceProvider
{
    use ForwardsCalls;

    /**
     * The controller namespace for the application.
     *
     * @var string|null
     */
    protected $namespace;

    /**
     * The callback that should be used to load the application's routes.
     *
     * @var Closure|null
     */
    protected $loadRoutesUsing;

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->booted(function () {
            $this->setRootControllerNamespace();

            //todo add route cache
            if (0/*$this->routesAreCached()*/) {
                //$this->loadCachedRoutes();
            } else {
                $this->loadRoutes();

                $this->app->booted(function () {
                    $this->app['router']->getRoutes()->refreshNameLookups();
                    $this->app['router']->getRoutes()->refreshActionLookups();
                });
            }
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        $this->configureRateLimiting();
        $this->registerRoutes();
        $this->app[ReactRouteBind::class]->bindParameters();
    }

    /**
     * Register the callback that will be used to load the application's routes.
     *
     * @param Closure $routesCallback
     * @return $this
     */
    protected function routes(Closure $routesCallback)
    {
        $this->loadRoutesUsing = $routesCallback;

        return $this;
    }

    /**
     * Set the root controller namespace for the application.
     *
     * @return void
     */
    protected function setRootControllerNamespace()
    {
        if (!is_null($this->namespace)) {
            $this->app[UrlGenerator::class]->setRootControllerNamespace($this->namespace);
        }
    }

    /**
     * Determine if the application routes are cached.
     *
     * @return bool
     */
    protected function routesAreCached()
    {
        return $this->app->routesAreCached();
    }

    /**
     * Load the cached routes for the application.
     *
     * @return void
     */
    protected function loadCachedRoutes()
    {
        $this->app->booted(function () {
            require $this->app->getCachedRoutesPath();
        });
    }

    /**
     * Load the application routes.
     *
     * @return void
     */
    protected function loadRoutes()
    {
        if (!is_null($this->loadRoutesUsing)) {
            $this->app->call($this->loadRoutesUsing);
        } elseif (method_exists($this, 'map')) {
            $this->app->call([$this, 'map']);
        }
    }

    /**
     * Pass dynamic methods onto the router instance.
     *
     * @param string $method
     * @param array $parameters
     * @return mixed
     */
    public function __call($method, $parameters)
    {
        return $this->forwardCallTo(
            $this->app->make(Router::class), $method, $parameters
        );
    }

    /**
     * Configure the rate limiters for the application.
     *
     * @return void
     */
    protected function configureRateLimiting()
    {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60);
        });
    }

    protected function registerRoutes()
    {

        $this->routes(function () {

            if (defined('APP_DEALER')) {
                Route::middleware('system-dealer')
                    ->group(base_path('routes/dealer.php'));
                return;
            }

            Route::pattern('page', '.*');

            Route::middleware('system-web')
                ->group(base_path('routes/web.php'));

            Route::middleware('system-api')
                ->group(base_path('routes/api.php'));

            if ($api = realpath(site_path('config/routes.api.php'))) {
                Route::prefix('site-api')
                    ->middleware('system-site-api')
                    ->group($api);
            }

            if ($web = realpath(site_path('config/routes.web.php'))) {
                Route::middleware('system-site-web')
                    ->group($web);
            }

            Route::middleware('system-web')
                ->group(base_path('routes/web.after.php'));

            $pageRoute = $this->app['router']->getRoutes()->getByName('page');
            if (!empty($pageRoute)) {
                $pageRoute->defaults('page', '/');
            }

        });
    }
}
