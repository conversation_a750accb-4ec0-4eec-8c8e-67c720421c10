<?php


namespace Exolog\Module\Routing;


use Exolog\Module\Support\Facades\Defaults;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;

class ReactRouteBind
{
    public function bindParameters()
    {

        $this->reactBind();
        $this->pageBind();
    }

    protected function reactBind()
    {
        Route::bind('react', function ($value, $route) {
            [$field, $params] = explode('__', $route->bindingFieldFor('react'));

            if ($field === 'permalink') {
                $value = Str::start($value, '/');
            }

            $query = react()
                ->edition(getCurrentEdition())
                ->where($field, $value);

            if ($params === 'published') {
                $query->published();
            }
            $react = $query->first();
            if (empty($react)) {
                throw404();
            }
            return $react;
        });
    }

    protected function pageBind()
    {
        Route::bind('page', function ($value, $route) {

            $page = Defaults::getCurrentReact();
            if ($page) {
                return $page;
            }

            $page = CurrentReact::resolveReact($value);

            if (is_null($page)) {
                throw404();
            }

            Defaults::setCurrentReact($page);

            return $page;
        });
    }
}