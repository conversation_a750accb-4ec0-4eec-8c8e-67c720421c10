<?php


namespace Exolog\Module\Routing;


use Exolog\Core\Forms\React;
use Exolog\Module\Support\Facades\Auth;
use Exolog\Module\Support\Facades\Defaults;
use Illuminate\Support\Str;

class CurrentReact
{

    /**
     * this function try to detect current rout by permalink before Laravel route,
     * as result we can use additional route group attributes like (edition, form, permalink)
     */
    public static function detectCurrentReact(): ?React
    {
        $components = parse_url(request()->server("REQUEST_URI"));

        $currentReact = self::resolveReact($components['path']);
        if ($currentReact) {
            Defaults::setCurrentReact($currentReact);
        }
        return $currentReact;
    }

    public static function resolveReact($permalink): ?React
    {
        $permalink = Str::start($permalink, '/');
        $query = react()
            ->permalink($permalink)
            ->edition(getCurrentEdition());

        if (!Auth::isAdmin()) {
            $query->published();
        }

        return $query->first();
    }
}