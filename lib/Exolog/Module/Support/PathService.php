<?php


namespace Exolog\Module\Support;


use Exolog\Module\Support\Facades\Site;
use RuntimeException;

class PathService
{
    protected array $paths;

    public static function to($path_name, $path = '')
    {
        $paths = [
            //Exolog path related
            'path_base' => base_path(),
            //Site paths related
            'path_site' => site_path(),
            'path_site_cache' => site_path('cache/'),
            'path_dist' => site_path('dist/'),
            'path_resources' => site_path('resources/'),
        ];

        $p = $paths[$path_name] ?? $paths['path_' . $path_name];
        if (empty($p)) {
            throw new RuntimeException("Path name '$path_name' is not defined.");
        }
        return static::normalizePath($p . '/' . $path);

    }

    public static function web($path_name, $path = '')
    {
        $paths_web = [
            'path_site' => '/sites/' . Site::alias(),
            'path_dist' => '/sites/' . Site::alias() . '/dist/',
            'path_media' => '/sites/' . Site::alias() . '/media/',
            'path_cache' => '/sites/' . Site::alias() . '/cache/',
        ];
        $p = $paths_web[$path_name] ?? $paths_web['path_' . $path_name];
        if (empty($p)) {
            throw new RuntimeException("Path name '$path_name' is not defined.");
        }
        return static::normalizePath($p . '/' . $path);
    }

    public static function normalizePath($path)
    {
        do {
            $path = preg_replace(
                array('#//|/\./#', '#/([^/.]+)/\.\./#'),
                '/', $path, -1, $count
            );
        } while ($count > 0);
        return $path;
    }

    public function getSiteConfPath(): string
    {
        return base_path('homepages.webkracht.nl/configs/sites/' . Site::alias());
    }
}