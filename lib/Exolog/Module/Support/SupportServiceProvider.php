<?php


namespace Exolog\Module\Support;

use Illuminate\Queue\Events\JobFailed;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;

class SupportServiceProvider extends ServiceProvider
{

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {

    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Queue::failing(function (JobFailed $event) {
            Log::stack(['discord'])->error('Job Failed:' . $event->exception->getMessage(),
                ['exception' => $event->exception->getTraceAsString()]);
        });
    }

    public function initSite()
    {

    }
}