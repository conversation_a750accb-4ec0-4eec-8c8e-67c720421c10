<?php

use chillerlan\QRCode\QRCode;
use chillerlan\QRCode\QROptions;
use Exolog\Core\CoreInit;
use Exolog\Core\Exolog;
use Exolog\Core\Forms\React;
use Exolog\Dealer\Dealer;
use Exolog\Module\ECO\ECOService;
use Exolog\Module\Editions\Model\Edition;
use Exolog\Module\Http\Events\PageNotFound;
use Exolog\Module\Markers\Marker\EditorVar\EditorVarService;
use Exolog\Module\Permalinks\PermalinksService;
use Exolog\Module\Reacts\Query\ReactQueryBuilder;
use Exolog\Module\Support\Facades\Defaults;
use Exolog\Module\Support\Facades\Site;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use JetBrains\PhpStorm\NoReturn;


/**
 * Shortcut to set/get editor variables
 * @param null $name
 * @param null $value
 * @return EditorVarService|string
 */
function ev($name = null, $value = null)
{
    if (func_num_args() === 2) {
        return app('exolog.editor_vars')->set($name, $value);
    }

    if (func_num_args() === 1) {
        return app('exolog.editor_vars')->get($name);
    }

    return app('exolog.editor_vars');
}


/**
 * @param $path
 * @param $default
 * @return array|ECOService|mixed
 */
function ECO($path = null, $default = null)
{
    $eco = ECOService::make();
    if ($path) {
        return $eco->get($path, $default);
    }
    return $eco;
}


function coreInit(): CoreInit
{
    return app(CoreInit::class);
}

function sites_path($path = ''): string
{
    return base_path('homepages.webkracht.nl/htdocs/sites' . ($path ? DIRECTORY_SEPARATOR . $path : $path));
}

function site_path($path = ''): string
{
    return sites_path(Site::alias() . ($path ? DIRECTORY_SEPARATOR . $path : $path));
}

function dealer(): Dealer
{
    return app('dealer');
}


// error handler function


function ab($a, $b, $c = '', $d = '', $e = '', $f = '')
{
    return $a ? $a : ($b ? $b : ($c ? $c : ($d ? $d : ($e ? $e : $f))));
}


function trace_image($full_file_name, $filename = null)
{
    $response = response(null);

    $forceDownload = request('download');
    $forceDownload = !empty($forceDownload);
    $full_file_name = realpath($full_file_name);
    $mtime = filemtime($full_file_name);
    // making Etag
    $Etag = @sha1($full_file_name . filesize($full_file_name) . filectime($full_file_name));

    $if_modified_since = preg_replace('/;.*$/', '', $_SERVER['HTTP_IF_MODIFIED_SINCE']);

    $gmdate_mod = gmdate('D, d M Y H:i:s', $mtime) . ' GMT';

    $response->header("Cache-Control", "public");
    $response->header("Pragma", "cache");
    $response->header('Expires', gmdate('D, d M Y H:i:s', time() + 366 * 24 * 60 * 60) . ' GMT');
    $response->header('Etag', $Etag);

    if ($if_modified_since == $gmdate_mod || (!empty($_SERVER['HTTP_IF_NONE_MATCH']) && trim($_SERVER['HTTP_IF_NONE_MATCH']) == $Etag)) {
        $response->setStatusCode(304);
        return $response;
    }

    $response->header("Last-Modified: $gmdate_mod", true, ($if_modified_since == $gmdate_mod));


    // for images
    if (empty($filename)) {
        $filename = basename($full_file_name);
    }
    //replace spaces, commas, separators and dutch letters
    $filename = Str::slug(pathinfo($filename, PATHINFO_FILENAME), '_') . '.' . pathinfo($filename, PATHINFO_EXTENSION);

    $file_url = str_replace(base_path('homepages.webkracht.nl/htdocs'), '', $full_file_name);
    $response->header("X-Accel-Redirect", $file_url);

    if ($forceDownload) {
        $response->header("Content-Type", "application/x-force-download");
    } else {
        $mime = @getimagesize($full_file_name)['mime'];
        if (empty($mime)) {
            $mime = getContentType($full_file_name);
        }
        $response->header("Content-Type", $mime);
    }

    $response->header("Content-Disposition", "filename=" . $filename . ";");

    return $response;
}

function getContentType($inFileName)
{
    //--strip path
    $inFileName = basename($inFileName);
    //--check for no extension
    if (strrchr($inFileName, ".") == false) {
        return "application/octet-stream";
    }
    //--get extension and check cases
    $extension = strrchr($inFileName, ".");
    switch ($extension) {
        case ".gif":
            return "image/gif";
        case ".gz":
            return "application/x-gzip";
        case ".html":
        case ".htm":
            return "text/html";
        case ".jpg":
            return "image/jpeg";
        case ".webp":
            return "image/webp";
        case ".tar":
            return "application/x-tar";
        case ".txt":
            return "text/plain";
        case ".zip":
            return "application/zip";
        case ".pdf":
            return "application/pdf";
        case ".doc":
            return "application/msword";
        case ".ai":
            return "application/postscript";
        default:
            return "application/octet-stream";
    }
}

function delKeyPrefix($array, $prefix)
{
    if (count($array)) {
        $array_keys = array_keys($array);
        foreach ($array_keys as $key => $value) {
            if (strpos($value, $prefix) == 0) {
                $array_keys[$key] = str_replace($prefix, '', $value);
            }
        }

        return array_combine($array_keys, array_values($array));
    }

    return array();
}

// function for adding prefix to array keys
function add_keys_prefix($data, $prefix = '', $transform = '')
{
    if (!is_array($data)) {
        return array();
    }
    $new_data = array();
    // value
    foreach ($data as $data_key => $data_value) {
        $new_key = $prefix . $data_key;

        if ($transform === 'lowercase') {
            $new_key = strtolower($new_key);
        }

        $new_data[$new_key] = $data_value;
    }
    return $new_data;
}

#[NoReturn]
function runRedir($redir, $code = 302)
{
    exolog()->runRedir($redir, $code);
}

/**
 * @param $items
 * @param $ids
 * @return array|mixed
 * @deprecated use array_recursive_collect_key
 */
function collectIds($items, $ids = [])
{
    foreach ($items as $item) {
        if (array_key_exists('id', $item)) {
            $ids[] = $item['id'];
            if ($item['children']) {
                $ids = collectIds($item['children'], $ids);
            }
        } else {
            $ids = collectIds($item, $ids);
        }
    }
    return $ids;
}

function array_recursive_collect_key($arr, $key = 'id', $children = 'children', $values = [])
{
    foreach ($arr as $item) {
        if (is_array($item) && array_key_exists($key, $item)) {
            $values[] = $item[$key];
            if ($item[$children]) {
                $values = array_recursive_collect_key($item[$children], $key, $children, $values);
            }
        } else {
            $values = array_recursive_collect_key($item, $key, $children, $values);
        }
    }
    return $values;
}

function getSiteJsonConfig()
{
    $JSONFILE = [];
    if (file_exists(site_path('config/exedit.json'))) {
        $JSONFILE = json_decode(file_get_contents(site_path('config/exedit.json')), true);
    }
    return $JSONFILE;
}

function putSiteJsonConfig($JSONFILE)
{
    file_put_contents(site_path('config/exedit.json'), json_encode($JSONFILE));
}

function getCurrentEdition()
{
    return Defaults::get('edition');
}

function getDefaultEdition()
{
    return Edition::getDefaultOrFail()->e_id;
}

#[NoReturn]
function throw404()
{
    PageNotFound::dispatch();
    http_response_code(404);
    header("Status: 404 Not Found");
    while (ob_get_level() !== 0) {
        ob_end_clean();
    }
    $page404 = PermalinksService::make()->getCustomErrorPage(404);
    if (!is_null($page404)) {
        echo $page404;
        exit;
    }
    echo view('errors.404')->toHtml();
    exit;
}

function endsWith($haystack, $needle): bool
{
    return $needle === "" || substr($haystack, -strlen($needle)) === $needle;
}

function consoleLog($msg)
{
    if (is_array($msg)) {
        print_r($msg);
        echo PHP_EOL;
    } else {
        echo $msg . PHP_EOL;
    }
}

/**
 * implementation that is used to remove nodes from the array.
 * if the current array is not associative (only numeric keys in a row),
 * we pass through the child nodes recursively
 *
 * @param array $array
 * @param array $remove_keys - Keys to remove
 * @param array $leave_keys - Keys to leave, If non emoty, parametr $remove_keys is ignored
 * @param array $children - Wich key is children, empty all nested arrays are children
 * @return array
 */
function array_recursive_remove_key(array $array, array $remove_keys = [], array $leave_keys = [], array $children = [])
{
    $assoc = array_is_assoc($array);

    foreach ($array as $k => $v) {
        //check/remove only for associative arrays
        if ($assoc) {
            if (!empty($leave_keys)) {
                if (!in_array($k, $leave_keys) && !in_array($k, $children)) {
                    unset($array[$k]);
                    continue;

                }
            } else {
                if (in_array($k, $remove_keys)) {
                    unset($array[$k]);
                    continue;
                }
            }
        }

        if (is_array($v) && (in_array($k, $children, true) || empty($children) || !$assoc)) {
            $array[$k] = array_recursive_remove_key($v, $remove_keys, $leave_keys, $children);
        }
    }

    return $array;
}

/**
 * @param array $array
 * @return bool
 */
function array_is_assoc(array $array)
{
    if (array() === $array) {
        return false;
    }
    return array_keys($array) !== range(0, count($array) - 1);
}


/**
 * implementation that is used to filter nodes from the array.
 *
 * @param callable $filter The callback function to use (true - leave node)
 * @param string $children - Which key is children, empty all nested array pass to filterS
 */
function array_recursive_filter(array $array, callable $filter, string $children = 'children'): array
{
    $result = [];

    foreach ($array as $k => $v) {
        if (!$filter($k, $v)) {
            continue;
        }
        $element = $v;
        if (!empty($children)) {
            if (is_array($element[$children])) {
                $element[$children] = array_recursive_filter($element[$children], $filter, $children);
            }
        } else {
            if (is_array($element)) {
                $element = array_recursive_filter($element, $filter, $children);
            }
        }
        $result[] = $element;
    }

    return $result;
}

/**
 * @return Exolog
 */
function exolog(): Exolog
{
    return app('exolog');
}

function language2locale($language)
{
    switch ($language) {
        case 'english':
            return 'en';
        case 'dutch':
            return 'nl';
        case 'german':
            return 'de';
        case 'ukrainian':
            return 'uk';
        case 'russian':
            return 'ru';
    }
    return $language;

}

/**
 * Default value
 * Return not empty default value
 * @param mixed $value
 * @param mixed $default
 * @return mixed
 */
function dv($value, $default)
{
    //TODO check isset and ''?
    return empty($value) ? $default : $value;
}

/**
 * React Query builder
 * @return React|ReactQueryBuilder
 */
function react($react_id = null)
{
    if ($react_id) {
        return React::query()->findOrFail($react_id);
    }
    return React::query();
}

function build_url(array $parts): string
{
    return (isset($parts['scheme']) ? "{$parts['scheme']}:" : '') .
        ((isset($parts['user']) || isset($parts['host'])) ? '//' : '') .
        (isset($parts['user']) ? "{$parts['user']}" : '') .
        (isset($parts['pass']) ? ":{$parts['pass']}" : '') .
        (isset($parts['user']) ? '@' : '') .
        (isset($parts['host']) ? "{$parts['host']}" : '') .
        (isset($parts['port']) ? ":{$parts['port']}" : '') .
        (isset($parts['path']) ? "{$parts['path']}" : '') .
        (isset($parts['query']) ? "?{$parts['query']}" : '') .
        (isset($parts['fragment']) ? "#{$parts['fragment']}" : '');
}

function url_query($to, array $parameters = [], array $query = []): string
{
    return Str::finish(url($to, $parameters), count($query) ? '?' : '') . Arr::query($query);
}

function array_is_equal(?array $arr1, ?array $arr2): bool
{
    return empty(array_diff_assoc($arr1, $arr2))
        && empty(array_diff_assoc($arr2, $arr1));
}

function find_parent_react_in_tree(array $tree, int $react_id, $current_parent = null)
{
    foreach ($tree as $item) {
        if ((int)$item['react_id'] === $react_id) {
            return $current_parent;
        }
        if ($item['children']) {
            $parent = find_parent_react_in_tree($item['children'], $react_id, $item);
            if ($parent) {
                return $parent;
            }
        }
    }
    return null;
}


function getter_to_key(string $method): string
{
    return (string)Str::of($method)->replaceFirst('get', '')->snake();
}

function qrcode($data, $options = []): string
{
    $options = new QROptions(array_merge([
        'outputType' => QRCode::OUTPUT_IMAGE_PNG,
        'eccLevel' => QRCode::ECC_H,
        'imageBase64' => true
    ], $options));
    return (new QRCode($options))->render($data);
}


function jwt_decode(string $token): array
{
    list($header, $payload, $signature) = explode('.', $token);
    //$decodedHeader = json_decode(base64_decode($header), true);
    $decodedPayload = json_decode(base64_decode($payload), true);

    return $decodedPayload;
}
