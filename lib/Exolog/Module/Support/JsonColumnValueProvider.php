<?php


namespace Exolog\Module\Support;


use Exolog\Module\Contracts\PersistentEntityValueProvider;
use Illuminate\Database\Eloquent\Model;

class JsonColumnValueProvider implements PersistentEntityValueProvider
{
    private Model $model;
    private string $column;

    public function __construct(Model $model, string $column)
    {
        $this->model = $model;
        $this->column = $column;
    }

    public function getData()
    {
        return $this->model[$this->column];
    }

    public function setData($data)
    {
        $this->model[$this->column] = $data;
    }

    public function save(array $options = []): bool
    {
        return $this->model->save($options);
    }
}