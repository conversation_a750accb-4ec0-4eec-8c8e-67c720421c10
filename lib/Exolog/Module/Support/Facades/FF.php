<?php

namespace Exolog\Module\Support\Facades;

use Closure;
use Exolog\Core\Forms\React;
use Exolog\Module\FF\Elements\ExoForm;
use Galahad\Aire\Aire;
use Galahad\Aire\Elements\Button;
use Galahad\Aire\Elements\Checkbox;
use Galahad\Aire\Elements\CheckboxGroup;
use Galahad\Aire\Elements\Form;
use Galahad\Aire\Elements\Input;
use Galahad\Aire\Elements\Label;
use Galahad\Aire\Elements\RadioGroup;
use Galahad\Aire\Elements\Select;
use Galahad\Aire\Elements\Summary;
use Galahad\Aire\Elements\Textarea;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Facade;
use JsonSerializable;
use Traversable;

/**
 * @method static Aire setTheme(string $namespace = null, string $prefix = null, array $config = [])
 * @method static Aire resetTheme()
 * @method static string applyTheme(string $view)
 * @method static Aire setIdGenerator(Closure $id_generator)
 * @method static Form form($action = null, $bound_data = null)
 * @method static Form open($action = null, $bound_data = null)
 * @method static Form route(string $route_name, $parameters = [], bool $absolute = true)
 * @method static Form resourceful(Model $model, $resource_name = null, $prepend_parameters = [])
 * @method static mixed config(string $key, $default = null)
 * @method static mixed getBoundValue(string $name, $default = null)
 * @method static Label label(string $label)
 * @method static Button button(string $label = null)
 * @method static Button submit(string $label = 'Submit')
 * @method static Input input($name = null, $label = null)
 * @method static Select select(array|Collection|Arrayable|Jsonable|JsonSerializable|Traversable|string $options, $name = null, $label = null)
 * @method static Select timezoneSelect($name = null, $label = null)
 * @method static Textarea textArea($name = null, $label = null)
 * @method static Summary summary()
 * @method static Checkbox checkbox($name = null, $label = null)
 * @method static CheckboxGroup checkboxGroup(array|Collection|Arrayable|Jsonable|JsonSerializable|Traversable|string $options, $name, $label = null)
 * @method static RadioGroup radioGroup(array|Collection|Arrayable|Jsonable|JsonSerializable|Traversable|string $options, $name, $label = null)
 * @method static Input hidden($name = null, $value = null)
 * @method static Input color($name = null, $label = null)
 * @method static Input date($name = null, $label = null)
 * @method static Input dateTime($name = null, $label = null)
 * @method static Input dateTimeLocal($name = null, $label = null)
 * @method static Input email($name = null, $label = null)
 * @method static Input file($name = null, $label = null)
 * @method static Input image($name = null, $label = null)
 * @method static Input month($name = null, $label = null)
 * @method static Input number($name = null, $label = null)
 * @method static Input password($name = null, $label = null)
 * @method static Input range($name = null, $label = null, $min = 0, $max = 100)
 * @method static Input search($name = null, $label = null)
 * @method static Input tel($name = null, $label = null)
 * @method static Input time($name = null, $label = null)
 * @method static Input url($name = null, $label = null)
 * @method static Input week($name = null, $label = null)
 * @method static ExoForm exoForm($form, $formconf = [])
 * @method static ExoForm react(React $react, $formconf = [])
 *
 *  * @see \Exolog\Module\FF\FFService
 */
class FF extends Facade
{
    public static function getFacadeRoot()
    {
        return parent::getFacadeRoot();
    }

    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return 'ffaire';
    }
}
