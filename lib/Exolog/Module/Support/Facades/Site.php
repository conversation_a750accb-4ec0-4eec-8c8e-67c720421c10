<?php

namespace Exolog\Module\Support\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static string|array get(string $name)
 * @method static bool isInit()
 * @method static init(\Exolog\Module\Site\Model\Site $site)
 * @method static integer id()
 * @method static string alias()
 * @method static array toArray()
 *
 * @see \Exolog\Module\Site\SiteService
 */
class Site extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return 'exolog.site';
    }
}
