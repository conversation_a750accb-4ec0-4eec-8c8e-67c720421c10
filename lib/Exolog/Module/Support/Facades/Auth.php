<?php

namespace Exolog\Module\Support\Facades;


use Closure;
use Exolog\Module\Auth\SystemSessionGuard;
use Illuminate\Auth\AuthManager;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Auth\StatefulGuard;
use Illuminate\Contracts\Auth\UserProvider;
use Illuminate\Support\Facades\Facade;
use Symfony\Component\HttpFoundation\Response;

/**
 * @method static AuthManager extend(string $driver, Closure $callback)
 * @method static AuthManager provider(string $name, Closure $callback)
 * @method static Authenticatable loginUsingId(mixed $id, bool $remember = false)
 * @method static Authenticatable|null user()
 * @method static Guard|StatefulGuard guard(string|null $name = null)
 * @method static UserProvider|null createUserProvider(string $provider = null)
 * @method static Response|null onceBasic(string $field = 'email', array $extraConditions = [])
 * @method static bool attempt(array $credentials = [], bool $remember = false)
 * @method static bool hasUser()
 * @method static bool check()
 * @method static bool guest()
 * @method static bool once(array $credentials = [])
 * @method static bool onceUsingId(mixed $id)
 * @method static bool validate(array $credentials = [])
 * @method static bool viaRemember()
 * @method static bool|null logoutOtherDevices(string $password, string $attribute = 'password')
 * @method static int|string|null id()
 * @method static void login(Authenticatable $user, bool $remember = false)
 * @method static void logout()
 * @method static void logoutCurrentDevice()
 * @method static void setUser(Authenticatable $user)
 * @method static void shouldUse(string $name);
 *
 * @method static bool isAdmin()
 * @method static bool isDeveloper()
 * @method static bool|Authenticatable signIn(array $credentials, bool $remember = false)
 * @method static void signOut()
 * @method static bool|Authenticatable loginUsingHash(string $hash)
 * @method static bool|Authenticatable loginUsingDid(string $did)
 *
 *
 * @see AuthManager
 * @see \Illuminate\Contracts\Auth\Factory
 * @see Guard
 * @see \Exolog\Module\Auth\SystemSessionGuard
 * @mixin SystemSessionGuard
 */
class Auth extends Facade
{

    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return 'auth';
    }
}
