<?php

namespace Exolog\Module\Support\Facades;

use Exolog\Module\Log\BufferLogger;
use Illuminate\Support\Facades\Facade;

/**
 * @method static void alert(string $message, array $context = [])
 * @method static void critical(string $message, array $context = [])
 * @method static void debug(string $message, array $context = [])
 * @method static void emergency(string $message, array $context = [])
 * @method static void error(string $message, array $context = [])
 * @method static void info(string $message, array $context = [])
 * @method static void log($level, string $message, array $context = [])
 * @method static void notice(string $message, array $context = [])
 * @method static void warning(string $message, array $context = [])
 * @method static array getRecords()
 * @method static void clear()
 *
 * @see BufferLogger
 */
class BuffLog extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return BufferLogger::class;
    }
}
