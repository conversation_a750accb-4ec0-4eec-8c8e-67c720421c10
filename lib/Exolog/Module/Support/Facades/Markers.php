<?php

namespace Exolog\Module\Support\Facades;

use Illuminate\Support\Facades\Facade;
use Illuminate\Support\HtmlString;

/**
 * @method static mixed resolve($marker, array $params = [])
 * @method static HtmlString render($content, array $params = [])
 * @method static string apply($content, array $params = [])
 * @method static bool isMarker($text)
 * @method static mixed resolveIfMarker($text, array $params = [])
 *
 * @see \Exolog\Module\Markers\MarkersManager
 */
class Markers extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return 'exolog.markers';
    }
}
