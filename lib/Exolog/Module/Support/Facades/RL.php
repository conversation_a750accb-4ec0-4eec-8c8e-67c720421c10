<?php

namespace Exolog\Module\Support\Facades;

use Exolog\Module\ReactLayout\ReactLayout;
use Exolog\Module\ReactLayout\ReactLayoutService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Facade;

/**
 * @method static array withDefaults(...$data)
 * @method static void push(ReactLayout $reactLayout)
 * @method static array getTemplates()
 * @method static ReactLayout pull()
 * @method static ReactLayout|null current()
 * @method static Collection getBlocks(array $search_params = [])
 * @method static array getQBRules()
 * @method static saveQBRules(array $QBRules)
 *
 * @see ReactLayoutService
 */
class RL extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return ReactLayoutService::class;
    }
}
