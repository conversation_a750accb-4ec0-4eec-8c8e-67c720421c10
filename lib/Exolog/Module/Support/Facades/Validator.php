<?php

namespace Exolog\Module\Support\Facades;

use Illuminate\Translation\Translator;

class Validator extends \Illuminate\Support\Facades\Validator
{
    public static function validateWithAttributes(array $data, array $rules, $messages = [], ...$params): array
    {
        /** @var Translator $translator */
        $translator = trans();
        $messages = array_merge($messages, $translator->get('validation-attributes'));
        return static::validate($data, $rules, $messages, ...$params);
    }
}