<?php

namespace Exolog\Module\Support\Facades;

use Exolog\Module\Filesystem\ExoFileService;
use Illuminate\Support\Facades\Facade;
use Symfony\Component\HttpFoundation\Response;

/**
 *
 * @method static string url($fileId)
 * @method static Response download(string $fileId)
 * @method static array parseFileId(string $fileId)
 * @method static string composeFileId($disk, $path, $filename = null)
 * @method static string encodeFileId(array $params)
 * @method static array decodeFileId($hash)
 * @method static string path($fileId)
 * @method static string basename($fileId)
 * @see  ExoFileService
 */
class ExoFile extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return ExoFileService::class;
    }
}
