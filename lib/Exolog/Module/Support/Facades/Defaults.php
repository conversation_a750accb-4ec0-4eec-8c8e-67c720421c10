<?php

namespace Exolog\Module\Support\Facades;

use Exolog\Core\Forms\React;
use Exolog\Module\Editions\Model\Edition;
use Illuminate\Support\Facades\Facade;

/**
 * @method static mixed get(string $name)
 * @method static void set(string $name, mixed $value)
 * @method static void setCurrentReact(React $react)
 * @method static React getCurrentReact()
 * @method static init()
 * @method static array toArray()
 * @method static void setEdition(Edition $default_edition)
 * @method static Edition getEdition()
 *
 * @see \Exolog\Module\Site\DefaultsService
 */
class Defaults extends Facade
{

    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return 'exolog.defaults';
    }
}
