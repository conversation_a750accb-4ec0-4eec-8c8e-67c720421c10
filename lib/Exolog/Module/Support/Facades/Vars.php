<?php

namespace Exolog\Module\Support\Facades;

use Exolog\Module\Vars\Variable;
use Exolog\Module\Vars\VariableContainer;
use Exolog\Module\Vars\VariablePicture;
use Exolog\Module\Vars\VariableUnit;
use Illuminate\Support\Facades\Facade;

/**
 * @method static Variable|VariableContainer|VariableUnit|VariablePicture create(string $var_name, $var_type, $var_parent, $defaultValue = null)
 * @method static Variable|VariableContainer|VariableUnit|VariablePicture get(string $var_name, $var_type = null, $var_parent = null, $defaultValue = null)
 * @method static Variable|VariableContainer|VariableUnit|VariablePicture site(string $var_name, $defaultValue = null)
 * @method static Variable|VariableContainer|VariableUnit|VariablePicture edition(string $var_name, $edition = null, $defaultValue = null)
 * @method static Variable|VariableContainer|VariableUnit|VariablePicture form(string $var_name, $form, $defaultValue = null)
 * @method static Variable|VariableContainer|VariableUnit|VariablePicture user(string $var_name, $var_parent = null, $defaultValue = null)
 * @method static Variable|VariableContainer|VariableUnit|VariablePicture getById($id)
 * @method static Variable[]|VariableContainer[]|VariableUnit[]|VariablePicture[] getAll($params)
 * @method static Variable[]|VariableContainer[]|VariableUnit[]|VariablePicture[] massUpdate(array $vars, string $var_type = null, $var_parent = null)
 *
 * @see \Exolog\Module\Vars\VarsService
 */
class Vars extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return 'exolog.vars';
    }
}
