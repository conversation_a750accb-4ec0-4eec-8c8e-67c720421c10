<?php

namespace Exolog\Module\Support\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static string to(string $path_name, $path = '')
 * @method static string web(string $path_name, $path = '')
 * @method static string getSiteConfPath()
 *
 * @see \Exolog\Module\Support\PathService
 */
class Path extends Facade
{

    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return 'exolog.path';
    }
}
