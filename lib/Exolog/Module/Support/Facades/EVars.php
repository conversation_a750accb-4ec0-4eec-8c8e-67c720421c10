<?php

namespace Exolog\Module\Support\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static void clear()
 * @method static array all()
 * @method static mixed get(string $name)
 * @method static void set(string $name, mixed $value)
 *
 * @see \Exolog\Module\Markers\Marker\EditorVar\EditorVarService
 */
class EVars extends Facade
{

    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return 'exolog.editor_vars';
    }
}
