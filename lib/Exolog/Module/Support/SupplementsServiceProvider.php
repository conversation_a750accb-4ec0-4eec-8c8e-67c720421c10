<?php


namespace Exolog\Module\Support;

use Closure;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class SupplementsServiceProvider extends ServiceProvider
{

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {

    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Arr::macro('wrapNull', static function ($value) {
            return is_array($value) ? $value : [$value];
        });

        Str::macro('format', static function (string $template, array $values, Closure $callback = null) {
            $result = $template;
            if (preg_match_all("/(\{.+?\})/i", $template, $matches)) {
                $in_keys = array_unique($matches[0]);
                foreach ($in_keys as $match) {
                    $key = trim($match, '{}');
                    $value = '';
                    if (array_key_exists($key, $values)) {
                        $value = $values[$key];
                    }
                    if ($callback) {
                        $value = $callback($key, $value);
                    }
                    $result = str_replace($match, $value, $result);
                }
            }
            return $result;
        });
    }

    public function initSite()
    {

    }
}