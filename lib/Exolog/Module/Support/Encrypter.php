<?php

namespace Exolog\Module\Support;

use Illuminate\Contracts\Encryption\DecryptException;
use Illuminate\Contracts\Encryption\Encrypter as EncrypterContract;
use Illuminate\Contracts\Encryption\EncryptException;
use Illuminate\Contracts\Encryption\StringEncrypter;
use function openssl_decrypt;
use function openssl_encrypt;

class Encrypter implements EncrypterContract, StringEncrypter
{
    /**
     * The encryption key.
     *
     * @var string
     */
    protected $key;

    /**
     * The algorithm used for encryption.
     *
     * @var string
     */
    protected $cipher;


    /**
     * Create a new encrypter instance.
     *
     * @param string|null $key
     * @param string $cipher
     */
    public function __construct(string $key = null, string $cipher = 'aes128')
    {
        $key = $key ?? (string)substr(config('app.key'), 1, 16);

        $this->key = $key;
        $this->cipher = $cipher;
    }


    /**
     * Encrypt the given value.
     *
     * @param mixed $value
     * @param bool $serialize
     * @return string
     *
     * @throws EncryptException
     */
    public function encrypt($value, $serialize = true)
    {
        $string = json_encode($value, JSON_THROW_ON_ERROR);
        return base64_encode(openssl_encrypt($string, $this->cipher, $this->key));
    }

    /**
     * Encrypt a string without serialization.
     *
     * @param string $value
     * @return string
     *
     * @throws EncryptException
     */
    public function encryptString($value)
    {
        return $this->encrypt($value, false);
    }

    /**
     * Decrypt the given value.
     *
     * @param string $payload
     * @param bool $unserialize
     * @return mixed
     *
     * @throws DecryptException
     */
    public function decrypt($payload, $unserialize = true)
    {
        $value = openssl_decrypt(base64_decode($payload), $this->cipher, $this->key);
        return json_decode($value, true, 512, JSON_THROW_ON_ERROR);
    }

    /**
     * Decrypt the given string without unserialization.
     *
     * @param string $payload
     * @return string
     *
     * @throws DecryptException
     */
    public function decryptString($payload)
    {
        return $this->decrypt($payload, false);
    }
}
