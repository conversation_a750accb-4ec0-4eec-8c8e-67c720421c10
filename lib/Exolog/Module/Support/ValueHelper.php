<?php


namespace Exolog\Module\Support;


use ArrayObject;

class ValueHelper
{
    public static function toJSON($value)
    {
        if (is_array($value)) {
            return $value;
        }
        $data = @json_decode($value, true);
        if (!is_array($data) || empty($data)) {
            $data = new ArrayObject();
        }
        return $data;
    }

    public static function toString($value)
    {
        if (empty($value)) {
            $value = new ArrayObject();
        }
        return json_encode($value);
    }
}