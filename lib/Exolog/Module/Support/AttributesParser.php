<?php

namespace Exolog\Module\Support;

class AttributesParser
{

    /**
     *  Retrieve all attributes from the text.
     *  The attributes list has the attribute name as the key and the value of the attribute as
     *  the value in the key/value pair.
     *
     * @param string|null $text Ex. attr1="value" attr2=value attr3='value' attr4
     * @return array|string   Ex [attr1=>'value', attr2=>'value', attr3=>'value', attr4=>'attr4']
     */
    public static function parse(?string $text)
    {
        /**
         * Source: wp-includes/shortcodes.php:512
         */
        $attrs = [];
        if (is_null($text)) {
            return $attrs;
        }

        $pattern = self::getAttributesRegex();
        $text = preg_replace("/[\x{00a0}\x{200b}]+/u", ' ', $text);
        if (preg_match_all($pattern, $text, $match, PREG_SET_ORDER)) {
            foreach ($match as $m) {
                if (!empty($m[1])) {
                    $attrs[strtolower(trim($m[1], '&'))] = stripcslashes($m[2]);
                } elseif (!empty($m[3])) {
                    $attrs[strtolower(trim($m[3], '&'))] = stripcslashes($m[4]);
                } elseif (!empty($m[5])) {
                    $attrs[strtolower(trim($m[5], '&'))] = stripcslashes($m[6]);
                } elseif (isset($m[7]) && strlen($m[7])) {
                    $attrs[strtolower(stripcslashes(trim($m[7], '&')))] = stripcslashes(trim($m[7], '&'));
                } elseif (isset($m[8]) && strlen($m[8])) {
                    $attrs[strtolower(stripcslashes(trim($m[8], '&')))] = stripcslashes(trim($m[8], '&'));
                } elseif (isset($m[9])) {
                    $attrs[strtolower(stripcslashes(trim($m[9], '&')))] = stripcslashes(trim($m[9], '&'));
                }
            }

            // Reject any unclosed HTML elements.
            foreach ($attrs as &$value) {
                if (false !== strpos($value, '<')) {
                    if (1 !== preg_match('/^[^<]*+(?:<[^>]*+>[^<]*+)*+$/', $value)) {
                        $value = '';
                    }
                }
            }
        } else {
            $attrs = ltrim($text);
        }

        return $attrs;
    }

    protected static function getAttributesRegex(): string
    {
        return '/([&\w-]+)\s*=\s*"([^"]*)"(?:\s|$)|([&\w-]+)\s*=\s*\'([^\']*)\'(?:\s|$)|([&\w-]+)\s*=\s*([^\s\'"]+)(?:\s|$)|"([^"]*)"(?:\s|$)|\'([^\']*)\'(?:\s|$)|(\S+)(?:\s|$)/';
    }
}