<?php


namespace Exolog\Module\Support;

use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{

    /**
     * The event handler mappings for the application.
     *
     * @var array
     */
    protected $listen = [];

    /**
     * The subscriber classes to register.
     *
     * @var array
     */
    protected $subscribe = [];

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->booting(function () {
            $events = $this->listen;
            if ($events) {
                $this->registerListeners($events);
            }
            $subscribe = $this->subscribe;
            if ($subscribe) {
                $this->registerSubscribers($subscribe);
            }

        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {

    }

    public function initSite()
    {
        $events = app('config')['site.listen'];
        if ($events) {
            $this->registerListeners($events);
        }

        $subscribe = app('config')['site.subscribe'];
        if ($subscribe) {
            $this->registerSubscribers($subscribe);
        }
    }

    private function registerListeners(array $events)
    {
        foreach ($events as $event => $listeners) {
            foreach (array_unique($listeners) as $listener) {
                Event::listen($event, $listener);
            }
        }
    }

    private function registerSubscribers(array $subscribe)
    {
        foreach ($subscribe as $subscriber) {
            Event::subscribe($subscriber);
        }
    }
}