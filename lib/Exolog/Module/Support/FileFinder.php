<?php

namespace Exolog\Module\Support;

use Exolog\Module\Support\Facades\ExoFile;
use Illuminate\Support\Facades\Storage;

class FileFinder
{
    private array $hashTable = [];

    public function findByHash(array $disks, $hash)
    {
        foreach ($disks as $disk) {
            if (!$this->hashTable[$disk]) {
                $this->buildHashTableForDisk($disk);
            }
            if (array_key_exists($hash, $this->hashTable[$disk])) {
                return ExoFile::composeFileId($disk, $this->hashTable[$disk][$hash]);
            }
        }
        return null;
    }

    private function buildHashTableForDisk($disk): void
    {
        $storage = Storage::disk($disk);
        $files = $storage->allFiles();
        $this->hashTable[$disk] = [];
        foreach ($files as $file) {
            $this->hashTable[$disk][md5($storage->get($file))] = $file;
        }
    }
}