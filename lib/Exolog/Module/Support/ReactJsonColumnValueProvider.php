<?php


namespace Exolog\Module\Support;


use Exolog\Core\Forms\React;
use Exolog\Module\Contracts\PersistentEntityValueProvider;

class ReactJsonColumnValueProvider implements PersistentEntityValueProvider
{
    private React $react;
    private string $column;

    public function __construct(React $react, string $column)
    {
        $this->react = $react;
        $this->column = $column;
    }

    public function getData()
    {
        return $this->react[$this->column];
    }

    public function setData($data): void
    {
        $this->react[$this->column] = $data;
    }

    public function save(array $options = [])
    {
        return $this->react->save($options);
    }
}