<?php

namespace Exolog\Module\Support\PHPDoc;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Scope;

/**
 * @method static static findOrFail(array|string|int $id, $columns = ['*'])
 * @method static static|null find(array|string $id, $columns = ['*'])
 * @method static static create(array $attributes = [])
 * @method static static updateOrCreate(array $attributes = [], $values = [])
 * @method static Builder withoutGlobalScope(Scope|string $scope)
 *
 *
 * @mixin Builder
 */
class PHPDoc_Builder
{

}