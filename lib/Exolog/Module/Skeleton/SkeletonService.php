<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 10.07.2020
 * Time: 16:22
 */

namespace Exolog\Module\Skeleton;


use Exolog\Module\Log\Concerns\HasBufferLogger;
use Exolog\Module\Skeleton\Creator\Creator;
use Exolog\Module\Support\Concerns\HasMakeFactory;
use Exolog\Module\Support\Facades\Site;
use Illuminate\Support\Collection;
use RuntimeException;

class SkeletonService
{
    use <PERSON><PERSON><PERSON><PERSON>Logger, HasMakeFactory;

    /**
     * @param array $skeletons List of skeletons to execute, empty - all
     */
    public function build(array $skeletons = []): void
    {
        if (!Site::isInit()) {
            throw new RuntimeException('Exolog site do not initialized!');
        }

        $skeletonsECO = collect(ECO('skeletons'))
            ->sortBy('order')
            ->when(empty($skeletons), function (Collection $collect) {
                return $collect->filter(function ($item) {
                    return empty($item['manual']);
                });
            })
            ->when(!empty($skeletons), function (Collection $collect) use ($skeletons) {
                return $collect->filter(function ($item, $key) use ($skeletons) {
                    return in_array($key, $skeletons, true);
                });
            })->all();

        $logger = $this->getLogger();

        foreach ($skeletonsECO as $name => $skeleton) {

            $logger->info(sprintf("Start create '%s'", $name));

            /** @var Creator $skeletonObj */
            $skeletonObj = new $skeleton['creator']($logger);
            $skeletonObj->create();
            $logger->info(sprintf("Finish create '%s'", $name));
            $logger->info('-------------------------------');
        }
    }

}