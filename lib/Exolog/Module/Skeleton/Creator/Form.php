<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 10.07.2020
 * Time: 16:36
 */

namespace Exolog\Module\Skeleton\Creator;

use Exception;
use Exolog\Module\Forms\Model\FormPermalink;

final class Form extends Creator
{
    public function create()
    {
        $entities = ECO('skeletons.form.entities');
        foreach ($entities as $entity) {
            $form = $entity['form'];
            try {
                $db_form = \Exolog\Module\Forms\Model\Form::findByName($form['form_name']);
                if ($db_form === null) {
                    $formModel = \Exolog\Module\Forms\Model\Form::saveFromArray($form);
                    $formModel->refresh();
                    foreach ($entity['form_permalinks'] as $key => $item) {
                        /** @var FormPermalink $fp */
                        $fp = $formModel->form_permalinks()->create($item);
                        if ($key === 0) {
                            $formModel->form_permalink_id = $fp->fp_id;
                            $formModel->save();
                        }
                    }
                    $this->logger->info(sprintf("Form '%s' created", $form['form_name']));
                } else {
                    $this->logger->info(sprintf("Form '%s' already exists", $form['form_name']));
                }
            } catch (Exception $exception) {
                $this->logger->error(sprintf("Error!!! %s ", $exception->getMessage()));
            }
        }
    }
}