<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 10.07.2020
 * Time: 16:36
 */

namespace Exolog\Module\Skeleton\Creator;


use Exception;

class Edition extends Creator
{
    public function create()
    {
        $edition = ECO('skeletons.edition.entities')[0];

        try {
            $editionModel = \Exolog\Module\Editions\Model\Edition::getDefault();

            if (is_null($editionModel)) {
                $editionModel = \Exolog\Module\Editions\Model\Edition::create($edition);

                $this->logger->info("Default edition created");
            } else {
                $this->logger->info("Default edition already exists");
            }
        } catch (Exception $exception) {
            $this->logger->error(sprintf("Error!!! %s ", $exception->getMessage()));
        }
    }
}