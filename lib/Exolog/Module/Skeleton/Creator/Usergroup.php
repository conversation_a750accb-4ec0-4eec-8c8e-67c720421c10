<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 10.07.2020
 * Time: 16:36
 */

namespace Exolog\Module\Skeleton\Creator;

use Exception;
use Exolog\Module\Users\Model\Usergroup as UsergroupModel;

final class Usergroup extends Creator
{
    public function create()
    {
        $groups = ECO('skeletons.usergroup.entities');
        foreach ($groups as $group) {
            try {
                $db_groups = UsergroupModel::resolve($group['ug_name']);
                if ($db_groups === null) {
                    UsergroupModel::create($group);
                    $this->logger->info(sprintf("Usergroup '%s' created", $group['ug_name']));
                } else {
                    $this->logger->info(sprintf("Usergroup '%s' already exists", $group['ug_name']));
                }
            } catch (Exception $exception) {
                $this->logger->error(sprintf("Error!!! %s ", $exception->getMessage()));
            }
        }
    }
}