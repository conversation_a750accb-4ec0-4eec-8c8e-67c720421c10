<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 10.07.2020
 * Time: 16:36
 */

namespace Exolog\Module\Skeleton\Creator;

use Exception;
use Exolog\Module\ReactLayout\ReactLayoutConfigurator;
use Exolog\Module\Support\Facades\Defaults;
use Illuminate\Support\Facades\Blade;

final class React extends Creator
{
    public function create()
    {
        $entities = ECO('skeletons.react.entities');

        if (empty(getCurrentEdition())) {
            Defaults::setEdition(\Exolog\Module\Editions\Model\Edition::getDefault());
        }
        $reacts = [];
        foreach ($entities as $react_key => $entity) {
            try {
                $db_entity = react()
                    ->form($entity['form_name'])
                    ->where('menu_title', $entity['react']['menu_title'])
                    ->first();
                if ($db_entity === null) {
                    $react = new \Exolog\Core\Forms\React($entity['form_name'], $entity['react']);
                    $react->save(['use_as_homepage' => $entity['use_as_homepage']]);
                    if ($entity['react_layout']) {
                        /** @var ReactLayoutConfigurator $configurator */
                        $configurator = app(ReactLayoutConfigurator::class);
                        $configurator->saveScope("holderId:{$react->id()}|holderType:react", 'react');
                        $configurator->save(
                            "holderId:{$react->id()}|holderType:react",
                            'react',
                            json_decode(Blade::render(json_encode($entity['react_layout']), ['reacts' => $reacts]),
                                true)
                        );
                    }

                    $this->logger->info(sprintf("React '%s' created, (%s)'",
                            $entity['react']['menu_title'],
                            $react->id())
                    );
                    $reacts[$react_key] = $react->id();
                } else {
                    $reacts[$react_key] = $db_entity->id();
                    $this->logger->info(sprintf("React '%s' already exists", $entity['react']['menu_title']));
                }
            } catch (Exception $exception) {
                $this->logger->error(sprintf("Error!!! %s ", $exception->getMessage()));
            }
        }
    }
}