<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 10.07.2020
 * Time: 16:36
 */

namespace Exolog\Module\Skeleton\Creator;

use Exception;
use Exolog\Module\Groups\Model\Group;

final class Groups extends Creator
{
    public function create()
    {
        $groups = ECO('skeletons.groups.entities');
        foreach ($groups as $group) {
            try {
                $db_groups = Group::query()
                    ->where('group_type', $group['group_type'])
                    ->where('group_name', $group['group_name'])
                    ->first();
                if (is_null($db_groups)) {
                    Group::create($group);
                    $this->logger->info(sprintf("Group '%s' created", $group['group_name']));
                } else {
                    $this->logger->info(sprintf("Group '%s' already exists", $group['group_name']));
                }
            } catch (Exception $exception) {
                $this->logger->error(sprintf("Error!!! %s ", $exception->getMessage()));
            }
        }
    }
}