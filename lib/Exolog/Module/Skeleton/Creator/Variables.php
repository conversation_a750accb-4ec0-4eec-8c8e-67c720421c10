<?php


namespace Exolog\Module\Skeleton\Creator;


use Exolog\Module\Support\Facades\Site;
use Exolog\Module\Support\Facades\Vars;
use RuntimeException;

class Variables extends Creator
{

    public function create()
    {
        $vars = ECO()->get('skeletons.vars.entities');

        foreach ($vars as $v) {
            if ($v['var_type'] === 'site') {
                $v['var_parent'] = Site::get('site_id');
            } else {
                throw new RuntimeException('Not supported ver type!');
            }
            $variable = Vars::get($v['var_name'], $v['var_type'], $v['var_parent'], $v['var_value']);
            $variable->save();
            $this->logger->info(sprintf("Var '%s' created", $v['var_name']));
        }
    }
}