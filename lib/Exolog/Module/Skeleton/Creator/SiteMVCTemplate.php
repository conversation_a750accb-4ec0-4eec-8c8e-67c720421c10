<?php


namespace Exolog\Module\Skeleton\Creator;

use Exception;
use Illuminate\Support\Facades\File;

final class SiteMVCTemplate extends Creator
{

    public function create()
    {
        $path_site = site_path();
        try {
            $success = File::copyDirectory(resource_path('skeleton/site'), $path_site);
            if ($success) {
                $this->logger->info("Site template created");
            } else {
                $this->logger->error("Error!!!");
            }
        } catch (Exception $exception) {
            $this->logger->error(sprintf("Error!!! %s ", $exception->getMessage()));
        }
    }
}