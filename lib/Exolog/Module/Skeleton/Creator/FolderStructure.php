<?php


namespace Exolog\Module\Skeleton\Creator;


use Exolog\Module\Support\Facades\Path;

final class FolderStructure extends Creator
{

    public function create()
    {
        $path_site = site_path();

        // check for the main site folder
        if (!file_exists($path_site)) {
            $mkdir_success = @mkdir($path_site, 0777);
        } else {
            $mkdir_success = true;
        }

        if ($mkdir_success) {
            // check and creating folders
            $folders_to_create = array(
                'cache',
                'cache/views',
                'media',
                'protected',
                'config',
                'class',
                'dist',
                'public',
                'resources'
            );

            foreach ($folders_to_create as $folder) {
                if (!file_exists($path_site . '/' . $folder)) {
                    @mkdir($path_site . '/' . $folder);
                    $this->logger->info(sprintf("Folder '%s' created", $path_site . '/' . $folder));
                }
            }
        }

        // check for folders /config/{site_alias}
        if (!file_exists(Path::getSiteConfPath())) {
            @mkdir(Path::getSiteConfPath(), 0777);
            $this->logger->info(sprintf("Folder '%s' created", Path::getSiteConfPath()));
        }
    }
}