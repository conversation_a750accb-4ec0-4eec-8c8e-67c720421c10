<?php


namespace Exolog\Module\Payments;


use Exolog\Module\Http\Request;
use Exolog\Module\Payments\Events\MollieWebhook;
use Exolog\Module\Routing\SystemController;
use Illuminate\Support\Facades\Log;
use Mollie\Laravel\Facades\Mollie;

class MollieController extends SystemController
{
    public function __invoke(Request $request)
    {
        $paymentId = $request->input('id');
        $payment = Mollie::api()->payments->get($paymentId);
        Log::channel('payments')->info('Mollie webhook payment.', compact('paymentId', 'payment'));
        MollieWebhook::dispatch(compact('payment'));
    }

}