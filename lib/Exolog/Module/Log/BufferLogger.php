<?php

namespace Exolog\Module\Log;

use Exolog\Module\Log\Formater\ExoAdminLineFormatter;
use Exolog\Module\Log\Handler\BufferLogHandler;
use Exolog\Module\Support\Concerns\HasMakeFactory;
use Illuminate\Log\Logger;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Traits\ForwardsCalls;
use Psr\Log\LoggerInterface;

/**
 * @method Logger withContext(array $context = [])
 * @method Logger withoutContext()
 *
 * @see Logger
 */
class BufferLogger implements LoggerInterface
{
    use ForwardsCalls;
    use HasMakeFactory;

    private BufferLogStore $store;
    private LoggerInterface $logger;

    public function __construct()
    {
        $this->store = BufferLogStore::make();

        $this->logger = Log::build([
            'driver' => 'monolog',
            'handler' => BufferLogHandler::class,
            'formatter' => ExoAdminLineFormatter::class,
            'with' => ['store' => $this->store]
        ]);
    }


    public function getRecords(): array
    {
        return $this->store->getRecords();
    }

    public function clear(): void
    {
        $this->store->clear();
    }

    public function __call($key, $arguments = [])
    {
        return $this->forwardCallTo($this->logger, $key, $arguments);
    }

    public function emergency($message, array $context = [])
    {
        $this->writeLog(__FUNCTION__, $message, $context);
    }

    /**
     * Log an alert message to the logs.
     *
     * @param string $message
     * @param array $context
     * @return void
     */
    public function alert($message, array $context = [])
    {
        $this->writeLog(__FUNCTION__, $message, $context);
    }

    /**
     * Log a critical message to the logs.
     *
     * @param string $message
     * @param array $context
     * @return void
     */
    public function critical($message, array $context = [])
    {
        $this->writeLog(__FUNCTION__, $message, $context);
    }

    /**
     * Log an error message to the logs.
     *
     * @param string $message
     * @param array $context
     * @return void
     */
    public function error($message, array $context = [])
    {
        $this->writeLog(__FUNCTION__, $message, $context);
    }

    /**
     * Log a warning message to the logs.
     *
     * @param string $message
     * @param array $context
     * @return void
     */
    public function warning($message, array $context = [])
    {
        $this->writeLog(__FUNCTION__, $message, $context);
    }

    /**
     * Log a notice to the logs.
     *
     * @param string $message
     * @param array $context
     * @return void
     */
    public function notice($message, array $context = [])
    {
        $this->writeLog(__FUNCTION__, $message, $context);
    }

    /**
     * Log an informational message to the logs.
     *
     * @param string $message
     * @param array $context
     * @return void
     */
    public function info($message, array $context = [])
    {
        $this->writeLog(__FUNCTION__, $message, $context);
    }

    /**
     * Log a debug message to the logs.
     *
     * @param string $message
     * @param array $context
     * @return void
     */
    public function debug($message, array $context = [])
    {
        $this->writeLog(__FUNCTION__, $message, $context);
    }

    /**
     * Log a message to the logs.
     *
     * @param string $level
     * @param string $message
     * @param array $context
     * @return void
     */
    public function log($level, $message, array $context = [])
    {
        $this->writeLog($level, $message, $context);
    }

    protected function writeLog($level, $message, $context)
    {
        $this->logger->{$level}($message, $context);
    }
}