<?php

namespace Exolog\Module\Log\Formater;

use Monolog\Formatter\LineFormatter;

class ExoAdminLineFormatter extends LineFormatter
{
    public function __construct(
        ?string $format = null,
        ?string $dateFormat = null,
        bool    $allowInlineLineBreaks = false,
        bool    $ignoreEmptyContextAndExtra = false,
        bool    $includeStacktraces = false
    )
    {
        $format = $format ?? '[%datetime%] %level_name%: %message% %context% %extra%';
        $dateFormat = $dateFormat ?? 'Y-m-d H:i:s';
        parent::__construct($format, $dateFormat, true, true,
            $includeStacktraces);
    }
}