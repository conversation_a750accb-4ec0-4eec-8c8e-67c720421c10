<?php

namespace Exolog\Module\Log;

use Exolog\Module\Support\Concerns\HasMakeFactory;

class BufferLogStore
{
    use HasMakeFactory;

    private array $records = [];

    public function write($record): void
    {
        $this->records[] = $record;
    }

    public function getRecords(): array
    {
        return $this->records;
    }

    public function clear(): void
    {
        $this->records = [];
    }
}