<?php declare(strict_types=1);


namespace Exolog\Module\Log\Handler;

use BadMethodCallException;
use Exolog\Module\Log\BufferLogStore;
use InvalidArgumentException;
use Monolog\Handler\AbstractProcessingHandler;
use Monolog\Logger;

/**
 * Used for testing purposes.
 *
 *
 * @phpstan-import-type Record from Logger
 * @phpstan-import-type Level from Logger
 * @phpstan-import-type LevelName from Logger
 */
class BufferLogHandler extends AbstractProcessingHandler
{

    private BufferLogStore $store;

    public function __construct($level = Logger::DEBUG, bool $bubble = true, BufferLogStore $store = null)
    {
        if (is_null($store)) {
            throw new InvalidArgumentException('BufferLogStore is not initialized!');
        }
        $this->store = $store;
        parent::__construct($level, $bubble);
    }

    public function clear()
    {
        $this->store->clear();
    }

    /**
     * @return void
     */
    public function reset()
    {
        if (!$this->skipReset) {
            $this->clear();
        }
    }

    /**
     * @return void
     */
    public function setSkipReset(bool $skipReset)
    {
        $this->skipReset = $skipReset;
    }


    /**
     * {@inheritDoc}
     */
    protected function write(array $record): void
    {
        $this->store->write($record);
    }

    /**
     * @param string $method
     * @param mixed[] $args
     * @return bool
     */
    public function __call($method, $args)
    {
        if (preg_match('/(.*)(Debug|Info|Notice|Warning|Error|Critical|Alert|Emergency)(.*)/', $method, $matches) > 0) {
            $genericMethod = $matches[1] . ('Records' !== $matches[3] ? 'Record' : '') . $matches[3];
            $level = constant('Monolog\Logger::' . strtoupper($matches[2]));
            $callback = [$this, $genericMethod];
            if (is_callable($callback)) {
                $args[] = $level;

                return call_user_func_array($callback, $args);
            }
        }

        throw new BadMethodCallException('Call to undefined method ' . get_class($this) . '::' . $method . '()');
    }
}
