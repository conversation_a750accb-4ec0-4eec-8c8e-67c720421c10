<?php

namespace Exolog\Module\Log\Concerns;

use Exolog\Module\Log\BufferLogger;
use Exolog\Module\Support\Facades\BuffLog;

trait HasBufferLogger
{
    protected BufferLogger $logger;

    public function getLogger(): BufferLogger
    {
        return $this->resolveLogger();
    }

    protected function setLogger($logger): void
    {
        $this->logger = $logger;
    }

    public function getLogRecords(): array
    {
        return $this->logger->getRecords();
    }

    protected function resolveLogger()
    {
        if (!isset($this->logger)) {
            $this->logger = BuffLog::getFacadeRoot();
        }
        return $this->logger;
    }
}