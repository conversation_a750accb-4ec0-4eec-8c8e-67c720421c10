<?php

namespace Exolog\Module\Newsletter\Commands;

use Exception;
use Exolog\Module\Console\SystemCommand;
use Exolog\Module\Database\Scopes\SiteScope;
use Exolog\Module\Newsletter\Model\Newsletter;
use Exolog\Module\Support\Facades\Site;
use Illuminate\Support\Facades\Artisan;

class SendNewslettersCommand extends SystemCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'exolog:send-newsletters {--site_id=}';


    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a scheduled newsletter for the site.';

    /**
     * Execute the console command.
     *
     * @return int
     * @throws Exception
     */
    public function handle()
    {
        if ($this->option('site_id')) {
            $this->sendOneSite();
        } else {
            $this->sendAllSite();
        }
        return 0;
    }

    private function sendOneSite(): void
    {
        $this->resolveSite();

        $newsletters = Newsletter::readyForMailing()->orderBy('rn_date_created')->get();
        $this->info('Start sending newsletters for site:' . Site::alias());
        $newsletters->each(function (Newsletter $newsletter) {
            $log = $newsletter->sendMailableNewsletter();
            $this->info('Newsletter:' . $newsletter->rn_id . '-' . $newsletter->rn_subject);
            $this->info('Sent:' . $log->getSentCount());
            if ($log->getErrorCount()) {
                $this->error('Errors:' . $log->getErrorCount());
            }
            $this->info('---------------------------------');
        });
        $this->info('Finish sending newsletters for site:' . Site::alias());

    }

    private function sendAllSite(): void
    {
        $sites = Newsletter::readyForMailing()
            ->withoutGlobalScopes([SiteScope::class])
            ->select(['rn_site'])
            ->groupBy('rn_site')->pluck('rn_site');

        $sites->each(function ($site_id) {
            $this->info('Send the job to queue for site:' . $site_id);
            Artisan::queue('exolog:send-newsletters', ['--site_id' => $site_id]);
        });

    }
}
