<?php


namespace Exolog\Module\Newsletter\Model;


use Exception;
use Exolog\Module\Database\Concerns\HasSiteScope;
use Exolog\Module\Forms\FQT\Mailable\SendLog;
use Exolog\Module\Site\Model\Site;
use Exolog\Module\Support\Facades\Auth;
use Exolog\Module\Support\PHPDoc\PHPDoc_Builder;
use Exolog\Module\Users\Model\User;
use Exolog\Module\Users\Model\Usergroup;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use RuntimeException;

/**
 * @property integer $rn_id
 * @property string $rn_status
 * @property int $rn_user
 * @property Carbon $rn_date_created
 * @property int $rn_react_id
 * @property int $rn_usergroup
 * @property string $rn_send_from
 * @property string $rn_send_fromname
 * @property string $rn_subject
 * @property int $rn_fq_id
 * @property array $rn_extra
 * @method static Builder readyForMailing()
 *
 * @mixin PHPDoc_Builder
 *
 */
class Newsletter extends Model
{
    use HasSiteScope;

    public const SITE_FIELD = 'rn_site';
    public const STATUS_NEW = 'new';
    public const STATUS_BUSY = 'busy';
    public const STATUS_DONE = 'done';
    public const STATUS_ERROR = 'error';
    public const STATUS_TERMINATED = 'terminated';

    protected $table = 'read_newsletter';
    protected $primaryKey = 'rn_id';
    public $timestamps = false;
    protected $fillable = [
        'rn_send_from',
        'rn_send_fromname',
        'rn_subject',
        'rn_mail_body',
        'rn_usergroup',
        'rn_date_scheduled',
        'rn_react_id',
        'rn_count_mails',
        'rn_count_mails_error',
        'rn_status',
        'rn_fq_id',
        'rn_extra',
    ];

    protected $casts = [
        'rn_date_scheduled' => 'datetime',
        'rn_date_created' => 'datetime',
        'rn_date_done' => 'datetime',
        'rn_date_busy' => 'datetime',


        'rn_react_id' => 'int',
        'rn_user' => 'int',
        'rn_usergroup' => 'int',
        'rn_site' => 'int',
        'rn_fq_id' => 'int',

        'rn_extra' => 'array',
    ];

    public function scopeReadyForMailing(Builder $query)
    {
        return $query
            ->where('rn_status', static::STATUS_NEW)
            ->where('rn_date_scheduled', '<=', now());
    }

    public function usergroup(): HasOne
    {
        return $this->hasOne(Usergroup::class, 'ug_id', 'rn_usergroup');
    }

    public function user(): HasOne
    {
        return $this->hasOne(User::class, 'u_id', 'rn_user');
    }

    public function site(): HasOne
    {
        return $this->hasOne(Site::class, 'site_id', 'rn_site');
    }

    public function terminate(): Newsletter
    {
        if ($this->rn_status === static::STATUS_NEW) {
            $this->rn_status = static::STATUS_TERMINATED;
            $this->save();
        } else {
            throw new  RuntimeException('You can terminate the newsletter in status "new" only!');
        }
        return $this;
    }

    protected static function booted()
    {
        static::creating(function (Newsletter $model) {
            $model->rn_user = Auth::id();
            $model->rn_date_created = now();
        });
    }

    /**
     * @throws Exception
     */
    public function sendMailableNewsletter(): SendLog
    {
        $errorLog = [];

        // set busy to newsletter
        $this->rn_status = static::STATUS_BUSY;
        $this->save();

        try {
            $react = react()->find($this->rn_react_id);
            if ($react === null) {
                throw new RuntimeException("Mailable react not found!");
            }
            $sendLog = $react->getMailable()
                ->send([
                    'toemail' => $this->rn_usergroup ?? 'fq_id:' . $this->rn_fq_id,
                    'fromEmail' => $this->rn_send_from,
                    'fromName' => $this->rn_send_fromname,
                    'subject' => $this->rn_subject,
                    'QBRules' => $this->rn_extra['QBRules'] ?? [],
                ])
                ->getSendLog();

            $this->fill([
                'rn_status' =>  static::STATUS_DONE,
                'rn_date_done' => date("Y-m-d H:i:s"),
                'rn_count_mails' => $sendLog->getSentCount(),
                'rn_count_mails_error' => $sendLog->getErrorCount(),
                'error_log' => var_export(array_merge($errorLog, $sendLog->getErrors()), true)
            ]);
            $this->save();
            return $sendLog;

        } catch (Exception $exception) {
            Log::stack(['exolog-critical'])->critical($exception);
            $this->fill([
                'rn_status' => static::STATUS_ERROR,
                'rn_date_done' => date("Y-m-d H:i:s"),
                'error_log' => var_export($exception, true)
            ]);
            $this->save();
            throw $exception;
        }
    }
}