<?php


namespace Exolog\Module\Container\Traits;


use Exolog\Core\Forms\React;
use Exolog\Module\Container\Container;
use Exolog\Module\Container\HelperTree;
use Illuminate\Support\Arr;
use RuntimeException;


/**
 * Trait ItemTreeOprations
 * This is tempaporary traits while we remove LegacyContainer
 * @package Exolog\Module\Container\Traits
 */
trait ItemTreeOprations
{
    public function copyItem(string $path): Container
    {
        $tree = $this->getItemsTree();

        $tree = HelperTree::copyItem($tree, $path);

        $this->updateItems($tree);

        return $this;
    }

    public function removeItem($path): Container
    {
        $tree = $this->getItemsTree();

        $item = Arr::get($tree, $path);

        if (empty($item)) {
            throw new RuntimeException('Can`t find item');
        }

        $tree = HelperTree::removeItem($tree, $path);

        $this->updateItems($tree);

        //get items which gone from container
        $items_ids = collectIds([$item]);
        $items_ids = array_values(array_diff($items_ids, $this->getItemsIDs()));

        if (count($items_ids) > 0) {
            $reacts = react()->form($this->getForms())->whereIn('react_id', $items_ids)->get();
            foreach ($reacts as $react) {
                /** @var React $react */
                $react->removeParentContainer($this);
                $react->save();
            }
        }
        return $this;
    }
}