<?php

namespace Exolog\Module\Container;


use Exolog\Core\Forms\React;
use Exolog\Module\Container\Traits\ItemTreeOprations;
use Exolog\Module\Contracts\XEditable;
use Exolog\Module\Forms\Model\Form;
use Exolog\Module\Forms\Model\FormPermalink;
use Exolog\Module\Forms\ResponseRules;
use Exolog\Module\Vars\Variable;
use Exolog\Module\Vars\VariableContainer;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use RuntimeException;

class BaseContainer implements Container, XEditable
{
    use ItemTreeOprations;

    public const HOLDER_TYPE_VAR = 'var';
    public const HOLDER_TYPE_REACT = 'react';
    public const HOLDER_TYPE_UNIT = 'unit';

    protected ContainerDataProvider $dataProvider;

    public function __construct(ContainerDataProvider $dataProvider)
    {
        $this->dataProvider = $dataProvider;
    }

    public function getForms()
    {
        $config = $this->getConfig();
        return Form::find(Arr::pluck($config['forms'], 'form_id'))->pluck('form_name')->toArray();
    }

    public function getDataProvider(): ContainerDataProvider
    {
        return $this->dataProvider;
    }

    public function getConfig($path = null, $default = null)
    {
        $config = $this->dataProvider->getConfig();
        if (!is_null($path)) {
            $config = data_get($config, $path, $default);
        }
        return $config;
    }

    public function setConfig($config, $path = null): Container
    {
        if (is_null($path)) {
            $this->dataProvider->setConfig($config);
        } else {
            $full_config = $this->dataProvider->getConfig();
            data_set($full_config, $path, $config);
            $this->dataProvider->setConfig($full_config);
        }
        return $this;
    }

    public function getContainerId()
    {
        return $this->dataProvider->getContainerId();
    }

    public function getHolderType()
    {
        return $this->dataProvider->getHolderType();
    }

    /**
     * @return React|Variable|VariableContainer|FormPermalink
     */
    public function getHolder()
    {
        return $this->dataProvider->getHolder();
    }

    public function getHolderId()
    {
        return $this->dataProvider->getHolderId();
    }


    protected function buildTree($inPlaceReact, $filter = []): array
    {
        $reacts = $this->getReacts($filter)->keyBy('react_id')->toArray();

        if ($this->getDataProvider() instanceof VarContainerDataProvider) {
            if ($this->getDataProvider()->getHolder()->getType() === 'form') {
                foreach ($reacts as $react) {
                    $redir_rules = $this->responseRules()->getRelatedReactRules($react['react_id'], 'redir_rules');
                    $reacts[$react['react_id']]['react_rule_use_for_redir'] = $redir_rules['rule_use_for_redir'];
                }
            }
        }

        return HelperTree::buildTree($reacts, $this->getData('items'), $inPlaceReact);
    }


    public function getItemsTree($filter = [])
    {
        return $this->buildTree(false, $filter);
    }

    protected function getItemsIDs()
    {
        $items = $this->getData('items');
        if (empty($items)) {
            return [];
        }
        return collectIds($items);
    }

    protected function updateItems($items): Container
    {
        if (empty($items)) {
            $items = [];
        }
        if (!is_array($items)) {
            throw new RuntimeException('Invalid parameter. Array expected');
        }

        $items = array_recursive_remove_key($items, [], ['id', 'form'], ['children']);
        $this->setData($items, 'items');

        return $this;
    }

    public function setItems($items): Container
    {

        $this->updateItems($items);

        $this->addParent($this->getItemsIDs());

        return $this;
    }

    public function addItems(array $items): Container
    {
        if (empty($items)) {
            return $this;
        }

        $items_id_array = array_column($items, 'id');

        if (empty($items_id_array)) {
            return $this;
        }

        $tree = $this->getItemsTree();
        foreach ($items as $item) {
            array_unshift($tree, $item);
        }
        $this->updateItems($tree);

        $this->addParent($items_id_array);

        return $this;
    }

    public function removeItems(array $items): Container
    {
        $tree = $this->getItemsTree();
        $items_ids = array_column($items, 'id');
        $tree = array_recursive_filter($tree, function ($key, $val) use ($items_ids) {
            return !in_array($val['id'], $items_ids);
        });
        $this->updateItems($tree);

        react()->form($this->getForms())
            ->whereIn('react_id', $items_ids)
            ->get()
            ->each(function (React $react) {
                $react->removeParentContainer($this);
                $react->save();
            });

        return $this;
    }


    public function getUselessItems(array $search_params = []): Collection
    {
        $query = $this->buildUselessQuery($search_params);
        return $query->get();
    }

    public function getExeditAttr($exedit = []): HtmlString
    {
        return $this->dataProvider->getExeditAttr($exedit);
    }

    protected function getData(string $path)
    {
        $data = $this->dataProvider->getData();
        if (!empty($path)) {
            return $data[$path];
        }
        return $data;
    }

    protected function setData($data, string $path)
    {
        $fullData = $this->dataProvider->getData();

        if (!empty($path)) {
            $fullData[$path] = $data;
        } else {
            $fullData = $data;
        }
        $this->dataProvider->setData($fullData);
    }

    public function save()
    {
        $this->dataProvider->save();
        $this->updatePermalinks();
    }

    public function responseRules()
    {
        $prefix = Str::after($this->dataProvider->getHolder()->getName(), 'container_');
        return new ResponseRules($this->getHolder()->getParent(), $prefix);
    }

    protected function getExtraParams()
    {
        $str = $this->getConfig()['extraParams'];
        if (!empty($str)) {
            return json_decode($str, true);
        }
        return [];
    }

    public function updatePermalinks()
    {
        $ids = $this->getItemsIDs();
        Helper::updatePermalinks($ids);
    }

    public function getReactsTree($filter = []): array
    {
        return $this->buildTree(true, $filter);
    }

    public function getReactsArray($filter = []): array
    {
        return $this->getReacts($filter)->toArray();
    }

    /**
     * save reference to container on react(back link)
     * used for permalink update after add to container
     */
    private function addParent(array $react_ids)
    {
        react()
            ->form($this->getForms())
            ->whereIn('react_id', $react_ids)
            ->get()
            ->each(function (React $react) {
                $react->addParentContainer($this);
                $react->save();
            });
    }

    private function getReacts(array $filter = []): Collection
    {
        if (empty($filter['_ignore_publish'])) {
            $outputOnlyPublished = $this->getConfig('outputOnlyPublished', false);
            if ($outputOnlyPublished && !isset($filter['published'])) {
                $filter['publish'] = '1';
            }
        }

        $ids = $this->getItemsIDs();

        if (empty($ids)) {
            return collect([]);
        }
        $query = react()
            ->form($this->getForms())
            ->withPublishState()
            ->whereIn('react_id', $ids);

        foreach ($filter as $key => $value) {
            if (Str::startsWith($key, '_')) {
                continue;
            }
            if (is_numeric($value)) {
                $query->where($key, $value);
            } else {
                $query->where($key, 'LIKE', $value);
            }
        }
        return $query->get(['container' => $this->getContainerId()]);

    }

    public function getUselessItemsPaginate(array $search_params = []):LengthAwarePaginator
    {
        return $this->buildUselessQuery($search_params)->exoPaginate($search_params);
    }

    private function buildUselessQuery(array $search_params)
    {
        $forms = $this->getForms();
        $items_ids = $this->getItemsIDs();
        $query = react()->form($forms);

        $qbRules = $this->getConfig('QBRules', []);
        $query->applyQBRules($qbRules);

        $query->whereNotIn('react_id', $items_ids);

        if (count($search_params)) {
            $query = $query->search($search_params);
        }
        return $query;
    }
}
