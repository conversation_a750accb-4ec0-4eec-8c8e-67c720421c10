<?php


namespace Exolog\Module\Container;


use Exolog\Module\Contracts\EntityValueProvider;
use Exolog\Module\Vars\Variable;
use Exolog\Module\Vars\VariableContainer;
use Illuminate\Support\Str;
use RuntimeException;

class VarContainerDataProvider extends AbstractContainerDataProvider
{
    private Variable $var;

    protected EntityValueProvider $value;

    public function __construct(EntityValueProvider $value, VariableContainer $var)
    {
        //TODO move it to getContainerId()?
        /*if (empty($var) || empty($var->getId())) {
            throw new \RuntimeException('"var_id" is not defined. Can`t create a container!');
        }*/
        $this->value = $value;
        $this->var = $var;
    }

    protected static function _getCommonConfig($params)
    {
        return [];
    }

    protected static function _saveCommonConfig($config, $params)
    {
        throw new RuntimeException('Not implemented');
    }

    protected function commonConfigParams()
    {
        return [];
    }

    protected static function getContainersWithCommonConfig($params)
    {
        return [];
    }

    public function getContainerId()
    {
        return Helper::array_to_str([
            'holderType' => $this->getHolderType(),
            'holderId' => $this->getHolderId(),
            //just for info
            'var_name' => $this->var->getName(),
            'var_type' => $this->var->getType(),
            'var_parent' => $this->var->getParent(),
        ]);
    }

    public function save()
    {
        $this->var->save();
    }

    public function getHolderType()
    {
        return BaseContainer::HOLDER_TYPE_VAR;
    }

    public function getHolder()
    {
        return $this->var;
    }

    public function getHolderId()
    {
        return $this->var->getId();
    }

    protected function _getConfig()
    {
        $config = $this->getData()['config'];
        $config = empty($config) ? [] : $config;
        $config['useOwnConfig'] = 1;
        return $config;
    }

    public function getDefaultName()
    {
        return Str::after($this->var->getName(), 'container_');
    }
}