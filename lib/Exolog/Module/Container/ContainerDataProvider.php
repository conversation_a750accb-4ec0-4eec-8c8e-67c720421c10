<?php


namespace Exolog\Module\Container;


use Exolog\Core\Forms\React;
use Exolog\Module\Vars\Variable;
use Illuminate\Support\HtmlString;

interface ContainerDataProvider
{
    public function getData();

    public function setData($data);

    public function getContainerOwnConfig();

    public function getContainerCommonConfig();

    public function getConfig();

    public function setConfig(array $config);

    public function getExeditAttr($exedit = []): HtmlString;

    public function getDefaultName();

    public function getContainerId();

    public function save();

    public function getHolderType();

    /**
     * @return Variable|React|mixed
     */
    public function getHolder();

    public function getHolderId();

    public static function getCommonConfig($params);

}