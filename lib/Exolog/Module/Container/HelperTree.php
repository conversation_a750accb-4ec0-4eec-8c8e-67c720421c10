<?php


namespace Exolog\Module\Container;


use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use RuntimeException;

class HelperTree
{

    public static function buildTree(array $reacts, $ids_tree, $inPlaceReact = false): array
    {
        $tree = [];
        $count = 0;
        foreach ($ids_tree as $tree_item) {
            if (in_array($reacts[$tree_item['id']], $reacts)) {
                if ($inPlaceReact) {
                    $tree[$count] = $reacts[$tree_item['id']];
                } else {
                    $tree[$count] = [
                        'id' => $tree_item['id'],
                        'react' => $reacts[$tree_item['id']]
                    ];
                }
                if ($tree_item['children']) {
                    $tree[$count]['children'] = static::buildTree($reacts, $tree_item['children'], $inPlaceReact);
                }
                $count++;
            }
        }
        return $tree;
    }

    public static function copyItem($tree, $path)
    {
        $item = Arr::get($tree, $path);

        if (empty($item)) {
            throw new RuntimeException('Can`t find source item');
        }

        if (Str::contains($path, '.')) {
            $parent_path = Str::beforeLast($path, '.');
            $parent_item = Arr::get($tree, $parent_path);
        } else {
            $parent_item = $tree;
        }

        $offset = Str::afterLast($path, '.');

        array_splice($parent_item, $offset, 0, [$item]);


        if (Str::contains($path, '.')) {
            $parent_path = Str::beforeLast($path, '.');
            Arr::set($tree, $parent_path, $parent_item);
        } else {
            $tree = $parent_item;
        }

        return $tree;
    }

    public static function removeItem($tree, $path)
    {
        $item = Arr::get($tree, $path);

        if (empty($item)) {
            throw new RuntimeException('Can`t find source item');
        }

        if (Str::contains($path, '.')) {
            $parent_path = Str::beforeLast($path, '.');
            $parent_item = Arr::get($tree, $parent_path);
        } else {
            $parent_item = $tree;
        }

        $offset = Str::afterLast($path, '.');

        array_splice($parent_item, $offset, 1);

        if (Str::contains($path, '.')) {
            $parent_path = Str::beforeLast($path, '.');
            Arr::set($tree, $parent_path, $parent_item);
        } else {
            $tree = $parent_item;
        }

        return $tree;
    }

}