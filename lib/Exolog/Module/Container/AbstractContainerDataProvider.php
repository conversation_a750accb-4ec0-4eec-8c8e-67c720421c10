<?php


namespace Exolog\Module\Container;


use Exolog\Module\Contracts\EntityValueProvider;
use Exolog\Module\Support\ValueAccessor;
use Illuminate\Support\Arr;
use Illuminate\Support\HtmlString;

abstract class AbstractContainerDataProvider implements EntityValueProvider, ContainerDataProvider
{
    use ValueAccessor;

    protected const defaultName = 'container';

    protected static function devalueConfig($config)
    {
        unset($config['$_isBaseContainer']);
        $forms = [];
        foreach ($config['forms'] as $form) {
            $forms[] = Arr::only($form, [
                'form_id',
                'form_permalink_id',
                'use_as_primary',
            ]);
        }
        $config['forms'] = $forms;
        return $config;
    }

    protected static function enrichConfig($config, $defaultContainerName)
    {
        $config = array_merge([
            '$_isBaseContainer' => 1,
            'name' => $defaultContainerName,
            'buttonPosition' => 'Auto',
            'maxDepth' => 1,
            'forms' => [],
        ], $config);

        $config['forms'] = empty($config['forms']) ? [] : $config['forms'];

        return $config;
    }

    public static function updatePermalinks($params)
    {
        foreach (static::getContainersWithCommonConfig($params) as $container) {
            $container->updatePermalinks();
        }
    }

    abstract protected static function _getCommonConfig($params);

    abstract protected static function _saveCommonConfig($config, $params);

    static function saveCommonConfig($config, $params)
    {
        $config = static::devalueConfig($config);
        static::_saveCommonConfig($config, $params);
    }

    /**
     * Just for UI
     * Load current own config
     */
    public function getContainerOwnConfig()
    {
        $config = $this->getData()['config'];
        $config = empty($config) ? [] : $config;
        $config['useOwnConfig'] = 1;
        return static::enrichConfig($config, $this->getDefaultName());
    }

    /**
     * Just for UI
     * Load current common config
     */
    public function getContainerCommonConfig()
    {
        return static::getCommonConfig($this->commonConfigParams());
    }

    /**
     * Return list of param to resole entity/storage for common config
     */
    abstract protected function commonConfigParams();

    /**
     * @param $params
     * @return BaseContainer[]
     */
    abstract protected static function getContainersWithCommonConfig($params);

    public static function getCommonConfig($params)
    {
        $config = static::_getCommonConfig($params);
        return static::enrichConfig($config, static::defaultName);
    }

    public function getConfig()
    {
        $config = $this->_getConfig();

        if (empty($config['useOwnConfig'])) {
            $config = static::_getCommonConfig($this->commonConfigParams());
        }

        return static::enrichConfig($config, $this->getDefaultName());
    }

    public function setConfig(array $config)
    {
        $config = static::devalueConfig($config);

        if (empty($config['useOwnConfig'])) {
            static::_saveCommonConfig($config, $this->commonConfigParams());

            $data = $this->getData();
            if (empty($data['config'])) {
                $data['config'] = [];
            }
            $data['config']['useOwnConfig'] = 0;
            $this->setData($data);

        } else {
            $data = $this->getData();
            $data['config'] = $config;
            $this->setData($data);
        }
    }

    public function getExeditAttr($exedit = []): HtmlString
    {
        if (empty($exedit)) {
            $exedit = [];
        }
        $exedit = array_merge([
            'container' => $this->getContainerId()
        ], $exedit);

        return new HtmlString(sprintf('exedit-container="%s"', htmlspecialchars(json_encode($exedit), ENT_QUOTES, 'UTF-8')));
    }

    public function getDefaultName()
    {
        return static::defaultName;
    }

    /**
     * String with Unique ID of this container
     * - resolve it from UI
     * - as identifier of parent container
     */
    abstract public function getContainerId();

    abstract public function save();

    /**
     *
     * @return array
     */
    abstract protected function _getConfig();
}