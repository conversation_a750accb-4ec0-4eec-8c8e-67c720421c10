<?php


namespace Exolog\Module\Container;


use Exolog\Module\Forms\FQT\Unit\PayloadValueProvider;
use RuntimeException;

class UnitContainerDataProvider extends AbstractContainerDataProvider
{
    protected PayloadValueProvider $value;

    public function __construct(PayloadValueProvider $value)
    {
        $this->value = $value;
    }

    protected static function _getCommonConfig($params)
    {
        return [];
    }

    protected static function _saveCommonConfig($config, $params)
    {
        throw new RuntimeException('Not implemented');
    }

    protected function commonConfigParams()
    {
        return [];
    }

    protected static function getContainersWithCommonConfig($params)
    {
        return [];
    }

    public function getContainerId()
    {
        //holderType          - react, var, unit,
        //holderId            - react_id, var_id, var_id|fq_name
        //holderKey           - only for unit
        //holderParentId      - only for unit react_id
        //holderParentType    - react, var

        return Helper::array_to_str([
            'holderType' => $this->getHolderType(),             //unit
            'holderId' => $this->getHolderId(),                 //var_id|fq_name
            'holderKey' => $this->getHolderKey(),               //payload key
            'holderParentId' => $this->getHolderParentId(),     //react_id for react
            'holderParentType' => $this->getHolderParentType(), //var|react
        ]);
    }

    public function save()
    {
        return $this->value->save();
    }

    public function getHolder()
    {
        return $this->value->getUnit();
    }

    protected function _getConfig()
    {
        $config = $this->getData()['config'];
        $config = empty($config) ? [] : $config;
        $config['useOwnConfig'] = 1;
        return $config;
    }

    public function getDefaultName()
    {
        return $this->value->getKey();
    }

    public function getHolderType()
    {
        return BaseContainer::HOLDER_TYPE_UNIT;
    }

    public function getHolderId()
    {
        return $this->value->getHolderId();
    }

    protected function getHolderKey()
    {
        return $this->value->getKey();
    }

    protected function getHolderParentId()
    {
        return $this->value->getUnit()->getHolderId();
    }

    protected function getHolderParentType()
    {
        return $this->value->getUnit()->getHolderType();
    }
}