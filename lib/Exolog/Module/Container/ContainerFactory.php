<?php


namespace Exolog\Module\Container;


use Exolog\Module\Forms\FQT\Unit\UnitValueInterface;
use Exolog\Module\Support\Facades\Vars;
use Exolog\Module\Vars\VariableUnit;
use RuntimeException;

class ContainerFactory
{
    /**
     * Container builder.
     * @param array|string $container Container ID string from exedit or Array of params
     */
    public static function resolve($container): Container
    {
        if (is_array($container)) {
            $containerParams = $container;
        } else {
            $containerParams = Helper::str_to_array($container);
        }

        if ($containerParams['holderType']) {
            if ($containerParams['holderType'] === BaseContainer::HOLDER_TYPE_UNIT
                && $containerParams['holderParentType'] === UnitValueInterface::HOLDER_TYPE_REACT) {
                $react = react()->find($containerParams['holderParentId']);
                if ($react === null) {
                    throw new ContainerHolderNotFoundException($containerParams);
                }
                $fq = $react->getFieldValue($containerParams['holderId']);
                return $fq->getPayload([
                    'key' => $containerParams['holderKey'],
                    'type' => 'container'
                ])->getContainer();
            }

            if ($containerParams['holderType'] === BaseContainer::HOLDER_TYPE_UNIT
                && $containerParams['holderParentType'] === UnitValueInterface::HOLDER_TYPE_VAR) {
                $var = Vars::getById($containerParams['holderId']);
                if ($var === null) {
                    throw new ContainerHolderNotFoundException($containerParams);
                }
                if ($var instanceof VariableUnit) {
                    return $var->getPayload([
                        'key' => $containerParams['holderKey'],
                        'type' => 'container'
                    ])->getContainer();
                }
                throw new RuntimeException('Can`t get container from unit variable! Please check variable type(prefix).' . json_encode($containerParams));

            }

            if ($containerParams['holderType'] === BaseContainer::HOLDER_TYPE_REACT) {
                $react = react()->find($containerParams['holderId']);
                if ($react === null) {
                    throw new ContainerHolderNotFoundException($containerParams);
                }
                $fq = $react->getFieldValue($containerParams['fq_name']);
                return $fq->getContainer();
            }

            if ($containerParams['holderType'] === BaseContainer::HOLDER_TYPE_VAR) {
                if ($containerParams['holderId']) {
                    $var = Vars::getById($containerParams['holderId']);
                } else {
                    $var = Vars::get($containerParams['var_name'], $containerParams['var_type'],
                        $containerParams['var_parent'], $containerParams['holderId']);
                }
                if ($var === null) {
                    throw new ContainerHolderNotFoundException($containerParams);
                }
                return $var->getContainer();
            }

            throw new RuntimeException("Container holder {$containerParams['holderType']} not implemented!");
        }

        throw new RuntimeException("Container type isn't supported.");
    }
}