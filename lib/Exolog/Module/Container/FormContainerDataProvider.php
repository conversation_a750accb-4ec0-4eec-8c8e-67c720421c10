<?php


namespace Exolog\Module\Container;


use Exolog\Core\Forms\React;
use Exolog\Module\Contracts\EntityValueProvider;
use Exolog\Module\Forms\Model\Form;

class FormContainerDataProvider extends AbstractContainerDataProvider
{
    /**
     * @var React
     */
    private React $react;
    private string $fq_name;

    public EntityValueProvider $value;
    protected array $config;

    public function __construct(EntityValueProvider $value, React $react, $fq_name)
    {
        $this->react = $react;
        $this->fq_name = $fq_name;
        $this->value = $value;
    }

    protected static function getCustomParam($form_id, $fq_name)
    {
        $fq = Form::find($form_id)->getFQ($fq_name);
        return json_decode($fq->fq_custom_params, true);
    }

    protected static function saveCustomParam($config, $form_id, $fq_name)
    {
        $fq = Form::find($form_id)->getFQ($fq_name);
        $fq->fq_custom_params = json_encode($config);
        $fq->save();
    }

    protected static function _getCommonConfig($params)
    {
        $config = static::getCustomParam($params['form_id'], $params['fq_name']);
        if (empty($config)) {
            $config = [];
        }
        return $config;
    }

    protected function commonConfigParams()
    {
        return [
            'fq_name' => $this->fq_name,
            'form_id' => $this->react->getReactFormId()
        ];
    }


    /**
     * @param $params
     * @return Container[]
     */
    protected static function getContainersWithCommonConfig($params)
    {
        $reacts = react()->form($params['form_id'])->get();
        $containers = [];
        foreach ($reacts as $react) {
            $fq = $react->getFieldValue($params['fq_name']);
            if ($fq instanceof \Exolog\Module\Forms\FQT\Container\Container) {
                if (!$fq->getContainer()->getConfig()['useOwnConfig']) {
                    $containers[] = $fq->getContainer();
                }
            }
        }
        return $containers;
    }

    public function getDefaultName()
    {
        return $this->fq_name;
    }

    public function getContainerId()
    {
        return Helper::array_to_str([
            'holderType' => $this->getHolderType(),
            'holderId' => $this->getHolderId(),
            'fq_name' => $this->fq_name
        ]);
    }

    public function save()
    {
        //use Fake rect to save only one field?
        $this->react->save();
    }

    protected static function _saveCommonConfig($config, $params)
    {
        static::saveCustomParam($config, $params['form_id'], $params['fq_name']);
        static::updatePermalinks($params);
    }

    public function getHolderType()
    {
        return BaseContainer::HOLDER_TYPE_REACT;
    }

    public function getHolder()
    {
        return $this->react;
    }

    public function getHolderId()
    {
        return $this->react['react_id'];
    }

    protected function _getConfig()
    {
        $config = $this->getData()['config'];
        return empty($config) ? [] : $config;
    }
}