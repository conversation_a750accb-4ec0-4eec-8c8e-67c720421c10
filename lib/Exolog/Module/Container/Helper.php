<?php


namespace Exolog\Module\Container;


use Exolog\Core\Forms\React;
use Exolog\Module\Permalinks\PermalinksService;
use Illuminate\Support\Arr;

class Helper
{
    public static function array_to_str(array $array): string
    {
        $tmp = [];
        foreach ($array as $key => $value) {
            $tmp[] = $key . ':' . $value;
        }
        return implode('|', $tmp);
    }

    public static function str_to_array(string $str): array
    {
        if (empty($str)) {
            return [];
        }
        $array = [];
        foreach (explode('|', $str) as $value) {
            if (str_contains($value, ':')) {
                $tmp = explode(':', $value);
                $array[$tmp[0]] = $tmp[1];
            } else {
                $array[] = $value;
            }

        }
        return $array;
    }


    /**
     * function for generate and update Permalink in reacts by
     * @param array|int $react_id react's ids
     */
    public static function updatePermalinks($react_id): void
    {
        react()->whereIn('react_id', Arr::wrap($react_id))
            ->get()
            ->each(function (React $react) {
                PermalinksService::make()->updateReactPermalinks($react);
            });
    }
}