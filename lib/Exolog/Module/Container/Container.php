<?php


namespace Exolog\Module\Container;

use Exolog\Core\Forms\React;
use Exolog\Module\Vars\VariableContainer;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface Container
{
    public function getDataProvider(): ContainerDataProvider;

    /**
     * get container config
     * @return array
     */
    public function getConfig($path = null, $default = null);

    /**
     * Rerurn array of nested forms
     * array('Product','Page')
     */
    public function getForms();

    /**
     * set container config
     */
    public function setConfig($config, $path = null): Container;

    public function getItemsTree($filter = []);


    public function getContainerId();

    /**
     * Replace items
     */
    public function setItems($items): Container;

    /**
     * add new items to container
     * @param array $items - list of new elements, each element should contain "id" field
     * @param bool $isNew
     * @return Container
     */
    public function addItems(array $items): Container;

    /**
     * Add duplicate an item by path and put it on the same level:
     * ex path = 0.children.5
     *
     * @param $path
     * @return Container
     */
    public function copyItem(string $path): Container;

    /**
     * @param array $items
     * @return $this
     */
    public function removeItems(array $items): Container;

    /**
     * method removes a given item from items tree using "dot" notation:
     * ex path = 0.children.5
     *
     * @param $path
     * @return Container
     */
    public function removeItem($path): Container;

    public function getUselessItems(array $search_params = []): Collection;

    public function getUselessItemsPaginate(array $search_params = []): LengthAwarePaginator;

    public function getHolderType();

    /**
     * @return VariableContainer|React|mixed
     */
    public function getHolder();

    public function getHolderId();

    public function getExeditAttr($exedit = []);

    //todo move to form
    public function responseRules();

    public function save();

    public function updatePermalinks();

    /**
     * @return array<React>
     */
    public function getReactsTree($filter = []): array;

    /**
     * @return array<React>
     */
    public function getReactsArray(array $filter = []): array;


}