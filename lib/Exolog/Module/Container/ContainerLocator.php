<?php


namespace Exolog\Module\Container;


use Exolog\Module\Forms\FQT\Unit\PayloadFieldContainer;
use Exolog\Module\Forms\FQT\Unit\UnitValueInterface;
use Exolog\Module\Support\Facades\Site;
use Exolog\Module\Support\Facades\Vars;
use Illuminate\Support\Facades\DB;

class ContainerLocator
{
    /**
     * collect all containers from:
     * react fields
     * var
     * unit
     * @return Container[]
     */
    public function collect(): array
    {
        return array_merge(
            $this->getFormContainers(),
            $this->getVarContainers(),
            $this->getUnitContainers(),
        );
    }

    private function getFormContainers(): array
    {
        $sql = "SELECT
                  r.react_id,
                  fq.fq_name
                FROM react r
                   JOIN form f ON (r.react_form = f.form_id)  
                   JOIN formquestion fq ON (f.form_id = fq.fq_form)
                   JOIN formquestiontype fqt ON (fq_type = fqt.fqt_id AND fqt.fqt_name = 'container')
                WHERE
                   f.form_site_id = :site_id AND
                   r.react_isdeleted = 0";
        $reacts = DB::select($sql, [':site_id' => Site::id()]);

        $containers = [];
        foreach ($reacts as $react) {
            $containers[] = ContainerFactory::resolve([
                'holderType' => 'react',
                'holderId' => $react['react_id'],
                'fq_name' => $react['fq_name'],
            ]);
        }

        return $containers;
    }

    private function getVarContainers(): array
    {
        $vars = Vars::getAll([['var_name', 'LIKE', 'container\_%']]);
        $containers = [];
        foreach ($vars as $var) {
            $containers[] = $var->getContainer();
        }
        return $containers;
    }

    private function getUnitVar(): array
    {
        $vars = Vars::getAll([['var_name', 'LIKE', 'unit\_%']]);
        $containers = [];
        foreach ($vars as $var) {
            $containers = array_merge($containers, $this->extractContainersFromUnit($var));
        }
        return $containers;
    }

    private function getUnitForm(): array
    {
        $sql = "SELECT
                  r.react_id,
                  fq.fq_name
                FROM react r
                   JOIN form f ON (r.react_form = f.form_id)  
                   JOIN formquestion fq ON (f.form_id = fq.fq_form)
                   JOIN formquestiontype fqt ON (fq_type = fqt.fqt_id AND fqt.fqt_name = 'unit')
                WHERE
                   f.form_site_id = :site_id AND
                   r.react_isdeleted = 0";

        $reacts = DB::select($sql, [':site_id' => Site::id()]);

        $containers = [];
        foreach ($reacts as $item) {
            $react = react()->find($item['react_id']);
            $unit = $react->getFieldValue($item['fq_name']);
            $containers = array_merge($containers, $this->extractContainersFromUnit($unit));
        }

        return $containers;
    }

    private function getUnitContainers(): array
    {
        return array_merge($this->getUnitVar(), $this->getUnitForm());
    }

    /**
     * @param UnitValueInterface $unit
     * @return array
     */
    private function extractContainersFromUnit(UnitValueInterface $unit): array
    {
        $containers = [];
        foreach ($unit->getPayload() as $item) {
            if ($item instanceof PayloadFieldContainer) {
                $containers[] = $item->getContainer();
            }
        }
        return $containers;
    }
}