<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 26.06.2020
 * Time: 18:09
 */

namespace Exolog\Module\Version;


use Exolog\Module\Support\Concerns\HasMakeFactory;
use Illuminate\Support\Facades\Http;
use RuntimeException;

class GitlabVersionCheckService
{
    use HasMakeFactory;

    public static string $********************** = 'https://gitlab.fidela.nl/api/v4/projects/1/repository/commits';

    public function getVersion(): array
    {
        if (!config('services.gitlab.token')) {
            throw new RuntimeException("Token 'GITLAB_PERSONAL_ACCESS_TOKEN' is not defined!");
        }

        $currentVersion = $this->getCurrentVersion();

        $response = Http::withHeaders([
            'PRIVATE-TOKEN' => config('services.gitlab.token'),
        ])->get(self::$**********************, [
            'ref_name' => $currentVersion['COMMIT_REF_NAME'],
        ])->throw()->json();

        return [
            'version' => $currentVersion,
            'last_version' => [
                'COMMIT_REF_NAME' => $currentVersion['COMMIT_REF_NAME'],
                'COMMIT_SHORT_SHA' => $response[0]['short_id'],
                'COMMITTED_DATE' => $response[0]['committed_date']
            ]
        ];
    }

    public function getCurrentVersion(): array
    {

        if (!file_exists(base_path('version.info'))) {
            throw new RuntimeException('File with current version "version.info" did not find!');
        }
        $info = file(base_path('version.info'), FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

        return [
            'COMMIT_REF_NAME' => $info[0],
            'COMMIT_SHORT_SHA' => $info[1],
        ];
    }
}