<?php


namespace Exolog\Module\Providers;

use Exolog\Core\CoreInit;
use Exolog\Core\Exolog;
use Exolog\Module\API\Api;
use Exolog\Module\Auth\SSI\SSIAgentService;
use Exolog\Module\ECO\ECOService;
use Exolog\Module\Markers\Marker\EditorVar\EditorVarService;
use Exolog\Module\Site\DefaultsService;
use Exolog\Module\Site\SiteService;
use Exolog\Module\Support\PathService;
use Exolog\Module\Support\ServiceProvider;
use Exolog\Module\Vars\VarsService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\View;

class ExologServiceProvider extends ServiceProvider
{

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        app()->singleton('exolog', Exolog::class);
        app()->singleton(CoreInit::class);
        app()->singleton(Api::class);

        app()->singleton('exolog.vars', VarsService::class);
        app()->singleton('exolog.site', SiteService::class);
        app()->singleton('exolog.defaults', DefaultsService::class);
        app()->singleton('exolog.path', PathService::class);
        app()->singleton('exolog.editor_vars', EditorVarService::class);
        app()->singleton(ECOService::class, ECOService::class);
        app()->singleton(SSIAgentService::class, SSIAgentService::class);
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        View::share('exolog', exolog());
        View::share('site', app('exolog.site'));
        View::share('defaults', app('exolog.defaults'));
        View::share('path', app('exolog.path'));

        //todo is this need for custom?
        Model::preventLazyLoading(!app()->isProduction());
    }
}
