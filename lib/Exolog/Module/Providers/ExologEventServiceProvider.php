<?php


namespace Exolog\Module\Providers;


use Exolog\Module\Container\Listeners\ReactContainerDefaultsSubscriber;
use Exolog\Module\Mails\Listeners\MailEventSubscriber;
use Exolog\Module\Markers\Listeners\MarkersEventSubscriber;
use Exolog\Module\Sitemap\Listeners\SitemapCacheEventSubscriber;
use Exolog\Module\Sitemap\Listeners\SitemapVariableEventSubscriber;
use Exolog\Module\Support\EventServiceProvider;
use Illuminate\Database\Events\StatementPrepared;
use Illuminate\Support\Facades\Event;
use PDO;

class ExologEventServiceProvider extends EventServiceProvider
{

    /**
     * The event handler mappings for the application.
     *
     * @var array
     */
    protected $listen = [];

    /**
     * The subscriber classes to register.
     *
     * @var array
     */
    protected $subscribe = [
        SitemapVariableEventSubscriber::class,
        SitemapCacheEventSubscriber::class,
        MarkersEventSubscriber::class,
        MailEventSubscriber::class
    ];


    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Event::listen(StatementPrepared::class, static function ($event) {
            $event->statement->setFetchMode(PDO::FETCH_ASSOC);
        });

    }
}