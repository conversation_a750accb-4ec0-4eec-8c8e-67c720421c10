<?php

namespace Exolog\Module\OpenProvider\OP;

use DOMDocument;
use DOMException;
use Illuminate\Support\Facades\Log;
use SimpleXMLElement;

/**
 * 2012(C)
 * Class for OpenProvider
 * https://doc.openprovider.eu/index.php/Example_Class_API_PHP
 *
 * Example
 * https://doc.openprovider.eu/index.php/Example_Script_PHP
 *
 * Documentation
 * https://doc.openprovider.eu/
 **/
class Api
{
    protected $url = null;
    protected $error = null;
    protected $timeout = null;
    static public $encoding = 'UTF-8';

    public function __construct($url, $timeout = 1000)
    {
        $this->url = $url;
        $this->timeout = $timeout;
    }

    /**
     * @throws ApiException
     */
    public function process(Request $r): Reply
    {
        $msg = $r->getRaw();
        $str = $this->send($msg);
        if (!$str) {
            throw new ApiException ('Bad reply');
        }
        $reply = new Reply($str);
        if ($reply->getFaultCode()) {
            Log::error("Open Provider error: {$reply->getFaultCode()} - {$reply->getFaultString()}");
            throw new ApiException ("Open provider return fault code: {$reply->getFaultCode()} with message {$reply->getFaultString()}", $reply->getFaultCode());
        }
        return $reply;
    }

    static function encode($str)
    {
        $ret = @htmlentities($str, null, self::$encoding);
        if (strlen($str) && !strlen($ret)) {
            $str = iconv('ISO-8859-1', 'UTF-8', $str);
            $ret = htmlentities($str, null, self::$encoding);
        }
        return $ret;
    }

    static function decode($str)
    {
        return html_entity_decode($str, null, self::$encoding);
    }

    /**
     * @throws ApiException
     */
    protected function send($str)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_POST, 1);
        //curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        //curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $str);
        curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);
        $ret = curl_exec($ch);
        $errno = curl_errno($ch);
        $this->error = $error = curl_error($ch);
        curl_close($ch);

        if ($errno) {
            throw new ApiException("CURL error. Code: $errno, Message: $error");
        }

        return $ret;
    }

    // convert SimpleXML to PhpObj
    public static function convertXmlToPhpObj($node)
    {
        $ret = array();

        if (is_object($node) && $node->hasChildNodes()) {
            foreach ($node->childNodes as $child) {
                $name = self::decode($child->nodeName);
                if ($child->nodeType == XML_TEXT_NODE) {
                    $ret = self::decode($child->nodeValue);
                } else {
                    if ('array' === $name) {
                        return self::parseArray($child);
                    } else {
                        $ret[$name] = self::convertXmlToPhpObj($child);
                    }
                }
            }
        }
        if (is_array($ret) && 0 === count($ret)) {
            return null;
        }

        return $ret;
    }

    /**
     * @throws ApiException
     */
    protected static function parseArray($node)
    {
        $ret = array();
        foreach ($node->childNodes as $child) {
            $name = self::decode($child->nodeName);
            if ('item' !== $name) {
                throw new ApiException('Wrong message format');
            }
            $ret[] = self::convertXmlToPhpObj($child);
        }
        return $ret;
    }

    /**
     * converts php-structure to DOM-object.
     *
     * @param array $arr php-structure
     * @param SimpleXMLElement $node parent node where new element to attach
     * @param DOMDocument $dom DOMDocument object
     * @throws DOMException
     */
    public static function convertPhpObjToDom($arr, $node, $dom)
    {
        if (is_array($arr)) {
            /**
             * If arr has integer keys, this php-array must be converted in
             * xml-array representation (<array><item>..</item>..</array>)
             */
            $arrayParam = array();
            foreach ($arr as $k => $v) {
                if (is_int($k)) {
                    $arrayParam[] = $v;
                }
            }
            if (0 < count($arrayParam)) {
                $node->appendChild($arrayDom = $dom->createElement("array"));
                foreach ($arrayParam as $key => $val) {
                    $new = $arrayDom->appendChild($dom->createElement('item'));
                    self::convertPhpObjToDom($val, $new, $dom);
                }
            } else {
                foreach ($arr as $key => $val) {
                    $new = $node->appendChild(
                        $dom->createElement(self::encode($key))
                    );
                    self::convertPhpObjToDom($val, $new, $dom);
                }
            }
        } else {
            $node->appendChild($dom->createTextNode(self::encode($arr)));
        }
    }
}