<?php

namespace Exolog\Module\OpenProvider\OP;

use DOMDocument;

class Request
{
    protected $cmd = null;
    protected $args = null;
    protected $username = null;
    protected $password = null;
    protected $token = null;
    protected $ip = null;
    protected $language = null;
    protected $raw = null;

    public function setCommand($v)
    {
        $this->cmd = $v;
        return $this;
    }

    public function getCommand()
    {
        return $this->cmd;
    }

    public function setLanguage($v)
    {
        $this->language = $v;
        return $this;
    }

    public function getLanguage()
    {
        return $this->language;
    }

    public function setArgs($v)
    {
        $this->args = $v;
        return $this;
    }

    public function getArgs()
    {
        return $this->args;
    }

    public function setAuth($args)
    {
        $this->username = $args["username"] ?? null;
        $this->password = $args["password"] ?? null;
        $this->token = $args["token"] ?? null;
        $this->ip = $args["ip"] ?? null;
        return $this;
    }

    public function getRaw()
    {
        if (!$this->raw) {
            $this->raw .= $this->getRequest();
        }
        return $this->raw;
    }

    protected function getRequest()
    {
        $dom = new DOMDocument('1.0', Api::$encoding);

        $credentialsElement = $dom->createElement('credentials');
        $usernameElement = $dom->createElement('username');
        $usernameElement->appendChild(
            $dom->createTextNode(Api::encode($this->username))
        );
        $credentialsElement->appendChild($usernameElement);

        $passwordElement = $dom->createElement('password');
        $passwordElement->appendChild(
            $dom->createTextNode(Api::encode($this->password))
        );
        $credentialsElement->appendChild($passwordElement);

        if (isset($this->language)) {
            $languageElement = $dom->createElement('language');
            $languageElement->appendChild($dom->createTextNode($this->language));
            $credentialsElement->appendChild($languageElement);
        }

        if (isset($this->token)) {
            $tokenElement = $dom->createElement('token');
            $tokenElement->appendChild($dom->createTextNode($this->token));
            $credentialsElement->appendChild($tokenElement);
        }

        if (isset($this->ip)) {
            $ipElement = $dom->createElement('ip');
            $ipElement->appendChild($dom->createTextNode($this->ip));
            $credentialsElement->appendChild($ipElement);
        }

        $rootElement = $dom->createElement('openXML');
        $rootElement->appendChild($credentialsElement);

        $rootNode = $dom->appendChild($rootElement);
        $cmdNode = $rootNode->appendChild(
            $dom->createElement($this->getCommand())
        );
        Api::convertPhpObjToDom($this->args, $cmdNode, $dom);

        return $dom->saveXML();
    }
}