<?php

namespace Exolog\Module\OpenProvider\OP;

use DOMDocument;

class Reply
{
    protected int $faultCode = 0;
    protected $faultString = null;
    protected $value = [];
    protected $raw = null;

    public function __construct($str)
    {
        $this->raw = $str;
        $this->_parseReply($str);
    }

    protected function _parseReply($str = ""): void
    {
        $dom = new DOMDocument;
        $dom->loadXML($str);
        $arr = Api::convertXmlToPhpObj($dom->documentElement);
        $this->faultCode = (int)$arr['reply']['code'];
        $this->faultString = $arr['reply']['desc'];
        $this->value = $arr['reply']['data'];
    }

    public function setFaultCode($v): Reply
    {
        $this->faultCode = $v;
        return $this;
    }

    public function setFaultString($v): Reply
    {
        $this->faultString = $v;
        return $this;
    }

    public function setValue($v): Reply
    {
        $this->value = $v;
        return $this;
    }

    public function getValue()
    {
        return $this->value;
    }

    public function getFaultString()
    {
        return $this->faultString;
    }

    public function getFaultCode()
    {
        return $this->faultCode;
    }

    public function getRaw(): string
    {
        if (!$this->raw) {
            $this->raw .= $this->getReply();
        }
        return $this->raw;
    }

    protected function getReply()
    {
        $dom = new DOMDocument('1.0', Api::$encoding);
        $rootNode = $dom->appendChild($dom->createElement('openXML'));
        $replyNode = $rootNode->appendChild($dom->createElement('reply'));
        $codeNode = $replyNode->appendChild($dom->createElement('code'));
        $codeNode->appendChild($dom->createTextNode($this->faultCode));
        $descNode = $replyNode->appendChild($dom->createElement('desc'));
        $descNode->appendChild(
            $dom->createTextNode(Api::encode($this->faultString))
        );
        $dataNode = $replyNode->appendChild($dom->createElement('data'));
        Api::convertPhpObjToDom($this->value, $dataNode, $dom);
        return $dom->saveXML();
    }
}