<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 18.12.2019
 * Time: 16:17
 */

namespace Exolog\Module\OpenProvider;

use Exolog\Module\OpenProvider\OP\Api;
use Exolog\Module\OpenProvider\OP\Request;

class OpenProvider
{
    private Api $api;
    private $username;
    private $password;

    public function __construct()
    {
        $this->username = config('services.openprovider.username');
        $this->password = config('services.openprovider.password');
        // Create a new API connection
        $this->api = new Api ('https://api.openprovider.eu');
    }

    public function checkDns($params)
    {
        if (is_string($params['domain'])) {
            $params['domains'] = array($params['domain']);
        }
        if (is_string($params['domains'])) {
            $params['domains'] = array($params['domains']);
        }
        if (!is_array($params['domains'])) {
            return false;
        }
        $domains = array();
        foreach ($params['domains'] as $domain) {
            $domain = trim($domain);
            if (empty($domain)) {
                continue;
            }
            $domain = explode('.', $domain);
            $name = $domain[0];
            unset($domain[0]);
            $domain = implode('.', $domain);
            $domains[] = [
                'extension' => $domain,
                'name' => $name
            ];
        }
        if (empty($domains)) {
            return false;
        }
        $options = [
            'domains' => $domains
        ];

        $request = new Request;
        $request->setCommand('checkDomainRequest')->setAuth([
            'username' => $this->username,
            'password' => $this->password
        ])->setArgs($options);
        $reply = $this->api->process($request)->getValue();
        if (empty($reply)) {
            return false;
        }
        return $reply;
    }

    public function getDns($params)
    {
        if (!isset($params['domain']) || empty($params['domain'])) {
            return false;
        }
        if (!isset($params['records'])) {
            $params['records'] = false;
        }
        if (!isset($params['history'])) {
            $params['history'] = false;
        }
        $request = new Request;
        $request->setCommand('retrieveZoneDnsRequest')
            ->setAuth(array('username' => $this->username, 'password' => $this->password))
            ->setArgs(array(
                'name' => $params['domain'],
                'withRecords' => (bool)$params['records'],
                'withHistory' => (bool)$params['history']
            ));
        $reply = $this->api->process($request);
        return $reply;
    }

    public function searchDns($params = []): OP\Reply
    {
        $request = new Request;
        $request->setCommand('searchZoneDnsRequest')
            ->setAuth(array('username' => $this->username, 'password' => $this->password))
            ->setArgs($params);
        $reply = $this->api->process($request);
        return $reply;
    }

    public function updateDns($params): OP\Reply
    {
        if (!isset($params['extension'])) {
            $name = explode('.', $params['name']);
            $params['name'] = $name[0];
            unset($name[0]);
            $params['extension'] = implode('.', $name);
        }
        foreach ($params['records'] as $id => $record) {
            $params['records'][$id]['name'] = str_replace('.' . $params['name'] . '.' . $params['extension'], '',
                $params['records'][$id]['name']);
            $params['records'][$id]['name'] = str_replace($params['name'] . '.' . $params['extension'], '',
                $params['records'][$id]['name']);
            if (in_array($record['type'], ['NS', 'SOA'])) {
                unset($params['records'][$id]);
            }
        }
        $args = [
            'domain' => ['name' => $params['name'], 'extension' => $params['extension']],
            'records' => $params['records']
        ];
        $request = new Request;
        $request->setCommand('modifyZoneDnsRequest')
            ->setAuth(['username' => $this->username, 'password' => $this->password])
            ->setArgs($args);
        return $this->api->process($request);
    }

    public function getDomain($params = []): OP\Reply
    {
        $request = new Request;
        $request->setCommand('retrieveDomainRequest')
            ->setAuth(['username' => $this->username, 'password' => $this->password])
            ->setArgs($params);
        return $this->api->process($request);
    }

    public function updateDomain($params = []): OP\Reply
    {
        if (isset($params['domain']) && !isset($params['domain']['extension'])) {
            $name = explode('.', $params['domain']);
            $params['domain'] = [
                'name' => array_shift($name),
                'extension' => implode('.', $name)
            ];
        }

        //var_dump_html($params);

        $request = new Request;
        $request->setCommand('modifyDomainRequest')
            ->setAuth(['username' => $this->username, 'password' => $this->password])
            ->setArgs($params);
        return $this->api->process($request);
    }
}









