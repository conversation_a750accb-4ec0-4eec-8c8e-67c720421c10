<?php


namespace Exolog\Module\Domains\Model;


use Exolog\Module\Database\Concerns\HasSiteScope;
use Exolog\Module\Database\Scopes\SiteScope;
use Exolog\Module\Domains\Model\Traits\HasDNS;
use Exolog\Module\Domains\Model\Traits\HasDomainSaveFrom;
use Exolog\Module\Domains\Model\Traits\HasSES;
use Exolog\Module\Domains\Observer\DomainMailhostObserver;
use Exolog\Module\Support\PHPDoc\PHPDoc_Builder;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use RuntimeException;

/**
 * @property mixed $domain_main
 * @property string $domain_name
 * @property string $domain_ssl_status
 * @property int $domain_id
 * @property int $domain_isdefault
 * @property int $domain_isssl
 * @property int $domain_isletsencrypt
 * @property int $domain_site
 * @property int $domain_ismail
 * @property int $domain_edition_id
 *
 * @mixin PHPDoc_Builder
 */
class Domain extends Model
{
    use HasSES;
    use HasDNS;
    use HasSiteScope;
    use HasDomainSaveFrom;

    public const SITE_FIELD = 'domain_site';

    protected $connection = 'exolog';
    protected $table = 'domain';
    protected $primaryKey = 'domain_id';
    public $timestamps = false;
    protected $fillable = [
        'domain_name',
        'domain_ismain',
        'domain_isdefault',
        'domain_isssl',
        'domain_ismail',
        'domain_isletsencrypt',
        'domain_ssl_status',
        'domain_edition_id'
    ];
    protected $casts = [
        'domain_id' => 'int',
        'domain_site' => 'int',
        'domain_edition_id' => 'int',
        'domain_isdefault' => 'int',
        'domain_isssl' => 'int',
        'domain_isletsencrypt' => 'int',
        'domain_ismail' => 'int',
        'domain_ismain' => 'int',
    ];

    /**
     * @return Collection<Domain>
     */
    public static function mailhosts(): Collection
    {
        return static::query()->where('domain_ismail', 1)->get();
    }

    protected static function booted()
    {
        static::observe(DomainMailhostObserver::class);

        static::saving(static function (Domain $domain) {

            if ($domain->exists && $domain->domain_name !== $domain->getOriginal('domain_name')) {
                throw new RuntimeException('"domain_name" change is not permitted!');
            }

            if ($domain->domain_isdefault) {
                Domain::query()
                    ->where('domain_id', '<>', $domain->domain_id)
                    ->where('domain_edition_id', $domain->domain_edition_id)
                    ->where('domain_isdefault', 1)
                    ->update(['domain_isdefault' => 0]);
            }
        });
    }

    public function setDefault(): void
    {
        throw new RuntimeException('Not implemented yet!');
    }

    public static function getMainDomain(): self
    {
        $domains = static::query()->where('domain_ismain', 1)->get();
        if (count($domains) > 1) {
            throw new RuntimeException('Detected more than one main domain!');
        }
        if (count($domains) === 0) {
            throw new RuntimeException('Main domain not found!');
        }
        return $domains->first();
    }

    public static function crossSiteQuery(): Builder
    {
        return static::withoutGlobalScope(SiteScope::class);
    }

    public function isSubDomain(): bool
    {
        return $this->findParent() !== null;
    }

    public function findParent(): ?Domain
    {
        $query = static::query();
        $query->whereRaw(DB::raw("? like CONCAT('%.',domain_name)"), [':hostname' => $this->domain_name]);
        $query->orderByRaw(DB::raw('LENGTH(domain_name)'));
        /** @var Domain */
        return $query->first();
    }
}