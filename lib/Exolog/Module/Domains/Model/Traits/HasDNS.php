<?php

namespace Exolog\Module\Domains\Model\Traits;

use Exception;
use Exolog\Module\DNS\DnsProviderFactory;
use Exolog\Module\DNS\Utils;
use Exolog\Module\OpenProvider\OpenProvider;
use RuntimeException;

trait HasDNS
{

    public static function isDomainOpenProvider(string $domain): bool
    {
        $openProvider = new OpenProvider();
        $dns = $openProvider->getDns(['domain' => $domain]);
        $dns = $dns->getValue();

        if (empty($dns['records'])) {
            return false;
        }

        return true;
    }


    /**
     * @throws Exception
     */
    private function insertDomainInDNS(): void
    {
        //check if we have a provider for this domain
        $dnsProvider = DnsProviderFactory::make($this->domain_name);

        if ($dnsProvider === null) {
            return;
        }

        $domain_ip = Utils::getHostAddr($this->domain_name);
        $server_ip = Utils::getHostAddr(config('exolog.server_domain'));

        if (empty($server_ip)) {
            throw new RuntimeException("Can't resolve IP for SERVER (config('exolog.server_domain'))");
        }

        if ($this->isSubDomain()) {
            if (!empty($domain_ip) && $domain_ip !== $server_ip) {
                throw new RuntimeException(
                    sprintf('A DNS entry for "%s" already exists and point to different server. You must remove this DNS entry.',
                        $this->domain_name));
            }
        } else {
            //We add A record only for subdomain of exolog server name
            if (endsWith($this->domain_name, config('exolog.server_domain'))) {
                //insert
            } else {
                if (empty($domain_ip)) {
                    throw new RuntimeException(
                        sprintf('DNS entry for "%s" not found. You must add DNS entry.', $this->domain_name));
                }

                if ($domain_ip !== $server_ip) {
                    throw new RuntimeException(
                        sprintf('DNS entry for "%s" point to different server. You must change this DNS entry.',
                            $this->domain_name));
                }
                return;
            }
        }

        //the record exists or CANME
        if (!empty($domain_ip)) {
            return;
        }

        $rec = [
            'name' => $this->domain_name,
            'ttl' => '900',
            'type' => 'A',
            'value' => $server_ip
        ];

        $dnsProvider->updateRecord($rec);
    }
}