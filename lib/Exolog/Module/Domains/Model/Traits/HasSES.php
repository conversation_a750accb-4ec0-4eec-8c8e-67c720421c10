<?php

namespace Exolog\Module\Domains\Model\Traits;

use Aws\Ses\SesClient;
use Exolog\Module\DNS\DnsProviderFactory;
use Illuminate\Support\Arr;
use RuntimeException;

trait HasSES
{
    public function addIdentitySES(): void
    {
        $client = $this->makeSesClient();
        $response = $client->verifyDomainDkim(['Domain' => $this->domain_name]);

        $tokens = $response->get('DkimTokens');

        $dnsProvider = DnsProviderFactory::make($this->domain_name);

        if ($dnsProvider === null) {
            throw new RuntimeException("Can`t detect dns provider for domain $this->domain_name!");
        }

        foreach ($tokens as $token) {
            $rec = [
                'name' => "$token._domainkey.$this->domain_name",
                'ttl' => '900',
                'type' => 'CNAME',
                'value' => "$token.dkim.amazonses.com"
            ];

            $dnsProvider->updateRecord($rec);
        }
    }


    public function checkIdentitySES()
    {
        $client = $this->makeSesClient();
        $response = $client->getIdentityDkimAttributes(['Identities' => [$this->domain_name]]);
        $attrs = $response->get('DkimAttributes');
        if (array_key_exists($this->domain_name, $attrs)) {
            return data_get($attrs[$this->domain_name], "DkimVerificationStatus", 'NA');
        }
        return 'NA';
    }

    private function makeSesClient(): SesClient
    {
        $credentials = Arr::only(config('mail.mailers.system-ses'), ['key', 'secret', 'token']);
        return new SesClient([
            'credentials' => $credentials,
            'version' => 'latest',
            'region' => config('mail.mailers.system-ses.region')
        ]);
    }
}