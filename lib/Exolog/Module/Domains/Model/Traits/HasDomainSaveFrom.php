<?php

namespace Exolog\Module\Domains\Model\Traits;

use Exception;
use Exolog\Module\Domains\Model\Domain;
use Exolog\Module\Support\Facades\Site;
use Exolog\Module\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Unique;
use RuntimeException;

trait HasDomainSaveFrom
{
    /**
     * @throws Exception
     */
    public static function saveFromArray(array $data): Domain
    {
        // Debug logging to identify the issue
        \Log::info('Domain saveFromArray called with data:', [
            'data' => $data,
            'domain_id_value' => $data['domain_id'] ?? 'not_set',
            'domain_id_type' => gettype($data['domain_id'] ?? null)
        ]);

        $domainValue = Validator::validateWithAttributes($data,
            [
                'domain_id' => 'nullable|numeric',
                'domain_name' => [
                    tap(Rule::unique(Domain::class), static function (Unique $rule) use ($data) {
                        if (is_numeric($data['domain_id'])) {
                            $rule->ignore($data['domain_id'], 'domain_id');
                        }
                    })
                ],

                'domain_edition_id' => 'nullable|numeric',
                'domain_ismain' => 'nullable|numeric',
                'domain_isdefault' => 'nullable|numeric',
                'domain_isssl' => 'nullable|numeric',
                'domain_ismail' => 'nullable|numeric',
                'domain_isletsencrypt' => 'nullable|numeric',
                'domain_ssl_status' => 'nullable',
            ]);

        //only alias name for server name subdomain for each site
        if (endsWith(strtolower($domainValue['domain_name']), (strtolower(config('exolog.server_domain'))))
            //check for subdomain of alias
            && !endsWith(strtolower($domainValue['domain_name']),
                strtolower('.' . Site::alias() . '.' . config('exolog.server_domain')))
        ) {
            if (strtolower($domainValue['domain_name']) !== strtolower(Site::alias() . '.' . config('exolog.server_domain'))) {
                throw new RuntimeException(
                    sprintf('Only site alias allowed for subdomain of server name (%s)',
                        config('exolog.server_domain')));
            }
        }

        /** @var Domain $domain */
        $domain = static::updateOrCreate([
            'domain_id' => $domainValue['domain_id']
        ], $domainValue);


        // DNS checking removed - we handle DNS setup separately in the domain setup process
        // No need to check DNS propagation when saving domains to database

        return $domain;
    }
}