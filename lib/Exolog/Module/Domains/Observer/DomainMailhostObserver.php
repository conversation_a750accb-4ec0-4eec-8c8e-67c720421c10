<?php

namespace Exolog\Module\Domains\Observer;

use Exolog\Dealer\Handler\MailhostHandler;
use Exolog\Module\Domains\Model\Domain;

class DomainMailhostObserver
{
    public function created(Domain $domain)
    {
        if ($domain->domain_ismail) {
            $mailHostHandler = MailhostHandler::make(['site_id' => $domain->domain_site]);
            $mailHostHandler->saveHost($domain->domain_name);
        }
    }

    public function updated(Domain $domain)
    {
        if ($domain->domain_ismail && !$domain->getOriginal('domain_ismail')) {
            $mailHostHandler = MailhostHandler::make(['site_id' => $domain->domain_site]);
            $mailHostHandler->saveHost($domain->domain_name);
        }
        if (!$domain->domain_ismail && $domain->getOriginal('domain_ismail')) {
            $mailHostHandler = MailhostHandler::make(['site_id' => $domain->domain_site]);
            $mailHostHandler->deleteHost($domain->domain_name);
        }
    }

    public function deleted(Domain $domain)
    {
        if ($domain->domain_ismail) {
            $mailHostHandler = MailhostHandler::make(['site_id' => $domain->domain_site]);
            $mailHostHandler->deleteHost($domain->domain_name);
        }
    }
}