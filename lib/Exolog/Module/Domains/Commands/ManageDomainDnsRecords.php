<?php

namespace Exolog\Module\Domains\Commands;

use Exception;
use Exolog\Module\Domains\Model\Domain;
use Exolog\Module\Mail\PosteDnsManager;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ManageDomainDnsRecords extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'domain:dns {action} {domain} {--force : Force the action without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add or remove DNS records for mail domains';

    private PosteDnsManager $dnsManager;

    /**
     * Create a new command instance.
     *
     * @param PosteDnsManager $dnsManager
     */
    public function __construct(PosteDnsManager $dnsManager)
    {
        parent::__construct();
        $this->dnsManager = $dnsManager;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $action = $this->argument('action');
        $domainName = $this->argument('domain');
        $force = $this->option('force');

        // Validate action
        if (!in_array($action, ['add', 'remove', 'update-dkim'])) {
            $this->error('Action must be either "add", "remove", or "update-dkim"');
            return 1;
        }

        // Check if domain exists in the system
        $domain = Domain::where('domain_name', $domainName)->first();
        if (!$domain) {
            $this->error("Domain $domainName not found in the system");
            return 1;
        }

        // Check if domain is configured for mail
        if (!$domain->domain_ismail) {
            $this->error("Domain $domainName is not configured for mail");
            return 1;
        }

        // Show confirmation unless force flag is used
        if (!$force) {
            switch ($action) {
                case 'add':
                    $confirmMessage = "Add mail DNS records (MX, SPF, DMARC, DKIM) for domain $domainName?";
                    break;
                case 'remove':
                    $confirmMessage = "Remove mail DNS records for domain $domainName?";
                    break;
                case 'update-dkim':
                    $confirmMessage = "Update DKIM record for domain $domainName?";
                    break;
                default:
                    $confirmMessage = "Perform DNS operation for domain $domainName?";
                    break;
            }

            if (!$this->confirm($confirmMessage)) {
                $this->info('Operation cancelled');
                return 0;
            }
        }

        try {
            switch ($action) {
                case 'add':
                    return $this->addDnsRecords($domainName);
                case 'remove':
                    return $this->removeDnsRecords($domainName);
                case 'update-dkim':
                    return $this->updateDkimRecord($domainName);
                default:
                    $this->error('Invalid action');
                    return 1;
            }
        } catch (Exception $e) {
            $this->error("Operation failed: " . $e->getMessage());
            Log::error('DNS management command failed', [
                'action' => $action,
                'domain' => $domainName,
                'error' => $e->getMessage()
            ]);
            return 1;
        }
    }

    /**
     * Add DNS records for the domain
     *
     * @param string $domainName
     * @return int
     * @throws Exception
     */
    private function addDnsRecords(string $domainName): int
    {
        $this->info("Adding mail DNS records for $domainName...");

        $this->line('Adding MX record...');
        $this->line('Adding SPF record...');
        $this->line('Adding DMARC record...');
        $this->line('Adding DKIM record...');

        $success = $this->dnsManager->addMailDnsRecords($domainName);

        if ($success) {
            $this->info("✓ All mail DNS records added successfully for $domainName");
            $this->line('');
            $this->line('Records added:');
            $this->line("  MX:    $domainName → " . config('services.poste.mail_domain'));
            $this->line("  SPF:   $domainName → v=spf1 a mx -all");
            $this->line("  DMARC: _dmarc.$domainName → v=DMARC1; p=none");
            $this->line("  DKIM:  [selector]._domainkey.$domainName → [public key]");
            $this->line('');
            $this->line('Note: DNS propagation may take up to 24 hours');
            return 0;
        } else {
            $this->error("✗ Failed to add DNS records for $domainName");
            return 1;
        }
    }

    /**
     * Remove DNS records for the domain
     *
     * @param string $domainName
     * @return int
     * @throws Exception
     */
    private function removeDnsRecords(string $domainName): int
    {
        $this->info("Removing mail DNS records for $domainName...");

        $this->line('Removing MX record...');
        $this->line('Removing SPF record...');
        $this->line('Removing DMARC record...');
        $this->line('Removing DKIM record...');

        $success = $this->dnsManager->removeMailDnsRecords($domainName);

        if ($success) {
            $this->info("✓ All mail DNS records removed successfully for $domainName");
            $this->line('');
            $this->warn('Warning: Email delivery for this domain will stop working!');
            return 0;
        } else {
            $this->error("✗ Failed to remove DNS records for $domainName");
            return 1;
        }
    }

    /**
     * Update DKIM record for the domain
     *
     * @param string $domainName
     * @return int
     * @throws Exception
     */
    private function updateDkimRecord(string $domainName): int
    {
        $this->info("Updating DKIM record for $domainName...");

        $success = $this->dnsManager->updateDkimRecord($domainName);

        if ($success) {
            $this->info("✓ DKIM record updated successfully for $domainName");
            $this->line('');
            $this->line('Note: DNS propagation may take up to 24 hours');
            return 0;
        } else {
            $this->error("✗ Failed to update DKIM record for $domainName");
            return 1;
        }
    }
}
