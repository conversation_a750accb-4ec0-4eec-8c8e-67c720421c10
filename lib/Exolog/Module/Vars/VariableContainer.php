<?php


namespace Exolog\Module\Vars;


use Exolog\Module\Container\BaseContainer;
use Exolog\Module\Container\Container;
use Exolog\Module\Container\VarContainerDataProvider;
use Exolog\Module\Contracts\ContainerValue;
use Exolog\Module\Contracts\EntityValueProvider;
use Exolog\Module\Forms\FQT\Container\HasContainerValue;

class VariableContainer extends Variable implements EntityValueProvider, ContainerValue
{
    use HasContainerValue;

    protected Container $container;

    public function __construct(array $var)
    {
        parent::__construct($var);
        $this->container = new BaseContainer(new VarContainerDataProvider($this->value, $this));
    }

    protected function getValueProvider()
    {
        return new JsonValueProvider($this);
    }

    public function getContainer()
    {
        return $this->container;
    }
}