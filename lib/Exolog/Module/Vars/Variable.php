<?php


namespace Exolog\Module\Vars;


use Exolog\Module\Contracts\EntityValueProvider;
use Exolog\Module\Contracts\XEditable;
use Exolog\Module\Support\Facades\Site;
use Exolog\Module\Vars\Events\VariableDeleted;
use Exolog\Module\Vars\Events\VariableRemoved;
use Exolog\Module\Vars\Events\VariableSaved;
use Exolog\Module\View\HasRenderValue;
use Exolog\Module\View\XEditableValue;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use JsonSerializable;
use RuntimeException;

class Variable implements XEditable, Htmlable, JsonSerializable
{
    use XEditableValue, HasRenderValue;

    public static array $FIELD_NAMES = [
        'var_id',
        'var_site',
        'var_name',
        'var_value',
        'var_type',
        'var_parent'
    ];
    protected array $var;
    protected array $exdit =
        [
            'contenttype' => 'tinymce',
            'toolbar' => 'custom'
        ];
    /**
     * @var EntityValueProvider
     */
    protected EntityValueProvider $value;

    /**
     * VarValue constructor.
     * @param array $var
     */
    public function __construct(array $var)
    {
        $this->var = $var;
        $this->value = $this->getValueProvider();
    }

    protected function getValueProvider()
    {
        return new ValueProvider($this);
    }


    public function __toString()
    {
        return (string)$this->var['var_value'];
    }

    public function __toArray()
    {
        return $this->var;
    }

    public function __serialize(): array
    {
        return $this->var;
    }

    public function remove()
    {
        DB::table('var')
            ->where('var_id', $this->var['var_id'])
            ->delete();
        VariableRemoved::dispatch(['variable' => $this]);
    }

    public function delete()
    {
        DB::table('var')
            ->where('var_id', $this->var['var_id'])
            ->delete();
        VariableDeleted::dispatch(['variable' => $this]);
    }

    public function getRawValue()
    {
        return $this->var['var_value'];
    }

    public function setRawValue($data)
    {
        $this->var['var_value'] = $data;
    }

    public function getValue()
    {
        return $this->getData();
    }

    public function setValue($data): self
    {
        $this->setData($data);
        return $this;
    }

    public function getData()
    {
        return $this->value->getData();
    }

    public function setData($data)
    {
        $this->value->setData($data);
    }

    public function toHtml()
    {
        if (!is_numeric($this->getId())) {
            $this->save();
        }
        return $this->render(array_merge(['tag' => 'div'], $this->renderParams));
    }

    public function getId()
    {
        return $this->var['var_id'];
    }

    public function save()
    {
        $values = Arr::only($this->var, self::$FIELD_NAMES);

        if (empty($values['var_type'])) {
            throw new RuntimeException('"var_type" can`t be null');
        }

        if (is_numeric($this->var['var_id'])) {
            DB::table('var')
                ->where('var_id', $this->var['var_id'])
                ->update($values);
        } else {
            $this->var['var_id'] = DB::table('var')
                ->insertGetId(array_merge($values, ['var_site' => Site::get('site_id')]));
        }
        VariableSaved::dispatch(['variable' => $this]);
    }

    public function getValueAsInt(): int
    {
        return (int)$this->getData();
    }

    public function getName()
    {
        return $this->var['var_name'];
    }

    public function getType()
    {
        return $this->var['var_type'];
    }

    public function getParent()
    {
        return $this->var['var_parent'];
    }

    public function jsonSerialize()
    {
        return $this->var;
    }

    protected function buildExedit($exedit = [])
    {
        if (empty($exedit)) {
            $exedit = [];
        }
        return array_merge(
            [
                'type' => 'var',
                'id' => $this->getId()
            ],
            $this->exdit,
            $exedit);
    }
}