<?php


namespace Exolog\Module\Vars;


use Illuminate\Support\Str;

class VarFactory
{
    public static function make(array $var)
    {
        if (Str::startsWith($var['var_name'], 'container_')) {
            return new VariableContainer($var);
        }
        if (Str::startsWith($var['var_name'], 'unit_')) {
            return new VariableUnit($var);
        }
        if (Str::startsWith($var['var_name'], 'picture_')) {
            return new VariablePicture($var);
        }
        if (Str::startsWith($var['var_name'], 'search_')) {
            return new VariableSearch($var);
        }

        if (Str::startsWith($var['var_name'], 'json_')) {
            return new VariableJson($var);
        }

        return new Variable($var);
    }
}