<?php


namespace Exolog\Module\Vars;


use Exolog\Module\Contracts\EntityValueProvider;

class ValueProvider implements EntityValueProvider
{
    private Variable $var;

    public function __construct(Variable $var)
    {
        $this->var = $var;
    }

    public function getData()
    {
        return $this->var->getRawValue();
    }

    public function setData($data)
    {
        $this->var->setRawValue($data);
    }
}