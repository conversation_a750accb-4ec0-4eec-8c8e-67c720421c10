<?php


namespace Exolog\Module\Vars;

use Exolog\Module\Forms\FQT\Picture\PictureValue;

class VariablePicture extends Variable
{
    use PictureValue;

    protected array $exdit = ['contenttype' => 'picture'];

    protected function getValueProvider()
    {
        return new JsonValueProvider($this);
    }

    protected function getCommonRenderParams()
    {
        return [];
    }

    protected function getPermalink()
    {
        return '';
    }

    public function save()
    {
        $this->updateHash();
        parent::save();
    }
}