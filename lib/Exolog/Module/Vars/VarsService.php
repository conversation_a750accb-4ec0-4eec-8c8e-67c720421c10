<?php


namespace Exolog\Module\Vars;


use Exolog\Module\Forms\Model\Form;
use Exolog\Module\Support\Facades\Auth;
use Exolog\Module\Support\Facades\Site;
use Illuminate\Support\Facades\DB;
use RuntimeException;

class VarsService
{
    /**
     * @param $var_name
     * @param null $var_type
     * @param null $var_parent
     * @param null $default_value
     * @return Variable|VariableContainer|VariableUnit|VariablePicture
     */
    public function get($var_name, $var_type = null, $var_parent = null, $default_value = null)
    {
        $var_parent = $this->resolveParent($var_parent, $var_type);

        $query = DB::table('var')
            ->select('var_id', 'var_name', 'var_value', 'var_type', 'var_parent')
            ->where('var_site', Site::id())
            ->where('var_name', $var_name);

        if ($var_type) {
            $query->where('var_type', $var_type);
        }
        if ($var_parent) {
            $query->where('var_parent', $var_parent);
        }

        $var = $query->first();


        if (empty($var) && !empty($var_name) && !empty($var_type) && !empty($var_parent)) {
            $var = [
                'var_name' => $var_name,
                'var_type' => $var_type,
                'var_parent' => $var_parent,
                'var_value' => is_null($default_value) ? '' : $default_value
            ];
        }

        if (empty($var)) {
            throw new RuntimeException("Can't get variable! name=$var_name (Not enough parameters)");
        }

        return VarFactory::make((array)$var);
    }


    public function getAll($params): array
    {
        $filter = array_filter($params, function ($value, $key) {
            if (is_numeric($key) && is_array($value)) {
                return true;
            }
            if (in_array($key, Variable::$FIELD_NAMES, true)) {
                return true;
            }
            return false;
        }, ARRAY_FILTER_USE_BOTH);

        $query = DB::table('var')
            ->select('*')
            ->where($filter)
            ->where('var_site', Site::id())
            ->orderBy('var_id');

        if (!empty($params['var_parent']) && !empty($params['var_type'])) {
            $parent = $this->resolveParent($params['var_parent'], $params['var_type']);
            $query->where('var_parent', $parent);
        }

        $vars = $query->get();

        $result = [];

        foreach ($vars as $var) {
            $result[] = VarFactory::make((array)$var);
        }
        return $result;
    }

    /**
     * @param $var_id
     * @return Variable|VariableContainer|VariableUnit|VariablePicture
     */
    public function getById($var_id)
    {
        $var = DB::table('var')
            ->select('var_id', 'var_name', 'var_value', 'var_type', 'var_parent')
            ->where('var_site', Site::id())
            ->where('var_id', $var_id)
            ->first();
        if (empty($var)) {
            return null;
        }
        return VarFactory::make((array)$var);
    }

    /**
     * @param $var_name
     * @param $form
     * @param null $default_value
     * @return Variable|VariableContainer|VariableUnit|VariablePicture
     */
    public function form($var_name, $form, $default_value = null)
    {
        $form = $this->resolveParent($form, 'form');
        return $this->get($var_name, 'form', $form, $default_value);
    }

    /**
     * @param $var_name
     * @param null $default_value
     * @return Variable|VariableContainer|VariableUnit|VariablePicture
     */
    public function site($var_name, $default_value = null)
    {
        return $this->get($var_name, 'site', Site::get('site_id'), $default_value);
    }

    /**
     * @param $var_name
     * @param null $edition
     * @param null $default_value
     * @return Variable|VariableContainer|VariableUnit|VariablePicture
     */
    public function edition($var_name, $edition = null, $default_value = null)
    {
        if (empty($edition)) {
            $edition = getCurrentEdition();
        } else {
            $edition = $this->resolveParent($edition, 'edition');
        }
        return $this->get($var_name, 'edition', $edition, $default_value);
    }

    /**
     * @param $var_name
     * @param null $user
     * @param null $default_value
     * @return Variable|VariableContainer|VariableUnit|VariablePicture
     */
    public function user($var_name, $user = null, $default_value = null)
    {
        if (empty($user) && Auth::check()) {
            $user = Auth::id();
        } else {
            $user = $this->resolveParent($user, 'user');
        }
        return $this->get($var_name, 'user', $user, $default_value);
    }

    /**
     * @param $var_name
     * @param $var_type
     * @param $var_parent
     * @param $default_value
     * @return Variable|VariableContainer|VariableUnit|VariablePicture
     */
    public function create($var_name, $var_type, $var_parent, $default_value)
    {
        $var_parent = $this->resolveParent($var_parent, $var_type);

        $id = DB::table('var')
            ->insertGetId([
                'var_site' => Site::get('site_id'),
                'var_name' => $var_name,
                'var_type' => $var_type,
                'var_parent' => $var_parent,
                'var_value' => ''
            ]);

        $var = $this->getById($id);
        $var->setData($default_value);
        $var->save();
        return $var;
    }

    public function massUpdate(array $vars, string $var_type = null, $var_parent = null): array
    {
        $result = [];
        foreach ($vars as $var) {
            if (empty($var['var_type'])) {
                $var['var_type'] = $var_type;
            }
            if (empty($var['var_parent'])) {
                $var['var_parent'] = $var_parent;
            }
            $varObject = VarFactory::make($var);

            if ($var['$_deleted'] == 1) {
                $varObject->remove();
            } else {
                $varObject->save();
                $result[] = $varObject;

            }
        }
        return $result;
    }

    protected function resolveParent($var_parent, $var_type)
    {
        $parentOrigin = $var_parent;
        if (empty($var_type)) {
            throw new RuntimeException('Can\'t resolve var_parent, parameter "type" is empty.');
        }

        if (empty($var_parent)) {
            throw new RuntimeException('Can\'t resolve var_parent, parameter "parent" is empty.');
        }

        switch ($var_type) {
            case 'form':
                if (!is_numeric($var_parent)) {
                    $var_parent = Form::findByName($var_parent)->form_id;
                }
                break;
            case 'edition':
                $query = DB::table('edition')
                    ->where('e_site', Site::get('site_id'));
                if (is_numeric($var_parent)) {
                    $query->where('e_id', $var_parent);
                } else {
                    $query
                        ->whereNull('e_deleted_at')
                        ->where('e_title', $var_parent);
                }
                $var_parent = $query->value('e_id');
                break;
            case 'user':
                $query = DB::table('user')
                    ->where('u_site', Site::get('site_id'));
                if (is_numeric($var_parent)) {
                    $query->where('u_id', $var_parent);
                } else {
                    $query
                        ->whereNull('u_deleted_at')
                        ->where('u_email', $var_parent);
                }

                $var_parent = $query->value('u_id');
                break;
            default:
                if (!is_numeric($var_parent)) {
                    throw new RuntimeException('Not implemented yet!');
                }
        }

        if (empty($var_parent)) {
            throw new RuntimeException("Can`t resolve var_parent. parent=$parentOrigin, type=$var_type.");
        }

        return $var_parent;
    }
}