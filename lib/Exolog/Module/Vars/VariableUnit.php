<?php


namespace Exolog\Module\Vars;

use Exolog\Module\Forms\FQT\Unit\PayloadValueProvider;
use Exolog\Module\Forms\FQT\Unit\UnitValue;
use Exolog\Module\Forms\FQT\Unit\UnitValueInterface;

class VariableUnit extends Variable implements UnitValueInterface
{
    use UnitValue;

    protected function getValueProvider()
    {
        return new JsonValueProvider($this);
    }

    protected function getPaylodValueProvider($key, $type)
    {
        return new PayloadValueProvider($this, $key, $type, function () {
            return [
                'type' => static::HOLDER_TYPE_VAR,
                'id' => $this->getId()
            ];
        });
    }

    public function getPayloadHolderId()
    {
        return $this->getId();
    }

    public function getHolderId()
    {
        return $this->getId();
    }

    public function getHolder()
    {
        return $this;
    }

    public function getHolderType()
    {
        return static::HOLDER_TYPE_VAR;
    }

    public function save()
    {
        $this->updateHash();
        parent::save();
    }
}