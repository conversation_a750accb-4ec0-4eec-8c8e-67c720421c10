<?php

namespace Exolog\Module\Image;

use Imagick;
use ImagickException;

/**
 * Exolog class for Imagick
 * <AUTHOR>
 */
class EImagick
{

    /**
     * Array of used methods
     * @var array
     */
    protected $methodsUsed = array();

    /**
     * Array of settings
     * @var array
     */
    public $settings = array();
    private $_im;

    /**
     * construct
     * @param array $settings = array('url' => array('from' => 'path to image'), 'path' => array('site' => 'path to site'))
     * <AUTHOR>
     */
    function __construct($settings)
    {
        $this->settings = $settings;
        $this->settings['extension'] = pathinfo($settings['url']['from'], PATHINFO_EXTENSION);
        $this->settings['auto-orient'] = false;
        $this->settings['no-cache'] = false;
        $this->settings['drop-cache'] = false;
    }

    /**
     * Collect used imagick methods
     * <AUTHOR>
     */
    public function __call($name, $arguments)
    {
        if ($name == 'autoOrientation') {
            $this->settings['auto-orient'] = true;
            return $this;
        }
        if ($name == 'noCache') {
            $this->settings['no-cache'] = true;
            return $this;
        }
        if ($name == 'dropCache') {
            $this->settings['no-cache'] = true;
            $this->settings['drop-cache'] = true;
            return $this;
        }
        $this->methodsUsed[] = array('method' => $name, 'arguments' => $arguments);
        if ($name == 'setImageFormat') {
            $this->settings['extension'] = current($arguments);
        }
        return $this;
    }

    /**
     * Execute all methods and save image
     * @throws ImagickException
     * <AUTHOR>
     */
    function save()
    {

        //if source not exists
        if (!file_exists($this->settings['url']['from'])) {
            return '';
        }

        $autoOrientPrefix = '';
        if (!empty($this->settings['auto-orient'])) {
            $autoOrientPrefix = 'autoorient';
        }
        //generate filename based on used methods
        //todo add size and time to hash
        $filename = sha1(
            $this->settings['url']['from'] .
            filesize($this->settings['url']['from']) .
            fileatime($this->settings['url']['from']) .
            serialize($this->methodsUsed) .
            $autoOrientPrefix);
        //path to target file
        $this->settings['url']['to'] = $this->settings['path']['site'] . '/cache/imagick/' . $filename . '.' . strtolower($this->settings['extension']);
        //return url
        $this->settings['url']['return'] = $this->settings['path']['static'] . '/cache/imagick/' . $filename . '.' . strtolower($this->settings['extension']);
        //if exists, return url to file
        if (empty($this->settings['no-cache']) && file_exists($this->settings['url']['to'])) {
            return $this->settings['url']['return'];
        }
        if (!empty($this->settings['drop-cache']) && file_exists($this->settings['url']['to'])) {
            unlink($this->settings['url']['to']);
        }

        //if not exists create imagick object
        $imagick = $this->imagick();
        if (!empty($this->settings['auto-orient'])) {
            $orientation = $imagick->getImageOrientation();
            switch ($orientation) {
                case Imagick::ORIENTATION_BOTTOMRIGHT:
                    $imagick->rotateimage("#000", 180); // rotate 180 degrees 
                    break;
                case Imagick::ORIENTATION_RIGHTTOP:
                    $imagick->rotateimage("#000", 90); // rotate 90 degrees CW 
                    break;
                case Imagick::ORIENTATION_LEFTBOTTOM:
                    $imagick->rotateimage("#000", -90); // rotate 90 degrees CCW 
                    break;
            }
            $imagick->setImageOrientation(Imagick::ORIENTATION_TOPLEFT);
        }
        //run all methods
        foreach ($this->methodsUsed as $level) {
            call_user_func_array(array($imagick, $level['method']), $level['arguments']);
        }
        //check if cache dir exists
        if (!file_exists($this->settings['path']['site'] . '/cache/imagick/')) {
            mkdir($this->settings['path']['site'] . '/cache/imagick/');
        }
        //save image
        $imagick->writeImage($this->settings['url']['to']);
        //$imagick->destroy();
        //return path to image        
        return $this->settings['url']['return'];
    }

    public function imagick()
    {
        if (empty($this->_im)) {
            $this->_im = new Imagick($this->settings['url']['from']);
        }
        return $this->_im;
    }
}