<?php

use Exolog\Module\Events\ExoEventContract;

/**
 * Dispatch an event and call the listeners.
 *
 * @param mixed ...$args
 * @return ExoEventContract
 */
function xEvent(...$args): ExoEventContract
{
    foreach ($args as $item) {
        if ($item instanceof ExoEventContract) {
            $event = $item;
        }
    }
    if (empty($event)) {
        throw new InvalidArgumentException('One of the arguments should be ExoEventContract!');
    }

    app('events')->dispatch(...$args);

    return $event;
}