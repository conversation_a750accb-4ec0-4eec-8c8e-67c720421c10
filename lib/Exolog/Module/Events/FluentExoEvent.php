<?php


namespace Exolog\Module\Events;


use Illuminate\Support\Fluent;

class FluentExoEvent extends Fluent implements ExoEventContract
{
    private string $status = self::STATUS_OK;


    public static function dispatch($attributes = [])
    {
        return xEvent(new static($attributes));
    }

    public function status($value = null)
    {
        if (func_num_args() !== 0) {
            $this->status = $value;
        } else {
            return $this->status;
        }
    }
}