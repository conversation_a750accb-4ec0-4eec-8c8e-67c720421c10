<?php

namespace Exolog\Module\Auth;

use Exolog\Module\Users\Model\User;
use Illuminate\Auth\EloquentUserProvider;
use Illuminate\Contracts\Auth\Authenticatable as UserContract;
use PragmaRX\Google2FA\Google2FA;

class ExologEloquentUserProvider extends EloquentUserProvider
{
    /**
     * The Eloquent user model.
     *
     * @var string
     */
    protected $model;

    public function validateCredentials(UserContract $user, array $credentials)
    {
        /** @var User $user */
        if (!empty($user->u_two_factor_secret)) {

            $gaCode = $credentials['code'];
            if (empty($gaCode)) {
                return false;
            }

            $ga_secret = $user->u_two_factor_secret;
            $google2fa = new Google2FA();

            //check if admin right setup generator
            if (!$google2fa->verify($gaCode, $ga_secret)) {
                return false;
            }
        }

        $plain = $credentials['password'];

        return $this->hasher->check($plain, $user->getAuthPassword());
    }

    public function retrieveByCredentials(array $credentials)
    {
        if ($credentials['email']) {
            $credentials['u_email'] = $credentials['email'];
            unset($credentials['email']);
        }
        if ($credentials['code']) {
            unset($credentials['code']);
        }
        return parent::retrieveByCredentials($credentials);
    }
}
