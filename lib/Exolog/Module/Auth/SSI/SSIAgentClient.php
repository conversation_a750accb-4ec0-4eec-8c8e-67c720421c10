<?php

namespace Exolog\Module\Auth\SSI;

use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;

class SSIAgentClient
{

    private string $url;

    private ?string $xApiKey;

    public function __construct()
    {
        $this->url = config('ssi.agent.url');
        $this->xApiKey = config('ssi.agent.x-api-key');
    }

    /**
     * @param array{nonce: ?string, state: ?string} $data
     * @return array|mixed
     * @throws RandomException
     */
    public function authorization_requests(array $data): string
    {
        if (!isset($data['nonce'])) {
            $data['nonce'] = bin2hex(random_bytes(16));
        }
        $url = 'v0/authorization_requests';
        $response = $this->getClient()
            ->asJson()
            ->post($url, $data)
            ->throw()
            ->body();
        return $response;
    }

    public function get_authorization_requests(string $state)
    {
        $url = '/v0/authorization_requests/' . $state;
        $response = $this->getClient()->get($url);
        return $response->json();
    }

    private function getClient(): PendingRequest
    {
        return Http::withHeaders([
            'X-API-KEY' => $this->xApiKey
        ])
            ->baseUrl($this->url);
    }

}
