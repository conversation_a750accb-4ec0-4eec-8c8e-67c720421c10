<?php

namespace Exolog\Module\Auth\SSI;

use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

class SSIAgentService
{
    private SSIAgentClient $sisAgent;

    public function __construct(SSIAgentClient $sisAgent)
    {
        $this->sisAgent = $sisAgent;
    }

    public function siopAuthorizationRequest()
    {
        $data = [
            'state' => Session::getId(),
        ];
        return $this->sisAgent->authorization_requests($data);
    }

    public function siopAuthorizationRequestsStatus(): array
    {
        $state = Session::getId();
        return $this->sisAgent->get_authorization_requests($state);
    }

    public function makeVpRequest(array $data = [])
    {
        //generate random state
        $state = Str::random();
        Session::put('vp_request_state', $state);
        $data = array_merge([
            "state" => $state,
        ], $data);
        return $this->sisAgent->authorization_requests($data);
    }

    public function getAuthorizationRequest($state)
    {
        return $this->sisAgent->get_authorization_requests($state);
    }
}
