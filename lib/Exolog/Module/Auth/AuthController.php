<?php

namespace Exolog\Module\Auth;

use Exolog\Module\Auth\SSI\SSIAgentService;
use Exolog\Module\Editions\Model\Edition;
use Exolog\Module\Routing\SystemController;
use Exolog\Module\Support\Facades\Auth;
use Exolog\Module\Users\Model\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use RuntimeException;


class AuthController extends SystemController
{
    public function editionHashLogin()
    {
        $e_id = request('e_id');
        $edition = Edition::findOrFail($e_id);
        $domain = $edition->getDefaultDomain();
        if ($domain === null) {
            throw new RuntimeException("The default domain for the edition is not found. Can't create a login link");
        }
        $link = auth()->user()->genUserLoginLink([
            'host' => $domain['domain_name'],
        ]);
        return redirect($link);
    }

    public function sisEvents(Request $request)
    {
        Log::info('SIS events', $request->all());
    }

    public function siopStatus(): JsonResponse
    {
        $agent = resolve(SSIAgentService::class);
        $siopAuthRequest = $agent->siopAuthorizationRequestsStatus();
        return response()->json(
            [
                'success' => true,
                'data' => !empty($siopAuthRequest['id_token']),
            ]
        );
    }

    public function siopLogin(Request $request)
    {
        $agent = resolve(SSIAgentService::class);
        $siopAuthRequest = $agent->siopAuthorizationRequestsStatus();
        if (empty($siopAuthRequest['id_token'])) {
            return redirect('/');
        }
        $token = jwt_decode($siopAuthRequest['id_token']);

        $user = Auth::loginUsingDid($token['iss']);
        if ($user === false) {
            User::create([
                'u_did' => $token['iss'],

            ]);
            $user = Auth::loginUsingDid($token['iss']);
        }
        if ($user === false) {
            return redirect('/');
        }
        if ($request->has('redir')) {
            return redirect($request->get('redir'));
        }

        return redirect('/');
    }
}
