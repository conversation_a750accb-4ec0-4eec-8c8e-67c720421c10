<?php


namespace Exolog\Module\Auth;

use Illuminate\Auth\AuthServiceProvider as IlluminateServiceProvider;

class AuthServiceProvider extends IlluminateServiceProvider
{
    public function boot()
    {
        $this->app['auth']->extend('system', function ($app, $name, array $config) {
            $guard = new SystemSessionGuard(
                $name,
                $this->app['auth']->createUserProvider($config['provider']),
                $this->app['session.store'],
                $this->app['request']
            );

            if (method_exists($guard, 'setCookieJar')) {
                $guard->setCookieJar($this->app['cookie']);
            }

            if (method_exists($guard, 'setDispatcher')) {
                $guard->setDispatcher($this->app['events']);
            }

            if (method_exists($guard, 'setRequest')) {
                $guard->setRequest($this->app->refresh('request', $guard, 'setRequest'));
            }

            if (isset($config['remember'])) {
                $guard->setRememberDuration($config['remember']);
            }

            return $guard;
        });

        $this->app['auth']->provider('exolog-eloquent', function ($app, array $config) {
            return new ExologEloquentUserProvider($app['hash'], $config['model']);
        });
    }
}