<?php

namespace Exolog\Module\Auth;

use Exolog\Module\Auth\SSI\SSIAgentService;
use Exolog\Module\Users\Model\User;
use Illuminate\Auth\SessionGuard;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Auth\UserProvider;
use Illuminate\Support\Arr;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use RuntimeException;

class SystemSessionGuard extends SessionGuard
{
    public function __construct($name, UserProvider $provider, $sessionKey, $guardKey)
    {
        parent::__construct($name, $provider, $sessionKey, $guardKey);
    }

    public function isAdmin(): bool
    {
        /** @var User $user */
        $user = $this->user();
        return $user && $user->isAdmin();
    }

    public function isDeveloper(): bool
    {
        $user = $this->user();
        return $user && $user->isDeveloper();
    }

    /**
     * Login to backend with backend hash generated with genBackendHash().
     * Warning! Hash is valid 24 hours and depends on used browser
     * @return Authenticatable|bool
     *
     */
    public function loginUsingHash(string $hash)
    {
        $authArray = decrypt($hash);

        $currentTime = time();
        $authTime = 1 * $authArray['t'];
        if (abs($currentTime - $authTime) < 24 * 60 * 60) {
            $u_id = $authArray['u'];
        } else {
            return false;
        }

        return $this->loginUsingId($u_id);
    }


    public function loginUsingDid(string $did)
    {
        $user = User::query()->where('u_did', $did)->first();
        if ($user === null) {
            return false;
        }
        return $this->loginUsingId($user->u_id);
    }

    /**
     * @return Authenticatable|bool
     */
    public function signIn(array $credentials, bool $remember = false)
    {
        if ($this->attempt($credentials, $remember)) {
            if (($request = request()) && $request->hasSession()) {
                $request->session()->regenerate();
            }
            return $this->user();
        }
        return false;
    }

    /**
     * @param $field
     * @return mixed false or value
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @deprecated
     * Get value from session
     * @example getValue('user') - return full userdata array
     * @example getValue(array('user', 'u_id')) - return value of u_id
     * <AUTHOR>
     */
    public function getValue($field)
    {
        $field = Arr::wrap($field);

        /** @var User $user */
        if (in_array($field[0], ['user', 'usergroups'])) {
            $user = $this->user();

            if (is_null($user)) {
                return false;
            }

            $usergroups = $user->usergroups->keyBy('ug_id')->toArray();
            if ($field[0] === 'user') {
                if (count($field) === 1) {
                    return $user->toArray();
                }
                if (count($field) === 2) {
                    return $user[$field[1]];
                }
            }

            if ($field[0] === 'usergroups') {
                if (count($field) === 1) {
                    return $usergroups;
                }

                if (count($field) === 2) {
                    return $usergroups[$field[1]];
                }
            }

            throw new RuntimeException('Not implemented yet!');
        }

        if (session()->has($field[0])) {
            return session()->get($field[0]);
        }

        return false;
    }


    /**
     * @param array $params
     * @return boolean;
     * @deprecated
     * Set value of session
     * <AUTHOR>
     * @example setValue(array('user'=>array('u_name' => 'Vasya')))
     */
    public function setValue($params)
    {
        session()->put($params);
        return true;
    }


    /**
     * @return boolean
     * @deprecated
     * Unset value of session
     * @example unsetValue('user') - unset userdata array
     * @example unsetValue(array('user', 'u_id')) - unset u_id
     * <AUTHOR>
     */
    public function unsetValue($field)
    {
        session()->forget($field);
        return true;
    }

    /**
     * Logout user destroy session
     */
    public function signOut(): void
    {
        $this->logout();
        if (($request = request()) && $request->hasSession()) {
            $request->session()->invalidate();
            $request->session()->regenerateToken();
        }
    }

    public function siopAuthorizationRequest(): string
    {
        return resolve(SSIAgentService::class)->siopAuthorizationRequest();
    }
}
