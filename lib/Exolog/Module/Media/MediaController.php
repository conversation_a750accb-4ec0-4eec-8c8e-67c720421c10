<?php

namespace Exolog\Module\Media;

use Exception;
use Exolog\Module\Image\EImagick;
use Exolog\Module\Routing\SystemController;
use Exolog\Module\Support\Facades\ExoFile;
use Illuminate\Support\Facades\File;
use Imagick;
use ImagickException;
use Intervention\Image\Facades\Image;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class MediaController extends SystemController
{
    private array $standardTransform = [
        'w' => null,
        'h' => null,
        'q' => null,
        't' => null,
        'c' => null
    ];

    /**
     * @throws ImagickException
     */
    public function show($path)
    {
        $opt = explode('/', $path);
        try {
            $data = ExoFile::decodeFileId($opt[0]);
        } catch (Exception $e) {
            throw404();
        }
        $file_id = $data['f'];
        $transform = $data['t'];

        if (empty($file_id)) {
            return $this->notFound();
        }

        $originalFileName = ExoFile::basename($file_id);
        $ext_original = pathinfo($originalFileName, PATHINFO_EXTENSION);
        $ext = dv(pathinfo($path, PATHINFO_EXTENSION), $ext_original);

        $file_path_local = ExoFile::path($file_id);

        if ($transform || $ext_original !== $ext) {

            $eim = new EImagick([
                    'url' => [
                        'from' => $file_path_local
                    ],
                    'path' => ['site' => site_path()]
                ]
            );
            $eim->setImageFormat($ext);


            $width = $transform['w'] ?: 0;
            $height = $transform['h'] ?: 0;

            if ($transform['q']) {
                $eim->setImageCompressionQuality($transform['q']);
            } else {
                $eim->setImageCompressionQuality(80);
            }

            if (array_key_exists('c', $transform)) {
                $this->image_cover($eim, $width, $height, $transform['c']);
            } elseif ($transform['t']) {
                $eim->cropThumbnailImage($width, $height);
            } else {

                $methods = array_diff_key($transform, $this->standardTransform);
                if (!empty($methods)) {
                    foreach ($methods as $method => $value) {
                        call_user_func_array(array($eim, $method), $value);
                    }
                } elseif ($width || $height) {
                    $eim->scaleImage($width, $height, $width && $height);
                }
            }

            try {
                $cache_path = $eim->save();
            } catch (ImagickException $e) {
                if (app()->isProduction()) {
                    return $this->error();
                }
                throw $e;
            }
            $file_path_local = site_path($cache_path);
        }

        return trace_image($file_path_local, pathinfo($path, PATHINFO_BASENAME));
    }

    private function notFound()
    {
        return redirect('/images/noimage.png');
    }

    private function error()
    {
        return redirect('/images/error.gif');
    }

    private function image_cover(&$eim, $width, $height, $crop_gravity)
    {
        $image = $eim->imagick();
        $ratio = $width / $height;

        // Original image dimensions.
        $old_width = $image->getImageWidth();
        $old_height = $image->getImageHeight();
        $old_ratio = $old_width / $old_height;

        // Determine new image dimensions to scale to.
        // Also determine cropping coordinates.
        if ($ratio > $old_ratio) {
            $new_width = $width;
            $new_height = $width / $old_width * $old_height;
            $crop_x = 0;
            switch ($crop_gravity) {
                case 0:
                    $crop_y = 0;
                    break;
                case 2:
                    $crop_y = (int)($new_height - $height);
                    break;
                default:
                    $crop_y = (int)(($new_height - $height) / 2);
            }
        } else {
            $new_width = $height / $old_height * $old_width;
            $new_height = $height;
            $crop_y = 0;
            switch ($crop_gravity) {
                case 0:
                    $crop_x = 0;
                    break;
                case 2:
                    $crop_x = (int)($new_width - $width);
                    break;
                default:
                    $crop_x = (int)(($new_width - $width) / 2);
            }

        }

        // Scale image to fit minimal of provided dimensions.
        $eim->resizeImage($new_width, $new_height, imagick::FILTER_LANCZOS, 0.9, true);

        // Now crop image to exactly fit provided dimensions.
        $eim->cropImage($width, $height, $crop_x, $crop_y);

        return $image;
    }

    public function preview($path)
    {
        $file_path_local = ExoFile::path($path);
        if (!File::exists($file_path_local)) {
            throw new NotFoundHttpException();
        }
        $image = Image::make($file_path_local);
        if (request()->hasAny(['height', 'width'])) {
            $image->resize(request('width'), request('height'), function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            });
        }
        return $image->response();
    }

    public function file($hash, $path)
    {
        $params = ExoFile::decodeFileId($hash);
        return ExoFile::download($params['f']);
    }
}
