<?php

namespace Exolog\Module\ReactLayout;

use DOMAttr;
use DOMDocument;
use DOMNode;
use Illuminate\Support\Facades\File;
use Illuminate\View\ViewFinderInterface;

class BladeBlocksParser
{
    public function parseView(string $view): array
    {
        /** @var ViewFinderInterface $finder */
        $finder = app('view.finder');
        $path = $finder->find($view);

        $dom = new DomDocument();
        $test = File::get($path);

        $dom->loadHTML($test);
        $placeholders = $dom->getElementsByTagName('x-layout-slot');
        $blocks = [];
        foreach ($placeholders as $placeholder) {
            /** @var DOMNode $placeholder */
            $block = [];
            foreach ($placeholder->attributes as $item) {
                /** @var DOMAttr $item */
                $block[$item->name] = $item->value;
            }
            $blocks[] = $block;
        }

        return collect($blocks)->keyBy('name')->toArray();
    }
}