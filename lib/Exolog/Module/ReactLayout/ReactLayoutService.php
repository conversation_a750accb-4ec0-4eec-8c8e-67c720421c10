<?php

namespace Exolog\Module\ReactLayout;

use Exolog\Module\Support\Facades\Path;
use Exolog\Module\Support\Facades\Vars;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class ReactLayoutService
{

    private BladeBlocksParser $parser;
    private array $reactLayouts;

    public function __construct(BladeBlocksParser $parser)
    {
        $this->parser = $parser;
    }

    public function withDefaults($data = null, ...$opts): array
    {
        return array_merge([
            'layouts' => ['__dump__' => []],
            'template_name' => null
        ], $data ?? [], ...$opts);
    }

    public function push(ReactLayout $reactLayout): void
    {
        $this->reactLayouts[] = $reactLayout;
    }

    public function pop(): ReactLayout
    {
        return array_pop($this->reactLayouts);
    }

    public function current(): ?ReactLayout
    {
        return Arr::last($this->reactLayouts);
    }

    public function getTemplates(): array
    {
        $templateFiles = File::files(Path::to('resources', '/views/templates/layouts'));
        $templates = [];
        foreach ($templateFiles as $file) {
            $basename = $file->getBasename();
            if (Str::finish($basename, '.blade.php')) {
                $templateName = Str::beforeLast($basename, '.blade.php');
                $templates[$templateName] = $this->parser->parseView('templates.layouts.' . $templateName);
            }
        }
        return $templates;
    }

    public function getBlocks(array $search_params = []): Collection
    {
        $qbRules = $this->getQBRules();
        return react()->form('pageblock')
            ->applyQBRules($qbRules)
            ->search($search_params)
            ->get();
    }

    public function getQBRules(): array
    {
        $var = Vars::edition('json_layout_QBRules');
        $value = $var->getValue();
        if (empty($value) || !is_array($value)) {
            $value = [];
        }
        return array_merge(['children' => [], 'logicalOperator' => 'AND'], $value);
    }

    public function saveQBRules(array $QBRules): void
    {
        $var = Vars::edition('json_layout_QBRules');
        $var->setValue($QBRules);
        $var->save();
    }
}