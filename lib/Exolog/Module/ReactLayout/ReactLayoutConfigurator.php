<?php

namespace Exolog\Module\ReactLayout;

use Exolog\Core\Forms\React;
use Exolog\Module\Container\Helper;
use Exolog\Module\Forms\Model\FormPermalink;
use Exolog\Module\Support\Facades\RL;
use RuntimeException;

class ReactLayoutConfigurator
{
    public function load(string $layoutId): array
    {
        $layout = Helper::str_to_array($layoutId);
        $result = [];
        if ($layout['holderType'] === ReactLayout::SCOPE_REACT) {
            $react = React::findOrFail($layout['holderId']);

            $result['scopes'][] = [
                'id' => ReactLayout::SCOPE_REACT,
                'label' => 'Current react',
                'info' => (string)$react['react_id']
            ];

            $fp = $react->getPrimaryPermalink()->form_permalink ?? null;

            if ($fp) {
                $result['scopes'][] = [
                    'id' => ReactLayout::SCOPE_FORM_PERMALINK,
                    'label' => "Form permalink",
                    'info' => $fp->fp_uri
                ];
            }

            if ($react['react_layout_scope'] === ReactLayout::SCOPE_FORM_PERMALINK) {
                if ($fp) {
                    $result['scope'] = ReactLayout::SCOPE_FORM_PERMALINK;
                    $result['react_layout'] = RL::withDefaults($fp['fp_react_layout']);
                } else {
                    $result['scope'] = null;
                    $result['react_layout'] = RL::withDefaults();
                }

            } else {
                $result['scope'] = ReactLayout::SCOPE_REACT;
                $result['react_layout'] = RL::withDefaults($react['react_layout']);
            }
        } elseif ($layout['holderType'] === ReactLayout::SCOPE_FORM_PERMALINK) {
            $fp = FormPermalink::find($layout['holderId']);
            $result['scope'] = ReactLayout::SCOPE_FORM_PERMALINK;
            $result['react_layout'] = RL::withDefaults($fp['fp_react_layout']);
            $result['scopes'][] = [
                'id' => ReactLayout::SCOPE_FORM_PERMALINK,
                'label' => "Form permalink",
                'info' => $fp->fp_uri
            ];
        } else {
            throw new RuntimeException('holderType is not supported!');
        }
        return $result;
    }

    public function save($layoutId, $scope, $react_layout): void
    {
        $layout = Helper::str_to_array($layoutId);

        if ($layout['holderType'] === ReactLayout::SCOPE_REACT) {
            $react = React::findOrFail($layout['holderId']);
            if ($scope === ReactLayout::SCOPE_REACT) {
                $react['react_layout'] = $react_layout;
                $react->save();
            } elseif ($scope === ReactLayout::SCOPE_FORM_PERMALINK) {
                $rfp = $react->getPermalink()->primary()->permalink ?? null;
                if (!$rfp) {
                    throw new RuntimeException('Related primary permalink is not defined!');
                }
                $rfp->load('form_permalink');
                $form_permalink = $rfp->form_permalink;
                if (!$form_permalink) {
                    throw new RuntimeException('Related form permalink is not defined!');
                }
                $rfp->form_permalink['fp_react_layout'] = $react_layout;
                $rfp->form_permalink->save();
            } else {
                throw new RuntimeException('Invalid react layout scope!');
            }
        }

        if ($layout['holderType'] === ReactLayout::SCOPE_FORM_PERMALINK) {
            $fp = FormPermalink::findOrFail($layout['holderId']);
            $fp['fp_react_layout'] = $react_layout;
            $fp->save();
        }
    }

    public function saveScope(string $layoutId, string $scope): void
    {
        $layout = Helper::str_to_array($layoutId);
        if ($layout['holderType'] !== ReactLayout::SCOPE_REACT) {
            throw new RuntimeException("You can change scope only for react!");
        }
        $react = React::findOrFail($layout['holderId']);
        $react['react_layout_scope'] = $scope;
        $react->save();
    }

}