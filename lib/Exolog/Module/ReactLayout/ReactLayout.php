<?php

namespace Exolog\Module\ReactLayout;


use Exolog\Core\Forms\React;
use Exolog\Module\Support\Facades\RL;

class ReactLayout
{
    public const SCOPE_REACT = 'react';
    public const SCOPE_FORM_PERMALINK = 'form_permalink';

    protected array $reactsLayout;

    public React $react;

    public function __construct(React $react)
    {
        if ($react['react_layout_scope'] === static::SCOPE_REACT) {
            $value = RL::withDefaults($react['react_layout']);
        } else {
            $rfp = $react->getPrimaryPermalink();
            if ($rfp->form_permalink !== null) {
                $value = RL::withDefaults($rfp->form_permalink['fp_react_layout']);
            } else {
                $value = RL::withDefaults();
            }
        }
        $this->value = $value;
        $this->react = $react;
    }

    protected array $value;

    public function getTemplateName(): ?string
    {
        return $this->value['template_name'];
    }

    public function getLayout(): ?array
    {
        return $this->value['layouts'][$this->getTemplateName()];
    }

    public function getReacts(string $slot_name): array
    {
        $rl = $this->geReactsLayout();
        return $rl[$slot_name] ?? [];
    }

    public function geReactsLayout(): array
    {
        if (isset($this->reactsLayout)) {
            return $this->reactsLayout;
        }
        $layout = $this->getLayout();
        $ids = array_unique(data_get($layout, '*.*.react_id', []));
        $reacts = react()->whereIn('react_id', $ids)->get()->keyBy('react_id');
        $reactsLayout = collect($layout)->map(function ($cell) use ($reacts) {
            return collect($cell)->map(function ($item) use ($reacts) {
                return $reacts[$item['react_id']];
            })->toArray();
        })->toArray();
        return $this->reactsLayout = $reactsLayout;
    }
}