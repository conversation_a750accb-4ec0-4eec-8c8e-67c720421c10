<?php


namespace Exolog\Module\Markers\Listeners;

use Exolog\Module\Markers\Marker\ContainerTreeAlias;
use Exolog\Module\Markers\Marker\EditorVar\EditorVar;
use Exolog\Module\Markers\Marker\FQV;
use Exolog\Module\Markers\Marker\Now;
use Exolog\Module\Markers\Marker\Test;
use Illuminate\Events\Dispatcher;

class MarkersEventSubscriber
{
    /**
     * Register the listeners for the subscriber.
     *
     * @param Dispatcher $events
     * @return void
     */
    public function subscribe($events)
    {
        $events->listen('Marker:EDITOR_VAR_*', [EditorVar::class]);
        $events->listen('Marker:FQV', [FQV::class]);
        $events->listen('Marker:NOW', [Now::class]);
        $events->listen('Marker:TEST_MARKER', [Test::class]);
        $events->listen('Marker:CONTAINER_TREE_ALIAS', [ContainerTreeAlias::class]);
    }
}