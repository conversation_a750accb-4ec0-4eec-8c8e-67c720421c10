<?php


namespace Exolog\Module\Markers;


use Exolog\Module\Markers\Listeners\MarkersEventSubscriber;
use Exolog\Module\Support\ServiceProvider;

class MarkerServiceProvider extends ServiceProvider
{

    /**
     * The subscriber classes to register.
     *
     * @var array
     */
    protected $subscribe = [
        MarkersEventSubscriber::class,
    ];

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton('exolog.markers', function ($app) {
            return new MarkersManager($app);
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        global $editor_var;
        $editor_var = [];
    }
}