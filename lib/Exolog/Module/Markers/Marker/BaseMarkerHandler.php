<?php

namespace Exolog\Module\Markers\Marker;

use Exolog\Module\Markers\Events\Marker;

abstract class BaseMarkerHandler
{
    abstract public function handle(Marker $event);

    public function __invoke(...$arg): Marker
    {
        if ($arg[0] instanceof Marker) {
            $val = $this->handle(func_get_arg(0));
            if ($val instanceof Marker) {
                return $val;
            }
            if (!is_null($val)) {
                $arg[0]->value = $val;
            }
            return $arg[0];
        }
        $val = $this->handle($arg[1][0]);
        if ($val instanceof Marker) {
            return $val;
        }
        if (!is_null($val)) {
            $arg[1][0]->value = $val;
        }
        return $arg[1][0];
    }
}