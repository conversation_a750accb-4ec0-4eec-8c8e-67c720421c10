<?php


namespace Exolog\Module\Markers\Marker\EditorVar;


class EditorVarService
{
    public function all()
    {
        global $editor_var;
        return $editor_var;
    }

    public function clear()
    {
        global $editor_var;
        $editor_var = [];
    }

    public function prepare()
    {
        global $editor_var;
        $editor_var = collect($editor_var)->mapWithKeys(function ($value, $key) {
            return [strtoupper($key) => $value];
        });
    }

    public function get($name)
    {
        global $editor_var;
        return $editor_var[strtoupper($name)];
    }

    public function set($name, $value)
    {
        global $editor_var;
        $editor_var[strtoupper($name)] = $value;
    }

    public function __get($name)
    {
        return $this->get($name);
    }

    public function __set($name, $value)
    {
        $this->set($name, $value);
    }

    public function __isset($name)
    {
        global $editor_var;
        return array_key_exists($name, $editor_var);
    }
}