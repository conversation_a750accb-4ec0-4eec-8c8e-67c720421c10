<?php


namespace Exolog\Module\Markers\Marker\EditorVar;

use Exolog\Module\Markers\Events\Marker;
use Exolog\Module\Markers\Marker\BaseMarkerHandler;
use Illuminate\Support\Str;

class EditorVar extends BaseMarkerHandler
{
    public function handle(Marker $event)
    {
        $var_name = Str::of($event->marker)->afterLast('EDITOR_VAR_');
        $event->value = dv(ev($var_name), '');
        return $event;
    }
}