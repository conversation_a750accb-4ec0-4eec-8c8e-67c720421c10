<?php

namespace Exolog\Module\Markers\Marker;

use Exolog\Module\Markers\Events\Marker;

class FQV extends BaseMarkerHandler
{
    public function handle(Marker $event)
    {
        $event->value = react()->form($event->params['form'])->get()->mapWithKeys(function ($react) use ($event) {
            return [
                $react[dv($event->params['id'], 'react_id')] => $react[dv($event->params['label'], 'react_subject')]
            ];
        })->toArray();
        return $event;
    }
}