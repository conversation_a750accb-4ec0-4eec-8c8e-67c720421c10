<?php

namespace Exolog\Module\Markers\Marker;

use Exolog\Module\Markers\Events\Marker;
use Illuminate\Support\Str;

class ContainerTreeAlias extends BaseMarkerHandler
{
    public function handle(Marker $event): Marker
    {
        $react = react($event['params']['keys']['react_id']);
        $containers = $react->parentContainers();
        if (count($containers) !== 1) {
            return $event;
        }

        $tree = $containers[0]->getReactsTree();
        $stack = [];
        $react_id = $event['params']['keys']['react_id'];
        while ($react_id) {
            $parent_tree_react = find_parent_react_in_tree($tree, $react_id);
            if ($parent_tree_react) {
                $stack[] = Str::slug($parent_tree_react['alias']);
                $react_id = $parent_tree_react->id();
            } else {
                $react_id = null;
            }
        }
        $event->value = implode('/', array_reverse($stack));

        return $event;
    }
}