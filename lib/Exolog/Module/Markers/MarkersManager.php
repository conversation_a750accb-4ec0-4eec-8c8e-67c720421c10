<?php


namespace Exolog\Module\Markers;


use Exolog\Module\Base\Application;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use Illuminate\Support\Traits\Macroable;

class MarkersManager

{
    use Macroable;

    protected Application $app;

    protected MarkersProcessor $processor;

    /**
     * Markers manager constructor.
     **/
    public function __construct(Application $app)
    {
        $this->app = $app;
        $this->processor = new MarkersProcessor($app, $this);
    }


    /**
     * Render markers in the content.
     *
     * @param string $content
     * @param array $params
     * @return HtmlString
     */
    public function render(string $content, array $params = []): HtmlString
    {
        return new HtmlString($this->apply($content, $params));
    }


    public function apply(string $content, array $params = []): string
    {
        return $this->processor->apply($content, $params);
    }

    /**
     * Handle one marker and return.
     *
     * @param string $marker
     * @param array $params
     * @return mixed
     */
    public function resolve(string $marker, array $params = [])
    {
        return $this->processor->resolveMarker($marker, $params);
    }

    public function isMarker($text): bool
    {
        return Str::of($text)->trim()->startsWith('%%');
    }

    public function resolveIfMarker(string $text, array $params = [])
    {
        return $this->isMarker($text) ? $this->resolve($text, $params) : $text;
    }
}
