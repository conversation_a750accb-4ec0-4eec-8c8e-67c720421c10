<?php


namespace Exolog\Module\Markers;

use Exolog\Module\Markers\Events\Marker;
use Exolog\Module\Support\AttributesParser;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Support\Str;
use Illuminate\Support\Traits\Macroable;

class MarkersProcessor
{
    use Macroable;

    public Application $app;

    protected MarkersManager $manager;

    /**
     * markers renderer constructor.
     *
     * @param Application $app
     * @param MarkersManager $manager
     */
    public function __construct(Application $app, MarkersManager $manager)
    {
        $this->app = $app;
        $this->manager = $manager;
    }

    /**
     * Apply markers to content.
     * Search content for markers and filter markers through their hooks.
     *
     * If there are no markers defined, then the content will be returned
     *
     * @param string $content Content to search for markers.
     * @return string Content with markers filtered out.
     */
    public function apply(string $content, array $params = []): string
    {
        if (false === strpos($content, '%%')) {
            return $content;
        }

        preg_match_all("'(%%.+%%)'U", $content, $marker_matches);
        foreach ($marker_matches[1] as $marker_raw) {
            $value = $this->resolveMarker($marker_raw, $params) ?? '';
            $content = str_ireplace($marker_raw, $value, $content);
        }
        return $content;
    }

    public function resolveMarker(string $marker_raw, $params = [])
    {
        $marker = $this->parseMarker($marker_raw);
        /** @var Marker $event */
        $event = xEvent('Marker:' . $marker['name'],
            new Marker(['marker' => $marker['name'], 'params' => array_merge($params, $marker['params']), 'value' => null]));
        return $event->value;
    }

    protected function parseMarker($marker_raw): array
    {
        $marker = [
            'name' => '',
            'params' => []
        ];

        $marker_raw = (string)Str::of($marker_raw)->trim('%')->trim();
        preg_match_all("/(.[^(]*)(\(([^\(\)]*)\)){0,1}/", $marker_raw, $matches);
        $marker['name'] = strtoupper(trim($matches[1][0]));
        if (!empty($matches[3]) && !empty($matches[3][0])) {
            $marker['params'] = AttributesParser::parse($matches[3][0]);
        }
        return $marker;
    }
}
