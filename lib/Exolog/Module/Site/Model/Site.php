<?php


namespace Exolog\Module\Site\Model;


use Exolog\Module\Database\Scopes\SiteScope;
use Exolog\Module\Domains\Model\Domain;
use Exolog\Module\Support\PHPDoc\PHPDoc_Builder;
use Exolog\Module\Users\Model\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * @property mixed $site_id
 * @property \Carbon\Carbon $site_deleted_at
 * @property Carbon $site_date_lastmod
 * @property mixed $site_alias
 * @property Carbon $site_date_added
 * @property Collection<User> $users
 * @property Collection<Domain> $domains
 *
 *
 * @mixin PHPDoc_Builder
 */
class Site extends Model
{
    use SoftDeletes;

    public const DELETED_AT = 'site_deleted_at';
    protected $table = 'site';
    protected $primaryKey = 'site_id';
    public $timestamps = false;
    protected $fillable = [
        'site_alias',
        'site_name',
        'site_info',
    ];
    protected $attributes = [
    ];

    protected $casts = [
        'site_date_added' => 'datetime',
        'site_date_lastmod' => 'datetime',
    ];

    public static function resolve($site): ?Site
    {
        if ($site instanceof static) {
            return $site;
        }
        if (is_numeric($site)) {
            return static::find($site);
        }
        return static::findByAlias($site);
    }

    protected static function booted()
    {
        static::created(static function (Site $site) {
            $site->site_date_added = now();
        });

        static::saving(static function (Site $site) {
            $site->site_date_lastmod = now();
        });
    }

    private static function findByAlias($site): ?Site
    {
        return static::query()->where('site_alias', $site)->first();
    }

    public function users(): HasMany
    {
        return $this->hasMany(User::class, 'u_site')->withoutGlobalScope(SiteScope::class);
    }


    public function domains(): HasMany
    {
        return $this->hasMany(Domain::class, 'domain_site')->withoutGlobalScope(SiteScope::class);
    }
}