<?php


namespace Exolog\Module\Site;


use Exolog\Core\Forms\React;
use Exolog\Module\Domains\Model\Domain;
use Exolog\Module\Editions\Model\Edition;
use Exolog\Module\Support\Facades\Site;
use Exolog\Module\Support\Facades\Vars;

class DefaultsService
{
    private array $defaults = [];
    private ?Edition $edition = null;

    public function __construct()
    {
        $this->init();
    }

    public function get($name)
    {
        if ($name === 'noindex') {
            return $this->isCurrentReactNoIndex();
        }
        if ($name === 'mailconf') {
            $this->getMailConf();
        }
        return $this->defaults[$name];
    }

    public function set($name, $value)
    {
        $this->defaults[$name] = $value;
    }

    public function __get($name)
    {
        return $this->get($name);
    }

    public function __set($name, $value)
    {
        $this->set($name, $value);
    }

    public function setCurrentReact(React $react)
    {
        $this->set('current_react_id', $react['react_id']);
        $this->set('current_react', $react);

        if (!empty($react['meta_title'])) {
            $this->set('title', $react['meta_title']);
        } elseif (!empty($react['title'])) {
            $this->set('title', $react['title']);
        } elseif (!empty($react['menu_title'])) {
            $this->set('title', $react['menu_title']);
        }
    }

    public function getCurrentReact()
    {
        return $this->get('current_react');
    }

    private function isCurrentReactNoIndex(): bool
    {
        /** @var React $react */
        $react = $this->getCurrentReact();
        if (is_null($react)) {
            return false;
        }
        //we check it due if remove field we still have value:(
        if (!$react->hasField('no_sitemap')) {
            return false;
        }
        return dv($react['no_sitemap'], false);
    }

    public function init(): void
    {
        $this->defaults = [
            'mailconf' => null,
            'domains' => [],
            'domain' => [],
            'edition' => null,
        ];
    }

    public function getMailConf(): array
    {
        if (!$this->defaults['mailconf']) {
            $fromEmail = Vars::edition('fromEmail');
            if ($fromEmail === null) {
                $host = Domain::getMainDomain();
                $fromEmail = "info@$host->domain_name";
            }
            $this->defaults['mailconf'] = [
                'fromEmail' => $fromEmail,
                'fromName' => Site::get('site_name')
            ];
        }
        return $this->defaults['mailconf'];
    }

    public function setEdition(Edition $default_edition): void
    {
        $this->edition = $default_edition;
        $this->defaults['edition'] = $default_edition->e_id;
    }

    public function getEdition(): ?Edition
    {
        return $this->edition;
    }

    public function toArray(): array
    {
        return $this->defaults;
    }
}