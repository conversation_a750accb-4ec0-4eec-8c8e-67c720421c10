<?php


namespace Exolog\Module\Site;


use Exolog\Module\Site\Model\Site;

class SiteService
{
    private ?Site $site = null;

    public function isInit(): bool
    {
        return $this->site && $this->site->site_id;
    }

    public function init(Site $site): void
    {
        $this->site = $site;
    }

    public function __get($name)
    {
        return $this->get($name);
    }

    public function get($name)
    {
        return $this->site[$name];
    }

    public function id()
    {
        return $this->get('site_id');
    }

    public function alias()
    {
        return $this->get('site_alias');
    }

    public function toArray(): array
    {
        return $this->site->toArray();
    }
}
