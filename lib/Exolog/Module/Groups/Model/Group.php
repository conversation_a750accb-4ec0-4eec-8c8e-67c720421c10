<?php


namespace Exolog\Module\Groups\Model;


use Exolog\Module\Database\Concerns\HasSiteScope;
use Exolog\Module\Support\PHPDoc\PHPDoc_Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

/**
 *
 * @mixin PHPDoc_Builder
 * @property string $group_name
 * @property string $group_alias
 * @property string $group_type
 * @property int $group_id
 */
class Group extends Model
{

    use HasSiteScope;

    public const SITE_FIELD = 'group_site';

    protected $table = 'groups';
    protected $primaryKey = 'group_id';
    protected $fillable = [
        'group_parent',
        'group_type',
        'group_name',
        'group_alias',
        'group_info'
    ];

    public $timestamps = false;
    public $incrementing = false;


    protected $casts = [
        'group_parent' => 'integer',
    ];

    protected static function booted()
    {
        static::saving(static function (Group $group) {
            if ($group->group_alias === null) {
                $group->group_alias = Str::slug($group->group_name);
            }
        });
    }


    public function parent(): BelongsTo
    {
        return $this->belongsTo(static::class, 'group_parent', 'group_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(static::class, 'group_parent', 'group_id')
            ->with('children')->orderBy('group_id');
    }

    public static function getGroupTree(string $group_type): Collection
    {
        return self::query()->with('children')
            ->where('group_type', $group_type)
            ->where('group_parent', 0)
            ->orderBy('group_id')
            ->get();
    }

    public function relations(): HasMany
    {
        return $this->hasMany(Relation::class, 'r_group', 'group_id');
    }

    public function addRelation($r_obj): void
    {
        /** @var Relation */
        /*return $this->relations()->create([
            'r_type' => $this->group_type,
            'r_obj' => $r_obj,
        ]);*/
        DB::table('relations')->insert([
            'r_type' => $this->group_type,
            'r_group' => $this->group_id,
            'r_obj' => $r_obj,
        ]);
    }

    public function removeRelation($r_obj): void
    {
        $this->relations()->where('r_obj', $r_obj)->delete();
    }
}