<?php


namespace Exolog\Module\Mails\Listeners;


use Exolog\Module\Mails\Model\MailLog;
use Exolog\Module\Support\Facades\Site;
use Illuminate\Events\Dispatcher;
use Illuminate\Mail\Events\MessageSent;

class MailEventSubscriber
{
    /**
     * Register the listeners for the subscriber.
     *
     * @param Dispatcher $events
     * @return void
     */
    public function subscribe(Dispatcher $events): void
    {
        $events->listen(MessageSent::class, [static::class, 'logSentMessage']);
    }

    public function logSentMessage(MessageSent $event): void
    {
        if (!Site::isInit()) {
            return;
        }
        MailLog::query()->create([
            'ml_to' => json_encode($event->message->getTo()),
            'ml_from' => json_encode($event->message->getFrom()),
            'ml_subject' => $event->message->getSubject()
        ]);
    }
}