<?php

namespace Exolog\Module\Mails\Mail;

use Exolog\Module\Users\Model\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class UserPasswordChangeLink extends Mailable
{
    use Queueable, SerializesModels;


    private User $user;
    private string $link;

    /**
     * Create a new message instance.
     *
     */
    public function __construct(User $user, string $link)
    {
        $this->user = $user;
        $this->link = $link;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $this->subject('Exolog Dealer');

        return $this->view('system.mail.user-password-change', [
            'user' => $this->user,
            'link' => $this->link
        ]);
    }
}
