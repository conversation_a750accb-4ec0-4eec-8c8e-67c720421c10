<?php


namespace Exolog\Module\Mails;


use DOMDocument;
use Illuminate\Container\Container;
use Illuminate\Mail\Mailable;

class ReactMail extends Mailable
{

    public function setTo($address, $name = null)
    {
        $this->to = [];
        return $this->setAddress($address, $name, 'to');
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        //we build html version before sent a message to mailer and cleanup script/link tags
        $this->html = Container::getInstance()->make('mailer')->render(
            $this->buildView(), $this->buildViewData()
        );

        $this->cleanupHTML();

        return $this;
    }

    private function cleanupHTML(): void
    {
        $dom = new DOMDocument();

        $dom->loadHTML($this->html);

        $remove = [];

        $script = $dom->getElementsByTagName('script');
        foreach ($script as $item) {
            $remove[] = $item;
        }

        $link = $dom->getElementsByTagName('link');
        foreach ($link as $item) {
            $remove[] = $item;
        }

        foreach ($remove as $item) {
            $item->parentNode->removeChild($item);
        }
        $this->html = $dom->saveHTML();
    }
}