<?php


namespace Exolog\Module\Mails\Model;


use Exolog\Module\Database\Scopes\SiteScope;
use Exolog\Module\Support\Facades\Site;
use Illuminate\Database\Eloquent\Model;

/**
 * @property string ml_id
 * @property string ml_site_id
 * @property string ml_from
 * @property string ml_to
 * @property string ml_subject
 * @method static MailLog|null find($form_id)
 */
class MailLog extends Model
{
    const CREATED_AT = 'ml_date';
    const UPDATED_AT = 'ml_date_changed';
    protected $table = 'mail_log';
    protected $primaryKey = 'ml_id';
    protected $fillable = ['ml_from', 'ml_to', 'ml_subject'];

    protected static function booted()
    {
        static::saving(function (MailLog $ml) {
            $ml->ml_site_id = Site::id();
        });
        static::addGlobalScope(new SiteScope('ml_site_id'));
    }


}