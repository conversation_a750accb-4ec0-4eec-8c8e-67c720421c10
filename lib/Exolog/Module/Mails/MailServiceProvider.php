<?php


namespace Exolog\Module\Mails;

use Exolog\Module\Support\ServiceProvider;
use Illuminate\Support\Facades\Config;

class MailServiceProvider extends ServiceProvider
{

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {

    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function initSite()
    {
        $this->overrideIfExists('mail.default');
        $this->overrideIfExists('mail.from');
        $this->overrideIfExists('mail.markdown');

        $siteMailers = Config::get('site.mail.mailers', []);
        foreach ($siteMailers as $name => $options) {
            if (!Config::has('mail.mailers.' . $name)) {
                Config::set('mail.mailers.' . $name, $options);
            }
        }
    }

    private function overrideIfExists($key): void
    {
        if (Config::has("site.$key")) {
            Config::set($key, Config::get("site.$key"));
        }
    }
}