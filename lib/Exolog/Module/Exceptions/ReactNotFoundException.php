<?php

namespace Exolog\Module\Exceptions;

use Illuminate\Database\RecordsNotFoundException;
use Illuminate\Support\Arr;

class ReactNotFoundException extends RecordsNotFoundException
{

    private array $ids;

    public function setIds($ids = [])
    {
        $this->ids = Arr::wrap($ids);

        $this->message = "No query results for react";

        if (count($this->ids) > 0) {
            $this->message .= ' ' . implode(', ', $this->ids);
        } else {
            $this->message .= '.';
        }

        return $this;
    }

    /**
     * Get the affected React IDs.
     *
     */
    public function getIds()
    {
        return $this->ids;
    }
}