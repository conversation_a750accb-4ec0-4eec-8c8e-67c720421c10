<?php


namespace Exolog\Module\Cache;


use Exolog\Module\Support\Facades\Path;
use Exolog\Module\Support\Facades\Site;
use Illuminate\Support\Facades\Config;

class CacheServiceProvider extends \Illuminate\Cache\CacheServiceProvider
{
    public function register()
    {
        parent::register();
    }

    public function initSite()
    {
        //dump('cache',  Site::id());
        Config::set('cache.stores.file.path', Path::to('site_cache', 'laravel'));
        Config::set('cache.prefix', 'exolog-site' . Site::id());
        parent::register();
    }

}