<?php

namespace Exolog\Module\Cache;

use Exolog\Module\Http\Response as ExologResponse;
use Spatie\ResponseCache\Serializers\DefaultSerializer;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Response;

class ExologDefaultSerializer extends DefaultSerializer
{
    protected function buildResponse(array $responseProperties): Response
    {
        $type = $responseProperties['type'] ?? static::RESPONSE_TYPE_NORMAL;

        if ($type === static::RESPONSE_TYPE_FILE) {
            return new BinaryFileResponse(
                $responseProperties['content'],
                $responseProperties['statusCode']
            );
        }

        $response = new ExologResponse($responseProperties['content'], $responseProperties['statusCode']);
        return $response;
    }
}
