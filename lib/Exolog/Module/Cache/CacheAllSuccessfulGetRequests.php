<?php


namespace Exolog\Module\Cache;

use Exolog\Module\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Spatie\ResponseCache\CacheProfiles\BaseCacheProfile;
use Symfony\Component\HttpFoundation\Response;

class CacheAllSuccessfulGetRequests extends BaseCacheProfile
{
    public function shouldCacheRequest(Request $request): bool
    {
        if (Auth::check()) {
            return false;
        }

        if (app()->environment('local')) {
            return false;
        }

        if ($request->input('debug')) {
            return false;
        }

        if ($request->ajax()) {
            return false;
        }

        if ($this->isRunningInConsole()) {
            return false;
        }

        return $request->isMethod('get');
    }

    public function shouldCacheResponse(Response $response): bool
    {
        if (!$this->hasCacheableResponseCode($response)) {
            return false;
        }

        if (!$this->hasCacheableContentType($response)) {
            return false;
        }

        return true;
    }

    public function enabled(Request $request): bool
    {
        if (Auth::check()) {
            return false;
        }

        if (app()->environment('local')) {
            return false;
        }

        if ($request->input('debug')) {
            return false;
        }

        return parent::enabled($request); // TODO: Change the autogenerated stub
    }

    public function hasCacheableResponseCode(Response $response): bool
    {
        if ($response->isSuccessful()) {
            return true;
        }

        if ($response->isRedirection()) {
            return true;
        }

        return false;
    }

    public function hasCacheableContentType(Response $response): bool
    {
        $contentType = $response->headers->get('Content-Type', '');

        if (Str::startsWith($contentType, 'text/')) {
            return true;
        }

        if (Str::contains($contentType, ['/json', '+json'])) {
            return true;
        }

        return false;
    }

    public function useCacheNameSuffix(Request $request): string
    {
        return Auth::check()
            ? (string)Auth::id()
            : '';
    }
}
