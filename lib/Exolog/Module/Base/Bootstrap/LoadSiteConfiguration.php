<?php


namespace Exolog\Module\Base\Bootstrap;


use Exception;
use Illuminate\Contracts\Config\Repository as RepositoryContract;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Foundation\Bootstrap\LoadConfiguration;
use <PERSON>ymfony\Component\Finder\Finder;

class LoadSiteConfiguration extends LoadConfiguration
{
    /**
     * Bootstrap the given application.
     *
     * @param Application $app
     * @return void
     */
    public function bootstrap(Application $app)
    {
        $this->loadConfigurationFiles($app, app('config'));
    }

    /**
     * Load the configuration items from all of the files.
     *
     * @param Application $app
     * @param RepositoryContract $repository
     * @return void
     *
     * @throws Exception
     */
    protected function loadConfigurationFiles(Application $app, RepositoryContract $repository)
    {
        if ($siteConfig = realpath($this->configSitePath())) {
            $files = $this->getSiteConfigurationFiles($siteConfig);
            foreach ($files as $key => $path) {
                $repository->set($key, require $path);
            }
        }
    }

    /**
     * Get all of the configuration files for the application.
     *
     * @param Application $app
     * @param $configPath
     * @return array
     */
    protected function getSiteConfigurationFiles($configPath)
    {
        $files = [];

        foreach (Finder::create()->files()->name('*.php')->in($configPath) as $file) {
            $directory = $this->getNestedDirectory($file, $configPath);

            $files[$directory . basename($file->getRealPath(), '.php')] = $file->getRealPath();
        }

        ksort($files, SORT_NATURAL);

        return $files;
    }

    public function configSitePath($path = '')
    {
        return site_path('config/app/');
    }
}