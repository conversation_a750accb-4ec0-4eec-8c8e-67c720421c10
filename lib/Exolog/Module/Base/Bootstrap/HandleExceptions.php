<?php

namespace Exolog\Module\Base\Bootstrap;

use Illuminate\Contracts\Foundation\Application;
use Illuminate\Foundation\Bootstrap\HandleExceptions as Foundation;

class HandleExceptions extends Foundation
{
    /**
     * Bootstrap the given application.
     *
     * @param Application $app
     * @return void
     */
    public function bootstrap(Application $app)
    {
        parent::bootstrap($app);
        //TODO Remove E_NOTICE ^ E_WARNING
        error_reporting(E_ALL ^ E_NOTICE ^ E_WARNING);
    }
}
