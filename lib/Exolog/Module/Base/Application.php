<?php


namespace Exolog\Module\Base;


use Exolog\Module\Http\Request;
use Exolog\Module\Routing\Router;
use Exolog\Module\Routing\RoutingServiceProvider;
use Illuminate\Auth\AuthManager;
use Illuminate\Cache\CacheManager;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Config\Repository;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Container\CircularDependencyException;
use Illuminate\Contracts\Container\Container;
use Illuminate\Contracts\Cookie\QueueingFactory;
use Illuminate\Contracts\Encryption\Encrypter;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Contracts\Filesystem\Cloud;
use Illuminate\Contracts\Filesystem\Filesystem;
use Illuminate\Contracts\Hashing\Hasher;
use Illuminate\Contracts\Mail\Mailer;
use Illuminate\Contracts\Mail\MailQueue;
use Illuminate\Contracts\Queue\Monitor;
use Illuminate\Contracts\Queue\Queue;
use Illuminate\Contracts\Redis\Connection;
use Illuminate\Contracts\Routing\BindingRegistrar;
use Illuminate\Contracts\Routing\Registrar;
use Illuminate\Contracts\Routing\UrlGenerator;
use Illuminate\Contracts\Session\Session;
use Illuminate\Contracts\Translation\Translator;
use Illuminate\Contracts\View\Factory;
use Illuminate\Cookie\CookieJar;
use Illuminate\Database\ConnectionInterface;
use Illuminate\Database\ConnectionResolverInterface;
use Illuminate\Database\DatabaseManager;
use Illuminate\Events\EventServiceProvider;
use Illuminate\Filesystem\FilesystemManager;
use Illuminate\Foundation\Application as ApplicationFoundation;
use Illuminate\Hashing\HashManager;
use Illuminate\Log\LogManager;
use Illuminate\Log\LogServiceProvider;
use Illuminate\Mail\MailManager;
use Illuminate\Queue\Failed\FailedJobProviderInterface;
use Illuminate\Queue\QueueManager;
use Illuminate\Redis\RedisManager;
use Illuminate\Routing\Redirector;
use Illuminate\Session\SessionManager;
use Illuminate\Session\Store;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Facade;
use Illuminate\Support\ServiceProvider;
use Illuminate\View\Compilers\BladeCompiler;
use Psr\Cache\CacheItemPoolInterface;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;
use Psr\SimpleCache\CacheInterface;
use Symfony\Component\Cache\Adapter\AdapterInterface;
use Symfony\Component\Cache\Adapter\Psr16Adapter;


class Application extends ApplicationFoundation
{
    public function __construct($basePath)
    {
        parent::__construct($basePath);
    }

    public function configPath($path = '')
    {
        return $this->basePath . DIRECTORY_SEPARATOR . 'configs/app';
    }

    public function databasePath($path = '')
    {
        return ($this->databasePath ?: $this->basePath . DIRECTORY_SEPARATOR . 'db') . ($path ? DIRECTORY_SEPARATOR . $path : $path);

    }


    public function publicPath()
    {
        return $this->basePath . DIRECTORY_SEPARATOR . 'homepages.webkracht.nl/htdocs';
    }

    protected function registerBaseServiceProviders()
    {
        $this->register(new EventServiceProvider($this));
        $this->register(new LogServiceProvider($this));
        $this->register(new RoutingServiceProvider($this));
    }

    public function register($provider, $force = false)
    {
        if (($registered = $this->getProvider($provider)) && !$force) {
            return $registered;
        }

        // If the given "provider" is a string, we will resolve it, passing in the
        // application instance automatically for the developer. This is simply
        // a more convenient way of specifying your service provider classes.
        if (is_string($provider)) {
            $provider = $this->resolveProvider($provider);
        }

        $provider->register();

        // If there are bindings / singletons set as properties on the provider we
        // will spin through them and register them with the application, which
        // serves as a convenience layer while registering a lot of bindings.
        if (property_exists($provider, 'bindings')) {
            foreach ($provider->bindings as $key => $value) {
                $this->bind($key, $value);
            }
        }

        if (property_exists($provider, 'singletons')) {
            foreach ($provider->singletons as $key => $value) {
                $this->singleton($key, $value);
            }
        }

        $this->markAsRegistered($provider);

        // If the application has already booted, we will call this boot method on
        // the provider class so it has an opportunity to do its boot logic and
        // will be ready for any usage by this developer's application logic.
        if ($this->isBooted()) {
            $this->bootProvider($provider);
        }

        if ($this->isInitSite()) {
            $this->initSiteProvider($provider);
        }

        return $provider;
    }

    private function isInitSite(): bool
    {
        return $this->resolved('exolog.site') && resolve('exolog.site')->isInit();
    }

    /**
     * Call initSite in the given service provider after site detected and init.
     *
     * @param ServiceProvider $provider
     * @return void
     */
    protected function initSiteProvider(ServiceProvider $provider)
    {
        if (method_exists($provider, 'initSite')) {
            $this->call([$provider, 'initSite']);
        }
    }

    /**
     * Register the core class aliases in the container.
     *
     * @return void
     */
    public function registerCoreContainerAliases()
    {
        foreach ([
                     /*\Illuminate\Foundation\Application::class => [
                         self::class
                     ],*/
                     'app' => [
                         self::class,
                         Container::class,
                         \Illuminate\Contracts\Foundation\Application::class,
                         ContainerInterface::class,
                         ApplicationFoundation::class,
                     ],
                     'auth' => [AuthManager::class, \Illuminate\Contracts\Auth\Factory::class],
                     'auth.driver' => [Guard::class],
                     'blade.compiler' => [BladeCompiler::class],
                     'cache' => [CacheManager::class, \Illuminate\Contracts\Cache\Factory::class],
                     'cache.store' => [
                         \Illuminate\Cache\Repository::class,
                         \Illuminate\Contracts\Cache\Repository::class,
                         CacheInterface::class
                     ],
                     'cache.psr6' => [
                         Psr16Adapter::class,
                         AdapterInterface::class,
                         CacheItemPoolInterface::class
                     ],
                     'config' => [\Illuminate\Config\Repository::class, Repository::class],
                     'cookie' => [
                         CookieJar::class,
                         \Illuminate\Contracts\Cookie\Factory::class,
                         QueueingFactory::class
                     ],
                     'encrypter' => [
                         \Illuminate\Encryption\Encrypter::class,
                         Encrypter::class
                     ],
                     'db' => [
                         DatabaseManager::class,
                         ConnectionResolverInterface::class
                     ],
                     'db.connection' => [
                         \Illuminate\Database\Connection::class,
                         ConnectionInterface::class
                     ],
                     'events' => [\Illuminate\Events\Dispatcher::class, Dispatcher::class],
                     'files' => [\Illuminate\Filesystem\Filesystem::class],
                     'filesystem' => [
                         FilesystemManager::class,
                         \Illuminate\Contracts\Filesystem\Factory::class
                     ],
                     'filesystem.disk' => [Filesystem::class],
                     'filesystem.cloud' => [Cloud::class],
                     'hash' => [HashManager::class],
                     'hash.driver' => [Hasher::class],
                     'translator' => [
                         \Illuminate\Translation\Translator::class,
                         Translator::class
                     ],
                     'log' => [LogManager::class, LoggerInterface::class],
                     'mail.manager' => [MailManager::class, \Illuminate\Contracts\Mail\Factory::class],
                     'mailer' => [
                         \Illuminate\Mail\Mailer::class,
                         Mailer::class,
                         MailQueue::class
                     ],
                     //'auth.password'        => [\Illuminate\Auth\Passwords\PasswordBrokerManager::class, \Illuminate\Contracts\Auth\PasswordBrokerFactory::class],
                     //'auth.password.broker' => [\Illuminate\Auth\Passwords\PasswordBroker::class, \Illuminate\Contracts\Auth\PasswordBroker::class],
                     'queue' => [
                         QueueManager::class,
                         \Illuminate\Contracts\Queue\Factory::class,
                         Monitor::class
                     ],
                     'queue.connection' => [Queue::class],
                     'queue.failer' => [FailedJobProviderInterface::class],
                     'redirect' => [Redirector::class],
                     'redis' => [RedisManager::class, \Illuminate\Contracts\Redis\Factory::class],
                     'redis.connection' => [
                         \Illuminate\Redis\Connections\Connection::class,
                         Connection::class
                     ],
                     'request' => [
                         \Illuminate\Http\Request::class,
                         \Symfony\Component\HttpFoundation\Request::class,
                         Request::class
                     ],
                     'router' => [
                         Router::class,
                         Registrar::class,
                         BindingRegistrar::class
                     ],
                     'session' => [SessionManager::class],
                     'session.store' => [
                         Store::class,
                         Session::class
                     ],
                     'url' => [
                         \Illuminate\Routing\UrlGenerator::class,
                         UrlGenerator::class
                     ],
                     'validator' => [
                         \Illuminate\Validation\Factory::class,
                         \Illuminate\Contracts\Validation\Factory::class
                     ],
                     'view' => [\Illuminate\View\Factory::class, Factory::class],

                     /** custom **/
                     //'exolog' => [Exolog::class]

                 ] as $key => $aliases) {
            foreach ($aliases as $alias) {
                $this->alias($key, $alias);
            }
        }
    }

    /**
     * @throws CircularDependencyException
     * @throws BindingResolutionException
     */
    public function bootExolog(): void
    {
        if ($this->has('exolog')) {
            $this->forgetInstance('exolog');
            $this->forgetInstance('exolog.vars');
            $this->forgetInstance('exolog.site');
            $this->forgetInstance('exolog.defaults');
            $this->forgetInstance('exolog.path');
            $this->forgetInstance('exolog.editor_vars');

            Facade::clearResolvedInstances();
        }
    }

    protected function registerProviderArray(array $providerlist)
    {
        if (empty($providerlist)) {
            return;
        }
        $providers = Collection::make($providerlist)
            ->partition(function ($provider) {
                return strpos($provider, 'Illuminate\\') === 0;
            });

        foreach ($providers->collapse()->toArray() as $provider) {
            $this->register($provider);
        }
    }

    public function registerSiteProviders()
    {
        if (empty($this->config['site.providers'])) {
            return;
        }
        $this->registerProviderArray($this->config['site.providers']);
    }

    public function initSite()
    {
        array_walk($this->serviceProviders, function ($p) {
            $this->initSiteProvider($p);
        });
    }

    public function getNamespace()
    {
        return '\\Exolog\\Site\\';
    }


    /**
     * Determine if the application ECO are cached.
     *
     * @return bool
     */
    public function ECOAreCached(): bool
    {
        return $this['files']->exists($this->getCachedECOPath());
    }

    /**
     * Get the path to the ECO cache file.
     *
     * @return string
     */
    public function getCachedECOPath(): string
    {
        return $this->normalizeCachePath('APP_ECO_CACHE', 'cache/eco.php');
    }
}