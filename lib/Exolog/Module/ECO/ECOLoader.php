<?php

namespace Exolog\Module\ECO;


use Exolog\Module\Support\Concerns\HasMakeFactory;

class ECOLoader
{
    use HasMakeFactory;

    private const max_scan_depth = 10;

    /**
     * Scan the api path, recursively including all PHP files
     *
     * @param string $dir
     * @param int $depth (optional)
     */
    protected function require_all(string $dir, int $depth = 0): void
    {
        if ($depth > self::max_scan_depth) {
            return;
        }
        // require all php files
        $scan = glob("$dir/*");
        foreach ($scan as $path) {
            if (preg_match('/\.php$/', $path)) {
                require $path;
            } elseif (is_dir($path)) {
                $this->require_all($path, $depth + 1);
            }
        }
    }

    public function load()
    {
        $this->require_all(base_path('/configs/ECO'));
        $this->require_all(base_path('/configs/ECO'));
        $ECO = $GLOBALS['ECO'];
        unset($GLOBALS['ECO']);
        return $ECO;
    }
}
