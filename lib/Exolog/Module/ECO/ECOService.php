<?php

namespace Exolog\Module\ECO;


use Exolog\Module\Base\Application;
use Exolog\Module\Support\Concerns\HasMakeFactory;
use Illuminate\Filesystem\Filesystem;

class ECOService
{
    use HasMakeFactory;

    private Application $app;
    private Filesystem $files;

    public function __construct(Application $app, Filesystem $files)
    {
        $this->app = $app;
        $this->files = $files;
    }

    private ?array $ECO = null;

    protected function resolveECO()
    {
        if ($this->ECO) {
            return $this->ECO;
        }
        $configPath = $this->app->getCachedECOPath();

        if ($this->app->ECOAreCached()) {
            $this->ECO = $this->files->getRequire($configPath);
            return $this->ECO;
        }

        $this->ECO = ECOLoader::make()->load();
        return $this->ECO;
    }

    public function get($path = null, $default = null)
    {
        if ($path === null) {
            return $this->resolveECO();
        }
        return data_get($this->resolveECO(), $path, $default);
    }
}
