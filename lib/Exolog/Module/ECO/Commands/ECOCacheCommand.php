<?php

namespace Exolog\Module\ECO\Commands;

use Exolog\Module\ECO\ECOLoader;
use Illuminate\Console\Command;
use Illuminate\Filesystem\Filesystem;
use LogicException;
use Throwable;

class ECOCacheCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'exolog:eco:cache';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a cache file for faster ECO loading';

    /**
     * The filesystem instance.
     *
     * @var Filesystem
     */
    protected $files;

    /**
     * Create a new config cache command instance.
     *
     * @param Filesystem $files
     * @return void
     */
    public function __construct(Filesystem $files)
    {
        parent::__construct();

        $this->files = $files;
    }

    /**
     * Execute the console command.
     *
     * @return void
     *
     * @throws LogicException
     */
    public function handle()
    {
        $this->call('exolog:eco:clear');

        $ECO = ECOLoader::make()->load();

        $configPath = $this->laravel->getCachedECOPath();

        $this->files->put(
            $configPath, '<?php return ' . var_export($ECO, true) . ';' . PHP_EOL
        );

        try {
            require $configPath;
        } catch (Throwable $e) {
            $this->files->delete($configPath);

            throw new LogicException('Your ECO files are not serializable.', 0, $e);
        }

        $this->info('ECO cached successfully!');
    }
}
