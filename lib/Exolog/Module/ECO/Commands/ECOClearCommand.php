<?php

namespace Exolog\Module\ECO\Commands;

use Illuminate\Console\Command;
use Illuminate\Filesystem\Filesystem;

class ECOClearCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'exolog:eco:clear';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove the ECO cache file';

    /**
     * The filesystem instance.
     *
     * @var Filesystem
     */
    protected $files;

    /**
     * Create a new config clear command instance.
     *
     * @param Filesystem $files
     * @return void
     */
    public function __construct(Filesystem $files)
    {
        parent::__construct();

        $this->files = $files;
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $this->files->delete($this->laravel->getCachedECOPath());

        $this->info('ECO cache cleared!');
    }
}
