<?php


namespace Exolog\Module\View;


use Exolog\Module\Support\ServiceProvider;
use Exolog\Module\View\Components\Container;
use Exolog\Module\View\Components\Layout;
use Exolog\Module\View\Components\LayoutSlot;
use Exolog\Module\View\Components\Link;
use Exolog\Module\View\Components\SiopLogin;
use Illuminate\Support\Facades\Blade;

class ComponentServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //Default laravel namespace we set to Exolog\Site as result we can`t use autoload class
        //for "x-" components
        Blade::component('layout', Layout::class);
        Blade::component('layout-slot', LayoutSlot::class);
        Blade::component('container', Container::class);
        Blade::component('link', Link::class);
        Blade::component('siop-login', SiopLogin::class);


        //Blade::componentNamespace('Exolog\\Module\\View\\Components', 'exo');
        //$this->loadViewsFrom(PathService::to('tmpl', '/views/system/'), 'exo');
    }

}
