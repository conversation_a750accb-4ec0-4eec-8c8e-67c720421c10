<?php


namespace Exolog\Module\View;


use Exolog\Module\Contracts\XEditable;
use Exolog\Module\Forms\FQT\FQTBase;
use Exolog\Module\Forms\FQT\Json\JsonField;
use Exolog\Module\Forms\FQT\Unit\PayloadField;
use Exolog\Module\Vars\Variable;

trait HasRenderValue
{
    protected array $renderParams = [];

    abstract public function getData();

    abstract public function setData($data);

    public function render($params = [])
    {
        if (!empty($params['tag'])) {
            return $this->renderTag($params);
        }
        return $this->getRenderData($params);
    }

    protected function renderTag($params = [])
    {
        $tag = $params['tag'] ?? 'div';
        $data = $this->getRenderData($params);

        $attr = $this->parseAttr($params,
            //todo move exclude params to property
            [
                'tag',
                'exedit',
                'exedit-container',
                'as',
                'filter',
                'view',
                'view_empty',
                'default',
                'format',
                'locale',
                'formatFunction',
                'trueValue',
                'falseValue',
            ]
        );

        if ($this instanceof XEditable) {
            $xeditAttr = $this->getExeditAttr($params['exedit']);
        } else {
            $xeditAttr = '';
        }
        return sprintf('<%s %s %s>%s</%s>',
            $tag,
            $attr,
            $xeditAttr,
            $data,
            $tag
        );
    }

    protected function parseAttr($params, $exclude = [])
    {
        $excludeAssoc = [];
        foreach ($exclude as $item) {
            $excludeAssoc[$item] = '';
        }
        if (empty($params)) {
            return '';
        }
        $attr_array = array_diff_key($params, $excludeAssoc);
        $attr = [];
        foreach ($attr_array as $key => $value) {
            $attr[] = $key . '=' . '"' . (is_array($value) ? implode(" ", $value) : $value) . '"';
        }
        return implode(' ', $attr);
    }

    protected function getRenderData($params = [])
    {
        return $this->getData() ?? $params['default'] ?? null;
    }

    public function toHtml()
    {
        return $this->render($this->renderParams);
    }

    /**
     * @return Variable|PayloadField|FQTBase|JsonField
     */
    public function with($renderParams)
    {
        $this->renderParams = $renderParams;
        return $this;
    }
}