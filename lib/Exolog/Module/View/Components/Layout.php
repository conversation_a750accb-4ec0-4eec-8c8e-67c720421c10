<?php


namespace Exolog\Module\View\Components;

use Exolog\Core\Forms\React;
use Exolog\Module\ReactLayout\ReactLayout;
use Exolog\Module\Support\Facades\RL;
use Illuminate\View\Component;

class Layout extends Component
{
    public ReactLayout $reactLayout;
    public React $react;
    public ?string $templateView = null;

    public function __construct(React $react)
    {
        $this->reactLayout = $react->getReactLayout();
        $this->react = $react;
        RL::push($this->reactLayout);
        if ($this->reactLayout->getTemplateName()) {
            $this->templateView = 'templates.layouts.' . $this->reactLayout->getTemplateName();
        }
    }

    public function render()
    {
        if ($this->templateView === null) {
            return view('system.partials.alert',
                ['message' => 'React layout name is not defined! Setup layout for react or for parent permalink.']
            );
        }
        return view('system.components.layout');
    }
}