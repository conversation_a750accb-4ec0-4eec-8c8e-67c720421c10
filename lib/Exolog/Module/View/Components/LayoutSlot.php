<?php


namespace Exolog\Module\View\Components;

use Exolog\Module\ReactLayout\ReactLayout;
use Exolog\Module\Support\Facades\RL;
use Illuminate\View\Component;
use RuntimeException;

class LayoutSlot extends Component
{
    public string $name;
    public array $blocks;

    public function __construct($name)
    {
        $this->name = $name;
        $reactLayout = $this->getReactLayout();
        if ($reactLayout === null) {
            throw new RuntimeException('The current react layout is not set. It looks like you try to render "blocks" out of <react-layout>');
        }
        $this->blocks = $reactLayout->getReacts($name);
    }

    public function getReactLayout(): ?ReactLayout
    {
        return RL::current();
    }

    public function render()
    {
        return view('system.components.layout-slot');
    }
}