<?php


namespace Exolog\Module\View\Components;

use Exolog\Core\Forms\React;
use Exolog\Module\Contracts\ContainerValue;
use Exolog\Module\Support\Facades\Auth;
use Illuminate\Support\HtmlString;
use Illuminate\View\Component;

class Container extends Component
{
    public \Exolog\Module\Container\Container $container;
    public string $as;
    public string $tag;
    /**
     * @var array|React[]
     */
    public array $reactsTree;
    public HtmlString $exeditAttr;

    public function __construct($container, $filter = [], $as = 'react', $tag = 'div')
    {
        if ($container instanceof ContainerValue) {
            $this->container = $container->getContainer();
        } else {
            $this->container = $container;
        }
        $this->as = $as;
        $this->tag = $tag;
        if (Auth::check() && Auth::isAdmin()) {
            $this->exeditAttr = $container->getExeditAttr();
        } else {
            $this->exeditAttr = new HtmlString();
        }

        $this->reactsTree = $this->container->getReactsTree($filter);
    }

    public function isEmpty(): bool
    {
        return empty($this->reactsTree);
    }

    public function render()
    {
        return function (array $data) {
            if (!isset($data['items'])) {
                $renderedItems = view('system.components.container-items', [
                    'component' => $this,
                    'itemSlot' => $data['slot']->toHtml()
                ]);

                $data['renderedItems'] = new HtmlString($renderedItems);
            }
            $data[$this->as] = "{{\$$this->as}}";

            return view('system.components.container', $data);
        };
    }
}