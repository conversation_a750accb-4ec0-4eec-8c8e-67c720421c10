<?php


namespace Exolog\Module\View\Components;

use Exolog\Core\Forms\React;
use Illuminate\Support\HtmlString;
use Illuminate\View\Component;

class Link extends Component
{

    public React $react;
    private string $field;

    public function __construct(React $react, $field = 'menu_title')
    {
        $this->react = $react;
        $this->field = $field;
    }

    public function render()
    {
        return function ($data) {
            $data['url'] = $this->react->getPermalink()->url;
            if ($data['slot']->isEmpty()) {
                $data['content'] = $this->react[$this->field];
                $data['exeditAttr'] = new HtmlString($this->react->getFieldValue($this->field)->getExeditAttr());
            } else {
                $data['content'] = $data['slot'];
                $data['exeditAttr'] = null;
            }
            return view('system.components.link', $data);
        };

    }


}