<?php


namespace Exolog\Module\View\Components;

use Exolog\Module\Support\Facades\Auth;
use Illuminate\View\Component;

class SiopLogin extends Component
{

    public string $redir;
    public string $request_uri;
    public string $request_qrcode;

    public function __construct(string $redir = '/')
    {
        $this->redir = $redir;
        $this->request_uri = Auth::siopAuthorizationRequest();
        $this->request_qrcode = qrcode($this->request_uri);
    }

    public function render()
    {
        return function ($data) {
            return view('system.components.siop-login', $data);
        };

    }
}
