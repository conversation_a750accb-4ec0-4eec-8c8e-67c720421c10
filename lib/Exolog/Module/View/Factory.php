<?php


namespace Exolog\Module\View;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\View\Factory as IlluminateViewFactory;

class Factory extends IlluminateViewFactory
{
    /**
     * Create a new view instance from the given arguments.
     *
     * @param string $view
     * @param string $path
     * @param Arrayable|array $data
     * @return \Illuminate\Contracts\View\View
     */
    protected function viewInstance($view, $path, $data)
    {
        return new View($this, $this->getEngineFromPath($path), $view, $path, $data,
            $this->container['exolog.markers']);
    }
}