<?php


namespace Exolog\Module\View;


use Exolog\Module\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;

class DirectiveServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {

        Blade::directive('toolbar', function ($expression) {
            return "<?php echo \Exolog\Module\ExoEditor\Toolbar::get() ?>";
        });

        Blade::directive('customCSS', function ($expression) {
            //todo push Sentry
            return "<?php echo '<!--deprecated customCSS-->' ?>";
        });
    }

}