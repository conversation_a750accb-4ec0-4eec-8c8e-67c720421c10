<?php


namespace Exolog\Module\View;


use Exolog\Module\Support\Concerns\HasMakeFactory;
use Exolog\Module\Support\Facades\Path;
use Illuminate\Support\Arr;

class RenderPDFService
{
    use HasMakeFactory;

    public static array $ALLOWED_PARAMS = [
        'collate',
        'no-collate',
        'cookie-jar',
        'copies',
        'd',
        'dpi',
        'H',
        'extended-help',
        'g',
        'grayscale',
        'image-dpi',
        'image-quality',
        'license',
        'log-level',
        'l',
        'lowquality',
        'B',
        'margin-bottom',
        'L',
        'margin-left',
        'R',
        'margin-right',
        'T',
        'margin-top',
        'O',
        'orientation',
        'page-height',
        's',
        'page-size',
        'page-width',
        'no-pdf-compression',
        'q',
        'quiet',
        'read-args-from-stdin',
        'title',
        'outline',
        'no-outline',
        'outline-depth',
        'background',
        'no-background',
        'bypass-proxy-for',
        'checkbox-checked-svg',
        'checkbox-svg',
        'cookie',
        'custom-header',
        'custom-header-propagation',
        'no-custom-header-propagation',
        'debug-javascript',
        'no-debug-javascript',
        'default-header',
        'encoding',
        'disable-external-links',
        'enable-external-links',
        'disable-forms',
        'enable-forms',
        'images',
        'no-images',
        'disable-internal-links',
        'enable-internal-links',
        'n',
        'disable-javascript',
        'enable-javascript',
        'javascript-delay',
        'keep-relative-links',
        'load-error-handling',
        'load-media-error-handling <han',
        'disable-local-file-access',
        'enable-local-file-access',
        'minimum-font-size',
        'exclude-from-outline',
        'include-in-outline',
        'page-offset',
        'print-media-type',
        'no-print-media-type',
        'radiobutton-checked-svg',
        'radiobutton-svg',
        'resolve-relative-links',
        'disable-smart-shrinking',
        'enable-smart-shrinking',
        'stop-slow-scripts',
        'no-stop-slow-scripts',
        'disable-toc-back-links',
        'enable-toc-back-links',
        'user-style-sheet',
        'viewport-size',
        'window-status',
        'zoom',
        'footer-center',
        'footer-font-name',
        'footer-font-size',
        'footer-html',
        'footer-left',
        'footer-line',
        'no-footer-line',
        'footer-right',
        'footer-spacing',
        'header-center',
        'header-font-name',
        'header-font-size',
        'header-html',
        'header-left',
        'header-line',
        'no-header-line',
        'header-right',
        'header-spacing',
        'replace',
        'disable-dotted-lines',
        'toc-header-text',
        'disable-toc-links',
        'toc-text-size-shrink',
        'xsl-style-sheet',
    ];

    public function htmlToPdf($html, $params)
    {
        $tmp_html = tempnam(Path::to('site_cache'), '') . '.html';
        $tmp_pdf = tempnam(Path::to('site_cache'), '') . '.pdf';

        file_put_contents($tmp_html, $html);

        exec(
            sprintf('%s %s %s %s',
                base_path('modules/wkhtmltopdf/wkhtmltopdf'),
                $this->prepareArgumentsString($params),
                $_SERVER['HTTP_HOST'] . Path::web('cache') . basename($tmp_html),
                $tmp_pdf,
            )
        );

        $pdf_content = file_get_contents($tmp_pdf);

        unlink($tmp_html);
        unlink($tmp_pdf);

        return $pdf_content;
    }

    private function prepareArgumentsString($params): string
    {
        $filtered_params = Arr::only($params, self::$ALLOWED_PARAMS);

        $str = '';
        foreach ($filtered_params as $key => $value) {
            $param = (strlen($key) === 1 ? '-' : '--') . $key . ' ' . ($value ? escapeshellarg($value) : '') . ' ';
            $str .= $param;
        }

        return $str;
    }
}