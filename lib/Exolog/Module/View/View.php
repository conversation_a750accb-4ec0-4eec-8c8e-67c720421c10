<?php


namespace Exolog\Module\View;


use Exolog\Module\Markers\MarkersManager;
use Illuminate\Contracts\View\Engine;
use Illuminate\View\View as IlluminateView;

class View extends IlluminateView
{
    /**
     * @var MarkersManager manager
     */
    private MarkersManager $markersManager;

    /**
     * @var bool If should render shortcodes
     */
    private bool $renderMarkers = true;

    /**
     * Create a new view instance.
     *
     */
    public function __construct(
        Factory        $factory,
        Engine         $engine,
                       $view,
                       $path,
                       $data,
        MarkersManager $shortcodes
    )
    {
        parent::__construct($factory, $engine, $view, $path, $data);

        $this->markersManager = $shortcodes;
    }

    /**
     * Should render shortcodes.
     */
    public function withMarkers()
    {
        $this->renderMarkers = true;

        return $this;
    }

    /**
     * Should not render shortcodes.
     */
    public function withoutMarkers()
    {
        $this->renderMarkers = false;

        return $this;
    }

    /**
     * Render without catching exceptions.
     *
     * @return string
     */
    public function renderSimple()
    {
        return $this->renderContents();
    }

    /**
     * Get the evaluated contents of the view.
     *
     * @return string
     */
    protected function getContents()
    {
        $contents = parent::getContents();

        if ($this->renderMarkers) {
            return (string)$this->markersManager->render($contents);
        }

        return $contents;
    }
}