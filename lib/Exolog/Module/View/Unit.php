<?php


namespace Exolog\Module\View;


use Closure;
use Exolog\Core\Forms\React;
use Exolog\Module\Forms\FQT\Unit\PayloadField;
use Exolog\Module\Forms\FQT\Unit\UnitValueInterface;
use Illuminate\View\Component;

class Unit extends Component
{

    /**
     * @var React|Unit
     */
    public $holder;
    /**
     * @var PayloadField[]
     */
    //public array $payload;

    public UnitValueInterface $unit;
    /**
     * @var array|mixed
     */
    private $params;

    /**
     * Create the component instance.
     *
     * @param $holder React|Unit
     * @param $unit UnitValueInterface
     * @param $params array|string
     */
    public function __construct($holder, UnitValueInterface $unit, $params)
    {
        $this->holder = $holder;
        $this->unit = $unit;
        $this->params = $params;
        //$this->payload = $unit->getPayload();
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|Closure|string
     */
    public function render()
    {
        return view($this->unitViewName());
    }

    protected function unitViewName()
    {
        return 'components.' . $this->componentName;
    }
}