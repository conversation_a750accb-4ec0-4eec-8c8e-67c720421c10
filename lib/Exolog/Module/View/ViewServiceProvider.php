<?php


namespace Exolog\Module\View;

use Exolog\Module\Support\Facades\Path;
use Exolog\Module\View\Compilers\BladeCompiler;
use Illuminate\View\DynamicComponent;
use Illuminate\View\FileViewFinder;
use Illuminate\View\ViewServiceProvider as IlluminateViewServiceProvider;

class ViewServiceProvider extends IlluminateViewServiceProvider
{
    public function registerViewFinder()
    {
        //TODO revert singleton to bind,extend FileViewFinder and add site path on initSite Event
        $this->app->singleton('view.finder', function ($app) {
            return new FileViewFinder($app['files'], $app['config']['view.paths']);
        });
    }

    /**
     * Register the Blade compiler implementation.
     *
     * @return void
     */
    public function registerBladeCompiler()
    {
        $this->app->singleton('blade.compiler', function ($app) {
            return tap(new BladeCompiler($app['files'], $app['config']['view.compiled']), function ($blade) {
                $blade->component('dynamic-component', DynamicComponent::class);
            });
        });
    }

    public function initSite()
    {
        $fined = $this->app['view.finder'];

        $view_paths = $fined->getPaths();
        if ($site_view = realpath(Path::to('site', '/resources/views/'))) {
            array_unshift($view_paths, $site_view);
            $fined->setPaths($view_paths);
        }

        $this->app['config']->set('view.compiled', Path::to('site_cache', '/views'));
        $this->app['blade.compiler']->setCachePath($this->app['config']['view.compiled']);

        //add namespace to site views as well
        //site views do not resolved on "resolve view instance"
        foreach ($fined->getHints() as $namespace => $paths) {
            if (is_dir($appPath = $site_view . '/vendor/' . $namespace)) {
                $fined->prependNamespace($namespace, $appPath);
            }
        }

    }

    protected function createFactory($resolver, $finder, $events)
    {
        return new Factory($resolver, $finder, $events);
    }
}