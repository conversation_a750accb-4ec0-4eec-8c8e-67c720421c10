<?php


namespace Exolog\Module\View;

use Exolog\Module\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

trait XEditableValue
{
    public function getExeditAttr($exedit = []): HtmlString
    {
        if (Auth::isAdmin()) {
            $attr = $this->buildExedit($exedit);
            return new HtmlString(sprintf('exedit="%s"', htmlspecialchars(json_encode($attr), ENT_QUOTES, 'UTF-8')));
        }
        return new HtmlString('');
    }

    abstract protected function buildExedit($exedit = []);
}