<?php

namespace Exolog\Module\Forms\Admin;

use Closure;
use Exolog\Core\Forms\React;
use Exolog\Module\Forms\FormSubmitHandler;
use Illuminate\Support\Arr;

class ReactFormSubmitHandler extends FormSubmitHandler
{
    public function resolveResponseHandler(): ?Closure
    {
        return static function (React $react) {
            return $react;
        };
    }

    public function resolveSaveHandler(): ?Closure
    {
        return static function (React $react) {

            $formconf = request('formconf');
            $options = Arr::only($formconf,
                [
                    'container',
                    'use_as_homepage',
                    'react_form_permalink_id',
                    'react_edition_id',
                ]);

            //do not calc permalink for new react if we are going add it to container
            if ($formconf['container'] && $react->isNew()) {
                $options['skip_permalink'] = true;
            }

            $react->save($options);
            return $react;
        };
    }
}