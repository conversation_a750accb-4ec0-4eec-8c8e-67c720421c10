<?php

namespace Exolog\Module\Forms\Admin;

use Carbon\Carbon;
use Exolog\Core\Forms\React;
use Exolog\Module\Forms\FormsFieldsVisibility;
use Exolog\Module\Forms\Model\Form;
use Exolog\Module\Forms\Model\FormQuestion;
use Exolog\Module\Support\Facades\ExoFile;
use Exolog\Module\Support\Facades\Vars;
use Illuminate\Support\Collection;
use JsonSerializable;

class ReactForm implements JsonSerializable
{
    /**
     * @var React
     */
    private React $react;
    private array $formconf;
    private Form $formModel;
    /**
     * @var \Illuminate\Database\Eloquent\Collection|Collection
     */
    private Collection $fields;

    public function __construct(React $react, $formconf = [])
    {
        $this->react = $react;
        $this->formModel = $this->react->getReactForm();

        $this->formconf = array_merge(
            $formconf,
            [
                'handler' => ReactFormSubmitHandler::class,
                'form_id' => $this->formModel->form_id,
                'react_id' => $this->react->id(),
                'errorBag' => 'form_' . $this->formModel->form_id,

                'use_as_homepage' => Vars::edition('homepage_react_id',
                        $this->react->getReactRelatedEditionId())->getValueAsInt() === $this->react->id(),
                'react_edition_id' => $this->react->getReactRelatedEditionId(),
                'react_form_permalink_id' => $this->react['react_form_permalink_id'],
            ]
        );
        $this->setupFields();
    }

    public function jsonSerialize()
    {
        $data = [];
        $data['fields'] = $this->fields->map(function ($field) {
            if (in_array($field['fqt_name'], ['datetime', 'date']) && $field['value'] instanceof Carbon) {
                $format = ECO()->get()['forms']['fqt'][$field['fqt_name']]['store_format']['php'];
                $field['value'] = $field['value']->format($format);
            }
            return $field;
        })->all();
        //$data['_formconf'] = encrypt($this->formconf);
        $data['formconf'] = $this->formconf;
        $data['react'] = $this->react;
        return $data;
    }

    private function setupFields(): void
    {
        $formData = $this->react->getAireFormData();
        $fields = $this->formModel->form_questions
            ->map(function (FormQuestion $fq) use ($formData) {
                $fq_name = $fq->fq_name;
                $field = [
                    'fq_name' => $fq_name,
                    'fq_id' => $fq->fq_id,
                    'fq_parent_id' => $fq->fq_parent_id,
                    'fq_info' => $fq->fq_info,
                    'fq_isrequired' => $fq->fq_isrequired,

                    'fqt_id' => $fq->type->fqt_id,
                    'fqt_name' => $fq->type->fqt_name,

                    'label' => dv($fq->fq_displayname, $fq->fq_name),
                    'placeholder' => $fq->getCustomParam('placeholder'),

                    'options' => collect($fq->getValues())
                        ->map(function ($value, $key) {
                            return ['value' => $key, 'text' => $value];
                        })
                        ->values()
                        ->all(),
                ];

                if ($fq->type->fqt_name === 'select') {
                    $field['ismultiple'] = $fq->isMultiple();
                }

                $field['value'] = $formData[$fq_name];

                if ($fq->type->fqt_name === 'file') {
                    //todo Use ExoFileService.parseFileId in client
                    $field['file_name'] = $formData[$fq_name] ? ExoFile::basename($formData[$fq_name]) : null;
                }

                /*   if ($fq->type->fqt_name === 'permalinks') {
                       $field['value'] = Permalink::query()->where('p_react_id', $this->react->id())->get();
                   }*/

                return $field;
            });

        $this->fields = $fields;

        $this->appendFieldsVisibility();

        $this->formconf['fields'] = $this->fields->pluck('fq_name');
    }

    private function appendFieldsVisibility(): void
    {

        //Todo we can move this part on client due all configs in form_options
        $container = $this->formconf['container'];
        $is_custom_fields = data_get($this->formModel->form_options, "containers.$container.is_custom_fields", 0);

        $form_questions = collect($this->formModel['form_questions'])->mapWithKeys(function ($item) {
            return [$item['fq_id'] => $item];
        })->all();

        $this->fields->transform(function ($item) use ($container, $is_custom_fields, $form_questions) {

            //set field visibility based on settings
            $fq = $form_questions[$item['fq_id']];

            if (in_array($item['fq_name'], ['menu_title', 'publish'])) {
                $item['isvisible'] = 1;
            } elseif ($is_custom_fields) {
                if ($this->react->isNew()) {
                    $item['isvisible'] = data_get($fq['fq_visibility'], "containers.$container.group_new", 0);
                } else {
                    $item['isvisible'] = data_get($fq['fq_visibility'], "containers.$container.group", 0);
                }
            } elseif ($this->react->isNew()) {
                $item['isvisible'] = data_get($fq['fq_visibility'], 'form_new', 0);
            } else {
                $item['isvisible'] = data_get($fq['fq_visibility'], 'form', 0);
            }

            return $item;
        });

    }
}