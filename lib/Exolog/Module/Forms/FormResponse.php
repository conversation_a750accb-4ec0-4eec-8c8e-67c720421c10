<?php


namespace Exolog\Module\Forms;


use Exolog\Core\Forms\React;
use Exolog\Module\Support\Facades\Vars;

abstract class FormResponse
{
    protected array $releted_reacts;
    protected ResponseRules $responseRules;

    protected function init($form_id, $form_response): void
    {
        $responseContainer = Vars::form('container_' . ($form_response ?? 'response'), $form_id)->getContainer();

        if ($responseContainer === null) {
            return;
        }

        $this->releted_reacts = $responseContainer->getItemsTree();

        $this->responseRules = $responseContainer->responseRules();
    }

    abstract public function perform();

    public function saveToSession(): void
    {
        session()->put(static::class, serialize($this));
    }

    public static function loadFromSession(): ?FormResponse
    {
        $obj = session(static::class);
        if (empty($obj)) {
            return null;
        }
        static::clearSession();
        return unserialize($obj);
    }

    public static function clearSession(): void
    {
        session()->forget(static::class);
    }

    protected function sendMailable(React $mail_react, $react, $rules): void
    {

        $mail_params = [
            'editor_vars' => ['react_id' => $react['react_id']]
        ];

        //TODO throw new \RuntimeException('Not implemented yet!');
        if (!empty($rules['rule_image_linked'])) {
            $mail_params['images'] = 'linked';
        }

        //collect attachments
        $mail_params['attachment'] = [];
        if (!empty($rules['rule_file_attach'])) {
            foreach ($rules['rule_file_attach'] as $field) {
                if (!empty($react[$field])) {
                    $mail_params['attachments'][] = $react[$field];
                }
            }
        }

        if ($rules['rule_send_type'] === 'email') {
            $list = explode(',', $rules['rule_send_email']);
            foreach ($list as $value) {
                $email = trim($value);
                if (empty($email)) {
                    continue;
                }
                $mail_params['toemail'] = $email;
                $mail_react->send($mail_params);
            }
        } elseif ($rules['rule_send_type'] === 'usergroups') {
            $list = explode(',', $rules['rule_send_groups']);
            foreach ($list as $value) {
                $email = trim($value);
                if (empty($email)) {
                    continue;
                }
                $mail_params['toemail'] = $email;
                $mail_react->send($mail_params);
            }
        } else {

            $rule_send_fields = explode(',', $rules['rule_send_fields']);

            foreach ($rule_send_fields as $field) {
                $value = $react[$field];
                $mail_params['toemail'] = $value;
                $mail_react->send($mail_params);
            }
        }


    }

}