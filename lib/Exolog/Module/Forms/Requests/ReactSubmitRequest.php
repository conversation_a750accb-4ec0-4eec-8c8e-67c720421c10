<?php

namespace Exolog\Module\Forms\Requests;

use Exolog\Module\Forms\Model\Form;
use Exolog\Module\Forms\Model\FormQuestion;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationRuleParser;
use RuntimeException;

/**
 * @property array $formconf
 */
class ReactSubmitRequest extends FormRequest
{
    protected ?Form $form;

    public function validateResolved()
    {
        $this->prepareRequsetData();
    }

    public function validateSubmit()
    {
        parent::validateResolved();
    }

    public function validatedFormData()
    {
        return $this->only($this->formconf['fields']);
    }

    public function rules()
    {
        return $this->form->form_questions
            ->filter(function (FormQuestion $fq) {
                return in_array($fq['fq_name'], $this->formconf['fields'], true);
            })
            ->mapWithKeys(function (FormQuestion $fq) {
                $_rules = [];
                if ($fq->fq_isrequired) {
                    $_rules[] = 'required';
                }


                if ($fq->fq_rules) {
                    $_rules = array_merge($_rules, explode('|', $fq->fq_rules));
                }

                $custom_rules = array_merge(
                    config('forms.validation_rules', []),
                    config('site.forms.validation_rules', [])
                );

                //convert custom defined rules
                $rules = [];
                foreach ($_rules as $rule) {
                    [$rule_name] = explode(':', $rule);
                    if ($ruleClass = $custom_rules[$rule_name]) {
                        [$_rule_, $parameters] = ValidationRuleParser::parse($rule);
                        $rule = new $ruleClass(...$parameters);
                    }
                    $rules[] = $rule;
                }
                return [$fq->fq_name => $rules];
            })->toArray();
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes(): array
    {
        return $this->form->form_questions->mapWithKeys(function (FormQuestion $fq) {
            return [$fq->fq_name => dv($fq->fq_displayname, $fq->fq_name)];
        })->toArray();
    }


    //TODO move to Request
    public function getErrorBagName(): string
    {
        return $this->errorBag;
    }

    private function prepareRequsetData()
    {
        if (empty($this->formconf)) {
            throw new RuntimeException('Invalid request! Parameter "formconf" is absent!');
        }
        if ($this->formconf['errorBag']) {
            $this->errorBag = $this->formconf['errorBag'];
        }
        $this->form = Form::find($this->formconf['form_id']);

        if (is_null($this->form)) {
            throw new RuntimeException("Invalid request! Can't find 'form' (form_id= ${$this->formconf['form_id']})");
        }
    }

    public function getForm(): Form
    {
        return $this->form;
    }
}