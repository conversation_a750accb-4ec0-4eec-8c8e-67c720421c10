<?php


namespace Exolog\Module\Forms;


use Exolog\Module\Forms\FQT\Picture\PictureValue;
use Exolog\Module\Forms\FQT\Unit\PayloadFieldPicture;
use Exolog\Module\Forms\FQT\Unit\UnitValueInterface;
use Exolog\Module\Support\Facades\Site;
use Exolog\Module\Support\Facades\Vars;
use Illuminate\Support\Facades\DB;

class PictureLocator
{
    /**
     * collect all containers from:
     * react fields
     * var
     * unit
     * @return PictureValue[]
     */
    public function collect(): array
    {
        return array_merge(
            $this->getFormPictures(),
            $this->getVarPictures(),
            $this->getUnitPictures(),
        );
    }

    private function getFormPictures(): array
    {
        $sql = "SELECT
                  r.react_id,
                  fq.fq_name
                FROM react r
                   JOIN form f ON (r.react_form = f.form_id)  
                   JOIN formquestion fq ON (f.form_id = fq.fq_form)
                   JOIN formquestiontype fqt ON (fq_type = fqt.fqt_id AND fqt.fqt_name = 'picture')
                WHERE
                   r.react_isdeleted = 0 and
                   f.form_site_id=:site_id";
        $records = DB::select($sql, [':site_id' => Site::id()]);

        $pictures = [];
        foreach ($records as $record) {
            $react = react()->findOrFail($record['react_id']);
            $pictures[] =
                [
                    'holder' => $react,
                    'picture' => $react->getFieldValue($record['fq_name'])
                ];
        }

        return $pictures;
    }

    private function getVarPictures(): array
    {
        $vars = Vars::getAll([['var_name', 'LIKE', 'picture\_%']]);
        $pictures = [];
        foreach ($vars as $var) {
            $pictures[] =
                [
                    'holder' => $var,
                    'picture' => $var
                ];
        }
        return $pictures;
    }

    private function getUnitVar(): array
    {
        $vars = Vars::getAll([['var_name', 'LIKE', 'unit\_%']]);
        $pictures = [];
        foreach ($vars as $var) {
            array_push($pictures, ...$this->extractPicturesFromUnit($var));
        }
        return $pictures;
    }

    private function getUnitForm(): array
    {
        $sql = "SELECT
                  r.react_id,
                  fq.fq_name
                FROM react r
                   JOIN form f ON (r.react_form = f.form_id)  
                   JOIN formquestion fq ON (f.form_id = fq.fq_form)
                   JOIN formquestiontype fqt ON (fq_type = fqt.fqt_id AND fqt.fqt_name = 'unit')
                WHERE
                   f.form_site_id = :site_id AND
                   r.react_isdeleted = 0";

        $reacts = DB::select($sql, [':site_id' => Site::id()]);

        $pictures = [];
        foreach ($reacts as $item) {
            $react = react()->findOrFail($item['react_id']);
            $unit = $react->getFieldValue($item['fq_name']);
            array_push($pictures, ...$this->extractPicturesFromUnit($unit));
        }

        return $pictures;
    }

    private function getUnitPictures(): array
    {
        return array_merge($this->getUnitVar(), $this->getUnitForm());
    }

    /**
     * @param UnitValueInterface $unit
     * @return array
     */
    private function extractPicturesFromUnit(UnitValueInterface $unit): array
    {
        $pictures = [];
        foreach ($unit->getPayload() as $item) {
            if ($item instanceof PayloadFieldPicture) {
                $pictures[] = [
                    'holder' => $unit,
                    'picture' => $item,
                ];
            }
        }
        return $pictures;
    }
}