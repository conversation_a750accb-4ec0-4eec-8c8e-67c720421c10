<?php


namespace Exolog\Module\Forms;


use ArrayObject;
use Exolog\Module\Support\Facades\Vars;

class ResponseRules
{
    protected $response_rules;
    protected $form_id;
    protected $form_response;
    protected $var;

    /**
     * ResponseRules constructor.
     * @param $form_id
     * @param string $form_response
     */
    public function __construct($form_id, string $form_response = 'response')
    {
        $this->form_id = $form_id;
        $this->form_response = $form_response;

        $this->response_rules = $this->load();
    }

    private function load()
    {
        $this->var = Vars::form($this->form_response . '_rules', $this->form_id);

        $response_rules = $this->var->getValue();

        if (!empty($response_rules)) {
            $response_rules = json_decode($response_rules, true);
        } else {
            $response_rules = [];
        }

        return $response_rules;
    }

    private function save()
    {
        $this->var->setValue(json_encode($this->response_rules));
        $this->var->save();
    }

    public function getRelatedReactRules($related_react_id, $rules_name)
    {
        //use ArrayObject to get object afte json_encode with empty array
        $rules = new ArrayObject();

        if ($this->response_rules[$related_react_id] && $this->response_rules[$related_react_id][$rules_name]) {
            $rules = $this->response_rules[$related_react_id][$rules_name];
        }

        if (empty($rules)) {
            //fallback for stored rules in related react
            $related_react = react()->find($related_react_id);
            if (!empty($related_react[$rules_name])) {
                $rules = json_decode($related_react[$rules_name], true);
            }
        }

        return $rules;
    }

    public function saveRelatedReactRules($related_react_id, $rules_name, $rules)
    {
        $this->response_rules[$related_react_id][$rules_name] = $rules;
        $this->save();
    }
}