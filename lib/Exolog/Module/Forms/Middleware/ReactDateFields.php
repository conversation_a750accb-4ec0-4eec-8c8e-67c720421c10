<?php

namespace Exolog\Module\Forms\Middleware;

use Closure;
use Exolog\Module\Forms\Model\FormQuestion;
use Exolog\Module\Forms\Requests\ReactSubmitRequest;
use Illuminate\Support\Carbon;

class ReactDateFields implements FormRequestMiddleware
{
    public function handle(ReactSubmitRequest $request, Closure $next)
    {

        $data = $next($request);

        $request->getForm()->form_questions->each(function (FormQuestion $fq) use ($data) {

            if (!empty($data[$fq->fq_name]) && in_array($fq->getTypeName(), ['date', 'datetime'])) {
                $data[$fq->fq_name] = Carbon::parse($data[$data[$fq->fq_name]])->format($fq->getECO('store_format.php'));
            }
        });

        return $data;
    }
}