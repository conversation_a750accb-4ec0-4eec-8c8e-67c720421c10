<?php

namespace Exolog\Module\Forms\Middleware;

use Closure;
use Exolog\Module\Forms\Model\FormQuestion;
use Exolog\Module\Forms\Requests\ReactSubmitRequest;

class CheckboxFields implements FormRequestMiddleware
{
    public function handle(ReactSubmitRequest $request, Closure $next)
    {
        $data = $next($request);

        $request->getForm()->form_questions->each(function (FormQuestion $fq) use ($request, &$data) {
            if (!isset($data[$fq->fq_name])
                && $fq->getTypeName() === 'checkbox'
                && in_array($fq->fq_name, $request->formconf['fields'], true)) {
                $data[$fq->fq_name] = "0";
            }
        });

        return $data;
    }
}