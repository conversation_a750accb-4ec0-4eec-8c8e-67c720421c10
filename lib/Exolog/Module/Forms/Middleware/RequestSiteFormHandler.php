<?php

namespace Exolog\Module\Forms\Middleware;

use Closure;
use Exolog\Module\Forms\FormProcessor;
use Exolog\Module\Forms\Requests\ReactSubmitRequest;

class RequestSite<PERSON><PERSON><PERSON>andler implements FormRequestMiddleware
{

    public function handle(ReactSubmitRequest $request, Closure $next)
    {
        /** @var FormProcessor $formProcessor */
        $formProcessor = app(FormProcessor::class);
        $handler = $formProcessor->getHandler();
        if (is_null($handler)) {
            return $next($request);
        }

        $possible = $handler->beforeValidation($request) ?? $request;
        if ($possible instanceof ReactSubmitRequest) {
            $formData = $next($possible);
        } else {
            return $possible;
        }

        return $handler->afterValidation($formData) ?? $formData;

    }
}