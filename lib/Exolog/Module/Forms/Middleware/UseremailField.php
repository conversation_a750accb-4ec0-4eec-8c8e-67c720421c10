<?php

namespace Exolog\Module\Forms\Middleware;

use Closure;
use Exolog\Core\Forms\React;
use Exolog\Module\Forms\FormProcessor;
use Exolog\Module\Forms\Model\FormQuestion;
use Exolog\Module\Users\Model\User;
use Exolog\Module\Users\Model\Usergroup;
use Illuminate\Support\Str;


class UseremailField implements FormReactMiddleware
{
    public function handle(React $react, Closure $next)
    {

        $fp = $this->getFormProcessor();

        $fp->getRequest()->getForm()->form_questions->each(function (FormQuestion $fq) use ($react) {

            if ($fq->getTypeName() === 'useremail') {

                $email = $react[$fq->fq_name];

                if (empty($email)) {
                    return;
                }

                $user = User::findByEmail($email);
                if (is_null($user)) {
                    //create user
                    $user = User::create([
                        'u_email' => $email,
                        'u_password' => password_hash($this->getUserPass(), PASSWORD_BCRYPT)
                    ]);
                }

                $ug_name = $react->getReactForm()->form_name . '-users';
                $ug = Usergroup::findByName($ug_name);
                if (is_null($ug)) {
                    //create group
                    $ug = Usergroup::create([
                        'ug_name' => $ug_name,
                    ]);
                }

                if (!$user->memberOf($ug)) {
                    $user->addToGroup($ug);
                }

                $react['react_user'] = $user['u_id'];
            }
        });


        /** @var React $react */
        $react = $next($react);

        return $react;
    }

    protected function getFormProcessor(): FormProcessor
    {
        return app(FormProcessor::class);
    }

    private function getUserPass(): string
    {
        return Str::random(15);
    }
}