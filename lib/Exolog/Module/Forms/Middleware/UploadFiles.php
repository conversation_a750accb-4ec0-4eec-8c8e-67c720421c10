<?php

namespace Exolog\Module\Forms\Middleware;

use Closure;
use Exolog\Module\Forms\Requests\ReactSubmitRequest;
use Exolog\Module\Support\Facades\ExoFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class UploadFiles implements FormRequestMiddleware
{

    public function handle(ReactSubmitRequest $request, Closure $next)
    {
        $data = $next($request);

        foreach ($request->getForm()->form_questions as $fq) {

            if ($fq->getTypeName() !== 'file') {
                continue;
            }

            $file = request()->file($fq->fq_name);
            if ($file) {
                $path = $fq->getCustomParam('upload_path', $fq->getECO('upload_path'));

                [$disk, $path] = ExoFile::parseFileId($path);
                $filename = $this->getFileName($file->getClientOriginalName(), $path, $disk);
                $file->storeAs($path, $filename, $disk);
                $data[$fq->fq_name] = ExoFile::composeFileId($disk, $path, $filename);
            } else if (request()->has('__' . $fq->fq_name)) {
                //this allows clear file input by setting __file_input_name to empty string
                if (request('__' . $fq->fq_name) === null) {
                    $data[$fq->fq_name] = null;

                }
            }
        }

        return $data;
    }

    private function getFileName($filename, $path, $disk): string
    {

        $pathinfo = pathinfo($filename);

        $filename = preg_replace('/[^A-Za-z0-9_\-()]/', '_', $pathinfo['filename']);
        $filename .= (!empty($pathinfo['extension']) ? '.' . $pathinfo['extension'] : '');

        if ($filename === '') {
            $filename = Str::random();
        }
        if (Storage::disk($disk)->exists($path . '\\' . $filename)) {
            return $this->getFileName(time() . '_' . $filename, $path, $disk);
        }
        return $filename;
    }
}