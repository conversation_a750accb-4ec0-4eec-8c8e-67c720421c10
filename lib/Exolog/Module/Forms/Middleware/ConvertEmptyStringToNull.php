<?php

namespace Exolog\Module\Forms\Middleware;

use Closure;
use Exolog\Module\Forms\FormProcessor;
use Exolog\Module\Forms\Requests\ReactSubmitRequest;

class ConvertEmptyStringToNull implements FormRequestMiddleware
{
    public function handle(ReactSubmitRequest $request, Closure $next)
    {
        /** @var FormProcessor $fp */
        $fp = app(FormProcessor::class);
        foreach ($fp->getRequest()->formconf['fields'] as $field) {
            if ($request->has($field)) {
                $request[$field] = $request[$field] === '' ? null : $request[$field];
            }
        }

        $data = $next($request);

        return $data;
    }
}