<?php

namespace Exolog\Module\Forms\Middleware;

use Closure;
use Exolog\Core\Forms\React;
use Exolog\Module\Forms\FormProcessor;

class ReactSiteFormHandler implements FormReactMiddleware
{

    public function handle(React $react, Closure $next)
    {
        /** @var FormProcessor $formProcessor */
        $formProcessor = app(FormProcessor::class);
        $handler = $formProcessor->getHandler();
        if (is_null($handler)) {
            return $next($react);
        }
        $possible = $handler->beforeSave($react) ?? $react;

        if ($possible instanceof React) {
            $response = $next($react);
        } else {
            return $possible;
        }

        return $handler->afterResponse($response) ?? $response;
    }
}