<?php

namespace Exolog\Module\Forms\Middleware;

use Closure;
use Exolog\Module\Forms\Model\FormQuestion;
use Exolog\Module\Forms\Requests\ReactSubmitRequest;
use Exolog\Module\Forms\Validations\FormValidationException;
use Exolog\Module\Support\Facades\Auth;
use Exolog\Module\Support\Facades\Vars;
use RuntimeException;

class ReCaptcha implements FormRequestMiddleware
{

    /**
     * @throws FormValidationException
     */
    public function handle(ReactSubmitRequest $request, Closure $next)
    {
        /** @var FormQuestion $fq */
        $fq = $request->getForm()->form_questions->first(function (FormQuestion $fq) {
            return $fq->getTypeName() === 'captcha';
        });

        if ($fq && !Auth::isAdmin()) {

            $secret_key = $this->getkey($fq, 'secret_key');
            $minScore = dv($this->getkey($fq, 'minScore'), 0.3);

            $this->checkCaptcha(
                $request->get('g-recaptcha-response', ''),
                $secret_key,
                $minScore
            );
        }
        return $next($request);
    }

    /**
     * @throws FormValidationException
     */
    protected function checkCaptcha(?string $token, ?string $secret_key, float $minScore = 0.3): void
    {
        if (!empty($token)) {
            if (empty($secret_key)) {
                throw new RuntimeException('Please set a secret key in the custom parameters of the Captcha field or edition var or site var.');
            }
            $reCaptchaUrl = 'https://www.google.com/recaptcha/api/siteverify?secret=' . $secret_key . '&response=' . urlencode($token);
            $body = file_get_contents($reCaptchaUrl);
            $response = json_decode($body, true);
            if ($response['success']) {
                if ($response['action'] !== 'submit') {
                    $this->failedValidation();
                }
                if ($response['score'] <= $minScore) {
                    $this->failedValidation();
                }
            } else {
                $this->failedValidation();
            }
        } else {
            $this->failedValidation();
        }
    }

    /**
     * @throws FormValidationException
     */
    protected function failedValidation(): void
    {
        throw FormValidationException::withMessage(trans('ff.common.captcha'));
    }

    private function getKey(FormQuestion $fq, string $key)
    {
        $value = $fq->getCustomParam($key);

        if (empty($value)) {
            $value = Vars::edition("captcha_$key")->getData();
        }
        if (empty($value)) {
            $value = Vars::site("captcha_$key")->getData();
        }
        return $value;
    }
}
