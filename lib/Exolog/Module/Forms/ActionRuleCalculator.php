<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 14.07.2020
 * Time: 13:05
 */

namespace Exolog\Module\Forms;


use Exolog\Module\Reacts\ReactsCompare;
use Exolog\Module\Support\Concerns\HasMakeFactory;
use InvalidArgumentException;

class ActionRuleCalculator
{
    use HasMakeFactory;

    public static string $OR = 'any';
    public static string $AND = 'all';

    public function calc($rules, $react, $old_react, $newisdeleted = false): bool
    {
        if (!is_array($rules['rule_if']) || count($rules['rule_if']) === 0) {
            throw new InvalidArgumentException('Rules is empty!');
        }
        $rule_operator = empty($rules['rule_operator']) ? self::$OR : $rules['rule_operator'];
        //default for OR - false, for AND - true
        $truth = $rule_operator === self::$AND;
        foreach ($rules['rule_if'] as $rule) {
            if (!empty($rule['rule_if'])) {
                $exp = $this->calc($rule, $react, $old_react, $newisdeleted);
            } else {
                switch ($rule['exp']) {
                    case '=':
                        $exp = $react[$rule['field_name']] == $rule['field_value'];
                        break;
                    case '!=':
                        $exp = $react[$rule['field_name']] != $rule['field_value'];
                        break;
                    case '>':
                        $exp = $react[$rule['field_name']] > $rule['field_value'];
                        break;
                    case '>=':
                        $exp = $react[$rule['field_name']] >= $rule['field_value'];
                        break;
                    case '<':
                        $exp = $react[$rule['field_name']] < $rule['field_value'];
                        break;
                    case '<=':
                        $exp = $react[$rule['field_name']] <= $rule['field_value'];
                        break;
                    case 'empty':
                        $exp = empty($react[$rule['field_name']]);
                        break;
                    case '!empty':
                        $exp = !empty($react[$rule['field_name']]);
                        break;
                    case 'changed':
                        if ($rule['field_name'] === '__react') {
                            $exp = !ReactsCompare::compare($react, $old_react);
                        } else {
                            $exp = !empty($old_react) && ($react[$rule['field_name']] != $old_react[$rule['field_name']]);
                        }
                        break;
                    case 'deleted':
                        $exp = $react['react_isdeleted'] == 1 || !empty($newisdeleted);
                        break;
                    default:
                        throw new InvalidArgumentException("Unsupported \"${rule['exp']}\" operator!");
                }
            }

            $truth = $this->computeOperator($rule_operator, $truth, $exp);
        }

        return $truth;
    }

    private function computeOperator($rule_operator, $truth, $expression): bool
    {
        return $rule_operator === self::$AND ? $truth && $expression : $truth || $expression;
    }
}