<?php

namespace Exolog\Module\Forms;

abstract class FormPipeline
{
    protected function loadMiddleware($phase, $form_name): array
    {
        return array_merge(
            (array)config("forms.middleware.$phase._default", []),
            config("forms.middleware.$phase." . $form_name, []),
            config("site.forms.middleware.$phase._default", []),
            config("site.forms.middleware.$phase." . $form_name, []),
        );
    }
}