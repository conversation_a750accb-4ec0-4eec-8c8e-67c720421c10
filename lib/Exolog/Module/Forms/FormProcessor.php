<?php

namespace Exolog\Module\Forms;

use Exolog\Core\Forms\React;
use Exolog\Module\Forms\Requests\ReactSubmitRequest;
use Exolog\Module\Support\Concerns\HasMakeFactory;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Str;

class FormProcessor
{
    use HasMakeFactory;

    protected ReactSubmitRequest $request;
    protected ?FormSubmitHandler $handler;

    public function __construct()
    {
        $this->request = $this->resolveRequest();
        $this->handler = $this->resolveFormSubmitHandler();
    }

    public function runRequest(ReactSubmitRequest $request)
    {
        return (new RequestPipeline())->run($request);
    }

    public function runReact(React $react)
    {
        return (new ReactPipeline())->run($react);
    }

    public function run()
    {
        $validatedData = $this->runRequest($this->request);
        if (!is_array($validatedData)) {
            return $validatedData;
        }
        $react = $this->resolveReact($validatedData);
        return $this->runReact($react);
    }

    public function resolveReact($data): React
    {
        $request = $this->request;

        if ($request->formconf['react_id'] && is_numeric($request->formconf['react_id'])) {
            $react = React::find($request->formconf['react_id']);
            if (is_null($react)) {
                throw new ModelNotFoundException('React from request not found');
            }
        } else {
            $react = new React($request->formconf['form_id']);
        }
        $react->fill($data);

        return $react;

    }

    protected function resolveRequest(): ReactSubmitRequest
    {
        return app(data_get(request('formconf'), 'formRequest', ReactSubmitRequest::class));
    }

    public function getHandler(): ?FormSubmitHandler
    {
        return $this->handler;
    }

    protected function resolveFormSubmitHandler(): ?FormSubmitHandler
    {
        $formSubmitHandler = data_get(request('formconf'), 'handler');
        if (is_null($formSubmitHandler)) {
            $className = 'Exolog\Site\Forms\\' . Str::studly($this->getRequest()->getForm()->form_name);
            if (class_exists($className)) {
                $formSubmitHandler = $className;
            }
        }
        return $formSubmitHandler !== null ? app($formSubmitHandler) : null;
    }

    public function getRequest(): ReactSubmitRequest
    {
        return $this->request;
    }
}