<?php

namespace Exolog\Module\Forms;

use Exolog\Module\Forms\Requests\ReactSubmitRequest;
use Illuminate\Pipeline\Pipeline;

class RequestPipeline extends FormPipeline
{

    public function run(ReactSubmitRequest $request)
    {
        return app(Pipeline::class)
            ->send($request)
            ->through($this->loadMiddleware('request', $request->getForm()->form_name))
            ->then(function ($carry) {
                return $this->handleCarry($carry);
            });
    }

    protected function handleCarry($carry)
    {

        if ($carry instanceof ReactSubmitRequest) {
            $carry->validateSubmit();
            $carry = $carry->validatedFormData();
        }

        return $carry;
    }
}