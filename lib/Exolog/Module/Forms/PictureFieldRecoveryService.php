<?php

namespace Exolog\Module\Forms;

use Exolog\Core\Forms\React;
use Exolog\Module\Forms\FQT\Picture\Picture;
use Exolog\Module\Forms\FQT\Picture\PictureValue;
use Exolog\Module\Forms\FQT\Unit\Unit;
use Exolog\Module\Forms\Model\FormQuestion;
use Exolog\Module\Log\Concerns\HasBufferLogger;
use Exolog\Module\Support\Concerns\HasMakeFactory;
use Exolog\Module\Support\Facades\ExoFile;
use Exolog\Module\Support\FileFinder;
use Exolog\Module\Vars\Variable;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class PictureFieldRecoveryService
{
    use HasBufferLogger, HasMakeFactory;

    protected FileFinder $finder;

    public function __construct(FileFinder $finder)
    {
        $this->finder = $finder;
    }

    public function updateReactFieldsHash($react)
    {
        return $react->getReactForm()->form_questions->reduce(function ($result, FormQuestion $fq) use ($react) {
            if ($fq->getTypeName() === 'picture' || $fq->getTypeName() === 'unit') {
                /** @var Picture|Unit $field */
                $field = $react->getFieldValue($fq->fq_name);
                $updated = $field->updateHash();
                $result = $updated || $result;
            }
            return $result;
        }, false);
    }


    private function recoveryPicturesFields(array $pictures): void
    {
        $logger = $this->resolveLogger();

        foreach ($pictures as $item) {
            /** @var PictureValue $picture */
            ['holder' => $holder, 'picture' => $picture] = $item;
            $fileId = $picture->getFileId();
            if ($fileId) {
                [$disk, $path] = ExoFile::parseFileId($fileId);
                if ($disk === 'DB') {
                    continue;
                }
                if (!Storage::disk($disk)->exists($path)) {
                    $hash = $picture->getHash();
                    if (!empty($hash)) {
                        $newFileId = $this->finder->findByHash(['media'], $hash);
                        if ($newFileId) {
                            $picture->setImage($newFileId);
                            $holder->save();
                            $logger->info(Str::format("Image path updated from \"$fileId\"  to \"{$newFileId}\". Holder:{holder}",
                                ['holder' => $this->outputHolder($holder)]
                            ));
                        } else {
                            $logger->warning(Str::format("Picture not found by hash \"$hash\". Holder:{holder}",
                                ['holder' => $this->outputHolder($holder)]
                            ));
                        }
                    } else {
                        $logger->warning(Str::format("Can't recovery picture \"$fileId\", hash is not exists! Holder:{holder}",
                            ['holder' => $this->outputHolder($holder)]
                        ));
                    }
                } else {
                    if ($picture->updateHash()) {
                        $logger->info(Str::format("Hash updated for picture \"$fileId\", Holder:{holder}",
                            ['holder' => $this->outputHolder($holder)]
                        ));
                        $holder->save();
                    }
                }
            }
        }
    }

    /**
     * @param PictureValue $picture
     * @return void
     */
    public function recoveryPictureValue($picture): void
    {
        $this->recoveryPicturesFields([$picture]);
    }

    public function recovery(): void
    {
        $pictures = (new PictureLocator())->collect();
        $this->recoveryPicturesFields($pictures);
    }

    private function outputHolder($holder): string
    {
        if ($holder instanceof React) {
            return 'react_id:' . $holder->id();
        }
        if ($holder instanceof Variable) {
            return 'variable:' . $holder->getId();
        }
        if ($holder instanceof Unit) {
            return $holder->getHolderType() . ':' . $holder->getHolderId();
        }
        return 'Unknown holder type.';
    }
}

