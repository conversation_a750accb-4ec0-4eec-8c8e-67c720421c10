<?php

namespace Exolog\Module\Forms\Observer;

use Exolog\Module\Forms\Model\FormQuestion;
use Exolog\Module\Permalinks\Model\Permalink;
use Illuminate\Support\Facades\DB;

class FormQuestionObserver
{
    public function updating(FormQuestion $formQuestion)
    {
        $this->syncWithReacts($formQuestion);
    }

    public function deleted(FormQuestion $formQuestion)
    {
        $this->deleteInPermalinks($formQuestion);
        $this->deleteInReacts($formQuestion);
    }

    private function syncWithReacts(FormQuestion $formQuestion): void
    {
        //update json
        if ($formQuestion->fq_name !== $formQuestion->getOriginal('fq_name')) {

            $jsonFieldOld = "'$." . '"' . $formQuestion->getOriginal('fq_name') . '"' . "'";
            $jsonFieldNew = "'$." . '"' . $formQuestion->fq_name . '"' . "'";

            DB::table('react')
                ->where('react_form', $formQuestion->fq_form)
                ->whereRaw("JSON_EXTRACT(react_jdoc, $jsonFieldOld) IS NOT NULL")
                ->update([
                    'react_jdoc' => DB::raw("JSON_REMOVE(JSON_SET(react_jdoc, $jsonFieldNew, JSON_EXTRACT(react_jdoc, $jsonFieldOld)), $jsonFieldOld)"),
                ]);
        }
    }

    private function deleteInReacts(FormQuestion $formQuestion): void
    {

        $jsonRemoveQuery = "'$." . '"' . $formQuestion->fq_name . '"' . "'";

        DB::table('react')
            ->where('react_form', $formQuestion->fq_form)
            ->update([
                'react_jdoc' => DB::raw("JSON_REMOVE(react_jdoc, $jsonRemoveQuery)"),
            ]);
    }

    private function deleteInPermalinks(FormQuestion $formQuestion): void
    {
        if ($formQuestion->fq_type !== ECO('forms.fqt.permalink.fqt_id')) {
            return;
        }
        $ids = react()->form($formQuestion->fq_form)->get()->pluck('react_id');
        Permalink::query()->whereIn('p_react_id', $ids)->get()->each(fn(Permalink $permalink) => $permalink->delete());
    }
}
