<?php

namespace Exolog\Module\Forms\Model\Traits;

use Exolog\Module\Forms\Model\Form;
use Exolog\Module\Forms\Model\FormQuestion;
use Exolog\Module\Support\Facades\Site;
use Exolog\Module\Support\Facades\Validator;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Unique;

trait HasFormSaveFrom
{

    /**
     *
     * Save form and form fields with settings
     *
     */
    public static function saveFromArray(array $data): Form
    {
        $formValue = Validator::validateWithAttributes($data,
            [
                'form_id' => 'nullable|numeric',
                'form_name' => [
                    tap(Rule::unique(Form::class), static function (Unique $rule) use ($data) {
                        if (is_numeric($data['form_id'])) {
                            $rule->ignore($data['form_id'], 'form_id');
                        }
                        $rule->where('form_site_id', Site::id());
                    })
                ],
                'form_info' => 'nullable',
                'form_mailsubject' => 'required',
                'form_permalink_id' => 'nullable|numeric',
                'form_options' => 'nullable|array',
                //TODO make validation recursively
                'form_questions_tree' => 'nullable|array',
                'form_questions_tree.*.fq_name' => 'distinct|required',
                'form_questions_tree.*.values.*.fqv_value' => 'distinct|required',
            ]);

        $form = Form::updateOrCreate([
            'form_id' => $formValue['form_id']
        ], $formValue);

        if (array_key_exists('form_questions_tree', $formValue)) {

            $ids = array_recursive_collect_key($formValue['form_questions_tree'], 'fq_id', 'form_questions_tree');

            $form->form_questions->each(function (FormQuestion $fq) use ($ids) {
                if (!in_array($fq->fq_id, $ids, true)) {
                    $fq->delete();
                }
            });

            static::saveFormQuestions($formValue['form_questions_tree'], $form);
        }

        return $form->refresh();
    }

    private static function saveFormQuestions(
        $form_questions_values,
        Form $form,
        FormQuestion $parent = null,
        int &$order = 1
    ): void
    {
        foreach ($form_questions_values as $question) {

            $question['fq_order'] = $order;
            $order++;

            if ($parent) {
                $question['fq_parent_id'] = $parent->fq_id;
                $question['fq_form'] = $parent->fq_form;
            } else {
                $question['fq_parent_id'] = null;
            }
            /** @var FormQuestion $form_question */
            $form_question = $form->form_questions()->updateOrCreate(
                ['fq_id' => $question['fq_id']], $question
            );

            if (array_key_exists('values', $question)) {
                $ids = Arr::pluck($question['values'], 'fqv_id');
                $form_question->values()->whereNotIn('fqv_id', $ids)->delete();

                foreach ($question['values'] as $value) {
                    $form_question->values()->updateOrCreate(['fqv_id' => $value['fqv_id']], $value);
                }
            }

            if (array_key_exists('form_questions_tree', $question)) {
                static::saveFormQuestions(
                    $question['form_questions_tree'],
                    $form,
                    $form_question,
                    $order
                );
            }
        }
    }
}