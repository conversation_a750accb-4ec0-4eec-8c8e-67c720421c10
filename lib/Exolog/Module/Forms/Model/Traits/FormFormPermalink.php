<?php

namespace Exolog\Module\Forms\Model\Traits;

use Exolog\Module\Forms\Model\FormPermalink;
use Illuminate\Support\Arr;
use InvalidArgumentException;

trait FormFormPermalink
{
    public function getFormPermalinks(): array
    {
        $this->load('form_permalinks');
        return $this->form_permalinks->sortBy('fp_id')->values()->all();
    }

    public function saveFormPermalinks(array $form_permalinks, $update_permalinks = false): void
    {
        $this->load('form_permalinks');
        foreach ($form_permalinks as $form_permalink) {
            if ($form_permalink['$_deleted']) {
                if ($form_permalink['fp_id']) {
                    $this->form_permalinks->find($form_permalink['fp_id'])->delete();
                }
            } else {
                if (empty($form_permalink['fp_uri'])) {
                    throw new InvalidArgumentException('Permalink value required!');
                }
                if (!empty($form_permalink['fp_sitemap_active']) && empty($form_permalink['fp_sitemap_name'])) {
                    throw new InvalidArgumentException('Sitemap index name value required!');
                }
                $form_permalink['fp_id'] = $form_permalink['fp_id'] ?? null;
                $this->form_permalinks()
                    ->updateOrCreate(
                        Arr::only($form_permalink, 'fp_id'),
                        Arr::except($form_permalink, 'fp_id')
                    );

                if ($this->form_permalink && $update_permalinks) {
                    /** @var FormPermalink $fp */
                    $fp = $this->form_permalink->find($form_permalink['fp_id']);
                    $fp->updatePermalinks();
                }
            }
        }
    }
}