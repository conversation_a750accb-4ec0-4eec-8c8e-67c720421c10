<?php


namespace Exolog\Module\Forms\Model;


use Exolog\Module\Database\Concerns\HasSiteScope;
use Exolog\Module\Forms\Observer\FormQuestionObserver;
use Exolog\Module\Support\AttributesParser;
use Exolog\Module\Support\Facades\Markers;
use Exolog\Module\Support\PHPDoc\PHPDoc_Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Arr;

/**
 * @property int $fq_id
 * @property int $fq_form
 * @property string fq_name
 * @property string fq_displayname
 * @property ?string fq_info
 * @property string fq_isrequired
 * @property int fq_order
 * @property int fq_type
 * @property string fq_custom_params
 * @property string fq_prefix
 * @property string fq_postfix
 * @property string fq_rules
 * @property Collection<FormQuestionValue> $values
 * @property FormQuestionType $type
 * @property int $fq_parent_id
 *
 * @mixin PHPDoc_Builder
 *
 */
class FormQuestion extends Model
{
    use HasSiteScope;

    public const SITE_FIELD = 'fq_site';

    protected $table = 'formquestion';
    protected $primaryKey = 'fq_id';
    protected $fillable = [
        'fq_name',
        'fq_form',
        'fq_displayname',
        'fq_rules',
        'fq_info',
        'fq_isrequired',
        'fq_order',
        'fq_type',
        'fq_custom_params',
        'fq_prefix',
        'fq_postfix',
        'fq_parent_id',
        'fq_visibility'
    ];

    protected $casts = [
        'fq_parent_id' => 'int',
        'fq_order' => 'int',
        'fq_visibility' => 'json',
        'fq_type' => 'int'
    ];

    protected $with = ['type', 'values'];

    public $timestamps = false;


    public function type(): BelongsTo
    {
        return $this->belongsTo(FormQuestionType::class, 'fq_type', 'fqt_id');
    }

    public function form(): BelongsTo
    {
        return $this->belongsTo(Form::class, 'fq_form', 'form_id');
    }

    public function values(): HasMany
    {
        return $this->hasMany(FormQuestionValue::class, 'fqv_question', 'fq_id');
    }

    public function form_questions_tree(): HasMany
    {
        return $this->hasMany(static::class, 'fq_parent_id', 'fq_id');
    }

    /**
     * @param $path
     * @param $default
     * @return array|mixed
     */
    public function getECO($path = null, $default = null)
    {
        $eco = Arr::first(ECO('forms.fqt'), function ($item) {
            return $item['fqt_id'] == $this->fq_type;
        });
        if ($path) {
            return data_get($eco, $path, $default);
        }
        return $eco;
    }

    /**
     * @return array|string
     */
    public function getCustomParams()
    {
        return AttributesParser::parse($this->fq_custom_params);
    }

    public function getCustomParam($name, $default = null)
    {
        $name = strtolower($name);
        $cp = $this->getCustomParams();
        if (is_array($cp) && array_key_exists($name, $cp)) {
            return $cp[$name];
        }
        return $default;
    }

    public function getDefaultValue()
    {

        if ($default = data_get($this->getCustomParams(), 'default')) {
            $default = Markers::resolveIfMarker($default);
        }

        //Get default value from form question values for radio and select inputs
        if (is_null($default) && in_array($this->getTypeName(), ['select', 'radio'])) {
            $default = $this->values()->where('fqv_isdefault', '1')->limit(1)->value('fqv_value');
        }

        return $default;
    }

    public function getTypeName()
    {
        return $this->getECO('fqt_name');
    }

    public function getValues()
    {
        $this->load('values');
        return $this->values->reduce(static function ($carry, FormQuestionValue $fqv) {
            if (Markers::isMarker($fqv->fqv_value)) {
                $carry += dv(Markers::resolve($fqv->fqv_value), []);
            } else {
                $carry[$fqv->fqv_value] = dv($fqv->fqv_displayvalue, $fqv->fqv_value);
            }
            return $carry;
        }, []);
    }

    public function isMultiple(): bool
    {
        return $this->getCustomParam('isMultipleSelect', false);
    }

    protected static function booted()
    {
        static::observe(FormQuestionObserver::class);
    }

    public function getFqVisibilityAttribute($value)
    {
        if ($value === null) {
            return [
                'form' => 0,
                'form_new' => 0,
                'containers' => null
            ];
        }
        return json_decode($value, true, 512, JSON_THROW_ON_ERROR);
    }
}