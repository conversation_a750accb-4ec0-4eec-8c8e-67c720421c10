<?php


namespace Exolog\Module\Forms\Model;


use Exolog\Core\Forms\React;
use Exolog\Module\Container\ContainerService;
use Exolog\Module\Permalinks\PermalinksService;
use Exolog\Module\Support\PHPDoc\PHPDoc_Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use RuntimeException;

/**
 * @property string $fp_uri
 * @property integer $fp_id
 * @property integer $fp_form_id
 * @property string $fp_sitemap_name
 * @property boolean $fp_sitemap_active
 * @property boolean $fp_history_active
 *
 * @mixin PHPDoc_Builder
 *
 */
class FormPermalink extends Model
{
    protected $table = 'form_permalink';
    protected $primaryKey = 'fp_id';
    protected $fillable = [
        'fp_uri',
        'fp_sitemap_active',
        'fp_history_active',
        'fp_sitemap_name',
        'fp_react_layout'
    ];

    protected $casts = [
        'fp_sitemap_active' => 'boolean',
        'fp_history_active' => 'boolean',
        'fp_react_layout' => 'json',
    ];

    protected $appends = ['sitemap_index_url'];

    public $timestamps = false;

    public function form(): BelongsTo
    {
        return $this->belongsTo(Form::class, 'fp_form_id', 'form_id');
    }

    public function getSitemapIndexUrlAttribute()
    {
        if ($this->fp_sitemap_active && !empty($this->fp_sitemap_name)) {
            return "/{$this->fp_sitemap_name}-sitemap.xml";
        }
        return null;
    }


    protected static function booted()
    {
        static::creating(function (FormPermalink $fp) {
            if (Str::startsWith($fp->fp_uri, '/mail/')) {
                throw new RuntimeException('System uses this route!');
            }
        });

        static::deleting(function (FormPermalink $fp) {
            $containers = ContainerService::collcetContainers();

            //check on containers
            foreach ($containers as $container) {
                collect($container->getConfig()['forms'])->each(function ($value) use ($container, $fp) {
                    if ($value['form_id'] == $fp['fp_form_id'] && $value['form_permalink_id'] == $fp['fp_id']) {
                        throw new RuntimeException("Form permalink [{$fp['fp_id']}] is used by container. container_id = " . $container->getContainerId());
                    }
                });
            }

            if (Form::query()->where('form_permalink_id', $fp['fp_id'])->count()) {
                throw new RuntimeException("Form permalink [{$fp['fp_id']}] is used by form.");
            }

            if (React::query()->where('react_form_permalink_id', $fp['fp_id'])->count()) {
                throw new RuntimeException("Form permalink [{$fp['fp_id']}] is used by react.");
            }

        });
    }

    public function updatePermalinks(): Collection
    {
        return react()->where('react_form', $this->fp_form_id)
            ->get()
            ->filter(function (React $react) {
                $fp = $react->getPrimaryPermalink()->form_permalink;
                if ($fp && $fp->fp_id === $this->fp_id) {
                    PermalinksService::make()->updateReactPermalinks($react);
                }
                return false;
            });
    }
    /*public function setFpUriAttribute($value)
    {
        if (!Str::startsWith($value, ['https://', 'mailto:'])) {
            $value = Str::start($value, '/');
        }
        $this->attributes['fp_uri'] = $value;
    }*/
}