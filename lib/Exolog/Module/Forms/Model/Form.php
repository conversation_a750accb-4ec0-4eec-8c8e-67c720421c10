<?php


namespace Exolog\Module\Forms\Model;


use Exolog\Module\Database\Concerns\HasSiteScope;
use Exolog\Module\Forms\Model\Traits\FormFormPermalink;
use Exolog\Module\Forms\Model\Traits\HasFormSaveFrom;
use Exolog\Module\Support\PHPDoc\PHPDoc_Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property string form_id
 * @property string form_name
 * @property array form_options
 * @property Collection<FormQuestion> form_questions
 * @property Collection<FormQuestion> form_questions_tree
 *
 * @property Collection<FormPermalink> $form_permalink
 * @property Collection<FormPermalink> $form_permalinks
 * @property int $form_permalink_id
 * @property string $form_mailsubject
 *
 * @mixin PHPDoc_Builder
 */
class Form extends Model
{
    use HasSiteScope;
    use FormFormPermalink;
    use HasFormSaveFrom;

    public const SITE_FIELD = 'form_site_id';

    protected $table = 'form';
    protected $primaryKey = 'form_id';
    public $timestamps = false;
    protected $with = ['form_questions'];

    protected $fillable = [
        'form_name',
        'form_info',
        'form_mailsubject',
        'form_permalink_id',
        'form_options'
    ];

    protected $casts = [
        'form_options' => 'json'
    ];

    public static function findByName(string $form_name): ?Form
    {
        /** @var Form */
        return static::query()->where('form_name', $form_name)->first();
    }

    /**
     * All available form_permalink for this form
     */
    public function form_permalinks(): HasMany
    {
        return $this->hasMany(FormPermalink::class, 'fp_form_id');
    }

    /* public function groups(): HasMany
     {
         return $this->hasMany(Group::class, 'group_parent')->where('group_type','form');
     }*/

    public function form_questions(): HasMany
    {
        return $this->hasMany(FormQuestion::class, 'fq_form')->orderBy('fq_order');
    }

    public function form_questions_tree(): HasMany
    {
        return $this->hasMany(FormQuestion::class, 'fq_form')->whereNull('fq_parent_id')->orderBy('fq_order');
    }

    /**
     * Default form_permalink for reacts of this form
     */
    public function form_permalink(): HasOne
    {
        return $this->hasOne(FormPermalink::class, 'fp_id', 'form_permalink_id');
    }

    public function getFQ(string $fq_name): ?FormQuestion
    {
        return $this->form_questions->first(function (FormQuestion $fq) use ($fq_name) {
            return $fq->fq_name === $fq_name;
        });
    }
}