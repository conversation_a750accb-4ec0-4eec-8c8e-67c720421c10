<?php


namespace Exolog\Module\Forms\Model;


use Illuminate\Database\Eloquent\Model;

/**
 * @property mixed $fqt_id
 * @property string fqt_name
 * @property string fqt_displayname
 * @property string fqt_info
 */
class FormQuestionType extends Model
{

    protected $table = 'formquestiontype';
    protected $primaryKey = 'fqt_id';
    protected $fillable = [
        'fqt_name',
        'fqt_displayname',
        'fqt_info',
    ];

    protected $casts = [];

    protected $appends = [];

    public $timestamps = false;

    protected static function booted()
    {

    }

    public function getECO()
    {
        return ECO()->get()['forms']['fqt'][$this->fqt_name];
    }
}