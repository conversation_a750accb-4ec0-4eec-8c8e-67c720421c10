<?php


namespace Exolog\Module\Forms\Model;


use Exolog\Module\Database\Concerns\HasSiteScope;
use Illuminate\Database\Eloquent\Model;
use RuntimeException;

/**
 * @property string fqv_question
 * @property string fqv_value
 * @property string fqv_displayvalue
 * @property string fqv_isdefault
 * @property string fqv_custom_params
 *
 */
class FormQuestionValue extends Model
{

    use HasSiteScope;

    public const SITE_FIELD = 'fqv_site';

    protected $table = 'formquestionvalue';
    protected $primaryKey = 'fqv_id';
    protected $fillable = [
        'fqv_value',
        'fqv_displayvalue',
        'fqv_isdefault',
        'fqv_custom_params',
    ];

    protected $casts = [];

    protected $appends = [];

    protected $with = [];

    public $timestamps = false;

    protected static function booted()
    {
    }

    public function getDefaultValue()
    {
        throw new RuntimeException('Not implemented yet!');
    }

    public function getTypeName()
    {
        return $this->getECO('fqt_name');
    }
}