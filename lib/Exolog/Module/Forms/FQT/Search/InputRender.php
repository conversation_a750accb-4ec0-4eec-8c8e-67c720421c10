<?php


namespace Exolog\Module\Forms\FQT\Search;


use Exolog\Module\Contracts\XEditable;
use Illuminate\Contracts\Support\Htmlable;

class InputRender implements Htmlable
{
    private array $params;
    private XEditable $search;

    /**
     * InputRender constructor.
     * @param array $params
     * @param XEditable $search
     */
    public function __construct(array $params, XEditable $search)
    {
        $this->params = $params;
        $this->search = $search;
    }

    public function toHtml()
    {
        return view($this->params['view'], [
                'search' => $this->search,
                'params' => $this->params
            ]
        );
    }
}