<?php


namespace Exolog\Module\Forms\FQT\Search;

use Exolog\Core\Forms\React;
use Exolog\Module\Contracts\XEditable;
use Exolog\Module\Support\Facades\Auth;
use Exolog\Module\Support\ValueAccessor;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use JsonException;

trait SearchValue
{
    use ValueAccessor;

    /**
     * @param array $params Array containing the necessary params.
     *    $params = [
     *      'query' => (string) Query(search) string.
     *      'page' => (int) Pagination. page numper. Default: 1.
     *      'keyPage' => (string) Default: 'page'
     *      'paramQuery' => (string) Default: 'q'
     *      'limit' => (int) Reacts per page. Default: 100.
     *    ]
     * @return array []
     */
    public function execute(array $params = []): array
    {
        $params = $this->prepareParams($params);
        $params['form_questions'] = $this->getFQ();

        $result = [
            'rows' => [],
            'info' => [
                'total' => 0,
                'page' => $params['page'],
                'limit' => $params['limit']
            ]
        ];

        if (empty($params['query'])) {
            return $result;
        }

        $reacts = react()
            ->search($params)
            ->forPage($params['page'], $params['limit'])
            ->get();

        foreach ($reacts as $key => $react) {
            /** @var React $react */
            $result['rows'][$key] = [
                'react' => $react,
                'page' => null
            ];
            if (!$react->hasField('permalink')) {
                $parentContainers = $react->parentContainers();
                if (!empty($parentContainers[0]) && $parentContainers[0]->getHolderType() === 'react') {
                    $result['rows'][$key]['page'] = $parentContainers[0]->getHolder();
                }
            } else {
                $result['rows'][$key]['page'] = $react;
            }
        }
        $result['info']['totalPage'] = max(1, (int)ceil($result['info']['total'] / $result['info']['limit']));
        $result['info']['isLastPage'] = $result['info']['page'] >= $result['info']['totalPage'];
        $result['info']['isFirstPage'] = $result['info']['page'] <= 1;
        $result['info']['nextPageUrl'] = request()->fullUrlWithQuery([$params['keyPage'] => $result['info']['page'] + 1]);
        $result['info']['prevPageUrl'] = request()->fullUrlWithQuery([$params['keyPage'] => $result['info']['page'] - 1]);

        return $result;
    }

    private function prepareParams($params)
    {
        //TODO move to defaultRenderParams called from __construct
        $params = array_merge([
            'view' => 'system.search.results',
            'limit' => 50,
            'keyQuery' => 'q',
            'keyPage' => 'page'
        ],
            $params);
        $params['query'] = $params['query'] ?? request()->query($params['keyQuery']);
        $params['page'] = $params['page'] ?? request()->query($params['keyPage']) ?? 1;

        $params['query'] = filter_var($params['query'], FILTER_SANITIZE_SPECIAL_CHARS);
        $params['page'] = filter_var($params['page'], FILTER_VALIDATE_INT);
        if (!$params['page']) {
            $params['page'] = 1;
        }
        return $params;
    }

    private function getFQ(): array
    {
        $forms = $this->getData()['forms'];
        $form_questions = [];
        foreach ($forms as $form) {
            $form_questions = array_merge($form_questions, $form['form_questions']);
        }
        $form_questions = DB::table('formquestion')->whereIn('fq_id', $form_questions)->get()->toArray();
        return $form_questions;
    }

    public function __toString()
    {
        return (string)$this->getData();
    }

    public function input($params = [])
    {
        $params = array_merge([
            'view' => 'system.search.input',
            'permalink' => request()->path()
        ], $params);
        $params = $this->prepareParams($params);
        /** @var XEditable $this */
        return new InputRender($params, $this);
    }

    /**
     * @throws JsonException
     */
    public function getExeditAttr($exedit = []): HtmlString
    {
        if (Auth::isAdmin()) {
            $exedit['keyQuery'] = $this->renderParams['keyQuery'];
            $attr = json_encode($this->buildExedit($exedit), JSON_THROW_ON_ERROR);
            return new HtmlString(sprintf('exedit-search="%s"', htmlspecialchars($attr)));
        }
        return new HtmlString();
    }

    protected function getRenderData($params = [])
    {
        $results = $this->execute($params);

        return view($params['view'],
            [
                'search' => $this,
                'rows' => $results['rows'],
                'info' => $results['info'],
                'params' => $params
            ]);
    }

    public function with($renderParams)
    {
        $renderParams = $this->prepareParams($renderParams);
        parent::with($renderParams);
        return $this;
    }
}