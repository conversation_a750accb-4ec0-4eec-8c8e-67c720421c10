<?php


namespace Exolog\Module\Forms\FQT\View;


use Exolog\Module\Forms\FQT\FQTBase;
use Exolog\Module\Forms\FQT\JsonValueProvider;
use Exolog\Module\Forms\FQT\ValueProvider;

class View extends FQTBase
{
    protected function getValueProvider(): ValueProvider
    {
        return new JsonValueProvider($this->react, $this->fq_name);
    }

    public function render($params = [])
    {
        return view($this->getView(), $params)->toHtml();
    }

    public function getView()
    {
        return $this->getData()['view'];
    }

    public function __toString()
    {
        return empty($this->getView()) ? '' : $this->getView();
    }

}