<?php


namespace Exolog\Module\Forms\FQT;


use Exolog\Core\Forms\React;
use Exolog\Module\Contracts\XEditable;
use Exolog\Module\Forms\Model\FormQuestion;
use Exolog\Module\View\XEditableValue;

/**
 * This wrapper does not have deal with render data
 * output data as is and add/generate exidit magic string
 */
class FQTDump extends FQTBase implements XEditable
{
    use XEditableValue;

    public function __construct(React $react, FormQuestion $fq, array $arguments = [])
    {
        parent::__construct($react, $fq, $arguments);
        //TODO move it to ECO?
        $typeContentType = [
            'text' => 'tinymce',
            'textarea' => 'tinymce',
            'plaintext' => 'tinymce',
            'radio' => 'select',
            'checkbox' => 'checklist',
            'select' => 'select',
            'file' => 'file',
            'date' => 'date',
            'datetime' => 'datetime',
            'tinymce' => 'tinymce',
            'htmlarea' => '',
            'tinymce2' => '',
            'captcha' => '',
            'numeric' => 'number',
            'password' => '',
            'useremail' => '',
            'picture' => 'picture',
            'template' => '',
        ];
        $this->exdit['contenttype'] = $typeContentType[$this->getFQ()->getTypeName()];
    }

    public function toHtml()
    {
        return $this->render(array_merge(['tag' => 'div'], $this->renderParams));
    }

    public function value()
    {
        $value = parent::value();

        if ($this->fq->getTypeName() === 'select' && $this->fq->isMultiple()) {
            //Legacy code can set array directly for real value
            if (!is_array($value)) {
                $value = empty($value) ? [] : explode('||', trim($value, '|'));
            }
        }

        return $value;
    }
}