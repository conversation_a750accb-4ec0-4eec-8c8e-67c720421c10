<?php /** @noinspection MagicMethodsValidityInspection */


namespace Exolog\Module\Forms\FQT\Permalink;


use Exolog\Core\Forms\React;
use Exolog\Module\Exceptions\InvalidPropertyException;
use Exolog\Module\Permalinks\Model\Permalink;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Str;

/**
 * @property string url
 * @property string host
 *
 */
class PermalinkValue implements Htmlable
{
    private React $react;
    public ?Permalink $permalink;

    public function __construct(React $react, ?Permalink $permalink)
    {
        $this->react = $react;
        $this->permalink = $permalink;
    }

    public function getURL(bool $absolute = true): ?string
    {
        if ($this->permalink === null) {
            return null;
        }
        if (Str::startsWith($this->permalink->p_url, ['http:', 'https:'])) {
            return $this->permalink->p_url;
        }
        return ($absolute ? $this->getHost() : '') . Str::start($this->permalink->p_url, '/');
    }

    private function getHost(): string
    {
        return exolog()->getEditionDefaultUrl($this->react->getReactRelatedEditionId());
    }

    public function __get($name)
    {
        if (strtolower($name) === 'url') {
            return $this->getURL();
        }
        if (strtolower($name) === 'host') {
            return $this->getHost();
        }
        throw new InvalidPropertyException($name, __CLASS__);

    }

    public function __toString()
    {
        return $this->toHtml();
    }

    public function toHtml()
    {
        return $this->getURL();
    }
}