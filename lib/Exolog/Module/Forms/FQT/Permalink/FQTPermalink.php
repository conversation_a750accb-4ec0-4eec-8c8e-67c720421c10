<?php


namespace Exolog\Module\Forms\FQT\Permalink;


use Exolog\Module\Exceptions\InvalidPropertyException;
use Exolog\Module\Forms\FQT\FQTBase;
use Exolog\Module\Forms\FQT\ValueProvider;
use Exolog\Module\Permalinks\Model\Permalink;
use Illuminate\Support\Collection;

/**
 * @method Collection<Permalink> getData()
 * @property Collection<PermalinkValue> alternates
 * @property string|null $url
 * @property PermalinkValue|null $current
 */
class FQTPermalink extends FQTBase
{
    public function primary(): ?PermalinkValue
    {
        return $this->convertToPermalinkValue($this->getData()->first(fn($item) => $item['p_type'] === Permalink::TYPE_PRIMARY));
    }

    public function alternates(): Collection
    {
        return $this->getData()
            ->filter(fn($item) => $item['p_type'] === Permalink::TYPE_ALTERNATE)
            ->map(fn($item) => $this->convertToPermalinkValue($item));
    }

    public function manuals(): Collection
    {
        return $this->getData()
            ->filter(fn($item) => $item['p_type'] === Permalink::TYPE_MANUAL)
            ->map(fn($item) => $this->convertToPermalinkValue($item));
    }

    public function current(): ?PermalinkValue
    {
        if ($this->react['p_id'] !== null) {
            $permalink = $this->getData()->first(fn(Permalink $item) => $item->p_id === (int)$this->react['p_id']);
        } else {
            $container = $this->react->getReactOptions()['container'];
            if ($container !== null) {
                $permalink = $this->getData()
                    ->first(fn($item) => (
                            $item['p_type'] === Permalink::TYPE_ALTERNATE) &&
                        data_get($item, 'p_context.container') === $container);
            }
            if (empty($permalink)) {
                $permalink = $this->primary() ?? $this->getData()->first(fn($item) => ($item['p_type'] === Permalink::TYPE_MANUAL));
            }

        }

        return $this->convertToPermalinkValue($permalink);
    }

    public function all(): Collection
    {
        return $this->getData();
    }


    public function __get($name)
    {
        if ($name === 'alternates') {
            return $this->alternates();
        }
        if ($name === 'primary') {
            return $this->primary();
        }
        if ($name === 'current') {
            return $this->current();
        }
        if ($name === 'manuals') {
            return $this->manuals();
        }
        if ($name === 'url') {
            return $this->getURL();
        }

        throw new InvalidPropertyException($name, __CLASS__);

    }

    protected function getValueProvider(): ValueProvider
    {
        return new PermalinksValueProvider($this->react, $this->fq_name);
    }

    public function getURL(bool $absolute = true): ?string
    {
        if ($permalink = $this->current()) {
            return $permalink->getURL($absolute);
        }
        return null;
    }

    public function save()
    {
        $this->react->save();
        return $this;
    }

    /**
     * @param Permalink|PermalinkValue|null $permalink
     * @return PermalinkValue|null
     */
    private function convertToPermalinkValue($permalink): ?PermalinkValue
    {
        if ($permalink instanceof PermalinkValue) {
            return $permalink;
        }
        if ($permalink !== null) {
            return new PermalinkValue($this->react, $permalink);
        }
        return null;
    }

    protected function getRenderData($params = [])
    {
        if ($current = $this->current) {
            return $current->toHtml();
        }
        return '';
    }

    public function __toString()
    {
        return $this->toHtml();
    }
}