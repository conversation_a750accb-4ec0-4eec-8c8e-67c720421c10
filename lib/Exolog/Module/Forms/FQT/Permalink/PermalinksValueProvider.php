<?php


namespace Exolog\Module\Forms\FQT\Permalink;

use Exolog\Core\Forms\React;
use Exolog\Module\Forms\FQT\ValueProvider;
use Exolog\Module\Permalinks\Model\Permalink;
use Illuminate\Support\Collection;

class PermalinksValueProvider extends ValueProvider
{
    public function __construct(React $react, $fq_name)
    {
        parent::__construct($react, $fq_name);
        $react->registerAfterSaveCallback([$this, 'save']);
    }

    private Collection $data;

    public function getData()
    {
        if (!isset($this->data)) {
            $this->data = Permalink::query()->where('p_react_id', $this->react->id())->get();
        }
        return $this->data;
    }

    public function setData($data)
    {
        $this->data = collect($data);
    }

    public function save(): void
    {
        //if we do not set/get data to field, we do not save anything
        if (!isset($this->data)) {
            return;
        }
        //we save only manual and sync only manual/history
        $permalinks = $this->data
            ->filter(fn($item) => $item['p_type'] === Permalink::TYPE_MANUAL
                || $item['p_type'] === Permalink::TYPE_HISTORY);

        $ids = $permalinks->pluck('p_id');
        Permalink::query()
            ->where('p_react_id', $this->react->id())
            ->whereIn('p_type', [Permalink::TYPE_MANUAL, Permalink::TYPE_HISTORY])
            ->whereNotIn('p_id', $ids)
            ->delete();

        $permalinks->filter(fn($item) => $item['p_type'] === Permalink::TYPE_MANUAL)
            ->each(function ($permalink) {
                $permalink['p_react_id'] = $this->react->id();
                Permalink::updateOrCreate(['p_id' => $permalink['p_id']], $permalink);
            });

        unset($this->data);
    }
}