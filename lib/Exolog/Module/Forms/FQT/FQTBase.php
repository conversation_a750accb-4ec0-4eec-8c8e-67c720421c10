<?php


namespace Exolog\Module\Forms\FQT;

use Exolog\Core\Forms\React;
use Exolog\Module\Contracts\EntityValueProvider;
use Exolog\Module\Forms\Model\FormQuestion;
use Exolog\Module\Support\ValueAccessor;
use Exolog\Module\View\HasRenderValue;
use Illuminate\Contracts\Support\Htmlable;

class FQTBase implements Htmlable
{
    use ValueAccessor, HasRenderValue;

    protected array $exdit = [];
    public React $react;
    protected string $fq_name;
    protected string $fqt_name;
    protected EntityValueProvider $value;
    //protected array $renderParams = [];
    protected FormQuestion $fq;

    /**
     * FQT field wrapper constructor.
     */
    public function __construct(React $react, FormQuestion $fq, array $arguments = [])
    {
        $this->react = $react;
        $this->fq = $fq;
        //todo remove this props
        $this->fq_name = $fq->fq_name;
        $this->fqt_name = $fq->getTypeName();

        $this->value = $this->getValueProvider();
        $this->renderParams = is_array($arguments) && !empty($arguments[0]) ? $arguments[0] : [];
    }

    protected function getValueProvider(): ValueProvider
    {
        return new ValueProvider($this->react, $this->fq_name);
    }

    public function getFQ(): FormQuestion
    {
        return $this->fq;
    }

    //todo move to subclass
    protected function buildExedit($exedit = [])
    {
        if (empty($exedit)) {
            $exedit = [];
        }
        return array_merge(
            [
                'type' => 'react',
                'id' => $this->react['react_id'],
                'field' => $this->fq_name
            ],
            $this->exdit,
            $exedit);
    }

    public function __toString()
    {
        return (string)$this->getData();
    }

    public function value()
    {
        return $this->getData();
    }
}