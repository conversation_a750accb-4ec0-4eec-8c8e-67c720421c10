<?php


namespace Exolog\Module\Forms\FQT\Picture;

use Exolog\Module\Contracts\XEditable;
use Exolog\Module\Forms\FQT\FQTBase;
use Exolog\Module\Forms\FQT\JsonValueProvider;
use Exolog\Module\Forms\FQT\ValueProvider;
use Exolog\Module\Support\AttributesParser;
use Exolog\Module\View\XEditableValue;
use RuntimeException;

class Picture extends FQTBase implements XEditable
{
    use XEditableValue, PictureValue;

    protected array $exdit = ['contenttype' => 'picture'];

    protected function getValueProvider(): ValueProvider
    {
        return new JsonValueProvider($this->react, $this->fq_name);
    }

    protected function getCommonRenderParams()
    {
        $fq = $this->react->getReactForm()->getFQ($this->fq_name);
        $params = AttributesParser::parse($fq['fq_custom_params']);
        $params = $params['renderParams'];
        if (!empty($params)) {
            $params = json_decode($params, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new RuntimeException('Error! Can\'t parse param \'renderParams\'');
            }
        }
        return empty($params) ? [] : $params;
    }

    protected function getPermalink()
    {
        return $this->react->getReactURL(false) ?? '';//Helper::getParentPermalink($this->react);
    }
}