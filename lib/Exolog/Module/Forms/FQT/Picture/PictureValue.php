<?php


namespace Exolog\Module\Forms\FQT\Picture;

use Exolog\Module\Forms\PictureFieldRecoveryService;
use Exolog\Module\Support\Facades\ExoFile;
use Exolog\Module\Support\ValueAccessor;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Support\Facades\Storage;
use RuntimeException;

trait PictureValue
{
    use ValueAccessor;

    /**
     * @param array $params
     * @return string
     */
    public function render($params = [])
    {
        try {
            $params = $this->mergeRenderParams($params);
        } catch (RuntimeException $exception) {
            return $this->renderError($exception->getMessage());
        }

        try {
            if ($params['source']) {
                return $this->renderPicture($params);
            }

            return $this->renderImg($params);

        } catch (FileNotFoundException $exception) {
            return $this->renderNotFound($params);
        }
    }

    public function setImage($file_id): void
    {
        $data = $this->getData();
        $data['file_id'] = $file_id;
        $this->setData($data);
    }

    public function setHash($value): void
    {
        $data = $this->getData();
        $data['hash'] = $value;
        $this->setData($data);
    }

    public function getHash(): ?string
    {
        $data = $this->getData();
        return $data['hash'];
    }

    /**
     * To use in background
     * @param $params
     * @return string|string[]
     */
    public function getURL($params = [])
    {
        try {
            $params = $this->mergeRenderParams($params);
        } catch (RuntimeException $exception) {
            return $exception->getMessage();
        }
        try {
            return $this->genURL($params['src'], $params['type'], $params['absolute_url']);
        } catch (FileNotFoundException $exception) {
            return '/file/0';
        }
    }

    public function __toString()
    {
        return empty($this->getData()['file_id']) ? '0' : (string)$this->getData()['file_id'];
    }

    public function getFileId()
    {
        return $this->getData()['file_id'];
    }

    /**
     * @param $message
     * @return string
     */
    private function renderError($message)
    {
        return sprintf('<code>%s</code>', $message);
    }

    /**
     * @return string
     */
    private function renderNotFound($params)
    {
        return sprintf('<img %s src="/file/0" alt="file not found" >',
            $this->getExeditAttr($params['exedit']));
    }


    /**
     * @return mixed
     */
    private function getWidth()
    {
        return @getimagesize($this->getFilePath())[0];
    }


    /**
     * Return full path to file on filesystem
     * @return string
     */
    private function getFilePath(): ?string
    {
        $fileId = $this->getFileId();
        return empty($fileId) ? null : ExoFile::path($this->getFileId());
    }

    /**
     * Return base name of picture
     */
    private function getFileName(): ?string
    {
        if (empty($this->getFileId())) {
            return null;
        }
        return ExoFile::basename($this->getFileId());
    }

    /**
     * @param $source
     * @return string
     */
    private function genSourceTag($source): string
    {
        $type = '';
        if (!empty($source['type'])) {
            $type = sprintf('type="%s"', $source['type']);
        }
        $media = '';
        if (!empty($source['media'])) {
            $media = sprintf('media="%s"', $source['media']);
        }
        $srcset = $this->genSrcset($source);
        return sprintf('<source %s %s %s />', $media, $type, $srcset);
    }

    /**
     * @param $params
     * @return string
     */
    private function genPictureTag($params): string
    {
        $source_tags = '';
        foreach ($params['source'] as $source) {
            $source['lazy'] = $params['lazy'];
            $source_tags .= $this->genSourceTag($source);
        }
        return sprintf('<picture>%s %s</picture>', $source_tags, $this->genImgTag($params));
    }

    /**
     * @param $params
     * @return string
     */
    private function genImgTag($params): string
    {
        $alt = dv($params['alt'], $this->getAlt());
        $alt = empty($alt) ? '' : sprintf('alt="%s"', $alt);
        $attr = $this->parseAttr($params, ['src', 'srcset', 'type', 'source', 'sizes', 'lazy']);
        return sprintf('<img %s %s %s %s %s>',
            $attr,
            $this->getSrcAttr($params),
            $params['srcset'] ? $this->genSrcset($params) : '',
            $alt,
            $this->getExeditAttr($params['exedit'])
        );
    }

    protected function buildExedit($exedit = [])
    {
        if ($this->getFileId()) {
            $this->exdit['fileId'] = $this->getFileId();
        }
        return parent::buildExedit($exedit);
    }

    /**
     * @param $params
     * @return string|string[]
     */
    private function genSrcset($params)
    {
        $srcset_w = empty($params['srcset']) ? [] : explode(',', $params['srcset']);
        $img_width = $this->getWidth();
        if (!empty($img_width) && empty($srcset_w)) {
            $srcset_w[] = $img_width . 'w';
        }
        $srcset = [];
        foreach ($srcset_w as $src) {
            $src = $this->normalizeSrcParams($src);
            $width = $this->extractWidth($src);
            if ($this->isEmptyWidth($width)) {
                $src = $img_width . 'w';
                $width = $this->extractWidth($src);
            }
            $srcset[] = sprintf('%s  %s',
                $this->buildUrlPath([
                    'host' => $params['absolute_url'] ? request()->getSchemeAndHttpHost() : '',
                    'hash' => ExoFile::encodeFileId(
                        [
                            'f' => $this->getFileId(),
                            't' => $src
                        ]
                    ),
                    'permalink' => $this->getPermalink(),
                    'filename' => $this->getRenderFilename() . '.' . $this->getRenderExtension($params['type'])
                ]),
                //do not add width if srcset have one size
                count($srcset_w) == 1 ? '' : $width);
        }
        $sizes = $params['sizes'] ? sprintf('sizes="%s"', $params['sizes']) : '';
        $srcset = implode(',', $srcset);
        $srcsetName = $params['lazy'] == 2 ? 'data-srcset' : 'srcset';
        $srcset = sprintf('%s="%s" %s', $srcsetName, $srcset, $sizes);
        $srcset = str_replace('//', '/', $srcset);
        return $srcset;
    }

    private function getRenderFilename(): string
    {
        if (!empty($this->getData()['file_name'])) {
            $filename = pathinfo($this->getData()['file_name'], PATHINFO_FILENAME);
        } else {

            $fs_filename = $this->getFileName();
            if (empty($fs_filename)) {
                return 'noimage';
            }

            $filename = pathinfo($fs_filename, PATHINFO_FILENAME);
        }

        return strtolower(str_replace(' ', '_', $filename));
    }

    /**
     * calculate file extension/type
     * @param null $type
     * @return mixed|string|string[]|null
     */
    private function getRenderExtension($type = null)
    {
        $ext = pathinfo($this->getFileName(), PATHINFO_EXTENSION);
        if ($type) {
            $ext = str_replace('image/', '', $type);
        }

        return $ext;
    }

    /**
     * @param $params
     * @return string
     */
    private function renderImg($params)
    {
        return $this->genImgTag($params);
    }

    /**
     * @param $params
     * @return string
     */
    private function renderPicture($params)
    {
        return $this->genPictureTag($params);
    }

    /**
     * @param string $src image params '100w100h75q'
     * @param string $type media type 'image/png'
     * @param bool $absolute_url
     * @return string|string[]
     */
    private function genURL($src, $type, $absolute_url = false)
    {
        return $this->buildUrlPath([
                'host' => $absolute_url ? request()->getSchemeAndHttpHost() : '',
                'hash' => ExoFile::encodeFileId(['f' => $this->getFileId(), 't' => $this->normalizeSrcParams($src)]),
                'permalink' => $this->getPermalink(),
                'filename' => $this->getRenderFilename() . '.' . $this->getRenderExtension($type)
            ]
        );
    }

    public function getAlt()
    {
        return $this->getData()['alt'];
    }

    public function getSitemap()
    {
        return $this->getData()['sitemap'] && (!empty($this->getAlt()) || !empty($this->getData()['file_name'])) ? 1 : 0;
    }

    abstract protected function getCommonRenderParams();

    private function getReactRenderParams()
    {
        $params = $this->getData()['params'];
        if (!empty($params)) {
            $params = json_decode($params, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new RuntimeException('Error! Can\'t parse default params in react id=' . $this->react['react_id']);
            }
        }
        return empty($params) ? [] : $params;
    }


    /**
     * Merge all params from Form level, React level, Template level
     * @param $params
     * @return array
     */
    private function mergeRenderParams($params)
    {
        if (empty($params)) {
            $params = [];
        }
        $params = array_merge($this->getCommonRenderParams(), $this->getReactRenderParams(), $params);
        $params = $this->parseLazy($params);
        return $params;
    }

    /**
     * Exttract width of image from srcset
     * @param $src
     * @return mixed
     */
    private function extractWidth($src)
    {
        if (is_string($src)) {
            $matches = [];
            preg_match('/\d+w/i', $src, $matches);
            return $matches[0];
        }

        return $src['w'];
    }

    /**
     * if src param set as array transform long name to shot like 'width' => 'w'
     * @param $src
     * @return array|string
     */
    private function normalizeSrcParams($src)
    {
        if (is_string($src)) {
            return $src;
        }
        if (is_array($src)) {
            $tsrc = [];
            foreach ($src as $key => $value) {
                switch ($key) {
                    case 'width':
                        $tsrc['w'] = $value;
                        break;
                    case 'height':
                        $tsrc['h'] = $value;
                        break;
                    case 'crop':
                        $tsrc['c'] = $value;
                        break;
                    case 'quality':
                        $tsrc['q'] = $value;
                        break;
                    case 'thumbnail':
                        $tsrc['t'] = $value;
                        break;
                    default:
                        $tsrc[$key] = $value;
                }
            }
            return $tsrc;
        }
        return '';
    }

    abstract protected function getPermalink();

    private function buildUrlPath(array $parts): string
    {
        $url = sprintf('/media/%s/%s/%s',
            $parts['hash'],
            $parts['permalink'],
            $parts['filename']
        );

        $url = preg_replace('(////|///|//)', '/', $url);
        if (!empty($parts['host'])) {
            $url = $parts['host'] . $url;
        }
        return filter_var($url, FILTER_SANITIZE_URL);
    }

    private function isEmptyWidth($width): bool
    {
        $px = (int)$width;
        return empty($px);
    }

    private function parseLazy($params)
    {
        $params['lazy'] = dv(dv($params['lazy'], $this->getData()['lazy']), 0);

        if ($params['lazy'] == 1) {
            //native
            //do not overide if user set custom value ex. eger
            if (empty($params['loading'])) {
                $params['loading'] = 'lazy';
            }
        } elseif ($params['lazy'] == 2) {
            //polyfill, add class
            if (isset($params['class'])) {
                if (is_array($params['class'])) {
                    $params['class'][] = 'lazyload';
                } else {
                    $params['class'] .= ' lazyload ';
                }
            } else {
                $params['class'] = 'lazyload';
            }
        }
        return $params;
    }

    private function getSrcAttr(array $params): string
    {
        $srcValue = $this->genURL($params['src'], $params['type'], $params['absolute_url']);
        $srcName = $params['lazy'] == 2 ? 'data-src' : 'src';
        return sprintf('%s=%s', $srcName, $srcValue);
    }


    public function updateHash(): bool
    {
        $fileId = $this->getFileId();
        if (!empty($fileId)) {
            [$disk, $path] = ExoFile::parseFileId($fileId);
            if ($disk !== 'DB') {
                if (!Storage::disk($disk)->exists($path)) {
                    return false;
                }
                $hash = md5(Storage::disk($disk)->get($path));
            } else {
                $hash = null;
            }
            $currentHash = $this->getHash();
            if ($currentHash !== $hash) {
                $this->setHash($hash);
                return true;
            }
        }
        return false;
    }

    public function recovery(): void
    {
        PictureFieldRecoveryService::make()->recoveryPictureValue($this);
    }
}