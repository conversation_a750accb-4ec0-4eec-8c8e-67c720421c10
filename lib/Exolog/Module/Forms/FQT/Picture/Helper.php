<?php


namespace Exolog\Module\Forms\FQT\Picture;


use Exolog\Core\Forms\React;
use Exolog\Module\Container\BaseContainer;

class Helper
{
    public static function getParentPermalink(React $react)
    {
        /** @var array<BaseContainer> $containers */
        $containers = $react->parentContainers();
        if (!empty($containers) && count($containers) === 1) {
            $parent_react = null;
            if ($containers[0]->getHolderType() === 'react') {
                $parent_react = $containers[0]->getHolder();
            } elseif ($containers[0]->getHolderType() === 'unit') {
                $parent_react = $containers[0]->getHolder()->getHolder();
            }
            if ($parent_react instanceof React) {
                return $parent_react->getReactURL(false) ?? '';
            }
        }
        return '';
    }
}