<?php


namespace Exolog\Module\Forms\FQT\Json;


use ArrayAccess;
use ArrayObject;
use BadMethodCallException;
use Exolog\Module\Contracts\XEditable;
use Exolog\Module\Forms\FQT\FQTBase;
use Exolog\Module\View\XEditableValue;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use ReturnTypeWillChange;

class Json extends FQTBase implements XEditable, ArrayAccess
{
    use XEditableValue;


    public const TYPE_PICTURE = 'picture';
    public const TYPE_TEXT = 'text';
    public const TYPE_DATE = 'date';
    public const TYPE_DATETIME = 'datetime';
    public const TYPE_NUMBER = 'number';
    public const TYPE_CHECKBOX = 'checkbox';
    public const TYPE_BOOLEAN = 'boolean';

    protected array $exdit = [];

    public function toHtml()
    {
        return $this->render(array_merge(['tag' => 'div'], $this->renderParams));
    }

    public function __get($name)
    {
        $object = new ArrayObject($this->getData(), ArrayObject::STD_PROP_LIST | ArrayObject::ARRAY_AS_PROPS);
        return $object->$name;
    }

    public function __set($name, $value)
    {
        $object = new ArrayObject($this->getData(), ArrayObject::STD_PROP_LIST | ArrayObject::ARRAY_AS_PROPS);
        $object->$name = $value;
        $this->setData($object->getArrayCopy());
    }

    public function save()
    {
        $this->react->save();
        return $this;
    }

    public function getHolder()
    {
        return $this->react;
    }

    /**
     * Dynamically check a property exists on the underlying object.
     *
     * @param mixed $name
     * @return bool
     */
    public function __isset($name)
    {
        $value = $this->value();
        if (is_object($value)) {
            return isset($value->{$name});
        }
        if (is_array($value) || $value instanceof ArrayObject) {
            return isset($value[$name]);
        }
        return false;
    }

    /**
     * Determine if an item exists at an offset.
     *
     * @param mixed $key
     * @return bool
     */
    #[ReturnTypeWillChange]
    public function offsetExists($key)
    {
        $value = $this->value();
        return Arr::accessible($value) && Arr::exists($value, $key);
    }

    /**
     * Get an item at a given offset.
     *
     * @param mixed $key
     * @return mixed
     */
    #[ReturnTypeWillChange]
    public function offsetGet($key)
    {
        $value = $this->value();
        return Arr::get($value, $key);
    }

    /**
     * Set the item at a given offset.
     *
     * @param mixed $key
     * @param mixed $value
     * @return void
     */
    #[ReturnTypeWillChange]
    public function offsetSet($key, $value)
    {
        $data = $this->value();
        if (Arr::accessible($data)) {
            $data[$key] = $value;
            $this->setData($data);
        }
    }

    /**
     * Unset the item at a given offset.
     *
     * @param string $key
     * @return void
     */
    #[ReturnTypeWillChange]
    public function offsetUnset($key)
    {
        $value = $this->value();
        if (Arr::accessible($value)) {
            unset($value[$key]);
            $this->setData($value);
        }
    }

    public function __call($method, $arguments)
    {
        if (Str::startsWith($method, 'get')) {
            $property = getter_to_key($method);
            if (count($arguments) === 2) {
                return $this->property(['property' => $property, 'type' => $arguments[1]], $arguments[0]);
            }
            return $this->property($property, $arguments[0]);
        }
        throw new BadMethodCallException(sprintf(
            'Call to undefined method %s::%s()', static::class, $method
        ));
    }

    public function property($property, $renderParams = [])
    {
        if (is_array($property)) {
            return JsonFieldFactory::build(
                $property['type'],
                $this->getJsonFiledValueProvider($property['property'], $property['type']),
                $renderParams);
        }
        $type = 'text';
        return JsonFieldFactory::build(
            $type,
            $this->getJsonFiledValueProvider($property, $type),
            $renderParams
        );
    }

    protected function getJsonFiledValueProvider($property, $type): JsonFiledValueProvider
    {
        return new JsonFiledValueProvider($this, $property, $type, function () {
            return [
                'type' => 'react',
                'id' => $this->react['react_id'],
                'field' => $this->fq_name
            ];
        });
    }

}