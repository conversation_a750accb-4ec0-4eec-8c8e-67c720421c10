<?php


namespace Exolog\Module\Forms\FQT\Json;

use Exolog\Module\Contracts\EntityValueProvider;

class JsonFiledValueProvider implements EntityValueProvider
{
    private Json $filed;
    private string $property;
    private $buildExedit;
    private string $type;

    public function __construct(Json $filed, string $property, ?string $type, ?callable $holderExedit)
    {
        $this->filed = $filed;
        $this->property = $property;
        $this->buildExedit = $holderExedit;
        $this->type = $type ?? Json::TYPE_TEXT;
    }

    public function getData()
    {
        $data = $this->filed->getData();
        $key_data = data_get($data, $this->property);
        return $key_data ?? null;
    }

    public function setData($data)
    {
        $field_data = $this->filed->getData();
        data_set($field_data, $this->property, $data);
        $this->filed->setData($field_data);
    }

    public function buildExedit()
    {
        return array_merge(call_user_func($this->buildExedit), ['property' => $this->property]);
    }

    public function getFiled()
    {
        return $this->filed;
    }

    public function getHolderId()
    {
        return $this->filed->getHolder()->id();
    }

    public function getPropery()
    {
        return $this->property;
    }

    public function getType()
    {
        return $this->type;
    }

    public function save()
    {
        return $this->filed->save();
    }
}
