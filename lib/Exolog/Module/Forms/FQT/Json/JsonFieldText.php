<?php


namespace Exolog\Module\Forms\FQT\Json;

use Exolog\Module\Contracts\XEditable;
use Exolog\Module\View\XEditableValue;

class JsonFieldText extends <PERSON>sonField implements XEditable
{
    use XEditableValue;

    protected string $key;

    protected array $exdit =
        [
            'contenttype' => 'tinymce',
            'toolbar' => 'custom',
            'property_type' => Json::TYPE_TEXT,
        ];

    public function toHtml()
    {
        return $this->render(array_merge(['tag' => 'div'], $this->renderParams));
    }

    public function __toString()
    {
        return $this->getData();
    }
}