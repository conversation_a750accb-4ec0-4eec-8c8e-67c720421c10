<?php


namespace Exolog\Module\Forms\FQT\Json;


class JsonFieldFactory
{
    public static function build($type, JsonFiledValueProvider $valueProvider, $renderParams)
    {
        switch ($type) {
            case Json::TYPE_PICTURE:
                return new JsonFieldPicture(
                    $valueProvider,
                    [$renderParams]
                );
            case Json::TYPE_DATE:
                return new JsonFieldDate(
                    $valueProvider,
                    [$renderParams, 'date']
                );
            case Json::TYPE_DATETIME:
                return new JsonFieldDate(
                    $valueProvider,
                    [$renderParams, 'datetime']
                );
            case Json::TYPE_NUMBER:
                return new JsonFieldNumber(
                    $valueProvider,
                    [$renderParams]
                );
            case Json::TYPE_BOOLEAN:
            case Json::TYPE_CHECKBOX:
                return new JsonFieldCheckbox(
                    $valueProvider,
                    [$renderParams]
                );
            default:
                return new JsonFieldText(
                    $valueProvider,
                    [$renderParams]
                );
        }
    }
}