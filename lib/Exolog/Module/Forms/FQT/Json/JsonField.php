<?php


namespace Exolog\Module\Forms\FQT\Json;

use BadMethodCallException;
use Exolog\Module\Contracts\EntityValueProvider;
use Exolog\Module\Support\ValueAccessor;
use Exolog\Module\View\HasRenderValue;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Str;
use RuntimeException;

class <PERSON>sonField implements EntityValueProvider, Htmlable
{
    use ValueAccessor, HasRenderValue;


    protected JsonFiledValueProvider $value;
    protected array $exdit = [];


    public function __construct(JsonFiledValueProvider $value, array $arguments = [])
    {
        $this->value = $value;
        $this->renderParams = is_array($arguments) && !empty($arguments[0]) ? $arguments[0] : [];
    }

    public function getProperty()
    {
        return $this->value->getPropery();
    }

    public function getType()
    {
        return $this->value->getType();
    }

    public function toHtml()
    {
        throw new RuntimeException('Not implemented yet!');
    }

    protected function buildExedit($exedit = [])
    {
        if (empty($exedit)) {
            $exedit = [];
        }
        return array_merge(
            $this->value->buildExedit(),
            $this->exdit,
            $exedit);
    }

    public function __call($method, $arguments)
    {
        if (Str::startsWith($method, 'get')) {
            $property = getter_to_key($method);
            $property = $this->value->getPropery() . '.' . $property;

            if (count($arguments) === 2) {
                return $this->value->getFiled()->property(['property' => $property, 'type' => $arguments[1]], $arguments[0]);
            }
            return $this->value->getFiled()->property($property, $arguments[0]);
        }
        throw new BadMethodCallException(sprintf(
            'Call to undefined method %s::%s()', static::class, $method
        ));
    }
}