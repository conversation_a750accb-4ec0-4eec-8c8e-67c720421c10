<?php


namespace Exolog\Module\Forms\FQT\Json;

use Exolog\Module\Contracts\XEditable;
use Exolog\Module\View\XEditableValue;


class JsonFieldNumber extends <PERSON><PERSON><PERSON>ield implements XEditable
{
    use XEditableValue;

    protected string $key;
    protected array $exdit =
        [
            'contenttype' => 'number',
            'property_type' => Json::TYPE_NUMBER,
        ];


    public function getData()
    {
        return parent::getData();
    }

    public function setData($data)
    {
        $data = (float)filter_var($data, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
        parent::setData($data);
    }

    public function toHtml()
    {
        return $this->render(array_merge(['tag' => 'span'], $this->renderParams));
    }

    protected function buildExedit($exedit = [])
    {
        $this->exdit['value'] = $this->getData();
        return parent::buildExedit($exedit);
    }
}