<?php


namespace Exolog\Module\Forms\FQT\Json;

use Exolog\Module\Contracts\XEditable;
use Exolog\Module\Forms\FQT\Picture\PictureValue;
use Exolog\Module\View\HasRenderValue;
use Exolog\Module\View\XEditableValue;

class JsonFieldPicture extends Json<PERSON>ield implements XEditable
{
    use  XEditableValue, PictureValue, HasRenderValue {
        PictureValue::render insteadof HasRenderValue;
    }

    protected array $exdit = [
        'contenttype' => 'picture',
        'property_type' => Json::TYPE_PICTURE,
    ];

    public function toHtml()
    {
        return $this->render($this->renderParams);
    }

    protected function getCommonRenderParams()
    {
        return [];
    }

    protected function getPermalink()
    {
        $react = $this->value->getFiled()->getHolder();
        return $react->getReactURL(false) ?? '';//Helper::getParentPermalink($react);
    }
}