<?php


namespace Exolog\Module\Forms\FQT\Json;

use Exolog\Module\Contracts\XEditable;
use Exolog\Module\View\XEditableValue;


class JsonFieldCheckbox extends <PERSON><PERSON><PERSON><PERSON> implements XEditable
{
    use XEditableValue;

    protected string $key;
    protected array $exdit =
        [
            'contenttype' => 'checkbox',
            'property_type' => Json::TYPE_CHECKBOX,
        ];

    public function getData()
    {
        return parent::getData();
    }

    public function setData($data)
    {
        //$data = (bool)filter_var($data, FILTER_VALIDATE_BOOLEAN);
        parent::setData($data);
    }

    public function toHtml()
    {
        return $this->render(array_merge(['tag' => 'span'], $this->renderParams));
    }

    protected function buildExedit($exedit = [])
    {
        $this->exdit['value'] = $this->getData() ?? $this->renderParams['default'] ?? null;

        if (array_key_exists('trueValue', $this->renderParams)) {
            $this->exdit['trueValue'] = $this->renderParams['trueValue'];
        }
        if (array_key_exists('falseValue', $this->renderParams)) {
            $this->exdit['falseValue'] = $this->renderParams['falseValue'];
        }
        return parent::buildExedit($exedit);
    }

    protected function getRenderData($params = [])
    {
        $value = parent::getRenderData($params);

        $trueValue = data_get($params, 'trueValue', true);
        $falseValue = data_get($params, 'falseValue', false);
        if ($value === $trueValue) {
            return is_bool($trueValue) ? 'true' : $trueValue;
        }
        if ($value === $falseValue) {
            return is_bool($falseValue) ? 'false' : $falseValue;
        }
        return $value;
    }
}