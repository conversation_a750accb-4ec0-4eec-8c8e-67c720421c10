<?php


namespace Exolog\Module\Forms\FQT\Date;


use Exolog\Core\Forms\React;
use Exolog\Module\Contracts\XEditable;
use Exolog\Module\Forms\FQT\DateTimeValueProvider;
use Exolog\Module\Forms\FQT\FQTBase;
use Exolog\Module\Forms\FQT\ValueProvider;
use Exolog\Module\Forms\Model\FormQuestion;
use Exolog\Module\View\XEditableValue;

class Date extends FQTBase implements XEditable
{
    use XEditableValue, DateValue;


    protected array $exdit;

    public function __construct(React $react, FormQuestion $fq, array $arguments = [])
    {
        parent::__construct($react, $fq, $arguments);
        $this->exdit['format'] = ECO()->get()['forms']['fqt'][$this->fqt_name]['store_format']['php'];
        $this->exdit['store_format'] = ECO()->get()['forms']['fqt'][$this->fqt_name]['store_format']['flatpicker'];
        $this->exdit['contenttype'] = ECO()->get()['forms']['fqt'][$this->fqt_name]['front']['exedit']['contenttype'];

        $this->settings(['formatFunction' => 'translatedFormat']);
    }

    protected function getValueProvider(): ValueProvider
    {
        return new DateTimeValueProvider($this->react, $this->fq_name, $this->fqt_name);
    }


    protected function getStoreFormat()
    {
        return ECO()->get()['forms']['fqt'][$this->fqt_name]['store_format']['php'];
    }
}