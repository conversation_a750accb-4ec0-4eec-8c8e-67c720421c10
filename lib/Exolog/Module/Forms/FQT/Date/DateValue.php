<?php


namespace Exolog\Module\Forms\FQT\Date;

use Illuminate\Support\Traits\ForwardsCalls;

trait DateValue
{
    use ForwardsCalls;

    abstract protected function getStoreFormat();

    public function toHtml()
    {
        return $this->render(array_merge(['tag' => 'span'], $this->renderParams));
    }

    protected function buildExedit($exedit = [])
    {
        $this->exdit['value'] = $this->getData()->format($this->getStoreFormat());
        return parent::buildExedit($exedit);
    }

    protected function getRenderData($params = [])
    {
        $dt = $this->getData();
        if ($params['format']) {
            $this->exdit['format'] = $params['format'];
        }
        if ($params['locale']) {
            $dt->locale($params['locale']);
            $this->exdit['locale'] = $params['locale'];
        }

        if ($params['formatFunction']) {
            $dt->settings(['formatFunction' => $params['formatFunction']]);
            $this->exdit['formatFunction'] = $params['formatFunction'];
        } else {
            $dt->settings(['formatFunction' => 'translatedFormat']);
        }

        return $dt->format($this->exdit['format']);
    }

    /**
     * Pass dynamic methods into the date Carbone instance.
     *
     * @param string $method
     * @param array $parameters
     * @return mixed
     */
    public function __call($method, $parameters)
    {
        return $this->forwardCallTo(
            $this->getData(), $method, $parameters
        );
    }
}