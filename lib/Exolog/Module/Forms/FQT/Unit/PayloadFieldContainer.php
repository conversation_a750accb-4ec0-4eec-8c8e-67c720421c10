<?php


namespace Exolog\Module\Forms\FQT\Unit;

use Exolog\Module\Container\BaseContainer;
use Exolog\Module\Container\UnitContainerDataProvider;
use Exolog\Module\Contracts\ContainerValue;
use Exolog\Module\Contracts\XEditable;
use Exolog\Module\Forms\FQT\Container\HasContainerValue;

class PayloadFieldContainer extends PayloadField implements XEditable, ContainerValue
{
    use HasContainerValue;

    public UnitValueInterface $unit;

    public function __construct(PayloadValueProvider $value, $arguments = [])
    {
        parent::__construct($value, $arguments);
        $this->container = new BaseContainer(new UnitContainerDataProvider($this->value));
    }

    /**
     * Return fq_id|fa_name or var_id
     *
     */
    public function getHolderId()
    {
        return $this->value->getHolderId();
    }

    public function toHtml()
    {
        return $this->render(array_merge(['tag' => 'div'], $this->renderParams));
    }
}