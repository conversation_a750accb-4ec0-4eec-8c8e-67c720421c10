<?php


namespace Exolog\Module\Forms\FQT\Unit;

use Exolog\Module\Contracts\EntityValueProvider;

class PayloadValueProvider implements EntityValueProvider
{
    private UnitValueInterface $unit;
    private $key;
    private $buildExedit;
    private $type;

    /**
     * ComponentPaylodValue constructor.
     * @param UnitValueInterface $unit
     * @param $key string
     * @param $type string
     * @param callable $holderExedit
     */
    public function __construct(UnitValueInterface $unit, $key, $type, callable $holderExedit)
    {
        $this->unit = $unit;
        $this->key = $key;
        $this->buildExedit = $holderExedit;
        $this->type = $type ?? Unit::TYPE_TEXT;
    }

    public function getData()
    {
        $data = $this->unit->getData();
        $key_data = $data['payload'][$this->key];
        return $key_data['value'] ?? null;
    }

    public function setData($data)
    {
        $unit_data = $this->unit->getData();
        $key_data = $unit_data['payload'][$this->key] ?? ['type' => $this->type, 'value' => null];
        $key_data['value'] = $data;
        $unit_data['payload'][$this->key] = $key_data;
        $this->unit->setData($unit_data);
    }

    public function buildExedit()
    {
        return array_merge(call_user_func($this->buildExedit), ['key' => $this->key]);
    }

    public function getUnit()
    {
        return $this->unit;
    }

    public function getHolderId()
    {
        return $this->unit->getPayloadHolderId();
    }

    public function getKey()
    {
        return $this->key;
    }

    public function getType()
    {
        return $this->type;
    }

    public function save()
    {
        return $this->unit->save();
    }
}
