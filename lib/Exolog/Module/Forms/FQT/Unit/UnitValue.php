<?php


namespace Exolog\Module\Forms\FQT\Unit;

use BadMethodCallException;
use Illuminate\Support\Str;
use RuntimeException;

trait UnitValue
{
    public function render($params = [])
    {
        if (empty($this->getComponent())) {
            throw new RuntimeException(sprintf(
                "Component is empty (holder: '%s',  id = '%s'", $this->getHolderType(), $this->getHolderId()));
        }
        return view('system.component', [
            'component' => $this->getComponentView(),
            'unit' => $this,
            'holder' => $this->getHolder(),
            'params' => $params
        ]);
    }

    private function getComponent()
    {
        return $this->getData()['component'];
    }

    private function getComponentView()
    {
        return 'units.' . $this->getComponent();
    }

    public function __toString()
    {
        return empty($this->getComponent()) ? '' : $this->getComponentView();
    }

    /**
     * @param null $key
     * @param array $renderParams
     * @return array|PayloadFieldContainer|PayloadFieldPicture|PayloadFieldText
     */
    public function getPayload($key = null, $renderParams = [])
    {
        if (!empty($key)) {
            if (is_array($key)) {
                return PayloadFieldFactory::build(
                    $key['type'],
                    $this->getPaylodValueProvider($key['key'], $key['type']),
                    $renderParams);
            }

            $payloadRaw = $this->getData()['payload'] ?? [];
            if ($payloadRaw[$key] && $payloadRaw[$key]['type']) {
                $type = $payloadRaw[$key]['type'];
            } else {
                $type = 'text';
            }
            return PayloadFieldFactory::build(
                $type,
                $this->getPaylodValueProvider($key, $type),
                $renderParams
            );
        }

        $payload = [];
        foreach ($this->getData()['payload'] ?? [] as $_key => $item) {
            $payload[$_key] = PayloadFieldFactory::build(
                $item['type'],
                $this->getPaylodValueProvider($_key, $item['type']),
                $renderParams
            );
        }
        return $payload;
    }

    abstract protected function getPaylodValueProvider($key, $type);

    public function __call($method, $arguments)
    {
        if (Str::startsWith($method, 'get')) {
            $key = getter_to_key($method);
            if (count($arguments) === 2) {
                return $this->getPayload(['key' => $key, 'type' => $arguments[1]], $arguments[0]);
            }
            return $this->getPayload($key, $arguments[0]);
        }
        throw new BadMethodCallException(sprintf(
            'Call to undefined method %s::%s()', static::class, $method
        ));

    }

    public function updateHash(): bool
    {
        $result = false;
        foreach ($this->getPayload() as $item) {
            if ($item instanceof PayloadFieldPicture) {
                $updated = $item->updateHash();
                $result = $result || $updated;
            }
        }
        return $result;
    }
}