<?php


namespace Exolog\Module\Forms\FQT\Unit;

use Exolog\Module\Contracts\EntityValueProvider;
use Exolog\Module\Support\ValueAccessor;
use Exolog\Module\View\HasRenderValue;
use Illuminate\Contracts\Support\Htmlable;

class PayloadField implements EntityValueProvider, Htmlable
{
    use ValueAccessor, HasRenderValue;

    protected PayloadValueProvider $value;
    protected array $exdit = [];
    public UnitValueInterface $unit;

    /**
     *
     * @param PayloadValueProvider $value
     * @param array $arguments
     */
    public function __construct(PayloadValueProvider $value, array $arguments = [])
    {
        $this->value = $value;
        $this->unit = $value->getUnit();
        $this->renderParams = is_array($arguments) && !empty($arguments[0]) ? $arguments[0] : [];
    }

    public function getKey()
    {
        return $this->value->getKey();
    }

    public function getType()
    {
        return $this->value->getType();
    }

    protected function buildExedit($exedit = [])
    {
        if (empty($exedit)) {
            $exedit = [];
        }
        return array_merge(
            $this->value->buildExedit(),
            $this->exdit,
            $exedit);
    }
}