<?php


namespace Exolog\Module\Forms\FQT\Unit;


use Exolog\Core\Forms\React;
use Exolog\Module\Vars\Variable;

interface UnitValueInterface
{
    public const HOLDER_TYPE_REACT = 'react';
    public const HOLDER_TYPE_VAR = 'var';


    public function getData();

    public function setData($data);

    /**
     * @param null $key
     * @param array $renderParams
     * @return PayloadField|PayloadField[]
     */
    public function getPayload($key = null, $renderParams = []);

    /**
     * Return fq_name for holder type 'react'
     */
    public function getPayloadHolderId();

    /**
     * var_id or react_id
     * @return mixed
     */
    public function getHolderId();

    /**
     * var or react
     * @return React|Variable
     */
    public function getHolder();

    /**
     * 'react'|'var'
     * @return string
     */
    public function getHolderType();

    public function save();
}