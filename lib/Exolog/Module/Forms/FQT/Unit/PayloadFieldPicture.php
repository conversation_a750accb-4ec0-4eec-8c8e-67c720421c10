<?php


namespace Exolog\Module\Forms\FQT\Unit;

use Exolog\Core\Forms\React;
use Exolog\Module\Contracts\XEditable;
use Exolog\Module\Forms\FQT\Picture\PictureValue;
use Exolog\Module\View\HasRenderValue;
use Exolog\Module\View\XEditableValue;

class PayloadFieldPicture extends PayloadField implements XEditable
{
    use  XEditableValue, PictureValue, HasRenderValue {
        PictureValue::render insteadof HasRenderValue;
    }

    protected array $exdit = ['contenttype' => 'picture'];
    public UnitValueInterface $unit;

    public function toHtml()
    {
        return $this->render($this->renderParams);
    }

    protected function getCommonRenderParams()
    {
        return [];
    }

    protected function getPermalink()
    {
        if ($this->value->getUnit()->getHolderType() !== 'react') {
            return '';
        }
        /** @var React $react */
        $react = $this->value->getUnit()->getHolder();
        return $react->getReactURL(false) ?? '';//Helper::getParentPermalink($react);
    }
}