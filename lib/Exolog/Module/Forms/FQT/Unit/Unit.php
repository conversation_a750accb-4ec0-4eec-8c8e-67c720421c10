<?php


namespace Exolog\Module\Forms\FQT\Unit;


use Exolog\Module\Forms\FQT\FQTBase;
use Exolog\Module\Forms\FQT\JsonValueProvider;
use Exolog\Module\Forms\FQT\ValueProvider;

class Unit extends FQTBase implements UnitValueInterface
{
    use UnitValue;

    public const TYPE_PICTURE = 'picture';
    public const TYPE_CONTAINER = 'container';
    public const TYPE_TEXT = 'text';
    public const TYPE_DATE = 'date';
    public const TYPE_DATETIME = 'datetime';
    public const TYPE_TEXTAREA = 'textarea';

    protected function getValueProvider(): ValueProvider
    {
        return new JsonValueProvider($this->react, $this->fq_name);
    }

    protected function getPaylodValueProvider($key, $type)
    {
        return new PayloadValueProvider($this, $key, $type, function () {
            return [
                'type' => static::HOLDER_TYPE_REACT,
                'id' => $this->react['react_id'],
                'field' => $this->fq_name
            ];
        });
    }

    public function getPayloadHolderId()
    {
        return $this->fq_name;
    }

    public function getHolderId()
    {
        return $this->react['react_id'];
    }

    public function getHolderType()
    {
        return static::HOLDER_TYPE_REACT;
    }

    public function save()
    {
        $this->react->save();
        return $this;
    }

    public function getHolder()
    {
        return $this->react;
    }
}