<?php


namespace Exolog\Module\Forms\FQT\Unit;

use Exolog\Module\Contracts\XEditable;
use Exolog\Module\View\XEditableValue;

class PayloadFieldText extends PayloadField implements XEditable
{
    use XEditableValue;

    protected string $key;

    protected array $exdit =
        [
            'contenttype' => 'tinymce',
            'toolbar' => 'custom'
        ];

    public function toHtml()
    {
        return $this->render(array_merge(['tag' => 'div'], $this->renderParams));
    }

    public function __toString()
    {
        return $this->getData();
    }
}