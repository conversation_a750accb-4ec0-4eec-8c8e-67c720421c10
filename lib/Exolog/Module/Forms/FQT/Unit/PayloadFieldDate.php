<?php


namespace Exolog\Module\Forms\FQT\Unit;

use Carbon\Carbon;
use Exolog\Module\Contracts\XEditable;
use Exolog\Module\Forms\FQT\Date\DateValue;
use Exolog\Module\View\XEditableValue;


class PayloadFieldDate extends PayloadField implements XEditable
{
    use XEditableValue, DateValue;

    protected string $key;

    protected array $exdit = [];
    protected string $store_format;


    public function __construct(PayloadValueProvider $value, $arguments = [])
    {
        parent::__construct($value, $arguments);
        $this->store_format = ECO()->get()['forms']['fqt'][$arguments[1]]['store_format']['php'];
        $this->exdit['format'] = ECO()->get()['forms']['fqt'][$arguments[1]]['store_format']['php'];
        $this->exdit['store_format'] = ECO()->get()['forms']['fqt'][$arguments[1]]['store_format']['flatpicker'];
        $this->exdit['contenttype'] = ECO()->get()['forms']['fqt'][$arguments[1]]['front']['exedit']['contenttype'];
    }

    public function getData()
    {
        $value = parent::getData();
        if (empty($value)) {
            $dt = Carbon::createFromTimestamp(0);
        } else {
            $dt = Carbon::createFromFormat($this->store_format, $value);
        }
        return $dt;
    }

    public function setData($data)
    {
        if ($data instanceof Carbon) {
            $dt = $data->format($this->store_format);
        } else {
            $dt = $data;
        }
        parent::setData($dt);
    }


    protected function getStoreFormat()
    {
        return $this->store_format;
    }
}