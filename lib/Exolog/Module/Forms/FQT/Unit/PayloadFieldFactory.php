<?php


namespace Exolog\Module\Forms\FQT\Unit;


class PayloadFieldFactory
{
    public static function build($type, PayloadValueProvider $valueProvider, $renderParams)
    {
        switch ($type) {
            case Unit::TYPE_CONTAINER:
                return new PayloadFieldContainer(
                    $valueProvider,
                    [$renderParams]
                );
            case Unit::TYPE_PICTURE:
                return new PayloadFieldPicture(
                    $valueProvider,
                    [$renderParams]
                );
            case Unit::TYPE_DATE:
                return new PayloadFieldDate(
                    $valueProvider,
                    [$renderParams, 'date']
                );
            case Unit::TYPE_DATETIME:
                return new PayloadFieldDate(
                    $valueProvider,
                    [$renderParams, 'datetime']
                );
            case Unit::TYPE_TEXTAREA:
                return new PayloadFieldTextarea(
                    $valueProvider,
                    [$renderParams]
                );
            default:
                return new PayloadFieldText(
                    $valueProvider,
                    [$renderParams]
                );
        }
    }
}