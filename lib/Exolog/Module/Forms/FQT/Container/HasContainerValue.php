<?php


namespace Exolog\Module\Forms\FQT\Container;


use ArrayObject;
use Exolog\Module\Container\Container;
use Exolog\Module\Support\Facades\Auth;
use Illuminate\Support\Facades\View;
use Illuminate\Support\HtmlString;
use RuntimeException;

trait HasContainerValue
{
    protected Container $container;

    protected function getRenderData($params = [])
    {
        $result = '';
        $filter = $params['filter'] ?? [];
        $reacts = $this->container->getReactsTree($filter);
        if (empty($params['view'])) {
            throw new RuntimeException("Container render param 'view' is empty!");
        }
        if (empty($reacts)) {
            if (!empty($params['view_empty'])) {
                $result = View::make($params['view_empty'])->render();
            }
        } else {
            foreach ($reacts as $key => $react) {
                $data = [
                    'loop' => new ArrayObject([
                        'index' => $key,
                        'count' => count($reacts),
                        'first' => $key === 0,
                        'last' => count($reacts) - 1 === $key
                    ], ArrayObject::ARRAY_AS_PROPS),
                    'container' => $this->container,
                    'reactsTree' => $reacts
                ];
                $data[$params['as'] ?? 'react'] = $react;
                $content = View::make($params['view'], $data)->render();
                $result = $result . $content;
            }
        }
        return $result;
    }

    public function getItemsTree($filter = [])
    {
        return $this->container->getItemsTree($filter);
    }

    public function getReactsTree($filter = [])
    {
        return $this->container->getReactsTree($filter);
    }

    public function getReactsArray($filter = [])
    {
        return $this->container->getReactsArray($filter);
    }

    public function getExeditAttr($exedit = []): HtmlString
    {
        if (Auth::isAdmin()) {
            return $this->container->getExeditAttr($exedit);
        }
        return new HtmlString();
    }

    public function getContainer()
    {
        return $this->container;
    }

    public function getContainerName()
    {
        return new ContainerName($this->container);
    }
}