<?php


namespace Exolog\Module\Forms\FQT\Container;


use Exolog\Module\Contracts\XEditable;
use Exolog\Module\View\HasRenderValue;
use Exolog\Module\View\XEditableValue;
use Illuminate\Contracts\Support\Htmlable;

class ContainerName implements XEditable, Htmlable
{
    use XEditableValue, HasRenderValue;

    protected \Exolog\Module\Container\Container $container;


    public function __construct(\Exolog\Module\Container\Container $container)
    {
        $this->container = $container;
    }

    public function toHtml()
    {
        return $this->render(array_merge(['tag' => 'div'], $this->renderParams));
    }


    public function getData()
    {
        return $this->container->getConfig('name');
    }

    public function setData($data)
    {
        return $this->container->setConfig($data, 'name');
    }

    protected function buildExedit($exedit = [])
    {
        if (empty($exedit)) {
            $exedit = [];
        }
        return array_merge(
            [
                'type' => 'container_config',
                'id' => $this->container->getContainerId(),
                'field' => 'name',
                'contenttype' => 'tinymce',
                'toolbar' => 'custom'
            ],
            $exedit);
    }
}