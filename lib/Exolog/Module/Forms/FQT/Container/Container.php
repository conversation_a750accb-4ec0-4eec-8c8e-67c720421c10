<?php


namespace Exolog\Module\Forms\FQT\Container;


use Exolog\Core\Forms\React;
use Exolog\Module\Container\BaseContainer;
use Exolog\Module\Container\FormContainerDataProvider;
use Exolog\Module\Contracts\ContainerValue;
use Exolog\Module\Contracts\XEditable;
use Exolog\Module\Forms\FQT\FQTBase;
use Exolog\Module\Forms\FQT\JsonValueProvider;
use Exolog\Module\Forms\FQT\ValueProvider;
use Exolog\Module\Forms\Model\FormQuestion;

class Container extends FQTBase implements XEditable, ContainerValue
{
    use HasContainerValue;

    protected function getValueProvider(): ValueProvider
    {
        return new JsonValueProvider($this->react, $this->fq_name);
    }

    public function __construct(React $react, FormQuestion $fq, array $arguments = [])
    {
        parent::__construct($react, $fq, $arguments);
        $this->container = new BaseContainer(new FormContainerDataProvider($this->value, $react, $fq->fq_name));
    }

    public function toHtml()
    {
        return $this->render(array_merge(['tag' => 'div'], $this->renderParams));
    }


}