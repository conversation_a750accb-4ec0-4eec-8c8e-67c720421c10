<?php


namespace Exolog\Module\Forms\FQT;

use Carbon\Carbon;
use Exolog\Core\Forms\React;

class DateTimeValueProvider extends ValueProvider
{
    private $store_format;

    public function __construct(React $react, $fq_name, $fqt_name)
    {
        parent::__construct($react, $fq_name);
        $this->store_format = ECO()->get()['forms']['fqt'][$fqt_name]['store_format']['php'];
    }

    public function getData()
    {
        $value = parent::getData();
        if ($value instanceof Carbon) {
            $dt = $value;
        } else {
            if (empty($value)) {
                $dt = Carbon::createFromTimestamp(0);
            } else {
                $dt = Carbon::createFromFormat($this->store_format, $value);
            }
            $this->setData($dt);
        }
        return $dt;
    }

    public function setData($data)
    {
        if (is_string($data)) {
            $dt = Carbon::parse($data);
        } else {
            $dt = $data;
        }
        parent::setData($dt);
    }
}