<?php


namespace Exolog\Module\Forms\FQT\Text;


use Exolog\Module\Contracts\XEditable;
use Exolog\Module\Forms\FQT\FQTBase;
use Exolog\Module\View\XEditableValue;

class Text extends FQTBase implements XEditable
{
    use XEditableValue;

    protected array $exdit =
        [
            'contenttype' => 'tinymce',
            'toolbar' => 'custom'
        ];

    public function toHtml()
    {
        //prepare for push script 
        //view('system.push-script', ['script' => '<script src="" crossorigin="anonymous"></script>'])->render();
        return $this->render(array_merge(['tag' => 'div'], $this->renderParams));
    }

    protected function getRenderData($params = [])
    {
        return $this->getData() ?? $params['default'] ?? null;
    }
}