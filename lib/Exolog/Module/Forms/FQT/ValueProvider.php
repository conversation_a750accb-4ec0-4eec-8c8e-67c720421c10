<?php


namespace Exolog\Module\Forms\FQT;


use Exolog\Core\Forms\React;
use Exolog\Module\Contracts\EntityValueProvider;

class ValueProvider implements EntityValueProvider
{

    protected React $react;
    protected string $fq_name;

    public function __construct(React $react, $fq_name)
    {
        $this->react = $react;
        $this->fq_name = $fq_name;
    }

    public function getData()
    {
        return $this->react[$this->fq_name];
    }

    public function setData($data)
    {
        $this->react[$this->fq_name] = $data;
    }
}