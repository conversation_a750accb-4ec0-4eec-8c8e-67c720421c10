<?php


namespace Exolog\Module\Forms\FQT\Mailable;


use Exception;
use Exolog\Core\Forms\React;
use Exolog\Module\Events\FluentExoEvent;
use Exolog\Module\Forms\FQT\FQTBase;
use Exolog\Module\Forms\FQT\JsonValueProvider;
use Exolog\Module\Forms\FQT\ValueProvider;
use Exolog\Module\Forms\Model\Form;
use Exolog\Module\Forms\Model\FormQuestion;
use Exolog\Module\Mails\ReactMail;
use Exolog\Module\Support\Facades\Auth;
use Exolog\Module\Support\Facades\Defaults;
use Exolog\Module\Support\Facades\ExoFile;
use Exolog\Module\Support\Facades\Site;
use Exolog\Module\Users\Model\User;
use Exolog\Module\Users\Model\Usergroup;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use RuntimeException;

class Mailable extends FQTBase
{
    private SendLog $sendLog;

    public function view(array $data = [])
    {
        $this->setupEVars([], true);
        return view($this->getView(), array_merge($this->defaultViewData(), $data));
    }

    protected function getValueProvider(): ValueProvider
    {
        return new JsonValueProvider($this->react, $this->fq_name);
    }

    public function render($params)
    {
        throw new RuntimeException('Not implemented yet!');
    }

    public function __toString()
    {
        return $this->getData();
    }


    /**
     * @throws Exception
     */
    public function send($params): Mailable
    {
        $this->resetLog();

        $params = array_merge([
            'fromEmail' => $this->getFrom(),
            'fromName' => $this->getFromName(),
            'subject' => $this->getSubject(),
        ], $params);

        foreach (Arr::wrapNull($params['toemail']) as $to) {

            if (!empty($to) && !Str::contains($to, '@')) {
                if (Str::start($to, 'fq_id:')) {
                    $fq_id = Str::after($to, 'fq_id:');
                    $fq = FormQuestion::findOrFail($fq_id);
                    $to = React::query()
                        ->form($fq->fq_form)
                        ->applyQBRules($params['QBRules'] ?? [])
                        ->get()
                        ->filter(function ($react) use ($fq) {
                            return !empty($react[$fq->fq_name]);
                        })
                        ->map(function ($react) use ($fq) {
                            return [
                                'email' => $react[$fq->fq_name],
                                'editor_vars' => [
                                    'react_id' => $react['react_id']
                                ]
                            ];
                        })
                        ->toArray();
                } else {
                    if (is_numeric($to)) {
                        $ug = Usergroup::find($to);
                    } else {
                        $ug = Usergroup::findByName($to, true);
                    }
                    if ($ug === null) {
                        throw new RuntimeException("Usergroup '$to' not found");
                    }
                    $to = $ug->users->pluck('u_email')->toArray();
                }
            }

            foreach (Arr::wrapNull($to) as $recipient) {
                try {
                    if (is_array($recipient)) {
                        if (!array_key_exists('email', $recipient)) {
                            throw new RuntimeException("Recipient array must have 'email' key");
                        }
                        $mail_params = $params;
                        $mail_params['editor_vars'] = array_merge($params['editor_vars'] ?? [], $recipient['editor_vars'] ?? []);
                        $this->sendToRecipient($recipient['email'], $mail_params);
                    } else {
                        $this->sendToRecipient($recipient, $params);
                    }
                    $this->sendLog->incrementSent();
                } catch (Exception $exception) {
                    $message = $exception->getMessage();
                    $prev = $exception->getPrevious();
                    if ($prev) {
                        $message = ' ' . $prev->getMessage();
                    }
                    $this->sendLog->incrementError();
                    $this->sendLog->pushError("Recipient:$recipient, error:$message");
                }
            }
        }
        return $this;
    }

    public function getSendLog(): SendLog
    {
        return $this->sendLog;
    }

    private function sendToRecipient($recipient, $params): void
    {
        $form_name = $this->react->getReactForm()->form_name;
        $react_id = $this->react['react_id'];

        $message = new ReactMail();

        $message->to($recipient);
        $message->from($params['fromEmail'], $params['fromName']);
        $message->subject($params['subject']);

        xEvent("MailBeforeSend:$form_name:$react_id",
            new FluentExoEvent([
                'message' => $message,
                'params' => $params,
                'form_name' => $form_name,
                'react_id' => $react_id
            ]));

        $recipient = data_get($message->to, '0.address', $recipient);

        if (empty($recipient)) {
            return;
        }

        $user = User::findByEmail($recipient);
        if ($user) {
            $params['editor_vars']['u_id'] = $user['u_id'];
        }
        $this->setupEVars($params['editor_vars']);

        $message->view($this->getView());
        $message->viewData = $this->defaultViewData();

        foreach ($params['attachments'] as $item) {
            $path = ExoFile::path($item);
            $message->attach($path, [
                    'as' => ExoFile::basename($item)
                ]
            );
        }

        Mail::send($message);
    }

    public function getView()
    {
        if (empty($this->getData()['view'])) {
            throw new RuntimeException(sprintf(
                "View is not set. react_id: %s", $this->react['react_id']));
        }
        return $this->getData()['view'];
    }

    public function getFrom()
    {
        return dv($this->getData()['fromEmail'], Defaults::get('mailconf')['fromEmail']);
    }

    public function getFromName()
    {
        return dv($this->getData()['fromName'], Defaults::get('mailconf')['fromName']);
    }

    public function getSubject()
    {
        return dv($this->getData()['subject'], sprintf('Mail from %s', Site::get('site_name')));
    }


    private function setupEVars($editor_vars, $isPreview = false)
    {
        ev()->clear();

        if ($isPreview) {
            $this->setupRequestEVars();
        }

        foreach ($editor_vars as $key => $value) {
            if ($key === 'react_id') {
                $this->addReactEVars($value);
            } elseif ($key === 'u_id') {
                $this->addUserEVars($value);
            } else {
                ev($key, $value);
            }

        }
    }

    private function setupRequestEVars(): void
    {
        if (request()->has('form_id')) {
            $form = Form::find(request('form_id'));
            if ($form) {
                $form->form_questions->each(function ($fq) {
                    ev('REACT_' . $fq->fq_name, $fq->fq_name);
                });
                ev('REACT_CONFIRMSTRING', 'REACT_CONFIRMSTRING');
            }
        }

        if (Auth::id()) {
            $this->addUserEVars(Auth::id());
        }

        if (request()->has('react_id')) {
            $this->addReactEVars(request('react_id'));
        }
    }

    private function addReactEVars($react_id): void
    {
        $react = React::find($react_id);
        if ($react) {
            $form = $react->getReactForm();
            $form->form_questions->each(function ($fq) use ($react) {
                ev('REACT_' . $fq->fq_name, $react[$fq->fq_name]);
            });

            ev('REACT_CONFIRMSTRING', $react['react_confirmstring']);

        }
    }

    private function addUserEVars($u_id): void
    {
        $user = User::find($u_id);
        if ($user) {
            collect($user->getAttributes())
                ->only(['u_email', 'u_first_name', 'u_last_name', 'u_newsletter_confirmstring'])
                ->each(function ($value, $key) use ($user) {
                    ev($key, $value);
                });
        }
    }

    private function defaultViewData(): array
    {
        return ['page' => $this->react];
    }

    protected function resetLog(): void
    {
        $this->sendLog = new SendLog();
    }
}