<?php

namespace Exolog\Module\Forms\FQT\Mailable;

use Illuminate\Contracts\Support\Arrayable;
use JsonSerializable;

class SendLog implements Arrayable, JsonSerializable
{

    protected int $sent_count = 0;
    protected int $error_count = 0;
    protected array $errors = [];

    public function __construct()
    {
    }

    public function setSentCount(int $sent_count): SendLog
    {
        $this->sent_count = $sent_count;
        return $this;
    }


    public function getSentCount(): int
    {
        return $this->sent_count;
    }

    public function setErrorCount(int $error_count): SendLog
    {
        $this->error_count = $error_count;
        return $this;
    }

    public function getErrorCount(): int
    {
        return $this->error_count;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }


    public function pushError($error): SendLog
    {
        $this->errors[] = $error;
        return $this;
    }

    public function incrementSent(): SendLog
    {
        $this->sent_count++;
        return $this;
    }


    public function incrementError(): SendLog
    {
        $this->error_count++;
        return $this;
    }

    public function toArray(): array
    {
        return [
            'sent_count' => $this->sent_count,
            'error_count' => $this->error_count,
            'errors' => $this->errors,
        ];
    }

    public function jsonSerialize()
    {
        return $this->toArray();
    }
}