<?php

namespace Exolog\Module\Forms;

use Closure;
use Exolog\Core\Forms\React;
use Illuminate\Pipeline\Pipeline;
use RuntimeException;

class ReactPipeline extends FormPipeline
{
    private FormResponseFF $formResponse;


    protected function handleCarry($carry)
    {
        if ($carry instanceof React) {
            $reactSaver = $this->resolveSaveHandler();
            $carry = $reactSaver($carry) ?? $carry;

            //serialize form response with react_id to session in case if user will be redirected in the afterSave
            $this->formResponse->saveToSession();

            if ($carry instanceof React) {
                if ($handler = $this->getFormProcessor()->getHandler()) {
                    $carry = $handler->afterSave($carry) ?? $carry;
                }
            }

            if ($carry instanceof React) {
                $responseHandler = $this->resolveResponseHandler();
                $carry = $responseHandler($carry);

                if (is_null($carry)) {
                    throw new RuntimeException('Response is null. Please return valid \Response');
                }
            }
        }
        return $carry;
    }

    public function run(React $react)
    {
        $this->formResponse = new FormResponseFF($react, $this->getFormProcessor()->getRequest()->formconf['response']);

        return app(Pipeline::class)
            ->send($react)
            ->through($this->loadMiddleware('react', $react->getReactForm()->form_name))
            ->then(function ($carry) {
                return $this->handleCarry($carry);
            });
    }

    protected function getFormProcessor(): FormProcessor
    {
        return app(FormProcessor::class);
    }

    protected function resolveSaveHandler(): Closure
    {
        $reactSaver = null;
        if ($handler = $this->getFormProcessor()->getHandler()) {
            $reactSaver = $handler->resolveSaveHandler();
        }
        return $reactSaver ?? $this->saveHandlerDefault();
    }

    protected function saveHandlerDefault(): Closure
    {
        return static function (React $react) {
            $react->save();
            return $react;
        };
    }

    protected function resolveResponseHandler(): Closure
    {
        $responseHandler = null;
        if ($handler = $this->getFormProcessor()->getHandler()) {
            $responseHandler = $handler->resolveResponseHandler();
        }
        return $responseHandler ?? $this->responseHandlerDefault();
    }

    protected function responseHandlerDefault(): Closure
    {
        return function ($react) {
            return $this->formResponse->perform();
        };
    }
}