<?php

namespace Exolog\Module\Forms\Validations;

use Illuminate\Contracts\Validation\Rule;

class UniqueReact implements Rule
{

    private string $form;
    private ?string $field;

    public function __construct($form, $field = null)
    {
        $this->form = $form;
        $this->field = $field;
    }

    public function passes($attribute, $value)
    {
        return react()->form($this->form)->where($this->field ?? $attribute, trim($value))->count() === 0;
    }

    public function message()
    {
        return trans('validation.unique');
    }
}