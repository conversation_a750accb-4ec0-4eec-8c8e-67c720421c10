<?php

namespace Exolog\Module\Forms\Validations;

use Illuminate\Support\Arr;
use Illuminate\Validation\ValidationException;

class FormValidationException extends ValidationException
{
    public static function withMessages(array $messages): self
    {
        $errorBag = data_get(request('formconf'), 'errorBag', 'default');
        return parent::withMessages($messages)->errorBag($errorBag);
    }

    public static function withMessage($message): self
    {
        return static::withMessages(Arr::wrap($message));
    }
}