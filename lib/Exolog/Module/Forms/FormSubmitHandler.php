<?php

namespace Exolog\Module\Forms;

use Closure;
use Exolog\Core\Forms\React;
use Exolog\Module\Forms\Requests\ReactSubmitRequest;
use Illuminate\Contracts\Support\Responsable;

abstract class FormSubmitHandler
{
    protected function getFormProcessor(): FormProcessor
    {
        return app(FormProcessor::class);
    }

    public function getFormRequest(): ReactSubmitRequest
    {
        return $this->getFormProcessor()->getRequest();
    }

    /**
     *  Perform action with FormRequest BEFORE validation.
     *
     *  Return $request|null to continue processing
     *  or return custom response and interrupt processing
     *   return redirect(url) or
     *   return view('name')  or
     *   return response(['message'=>'error']) for AJAX or any other laravel response.
     *
     *  Interrupt processing and show error on frontend form
     *  throw FormValidationException::withMessage('Something go wrong!');
     *  throw FormValidationException::withMessages(['Something go wrong!','Error data']);
     *
     * @param ReactSubmitRequest $request
     * @return null|mixed|ReactSubmitRequest|Responsable
     */
    public function beforeValidation(ReactSubmitRequest $request)
    {
        return null;
    }

    /**
     *  Perform action with validated data AFTER validation.
     *
     *  Return $array|null to continue processing
     *  or return custom response and interrupt processing
     *   return redirect(url) or
     *   return view('name')  or
     *   return response('['array']')
     *
     *  Interrupt processing and show error on frontend form
     *  throw FormValidationException::withMessage('Something go wrong!');
     *  throw FormValidationException::withMessages(['Something go wrong!','Error data']);
     *
     **/
    public function afterValidation(array $formData)
    {
        return null;
    }


    /**
     *  Perform action with react BEFORE save.
     *
     *  Return $react|null to continue processing
     *  or return custom response and interrupt processing
     *   return redirect(url) or
     *   return view('name')  or
     *   return response('['array']')
     *
     *  Interrupt processing and show error on frontend form
     *  throw FormValidationException::withMessage('Something go wrong!');
     *  throw FormValidationException::withMessages(['Something go wrong!','Error data']);
     *
     * @param React $react
     * @return null|React|mixed
     */
    public function beforeSave(React $react)
    {
        return null;
    }

    /**
     *  Custom handler for save react and custom response.
     *  Return Closure to take control over save react and response
     *
     * From "Closure" you can return null to continue and allow form processor perform default response from form,
     * or you can return any valid Laravel response
     *
     **/
    public function resolveSaveHandler(): ?Closure
    {
        /*
        return static function (\Exolog\Core\Forms\React $react) {
            $react->save();
            return redirect()->back()
        };
        */
        return null;
    }

    /**
     *  Perform action with react AFTER save react.
     *  This handler is not called if you use the custom save handler getReactSaveHandler
     *
     *  Return $react|null to continue processing
     *  or return custom response and interrupt processing
     *   return redirect(url) or
     *   return view('name')  or
     *   return response('['array']')
     *
     * @param React $react
     * @return null|React|mixed
     */
    public function afterSave(React $react)
    {
        return null;
    }

    /**
     *  Handler for return custom response.
     *  Return Closure to take control over get/calc response
     *  or return null to continue default flow.
     *
     * From "Closure" you must return response,
     * you can return any valid Laravel response
     *
     **/
    public function resolveResponseHandler(): ?Closure
    {
        /*
        return static function (\Exolog\Core\Forms\React $react) {
            return redirect('/thank-you')
        };
        */
        return null;
    }

    /**
     *  Perform action after response.
     *
     *  Return null to continue processing
     *  or return custom response
     *   return redirect(url) or
     *   return view('name')  or
     *   return response('['array']')
     *
     * @param React $react
     * @return null|React|mixed
     */
    public function afterResponse($response)
    {
        return null;
    }
}