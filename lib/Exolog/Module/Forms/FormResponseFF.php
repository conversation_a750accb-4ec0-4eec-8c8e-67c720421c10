<?php

namespace Exolog\Module\Forms;

use Exolog\Core\Forms\React;
use Exolog\Module\Permalinks\PermalinkUrlGenerator;

class FormResponseFF extends FormResponse
{

    private React $react;
    private string $form_response_name;

    public function __construct(React $react, $form_response_name = null)
    {
        $this->react = $react;
        $this->form_response_name = dv($form_response_name, 'response');
    }

    public function perform()
    {
        static::clearSession();

        //get fresh react to get last actual values
        $react = React::withTrashed()->find($this->react->id());

        if (is_null($react)) {
            $react = $this->react;
            $react['react_isdeleted'] = 1;
        }

        $this->init($this->react->getReactFormId(), $this->form_response_name);

        $this->performMails($react);

        return $this->performRedirects($react);
    }

    private function performMails($react): void
    {
        foreach ($this->releted_reacts as $related_react) {

            /** @var React $related_react */
            $related_react = $related_react['react'];

            if (!$related_react->isMailable()) {
                continue;
            }

            $rules = $this->responseRules->getRelatedReactRules(
                $related_react->id(),
                'mail_rules'
            );

            //check condition
            if (is_array($rules) && !empty($rules['rule_if'])) {
                $is_send = ActionRuleCalculator::make()->calc($rules,
                    $react,
                    array_merge(['react_id' => $react->id()], $this->react->getRawOriginal()),
                    $react['react_isdeleted'] !== $this->react->getRawOriginal('react_isdeleted'));

                if (!$is_send) {
                    continue;
                }

            }

            $this->sendMailable($related_react, $this->react, $rules);
        }
    }

    private function performRedirects(React $react)
    {
        foreach ($this->releted_reacts as $related_react) {

            /** @var React $related_react */
            $related_react = $related_react['react'];

            $rules = $this->responseRules->getRelatedReactRules(
                $related_react->id(),
                'redir_rules'
            );

            if (!is_array($rules) || !$rules['rule_use_for_redir']) {
                continue;
            }


            $redir = $related_react->getPermalink()->url;

            if (empty($redir)) {
                continue;
            }

            //check conditions
            if (!empty($rules['rule_if'])) {
                $is_redir = ActionRuleCalculator::make()->calc($rules,
                    $react,
                    array_merge(['react_id' => $react->id()], $this->react->getRawOriginal()),
                    $react['react_isdeleted'] !== $this->react->getRawOriginal('react_isdeleted')
                );
                if (!$is_redir) {
                    continue;
                }
            }

            if (!empty($rules['rule_redir_param'])) {
                $redir .= $rules['rule_redir_param'];
                $redir = PermalinkUrlGenerator::make()->buildURL($redir, $react->toArray());
            }

            return redirect($redir);
        }

        return request()->expectsJson() ?
            response()->json(['message' => trans('ff.common.response')]) :
            redirect()->back()->with('ff_message', trans('ff.common.response'));
    }
}