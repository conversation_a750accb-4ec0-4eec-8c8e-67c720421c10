<?php

namespace Exolog\Module\Database;

use Illuminate\Database\DatabaseServiceProvider as IlluminateDatabaseServiceProvider;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;


class DatabaseServiceProvider extends IlluminateDatabaseServiceProvider
{
    public function boot()
    {
        parent::boot();

        //TODO move to class
        //or add as scope and builder extension(we can add phpdoc in the trait)
        Builder::macro('vueTablePaginate', function ($params = []) {
            /** @var Builder $this */

            if (empty($params)) {
                $params = request()->only([
                    'query',
                    'limit',
                    'page',
                    'orderBy',
                    'ascending',
                    'filterableColumns'
                ]);
            }


            if ($params['query']) {
                if (is_array($params['query'])) {
                    $this->filterByColumn($params['query']);
                } else {
                    $this->filter($params['query'], $params['filterableColumns']);
                }
            }

            if ($params['orderBy']) {
                $direction = $params['ascending'] ? 'ASC' : 'DESC';
                $this->orderBy($params['orderBy'], $direction);
            }

            if (request('pagination', true)) {
                if (!empty($params['limit'])) {
                    if (empty($params['page'])) {
                        $params['page'] = 1;
                    }

                    $count = $this->count();

                    $this->limit($params['limit'])
                        ->skip($params['limit'] * ($params['page'] - 1));
                } else {
                    $count = null;
                }
            }

            $results = $this->get()->toArray();

            return request('pagination', true) ? [
                'data' => $results,
                'count' => $count ?? count($results),
            ] : $results;
        }
        );

        Builder::macro('filterByColumn', function ($queries) {
            /** @var Builder $this */
            return $this->where(function ($q) use ($queries) {
                foreach ($queries as $field => $query) {
                    if (is_array($query)) {
                        $start = Carbon::createFromFormat('Y-m-d', $query['start'])->startOfDay();
                        $end = Carbon::createFromFormat('Y-m-d', $query['end'])->endOfDay();
                        $q->whereBetween($field, [$start, $end]);
                    } else {
                        //TODO search exact by default
                        $q->where($field, 'LIKE', "%{$query}%");
                    }
                }
            });
        });

        Builder::macro('filter', function ($query, $fields) {
            /** @var Builder $this */
            return $this->where(function ($q) use ($query, $fields) {
                foreach ($fields as $index => $field) {
                    if (Str::contains($field, '.')) {
                        $method = $index ? 'orWhereHas' : 'whereHas';
                        $q->{$method}(explode('.', $field)[0], function ($q) use ($field, $query) {
                            $q->where(explode('.', $field)[1], 'LIKE', "%{$query}%");
                        });
                    } else {
                        $method = $index ? 'orWhere' : 'where';
                        $q->{$method}($field, 'LIKE', "%{$query}%");
                    }

                }
            });
        }
        );
    }
}