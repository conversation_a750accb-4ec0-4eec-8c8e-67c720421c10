<?php

namespace Exolog\Module\Database\Scopes;

use Exolog\Module\Support\Facades\Site;
use Illuminate\Database\Eloquent\Model;
use RuntimeException;

class SiteScopeSavingObserver
{
    public static function apply(string $siteKey)
    {
        if (empty($siteKey)) {
            throw new RuntimeException('"Site key" is not defined!');
        }
        return static function (Model $model) use ($siteKey) {
            if (empty($model[$siteKey])) {
                $model[$siteKey] = Site::id();
            }
        };
    }
}