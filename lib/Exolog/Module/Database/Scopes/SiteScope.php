<?php


namespace Exolog\Module\Database\Scopes;


use Exolog\Module\Support\Facades\Site;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use RuntimeException;

class SiteScope implements Scope
{
    private $siteKey;

    public function __construct($siteKey)
    {
        if (empty($siteKey)) {
            throw new RuntimeException('"siteKey" is not defined!');
        }
        $this->siteKey = $siteKey;
    }

    /**
     * Apply the scope to a given Eloquent query builder.
     *
     * @param Builder $builder
     * @param Model $model
     * @return void
     */
    public function apply(Builder $builder, Model $model)
    {
        $builder->where($this->siteKey, Site::id());
    }
}