<?php

namespace Exolog\Module\Database\Concerns;

use Exolog\Module\Database\Scopes\SiteScope;
use Exolog\Module\Database\Scopes\SiteScopeSavingObserver;
use Illuminate\Database\Eloquent\Builder;

trait HasSiteScope
{
    protected static function bootHasSiteScope()
    {
        $key = static::SITE_FIELD;
        static::addGlobalScope(new SiteScope($key));
        static::saving(SiteScopeSavingObserver::apply($key));
    }

    public static function crossSiteQuery(): Builder
    {
        return static::withoutGlobalScope(SiteScope::class);
    }
}