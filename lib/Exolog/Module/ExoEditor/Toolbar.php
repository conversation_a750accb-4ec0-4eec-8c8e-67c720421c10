<?php


namespace Exolog\Module\ExoEditor;

use Exolog\Module\Range\Range;
use Exolog\Module\Support\Facades\Auth;
use Exolog\Module\Support\Facades\Defaults;

class Toolbar
{
    public static function get()
    {
        if (request('noeditbar') || Defaults::get('noeditbar')) {
            return '';
        }

        $jsToolbar = ['content' => '', 'vars' => []];

        if (Auth::isAdmin()) {

            ev()->prepare();

            $react = Defaults::getCurrentReact();

            $jsToolbar['vars']['editor_var'] = ev()->all();
            $jsToolbar['vars']['react_id'] = $react ? $react['react_id'] : null;
            if ($react) {
                $jsToolbar['vars']['publish'] = $react->hasField('publish') ? (bool)$react['publish'] : null;
            } else {
                $jsToolbar['vars']['publish'] = null;
            }
            $jsToolbar['vars']['mailable'] = Defaults::getCurrentReact() && Defaults::getCurrentReact()->isMailable();
            $jsToolbar['vars']['page_id'] = 0;

            $jsToolbar['content'] .= '<meta name="exologEditor" content="true" />';
            $jsToolbar['content'] .= static::getScripts();
            $jsToolbar['content'] .= '<script>var ExologEditorParams = ' . json_encode($jsToolbar['vars']) . '</script>';

        } else {
            $jsToolbar['content'] .= static::getScripts();
        }
        return $jsToolbar['content'];
    }

    public static function getScripts($defered = false)
    {
        return sprintf(
            '<link rel="stylesheet" href="/exo_editor/exolog-loader.css">' .
            '<script %s src="/exo_editor/exolog-loader.js"></script>',
            $defered ? 'async defer' : '');
    }

}
