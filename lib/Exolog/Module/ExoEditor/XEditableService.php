<?php

namespace Exolog\Module\ExoEditor;

use Exolog\Core\Forms\React;
use Exolog\Module\Container\ContainerFactory;
use Exolog\Module\Forms\FQT\Json\Json;
use Exolog\Module\Forms\FQT\Unit\Unit;
use Exolog\Module\Forms\Model\Form;
use Exolog\Module\Support\Facades\Vars;
use Exolog\Module\Vars\VariablePicture;
use Exolog\Module\Vars\VariableUnit;
use InvalidArgumentException;
use RuntimeException;

class XEditableService
{
    //TODO move me to ECO
    public const EDITTOR_XEDITABLE = 'xeditable';
    public const EDITTOR_MCE = 'mce';
    public const EDITTOR_FILE = 'file';

    private array $cacheReacts = [];

    private $contentTypes = [
        'text',
        'textarea',
        'tinymce',
        'file',
        'picture',
        'date',
        'datetime',
        'select',
        'radio',
        'checkbox',
    ];

    public function prepare(array $elements): array
    {
        $result = [];

        foreach ($elements as $elementId => $element) {
            $exedit = $element['exedit'];

            if ($exedit['type'] === 'react') {

                $react = $this->getReact($exedit['id']);

                if ($react === null) {
                    throw new RuntimeException('React not found! react_id=' . $exedit['id']);
                }

                $fieldName = $exedit['field'];
                $field = $react->getFieldValue($fieldName);

                //set value
                if (!array_key_exists('value', $exedit)) {
                    $exedit['value'] = $field->value();
                }
                $exedit['formId'] = $react['react_form'];


                $exedit['title'] = $field->getFQ()->fq_displayname;


                if ($field->getFQ()->isMultiple()) {
                    $exedit['multiple'] = true;
                }

                if ($field->getFQ()->getCustomParam('allowAddNew')) {
                    $exedit['allowAddNew'] = true;
                }

                if ($field->getFQ()->getCustomParam('folderBack')) {
                    $exedit['folderBack'] = true;
                }

                if ($exedit['contenttype'] === 'select') {
                    foreach ($field->getFQ()->getValues() as $id => $value) {
                        $exedit['source'][] = ['id' => $id, 'text' => $value];
                    }
                }
            }

            $result[$elementId] = [
                'exedit' => $exedit,
                'editor' => $this->getEditorByContentType($exedit['contenttype'])
            ];
        }


        return $result;
    }

    public function handle($value, array $exedit): array
    {
        $type = $exedit['type'];
        $field = $exedit['field'];
        $id = $exedit['id'];
        $contenttype = $exedit['contenttype'];

        if ($contenttype === 'checklist') {
            $value = !empty($value) ? 1 : 0;
        }

        if (empty($type)) {
            throw new InvalidArgumentException('Type is required! Please check exedit attribute.');
        }

        if (empty($field) && in_array($type, ['edition', 'page', 'form', 'react'])) {
            throw new InvalidArgumentException('Field is required! Please check exedit attribute.');
        }

        if (empty($id)) {
            throw new InvalidArgumentException('"id" is required! Please check exedit attribute.');
        }

        if ($exedit['eventtmpl']) {
            throw new InvalidArgumentException("Attribute 'eventtmpl' is deprecated and  unsupported!");
        }

        switch ($type) {
            case 'edition':
                throw new RuntimeException('Not implemented yet!');
            case 'form':
                /** @var Form $form */
                $form = Form::findOrFail($id);
                if (!empty($exedit['label'])) {
                    $fq = $form->getFQ($field);
                    if ($fq === null) {
                        throw new InvalidArgumentException('Form field not found or forbidden.');
                    }
                    $fq->fq_displayname = html_entity_decode($value);
                    $fq->save();
                } else {
                    throw new InvalidArgumentException("Deprecated! Use type 'var'");
                }
                break;
            case 'react':
                $react = react()->find($id);
                if ($react === null) {
                    throw new InvalidArgumentException('React not found.');
                }

                if (!empty($exedit['label'])) {
                    throw new InvalidArgumentException("Deprecated! Use type 'form'");
                }

                $fq = $react->getReactForm()->getFQ($field);
                if ($fq === null) {
                    throw new InvalidArgumentException('Form field not found or forbidden.');
                }

                if ($fq->getTypeName() === 'picture') {
                    $react->getFieldValue($field)->setImage($value);
                    $value = $react->getFieldValue($field)->getURL();
                } elseif (in_array($fq->getTypeName(), ['date', 'datetime'])) {
                    $react->setFieldValue($field, $value);
                    //TODO the same for unit date/time
                    $renderParams = [
                        'exedit' => false,
                        'locale' => $exedit['locale'],
                        'format' => $exedit['format'],
                        'formatFunction' => $exedit['formatFunction']
                    ];
                    $value = $react->getFieldValue($field)->render($renderParams);
                } elseif ($fq->getTypeName() === 'unit') {
                    $key = $exedit['key'];
                    if (empty($key)) {
                        throw new InvalidArgumentException('Unit payload key is empty.');
                    }
                    //todo replace with setEditorValue
                    if ($exedit['contenttype'] === Unit::TYPE_PICTURE) {
                        $payloadField = $react->getFieldValue($field)->getPayload([
                            'key' => $key,
                            'type' => Unit::TYPE_PICTURE
                        ]);
                        $payloadField->setImage($value);
                        $value = $payloadField->getURL();
                    } else {
                        $payloadField = $react->getFieldValue($field)->getPayload($key);
                        $payloadField->setData($value);
                    }
                } elseif ($fq->getTypeName() === 'json') {
                    $property = $exedit['property'];
                    if (empty($property)) {
                        throw new InvalidArgumentException('Json key is empty.');
                    }
                    //todo replace with setEditorValue
                    if ($exedit['contenttype'] === Json::TYPE_PICTURE) {
                        $payloadField = $react->getFieldValue($field)->property([
                            'property' => $property,
                            'type' => Json::TYPE_PICTURE
                        ]);
                        $payloadField->setImage($value);
                        $value = $payloadField->getURL();
                    } else {
                        $property_type = null;
                        if ($exedit['property_type']) {
                            $property = [
                                'property' => $property,
                                'type' => $exedit['property_type']
                            ];
                        }
                        $payloadField = $react->getFieldValue($field)->property($property, $property_type);
                        $payloadField->setData($value);
                        $value = $payloadField->getData();
                    }
                } else {
                    $react->setFieldValue($field, $value);
                }
                $react->save();
                break;
            case 'var':
                $var = Vars::getById($id);
                if ($var instanceof VariablePicture) {
                    $var->setImage($value);
                    $value = $var->getURL();
                } elseif ($var instanceof VariableUnit) {
                    $key = $exedit['key'];
                    if (empty($key)) {
                        throw new InvalidArgumentException('Unit payload key is empty.');
                    }
                    //todo replace with setEditorValue
                    if ($exedit['contenttype'] === Unit::TYPE_PICTURE) {
                        $payloadField = $var->getPayload(['key' => $key, 'type' => Unit::TYPE_PICTURE]);
                        $payloadField->setImage($value);
                        $value = $payloadField->getURL();
                    } else {
                        $payloadField = $var->getPayload($key);
                        $payloadField->setData($value);
                    }
                } else {
                    $var->setValue($value);
                }
                $var->save();
                break;
            case 'container_config':
                $value = strip_tags($value);
                $container = ContainerFactory::resolve($id);
                $container->setConfig($value, 'name')->save();
                break;
            default:
                throw new InvalidArgumentException('Type is not supported.');
        }

        exolog()->emptyCache();

        return ['value' => $value];

    }

    private function getReact($react_id): ?React
    {
        if (array_key_exists($react_id, $this->cacheReacts)) {
            return $this->cacheReacts[$react_id];
        }
        $react = react()->find($react_id);
        $this->cacheReacts[$react_id] = $react;
        return $react;
    }

    private function getEditorByContentType($contentType): string
    {
        //todo move to ECO
        switch ($contentType) {
            case $contentType === 'picture':
            case $contentType === 'file':
                return static::EDITTOR_FILE;
            case $contentType === 'date':
            case $contentType === 'datetime':
            case $contentType === 'select':
            case $contentType === 'radio':
            case $contentType === 'checkbox':
            case $contentType === 'checklist':
            case $contentType === 'text':
            case $contentType === 'number':
                return static::EDITTOR_XEDITABLE;
            default:
                return static::EDITTOR_MCE;
        }
    }


}