<?php

namespace Tests\Unit\Mail;

use Exception;
use Exolog\Module\Mail\PosteApiService;
use Exolog\Module\Mail\PosteMailboxService;
use Illuminate\Support\Facades\Log;
use Mockery;
use PHPUnit\Framework\TestCase;

class PosteMailboxServiceTest extends TestCase
{
    private PosteMailboxService $service;
    private $mockPosteService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->mockPosteService = Mockery::mock(PosteApiService::class);
        $this->service = new PosteMailboxService();
        
        // Use reflection to inject the mock service
        $reflection = new \ReflectionClass($this->service);
        $serviceProperty = $reflection->getProperty('posteService');
        $serviceProperty->setAccessible(true);
        $serviceProperty->setValue($this->service, $this->mockPosteService);
        
        // Mock Log facade
        Log::shouldReceive('info')->byDefault();
        Log::shouldReceive('error')->byDefault();
        Log::shouldReceive('warning')->byDefault();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function testCreateMailboxSuccess()
    {
        $email = '<EMAIL>';
        $name = 'Test User';
        $password = 'password123';
        $destinations = ['<EMAIL>'];
        $options = ['quota' => 1000];
        
        $this->mockPosteService
            ->shouldReceive('addMailbox')
            ->once()
            ->with($email, $password, $name, $options)
            ->andReturn(true);

        $result = $this->service->createMailbox($email, $name, $password, $destinations, $options);
        
        $this->assertTrue($result);
    }

    public function testCreateMailboxFailure()
    {
        $email = '<EMAIL>';
        $name = 'Test User';
        $password = 'password123';
        
        $this->mockPosteService
            ->shouldReceive('addMailbox')
            ->once()
            ->andThrow(new Exception('API Error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('API Error');
        
        $this->service->createMailbox($email, $name, $password);
    }

    public function testDeleteMailboxSuccess()
    {
        $email = '<EMAIL>';
        
        $this->mockPosteService
            ->shouldReceive('removeMailbox')
            ->once()
            ->with($email)
            ->andReturn(true);

        $result = $this->service->deleteMailbox($email);
        
        $this->assertTrue($result);
    }

    public function testDeleteMailboxFailure()
    {
        $email = '<EMAIL>';
        
        $this->mockPosteService
            ->shouldReceive('removeMailbox')
            ->once()
            ->andThrow(new Exception('API Error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('API Error');
        
        $this->service->deleteMailbox($email);
    }

    public function testIsExistMailboxTrue()
    {
        $email = '<EMAIL>';
        
        $this->mockPosteService
            ->shouldReceive('mailboxExists')
            ->once()
            ->with($email)
            ->andReturn(true);

        $result = $this->service->isExistMailbox($email);
        
        $this->assertTrue($result);
    }

    public function testIsExistMailboxFalse()
    {
        $email = '<EMAIL>';
        
        $this->mockPosteService
            ->shouldReceive('mailboxExists')
            ->once()
            ->with($email)
            ->andReturn(false);

        $result = $this->service->isExistMailbox($email);
        
        $this->assertFalse($result);
    }

    public function testIsExistMailboxException()
    {
        $email = '<EMAIL>';
        
        $this->mockPosteService
            ->shouldReceive('mailboxExists')
            ->once()
            ->andThrow(new Exception('API Error'));

        $result = $this->service->isExistMailbox($email);
        
        $this->assertFalse($result);
    }

    public function testSetPasswordSuccess()
    {
        $email = '<EMAIL>';
        $password = 'newpassword123';
        
        $this->mockPosteService
            ->shouldReceive('updateMailboxPassword')
            ->once()
            ->with($email, $password)
            ->andReturn(true);

        $result = $this->service->setPassword($email, $password);
        
        $this->assertTrue($result);
    }

    public function testSetPasswordFailure()
    {
        $email = '<EMAIL>';
        $password = 'newpassword123';
        
        $this->mockPosteService
            ->shouldReceive('updateMailboxPassword')
            ->once()
            ->andThrow(new Exception('API Error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('API Error');
        
        $this->service->setPassword($email, $password);
    }

    public function testGetMailboxInfoSuccess()
    {
        $email = '<EMAIL>';
        $expectedInfo = [
            'email' => $email,
            'name' => 'Test User',
            'quota' => 1000
        ];
        
        $this->mockPosteService
            ->shouldReceive('getMailbox')
            ->once()
            ->with($email)
            ->andReturn($expectedInfo);

        $result = $this->service->getMailboxInfo($email);
        
        $this->assertEquals($expectedInfo, $result);
    }

    public function testGetMailboxInfoNotFound()
    {
        $email = '<EMAIL>';
        
        $this->mockPosteService
            ->shouldReceive('getMailbox')
            ->once()
            ->with($email)
            ->andReturn(null);

        $result = $this->service->getMailboxInfo($email);
        
        $this->assertNull($result);
    }

    public function testGetMailboxInfoException()
    {
        $email = '<EMAIL>';
        
        $this->mockPosteService
            ->shouldReceive('getMailbox')
            ->once()
            ->andThrow(new Exception('API Error'));

        $result = $this->service->getMailboxInfo($email);
        
        $this->assertNull($result);
    }

    public function testSetDestinationsSuccess()
    {
        $email = '<EMAIL>';
        $destinations = ['<EMAIL>', '<EMAIL>'];
        
        $result = $this->service->setDestinations($email, $destinations);
        
        // Since this is not fully implemented in Poste, it should return true
        // but log a warning
        $this->assertTrue($result);
    }

    public function testGetDestinationsWithExistingMailbox()
    {
        $email = '<EMAIL>';
        
        $this->mockPosteService
            ->shouldReceive('mailboxExists')
            ->once()
            ->with($email)
            ->andReturn(true);

        $result = $this->service->getDestinations($email, true);
        
        $this->assertEquals([['name' => $email]], $result);
    }

    public function testGetDestinationsWithoutExistingMailbox()
    {
        $email = '<EMAIL>';
        
        $this->mockPosteService
            ->shouldReceive('mailboxExists')
            ->once()
            ->with($email)
            ->andReturn(false);

        $result = $this->service->getDestinations($email, true);
        
        $this->assertEquals([], $result);
    }

    public function testSoftDeleteBoxesByHostSuccess()
    {
        $mailhost = 'example.com';
        $mailboxes = [
            ['email' => '<EMAIL>'],
            ['email' => '<EMAIL>']
        ];
        
        $this->mockPosteService
            ->shouldReceive('listMailboxes')
            ->once()
            ->with($mailhost)
            ->andReturn($mailboxes);

        $result = $this->service->softDeleteBoxesByHost($mailhost);
        
        $this->assertTrue($result);
    }

    public function testSoftUndeleteBoxesByHostSuccess()
    {
        $mailhost = 'example.com';
        
        $result = $this->service->softUndeleteBoxesByHost($mailhost);
        
        // Since Poste doesn't support soft delete, this should always return true
        $this->assertTrue($result);
    }
}
