<?php

namespace Tests\Unit\Mail;

use Exception;
use Exolog\Module\Mail\PosteApiClient;
use Exolog\Module\Mail\PosteApiService;
use Illuminate\Support\Facades\Config;
use Mockery;
use PHPUnit\Framework\TestCase;

class PosteApiServiceTest extends TestCase
{
    private PosteApiService $service;
    private $mockClient;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock the configuration
        Config::shouldReceive('get')
            ->with('services.poste.mail_domain')
            ->andReturn('mail.example.com');

        $this->mockClient = Mockery::mock(PosteApiClient::class);
        $this->service = new PosteApiService();
        
        // Use reflection to inject the mock client
        $reflection = new \ReflectionClass($this->service);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->service, $this->mockClient);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function testAddDomainSuccess()
    {
        $domain = 'example.com';
        
        $this->mockClient
            ->shouldReceive('post')
            ->once()
            ->with('domains', ['name' => $domain])
            ->andReturn(['success' => true]);

        $result = $this->service->addDomain($domain);
        
        $this->assertTrue($result);
    }

    public function testAddDomainFailure()
    {
        $domain = 'example.com';
        
        $this->mockClient
            ->shouldReceive('post')
            ->once()
            ->with('domains', ['name' => $domain])
            ->andThrow(new Exception('API Error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('API Error');
        
        $this->service->addDomain($domain);
    }

    public function testRemoveDomainSuccess()
    {
        $domain = 'example.com';
        
        $this->mockClient
            ->shouldReceive('delete')
            ->once()
            ->with("domains/$domain")
            ->andReturn(['success' => true]);

        $result = $this->service->removeDomain($domain);
        
        $this->assertTrue($result);
    }

    public function testDomainExistsTrue()
    {
        $domain = 'example.com';
        
        $this->mockClient
            ->shouldReceive('get')
            ->once()
            ->with("domains/$domain")
            ->andReturn(['name' => $domain]);

        $result = $this->service->domainExists($domain);
        
        $this->assertTrue($result);
    }

    public function testDomainExistsFalse()
    {
        $domain = 'example.com';
        
        $this->mockClient
            ->shouldReceive('get')
            ->once()
            ->with("domains/$domain")
            ->andThrow(new Exception('Not found'));

        $result = $this->service->domainExists($domain);
        
        $this->assertFalse($result);
    }

    public function testAddMailboxSuccess()
    {
        $email = '<EMAIL>';
        $password = 'password123';
        $name = 'Test User';
        $options = ['quota' => 1000];
        
        $expectedData = [
            'email' => $email,
            'password' => $password,
            'name' => $name,
            'quota' => 1000
        ];
        
        $this->mockClient
            ->shouldReceive('post')
            ->once()
            ->with('boxes', $expectedData)
            ->andReturn(['success' => true]);

        $result = $this->service->addMailbox($email, $password, $name, $options);
        
        $this->assertTrue($result);
    }

    public function testRemoveMailboxSuccess()
    {
        $email = '<EMAIL>';
        
        $this->mockClient
            ->shouldReceive('delete')
            ->once()
            ->with("boxes/$email")
            ->andReturn(['success' => true]);

        $result = $this->service->removeMailbox($email);
        
        $this->assertTrue($result);
    }

    public function testUpdateMailboxPasswordSuccess()
    {
        $email = '<EMAIL>';
        $password = 'newpassword123';
        
        $this->mockClient
            ->shouldReceive('put')
            ->once()
            ->with("boxes/$email", ['password' => $password])
            ->andReturn(['success' => true]);

        $result = $this->service->updateMailboxPassword($email, $password);
        
        $this->assertTrue($result);
    }

    public function testMailboxExistsTrue()
    {
        $email = '<EMAIL>';
        
        $this->mockClient
            ->shouldReceive('get')
            ->once()
            ->with("boxes/$email")
            ->andReturn(['email' => $email]);

        $result = $this->service->mailboxExists($email);
        
        $this->assertTrue($result);
    }

    public function testMailboxExistsFalse()
    {
        $email = '<EMAIL>';
        
        $this->mockClient
            ->shouldReceive('get')
            ->once()
            ->with("boxes/$email")
            ->andThrow(new Exception('Not found'));

        $result = $this->service->mailboxExists($email);
        
        $this->assertFalse($result);
    }

    public function testGetMailboxSuccess()
    {
        $email = '<EMAIL>';
        $expectedData = [
            'email' => $email,
            'name' => 'Test User',
            'quota' => 1000
        ];
        
        $this->mockClient
            ->shouldReceive('get')
            ->once()
            ->with("boxes/$email")
            ->andReturn($expectedData);

        $result = $this->service->getMailbox($email);
        
        $this->assertEquals($expectedData, $result);
    }

    public function testGetMailboxNotFound()
    {
        $email = '<EMAIL>';
        
        $this->mockClient
            ->shouldReceive('get')
            ->once()
            ->with("boxes/$email")
            ->andThrow(new Exception('Not found'));

        $result = $this->service->getMailbox($email);
        
        $this->assertNull($result);
    }

    public function testListMailboxesSuccess()
    {
        $domain = 'example.com';
        $expectedData = [
            ['email' => '<EMAIL>'],
            ['email' => '<EMAIL>']
        ];
        
        $this->mockClient
            ->shouldReceive('get')
            ->once()
            ->with("domains/$domain/boxes")
            ->andReturn($expectedData);

        $result = $this->service->listMailboxes($domain);
        
        $this->assertEquals($expectedData, $result);
    }

    public function testGetDkimRecordSuccess()
    {
        $domain = 'example.com';
        $expectedData = [
            'selector' => 'default',
            'record' => 'v=DKIM1; k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC...'
        ];
        
        $this->mockClient
            ->shouldReceive('get')
            ->once()
            ->with("domains/$domain/dkim")
            ->andReturn($expectedData);

        $result = $this->service->getDkimRecord($domain);
        
        $this->assertEquals($expectedData, $result);
    }

    public function testGetDkimRecordNotFound()
    {
        $domain = 'example.com';
        
        $this->mockClient
            ->shouldReceive('get')
            ->once()
            ->with("domains/$domain/dkim")
            ->andThrow(new Exception('Not found'));

        $result = $this->service->getDkimRecord($domain);
        
        $this->assertNull($result);
    }
}
