# 🚀 Exolog Project - Complete Setup Guide

This repository contains a complex Laravel application with multiple Vue.js frontends, Docker containers, Caddy reverse proxy, and various services including MySQL, Redis, mail server, and analytics.

## 📋 Quick Overview

### What's Included
- **Laravel 8** backend with PHP 7.4
- **3 Vue.js frontends**: <PERSON><PERSON>, <PERSON><PERSON>, Editor
- **Docker** containerization with Caddy proxy
- **MySQL 5.7** database
- **Redis** for caching
- **Mail server** (Poste.io)
- **Analytics** (Plausible)
- **CI/CD** with GitLab Runner

### Ports & Access
- **Main App**: http://localhost:8082
- **Dealer**: http://localhost:8081
- **Database Admin**: http://localhost:8080
- **Mail Testing**: http://localhost:8025

## 🎯 Choose Your Setup Method

### 1. 🏃‍♂️ Quick Start (Recommended for Development)
**Best for**: Local development, testing, learning the project

```bash
# One command setup
./quick-start.sh

# Access your applications immediately:
# • Main: http://localhost:8082
# • Dealer: http://localhost:8081
# • Database: http://localhost:8080
# • Mail: http://localhost:8025
```

**What it does:**
- ✅ Sets up simplified Docker environment
- ✅ Installs all dependencies
- ✅ Configures database and Redis
- ✅ Builds frontend applications
- ✅ Ready in ~5-10 minutes

### 2. 🏭 Full Production Setup
**Best for**: Production deployment, full feature testing

```bash
# Complete production setup
./setup.sh
```

**What it does:**
- ✅ Full production Docker stack
- ✅ Caddy reverse proxy with SSL
- ✅ Mail server (Poste.io)
- ✅ Analytics (Plausible)
- ✅ GitLab Runner for CI/CD
- ✅ Production-ready configuration

### 3. 🛠️ Manual Setup
**Best for**: Custom configurations, learning the architecture

Follow the detailed guide in `SETUP_GUIDE.md`

## 🚀 Getting Started (Quick Start)

### Prerequisites
- Docker & Docker Compose
- Git
- 8GB+ RAM recommended

### Step 1: Clone & Setup
```bash
<NAME_EMAIL>:fidela/exolog-reactJson.git
cd exolog-reactJson
./quick-start.sh
```

### Step 2: Access Applications
- **Main Application**: http://localhost:8082
- **Dealer Interface**: http://localhost:8081
- **Database Admin**: http://localhost:8080 (user: exolog, pass: password)
- **Mail Testing**: http://localhost:8025

### Step 3: Development
```bash
# Start frontend development servers
npm run serve:all

# Access development servers:
# • Dealer: http://localhost:3000
# • Admin: http://localhost:3001
# • Editor: http://localhost:3002
```

## 🔧 Common Commands

```bash
# View logs
docker-compose -f docker-compose.local.yml logs -f

# Stop services
./quick-start.sh stop

# Restart services
./quick-start.sh restart

# Clean everything
./quick-start.sh clean

# Access container
docker-compose -f docker-compose.local.yml exec dealer bash

# Run Laravel commands
docker-compose -f docker-compose.local.yml exec dealer php artisan migrate
```

## 🐛 Troubleshooting

### Port Already in Use
```bash
# Check what's using the port
sudo lsof -i :8081

# Kill the process
sudo kill -9 <PID>
```

### Database Issues
```bash
# Reset database
docker-compose -f docker-compose.local.yml down -v
./quick-start.sh
```

### Frontend Build Issues
```bash
# Clear and reinstall
cd resources/js/dealer
rm -rf node_modules package-lock.json
npm install
```

### Permission Issues
```bash
sudo chown -R $USER:$USER .
```

## 📁 Project Structure

```
exolog-reactJson/
├── resources/js/
│   ├── dealer/          # Dealer management frontend
│   ├── exo_admin/       # Admin interface frontend
│   └── exo_editor/      # Content editor frontend
├── docker/              # Docker configurations
├── app/                 # Laravel application
├── routes/              # API routes
├── database/            # Migrations and seeds
├── quick-start.sh       # Quick development setup
├── setup.sh            # Full production setup
├── docker-compose.local.yml  # Local development
└── SETUP_GUIDE.md      # Detailed setup guide
```

## 🎨 Frontend Development

Each frontend is a separate Vue.js application:

### Dealer Frontend (`resources/js/dealer/`)
- Dealer management interface
- Domain management (the one we just fixed!)
- Site configuration

### Admin Frontend (`resources/js/exo_admin/`)
- System administration
- User management
- Analytics dashboard

### Editor Frontend (`resources/js/exo_editor/`)
- Content management
- Page editor
- Media management

### Development Workflow
```bash
# Start all development servers
npm run serve:all

# Or individually
npm run serve:dealer
npm run serve:exo_admin
npm run serve:exo_editor

# Build for production
npm run build:direct:dealer
npm run build:direct:exo_admin
npm run build:direct:exo_editor
```

## 🔐 Environment Configuration

### Local Development (.env.local)
- Pre-configured for Docker
- Uses MailHog for email testing
- Redis for caching
- MySQL database

### Production (.env.docker)
- Generated with secure passwords
- Real mail server configuration
- SSL certificates
- Performance optimizations

## 📊 Monitoring & Logs

```bash
# View all logs
docker-compose -f docker-compose.local.yml logs -f

# View specific service
docker-compose -f docker-compose.local.yml logs -f dealer

# Monitor resources
docker stats
```

## 🚀 Next Steps

1. **Start with Quick Start**: `./quick-start.sh`
2. **Explore the applications**: Check each frontend
3. **Review the code**: Understand the architecture
4. **Make changes**: Test the domain addition fix we just implemented
5. **Deploy**: Use `./setup.sh` for production

## 📚 Additional Resources

- **Detailed Setup**: `SETUP_GUIDE.md`
- **Laravel Docs**: https://laravel.com/docs/8.x
- **Vue.js Docs**: https://vuejs.org/guide/
- **Docker Docs**: https://docs.docker.com/

## 🆘 Need Help?

1. Check logs: `docker-compose -f docker-compose.local.yml logs -f`
2. Review `SETUP_GUIDE.md` for detailed troubleshooting
3. Ensure all prerequisites are installed
4. Try the clean setup: `./quick-start.sh clean && ./quick-start.sh`

---

**Happy coding! 🎉** The domain addition issue we just fixed should work perfectly in this environment.
