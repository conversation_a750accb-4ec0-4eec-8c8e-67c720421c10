<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="WebServers">
    <option name="servers">
      <webServer id="1b5eb5d6-c487-4119-ab47-0cdf443ed4cf" name="W19 - VPS" url="http://dealer.w19.exolog.net">
        <fileTransfer rootFolder="/usr/local/WWW/W19" accessType="SFTP" host="dealer.w19.exolog.net" port="22" sshConfigId="b4a4440b-7a4c-4db9-92bd-9d9732ff2f8b" sshConfig="<EMAIL>:22 agent" authAgent="true">
          <advancedOptions>
            <advancedOptions dataProtectionLevel="Private" shareSSLContext="true" />
          </advancedOptions>
        </fileTransfer>
      </webServer>
      <webServer id="aefe9adb-631d-45ba-a0ce-b99657a19c10" name="W18 - VPS d01.africanetic" url="http://d01.africanetic.com">
        <fileTransfer rootFolder="/usr/local/WWW/W18" accessType="SFTP" host="d01.africanetic.info" port="22" sshConfigId="2e1287e2-aeeb-48da-bfa9-257803cccbac" sshConfig="<EMAIL>:22 agent" authAgent="true">
          <advancedOptions>
            <advancedOptions dataProtectionLevel="Private" shareSSLContext="true" />
          </advancedOptions>
        </fileTransfer>
      </webServer>
      <webServer id="41148c05-235b-4884-bec5-f0037ba7b439" name="W14 - VPS (test)" url="http://***************">
        <fileTransfer rootFolder="/usr/local/WWW/W14" accessType="SFTP" host="dealer.w14.exolog.net" port="22" sshConfigId="3092b820-65b0-4c6a-9f51-83e53efd8b14" sshConfig="<EMAIL>:22 agent" authAgent="true">
          <advancedOptions>
            <advancedOptions dataProtectionLevel="Private" shareSSLContext="true" />
          </advancedOptions>
        </fileTransfer>
      </webServer>
      <webServer id="40e367da-7f3e-4e5a-b8d1-07b81ee1271c" name="W31 - VPS (zonweringstunter)" url="http://***************">
        <fileTransfer rootFolder="/usr/local/WWW/W31" accessType="SFTP" host="dev.zonweringstunter.com" port="22" sshConfigId="3a5dfad7-cdb5-4da1-9dfc-842de1ae9172" sshConfig="<EMAIL>:22 agent" authAgent="true">
          <advancedOptions>
            <advancedOptions dataProtectionLevel="Private" shareSSLContext="true" />
          </advancedOptions>
        </fileTransfer>
      </webServer>
      <webServer id="bfe71c43-9e60-4e92-a4c8-b4966b786a41" name="W41 - d03" url="http://***************">
        <fileTransfer rootFolder="/usr/local/WWW/W41" accessType="SFTP" host="d03.fidela.net" port="22" sshConfigId="60155471-156b-4bed-8d83-c5a03a4d6531" sshConfig="<EMAIL>:22 agent" authAgent="true">
          <advancedOptions>
            <advancedOptions dataProtectionLevel="Private" shareSSLContext="true" />
          </advancedOptions>
        </fileTransfer>
      </webServer>
      <webServer id="8db15572-c41a-4d44-bf2a-d8f8d64b8fce" name="D06" url="http://***************">
        <fileTransfer rootFolder="/usr/local/WWW/D06" accessType="SFTP" host="dealer.d06.fidela.net" port="22" sshConfigId="ac66bed5-50e2-40cb-9abf-42318be9368a" sshConfig="<EMAIL>:22 agent" authAgent="true">
          <advancedOptions>
            <advancedOptions dataProtectionLevel="Private" keepAliveTimeout="0" shareSSLContext="true" />
          </advancedOptions>
        </fileTransfer>
      </webServer>
      <webServer id="ec01d733-5df9-403d-888b-db500960e9ea" name="W28 - dev" url="https://route.w28.exolog.net">
        <fileTransfer rootFolder="/usr/local/WWW/W28" accessType="SFTP" host="w28.exolog.net" port="22" sshConfigId="c6f7f098-d4df-4e1f-b0c3-f5be6b85f9f0" sshConfig="<EMAIL>:22 agent" authAgent="true">
          <advancedOptions>
            <advancedOptions dataProtectionLevel="Private" shareSSLContext="true" />
          </advancedOptions>
        </fileTransfer>
      </webServer>
      <webServer id="a5cdb65d-1eea-4403-b2df-6b62fbc3d80a" name="D04/W29" url="http://dealer.w27.exolog.net">
        <fileTransfer rootFolder="/usr/local/WWW/W29" accessType="SFTP" host="d04.fidela.net" port="22" sshConfigId="2a7303c3-3980-43e5-92de-00b42fc44415" sshConfig="<EMAIL>:22 agent" authAgent="true">
          <advancedOptions>
            <advancedOptions dataProtectionLevel="Private" shareSSLContext="true" />
          </advancedOptions>
        </fileTransfer>
      </webServer>
      <webServer id="8803f915-5a03-4cf9-97e1-4eb31d24c829" name="D05" url="http://***************">
        <fileTransfer rootFolder="/usr/local/WWW/D05" accessType="SFTP" host="d05.fidela.net" port="22" sshConfigId="3bb7d65a-036a-47ae-8719-9f3e75de7ee5" sshConfig="<EMAIL>:22 agent" authAgent="true">
          <advancedOptions>
            <advancedOptions dataProtectionLevel="Private" shareSSLContext="true" />
          </advancedOptions>
        </fileTransfer>
      </webServer>
      <webServer id="84c380dc-b534-4702-9a6f-90406cafea9a" name="D01/W27 - d01.fidela.net" url="http://dealer.w27.exolog.net">
        <fileTransfer rootFolder="/usr/local/WWW/W27" accessType="SFTP" host="dealer.w27.exolog.net" port="22" sshConfigId="25852209-ed5c-406b-aaa2-0c4d26823b36" sshConfig="<EMAIL>:22 agent" authAgent="true">
          <advancedOptions>
            <advancedOptions dataProtectionLevel="Private" shareSSLContext="true" />
          </advancedOptions>
        </fileTransfer>
      </webServer>
      <webServer id="a6a68cc2-a886-4ecd-b9b2-f2c37de94523" name="dev01.zonweringstunter.com" url="">
        <fileTransfer rootFolder="/usr/local/WWW/dev01" accessType="SFTP" host="dev01.zonweringstunter.com" port="22" sshConfigId="3dbadbed-2258-4f72-9bf2-26c985f561c8" sshConfig="<EMAIL>:22 agent" authAgent="true">
          <advancedOptions>
            <advancedOptions dataProtectionLevel="Private" shareSSLContext="true" />
          </advancedOptions>
        </fileTransfer>
      </webServer>
      <webServer id="6c910375-254d-4069-822b-f2fdf01036be" name="W30 - d02.fidela.net (DEV)" url="http://***************">
        <fileTransfer rootFolder="/usr/local/WWW/W30" accessType="SFTP" host="d02.fidela.net" port="22" sshConfigId="a14a4a1d-80ce-4aac-ade1-32c0c4c594aa" sshConfig="<EMAIL>:22 agent" authAgent="true">
          <advancedOptions>
            <advancedOptions dataProtectionLevel="Private" shareSSLContext="true" />
          </advancedOptions>
        </fileTransfer>
      </webServer>
    </option>
    <groups>
      <group>
        <name>Exolog</name>
        <servers>
          <name value="W19 - VPS" />
          <name value="W18 - VPS d01.africanetic" />
          <name value="W14 - VPS (test)" />
          <name value="W31 - VPS (zonweringstunter)" />
          <name value="W41 - d03" />
          <name value="D06" />
        </servers>
      </group>
      <group>
        <name>! Exolog 2.0</name>
        <servers>
          <name value="W28 - dev" />
          <name value="D04/W29" />
          <name value="D05" />
          <name value="D01/W27 - d01.fidela.net" />
          <name value="dev01.zonweringstunter.com" />
          <name value="W30 - d02.fidela.net (DEV)" />
        </servers>
      </group>
    </groups>
  </component>
</project>