<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MessDetectorOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCSFixerOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCodeSnifferOptionsConfiguration">
    <option name="codingStandard" value="PHPCompatibility" />
    <option name="installedPaths" value="$PROJECT_DIR$/vendor/phpcompatibility/php-compatibility/PHPCompatibility" />
    <option name="useInstalledPaths" value="true" />
    <option name="transferred" value="true" />
  </component>
  <component name="PhpCodeSniffer">
    <phpcs_settings>
      <PhpCSConfiguration beautifier_path="$PROJECT_DIR$/vendor/bin/phpcbf" standards="MySource;PEAR;PHPCompatibility;PSR1;PSR12;PSR2;Squiz;Zend" tool_path="$PROJECT_DIR$/vendor/bin/phpcs" />
    </phpcs_settings>
  </component>
  <component name="PhpDebugIgnoredPathsSettings">
    <skipped_paths>
      <skipped_path path="$PROJECT_DIR$/vendor/composer" />
    </skipped_paths>
  </component>
  <component name="PhpIncludePathManager">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/league/mime-type-detection" />
      <path value="$PROJECT_DIR$/vendor/league/flysystem" />
      <path value="$PROJECT_DIR$/vendor/ramsey/collection" />
      <path value="$PROJECT_DIR$/vendor/ramsey/uuid" />
      <path value="$PROJECT_DIR$/vendor/voku/portable-ascii" />
      <path value="$PROJECT_DIR$/vendor/phpoption/phpoption" />
      <path value="$PROJECT_DIR$/vendor/swiftmailer/swiftmailer" />
      <path value="$PROJECT_DIR$/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/vendor/psr/container" />
      <path value="$PROJECT_DIR$/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/vendor/psr/log" />
      <path value="$PROJECT_DIR$/vendor/spatie/laravel-package-tools" />
      <path value="$PROJECT_DIR$/vendor/spatie/laravel-responsecache" />
      <path value="$PROJECT_DIR$/vendor/intervention/image" />
      <path value="$PROJECT_DIR$/vendor/tijsverkoyen/css-to-inline-styles" />
      <path value="$PROJECT_DIR$/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/vendor/opis/closure" />
      <path value="$PROJECT_DIR$/vendor/graham-campbell/result-type" />
      <path value="$PROJECT_DIR$/vendor/nette/schema" />
      <path value="$PROJECT_DIR$/vendor/nette/utils" />
      <path value="$PROJECT_DIR$/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/vlucas/phpdotenv" />
      <path value="$PROJECT_DIR$/vendor/brick/math" />
      <path value="$PROJECT_DIR$/vendor/jeroendesloovere/vcard" />
      <path value="$PROJECT_DIR$/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/vendor/dflydev/dot-access-data" />
      <path value="$PROJECT_DIR$/vendor/glhd/aire" />
      <path value="$PROJECT_DIR$/vendor/glhd/aire-bootstrap" />
      <path value="$PROJECT_DIR$/vendor/nesbot/carbon" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/vendor/symfony/finder" />
      <path value="$PROJECT_DIR$/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/routing" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-client-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/vendor/symfony/css-selector" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/vendor/behat/transliterator" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/vendor/symfony/mime" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/vendor/symfony/error-handler" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/vendor/doctrine/lexer" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php73" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/vendor/doctrine/inflector" />
      <path value="$PROJECT_DIR$/vendor/symfony/console" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-iconv" />
      <path value="$PROJECT_DIR$/vendor/egulias/email-validator" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php81" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-kernel" />
      <path value="$PROJECT_DIR$/vendor/symfony/translation-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/vendor/symfony/translation" />
      <path value="$PROJECT_DIR$/vendor/symfony/process" />
      <path value="$PROJECT_DIR$/vendor/mollie/mollie-api-php" />
      <path value="$PROJECT_DIR$/vendor/mollie/laravel-mollie" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/vendor/league/config" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/vendor/league/commonmark" />
      <path value="$PROJECT_DIR$/vendor/laravel/serializable-closure" />
      <path value="$PROJECT_DIR$/vendor/aws/aws-crt-php" />
      <path value="$PROJECT_DIR$/vendor/mtdowling/jmespath.php" />
      <path value="$PROJECT_DIR$/vendor/aws/aws-sdk-php" />
      <path value="$PROJECT_DIR$/vendor/cloudflare/sdk" />
      <path value="$PROJECT_DIR$/vendor/dealerdirect/phpcodesniffer-composer-installer" />
      <path value="$PROJECT_DIR$/vendor/squizlabs/php_codesniffer" />
      <path value="$PROJECT_DIR$/vendor/phpcsstandards/phpcsutils" />
      <path value="$PROJECT_DIR$/homepages.webkracht.nl/htdocs/sites/route/vendor/web-token/jwt-signature-algorithm-rsa" />
      <path value="$PROJECT_DIR$/homepages.webkracht.nl/htdocs/sites/route/vendor/web-token/jwt-signature" />
      <path value="$PROJECT_DIR$/homepages.webkracht.nl/htdocs/sites/route/vendor/web-token/jwt-key-mgmt" />
      <path value="$PROJECT_DIR$/homepages.webkracht.nl/htdocs/sites/route/vendor/web-token/jwt-core" />
      <path value="$PROJECT_DIR$/homepages.webkracht.nl/htdocs/sites/route/vendor/composer" />
      <path value="$PROJECT_DIR$/homepages.webkracht.nl/htdocs/sites/route/vendor/spomky-labs/base64url" />
      <path value="$PROJECT_DIR$/homepages.webkracht.nl/htdocs/sites/route/vendor/fgrosse/phpasn1" />
      <path value="$PROJECT_DIR$/homepages.webkracht.nl/htdocs/sites/route/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/homepages.webkracht.nl/htdocs/sites/route/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/homepages.webkracht.nl/htdocs/sites/route/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/homepages.webkracht.nl/htdocs/sites/route/vendor/brick/math" />
      <path value="$PROJECT_DIR$/homepages.webkracht.nl/htdocs/sites/route/vendor/firebase/php-jwt" />
      <path value="$PROJECT_DIR$/vendor/pragmarx/google2fa" />
      <path value="$PROJECT_DIR$/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/vendor/spatie/image" />
      <path value="$PROJECT_DIR$/vendor/spatie/temporary-directory" />
      <path value="$PROJECT_DIR$/vendor/spatie/image-optimizer" />
      <path value="$PROJECT_DIR$/vendor/spatie/browsershot" />
      <path value="$PROJECT_DIR$/vendor/league/glide" />
      <path value="$PROJECT_DIR$/vendor/mockery/mockery" />
      <path value="$PROJECT_DIR$/vendor/hamcrest/hamcrest-php" />
      <path value="$PROJECT_DIR$/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/vendor/sebastian/code-unit-reverse-lookup" />
      <path value="$PROJECT_DIR$/vendor/sebastian/comparator" />
      <path value="$PROJECT_DIR$/vendor/sebastian/diff" />
      <path value="$PROJECT_DIR$/vendor/sebastian/environment" />
      <path value="$PROJECT_DIR$/vendor/sebastian/exporter" />
      <path value="$PROJECT_DIR$/vendor/sebastian/global-state" />
      <path value="$PROJECT_DIR$/vendor/sebastian/object-enumerator" />
      <path value="$PROJECT_DIR$/vendor/sebastian/object-reflector" />
      <path value="$PROJECT_DIR$/vendor/sebastian/recursion-context" />
      <path value="$PROJECT_DIR$/vendor/sebastian/resource-operations" />
      <path value="$PROJECT_DIR$/vendor/sebastian/type" />
      <path value="$PROJECT_DIR$/vendor/sebastian/version" />
      <path value="$PROJECT_DIR$/vendor/phar-io/version" />
      <path value="$PROJECT_DIR$/vendor/phar-io/manifest" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-file-iterator" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-code-coverage" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-text-template" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-invoker" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-token-stream" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-timer" />
      <path value="$PROJECT_DIR$/vendor/phpunit/phpunit" />
      <path value="$PROJECT_DIR$/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/vendor/myclabs/deep-copy" />
      <path value="$PROJECT_DIR$/vendor/theseer/tokenizer" />
      <path value="$PROJECT_DIR$/vendor/doctrine/instantiator" />
      <path value="$PROJECT_DIR$/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/vendor/phpspec/prophecy" />
      <path value="$PROJECT_DIR$/vendor/nikic/php-parser" />
      <path value="$PROJECT_DIR$/vendor/asm89/stack-cors" />
      <path value="$PROJECT_DIR$/vendor/dragonmantank/cron-expression" />
      <path value="$PROJECT_DIR$/vendor/fruitcake/laravel-cors" />
      <path value="$PROJECT_DIR$/vendor/psy/psysh" />
      <path value="$PROJECT_DIR$/vendor/laravel/tinker" />
      <path value="$PROJECT_DIR$/vendor/laravel/framework" />
      <path value="$PROJECT_DIR$/vendor/facade/ignition" />
      <path value="$PROJECT_DIR$/vendor/facade/flare-client-php" />
      <path value="$PROJECT_DIR$/vendor/facade/ignition-contracts" />
      <path value="$PROJECT_DIR$/vendor/marvinlabs/laravel-discord-logger" />
      <path value="$PROJECT_DIR$/vendor/psr/cache" />
      <path value="$PROJECT_DIR$/vendor/doctrine/cache" />
      <path value="$PROJECT_DIR$/vendor/doctrine/event-manager" />
      <path value="$PROJECT_DIR$/vendor/doctrine/dbal" />
      <path value="$PROJECT_DIR$/vendor/phpcompatibility/php-compatibility" />
      <path value="$PROJECT_DIR$/vendor/illuminate/database" />
      <path value="$PROJECT_DIR$/vendor/illuminate/support" />
      <path value="$PROJECT_DIR$/vendor/illuminate/routing" />
      <path value="$PROJECT_DIR$/vendor/illuminate/cache" />
      <path value="$PROJECT_DIR$/vendor/illuminate/redis" />
      <path value="$PROJECT_DIR$/vendor/illuminate/view" />
      <path value="$PROJECT_DIR$/vendor/illuminate/config" />
      <path value="$PROJECT_DIR$/vendor/illuminate/auth" />
      <path value="$PROJECT_DIR$/vendor/illuminate/container" />
      <path value="$PROJECT_DIR$/vendor/illuminate/filesystem" />
      <path value="$PROJECT_DIR$/vendor/illuminate/http" />
      <path value="$PROJECT_DIR$/vendor/illuminate/contracts" />
      <path value="$PROJECT_DIR$/vendor/illuminate/events" />
      <path value="$PROJECT_DIR$/vendor/illuminate/pagination" />
      <path value="$PROJECT_DIR$/vendor/illuminate/pipeline" />
      <path value="$PROJECT_DIR$/vendor/illuminate/console" />
      <path value="$PROJECT_DIR$/vendor/illuminate/bus" />
      <path value="$PROJECT_DIR$/vendor/illuminate/macroable" />
      <path value="$PROJECT_DIR$/vendor/illuminate/mail" />
      <path value="$PROJECT_DIR$/vendor/illuminate/log" />
      <path value="$PROJECT_DIR$/vendor/illuminate/validation" />
      <path value="$PROJECT_DIR$/vendor/illuminate/session" />
      <path value="$PROJECT_DIR$/vendor/illuminate/collections" />
      <path value="$PROJECT_DIR$/vendor/illuminate/queue" />
      <path value="$PROJECT_DIR$/vendor/illuminate/translation" />
      <path value="$PROJECT_DIR$/vendor/fenom/fenom" />
      <path value="$PROJECT_DIR$/vendor/ircmaxell/password-compat" />
      <path value="$PROJECT_DIR$/vendor/filp/whoops" />
      <path value="$PROJECT_DIR$/vendor/illuminate/encryption" />
      <path value="$PROJECT_DIR$/vendor/illuminate/hashing" />
      <path value="$PROJECT_DIR$/vendor/illuminate/testing" />
      <path value="$PROJECT_DIR$/vendor/illuminate/cookie" />
      <path value="$PROJECT_DIR$/vendor/maximebf/debugbar" />
      <path value="$PROJECT_DIR$/vendor/barryvdh/laravel-debugbar" />
      <path value="$PROJECT_DIR$/vendor/_laravel_idea" />
      <path value="$PROJECT_DIR$/vendor/chillerlan/php-settings-container" />
      <path value="$PROJECT_DIR$/vendor/chillerlan/php-qrcode" />
    </include_path>
  </component>
  <component name="PhpInterpreters">
    <interpreters>
      <interpreter id="22bae98a-7bde-457d-a741-b506bc897dd3" name="W28 - DEV" home="sshConfig://DATA" auto="false" debugger_id="php.debugger.XDebug">
        <configuration_options>
          <configuration_option name="xdebug.client_host" value="localhost" />
          <configuration_option name="xdebug.idekey" value="PHPSTORM" />
        </configuration_options>
        <remote_data INTERPRETER_PATH="/usr/bin/php" HELPERS_PATH="/root/.phpstorm_helpers" VALID="true" RUN_AS_ROOT_VIA_SUDO="false" SSH_CONFIG_ID="c6f7f098-d4df-4e1f-b0c3-f5be6b85f9f0" SSH_CONFIG_NAME="<EMAIL>:22 agent" SSH_CREDENTIALS_ID="sshConfig://<EMAIL>:22 agent" />
      </interpreter>
      <interpreter id="53e96e5e-ed52-4081-9cfd-631a9f34d74d" name="W27" home="sshConfig://DATA" auto="false" debugger_id="php.debugger.XDebug">
        <remote_data INTERPRETER_PATH="/usr/bin/php" HELPERS_PATH="/root/.phpstorm_helpers" VALID="true" RUN_AS_ROOT_VIA_SUDO="false" SSH_CONFIG_ID="25852209-ed5c-406b-aaa2-0c4d26823b36" SSH_CONFIG_NAME="<EMAIL>:22 agent" SSH_CREDENTIALS_ID="sshConfig://<EMAIL>:22 agent" />
      </interpreter>
      <interpreter id="59979861-894f-4195-928b-82716213adc1" name="D02" home="sshConfig://DATA" auto="false" debugger_id="php.debugger.XDebug">
        <configuration_options>
          <configuration_option name="xdebug.client_host" value="localhost" />
          <configuration_option name="xdebug.idekey" value="PHPSTORM" />
        </configuration_options>
        <remote_data INTERPRETER_PATH="/usr/bin/php" HELPERS_PATH="/root/.phpstorm_helpers" VALID="true" RUN_AS_ROOT_VIA_SUDO="false" SSH_CONFIG_ID="a14a4a1d-80ce-4aac-ade1-32c0c4c594aa" SSH_CONFIG_NAME="<EMAIL>:22 agent" SSH_CREDENTIALS_ID="sshConfig://<EMAIL>:22 agent" />
      </interpreter>
    </interpreters>
  </component>
  <component name="PhpInterpretersPhpInfoCache">
    <phpInfoCache>
      <interpreter name="W28 - DEV">
        <phpinfo binary_type="PHP" php_cgi="/usr/bin/php-cgi" php_cli="/usr/bin/php" path_separator=":" remote_host="*************" version="7.4.33">
          <additional_php_ini>/etc/php.d/10-mongodb.ini, /etc/php.d/10-opcache.ini, /etc/php.d/15-xdebug.ini, /etc/php.d/20-bcmath.ini, /etc/php.d/20-bz2.ini, /etc/php.d/20-calendar.ini, /etc/php.d/20-ctype.ini, /etc/php.d/20-curl.ini, /etc/php.d/20-dom.ini, /etc/php.d/20-enchant.ini, /etc/php.d/20-exif.ini, /etc/php.d/20-fileinfo.ini, /etc/php.d/20-ftp.ini, /etc/php.d/20-gd.ini, /etc/php.d/20-gettext.ini, /etc/php.d/20-iconv.ini, /etc/php.d/20-imap.ini, /etc/php.d/20-intl.ini, /etc/php.d/20-json.ini, /etc/php.d/20-ldap.ini, /etc/php.d/20-mbstring.ini, /etc/php.d/20-mysqlnd.ini, /etc/php.d/20-pdo.ini, /etc/php.d/20-phar.ini, /etc/php.d/20-posix.ini, /etc/php.d/20-shmop.ini, /etc/php.d/20-simplexml.ini, /etc/php.d/20-soap.ini, /etc/php.d/20-sockets.ini, /etc/php.d/20-sqlite3.ini, /etc/php.d/20-sysvmsg.ini, /etc/php.d/20-sysvsem.ini, /etc/php.d/20-sysvshm.ini, /etc/php.d/20-tokenizer.ini, /etc/php.d/20-xml.ini, /etc/php.d/20-xmlwriter.ini, /etc/php.d/20-xsl.ini, /etc/php.d/30-mcrypt.ini, /etc/php.d/30-mysqli.ini, /etc/php.d/30-pdo_mysql.ini, /etc/php.d/30-pdo_sqlite.ini, /etc/php.d/30-xmlreader.ini, /etc/php.d/30-zip.ini, /etc/php.d/40-apcu.ini, /etc/php.d/40-mailparse.ini, /etc/php.d/blitz.ini, /etc/php.d/imagick.ini, /etc/php.d/newrelic.ini, /etc/php.d/xdebug.ini</additional_php_ini>
          <configuration_file>/etc/php.ini</configuration_file>
          <configuration_options>
            <configuration_option name="include_path" value=".:/usr/local/WWW/modules/ZendFramework:/usr/share/pear:/usr/local/WWW/modules" />
          </configuration_options>
          <debuggers>
            <debugger_info debugger="xdebug" debugger_version="2.9.8">
              <debug_extensions />
            </debugger_info>
          </debuggers>
          <loaded_extensions>
            <extension name="Core" />
            <extension name="PDO" />
            <extension name="Phar" />
            <extension name="Reflection" />
            <extension name="SPL" />
            <extension name="SimpleXML" />
            <extension name="Zend OPcache" />
            <extension name="apcu" />
            <extension name="bcmath" />
            <extension name="blitz" />
            <extension name="bz2" />
            <extension name="calendar" />
            <extension name="ctype" />
            <extension name="curl" />
            <extension name="date" />
            <extension name="dom" />
            <extension name="enchant" />
            <extension name="exif" />
            <extension name="fileinfo" />
            <extension name="filter" />
            <extension name="ftp" />
            <extension name="gd" />
            <extension name="gettext" />
            <extension name="hash" />
            <extension name="iconv" />
            <extension name="imagick" />
            <extension name="imap" />
            <extension name="intl" />
            <extension name="json" />
            <extension name="ldap" />
            <extension name="libxml" />
            <extension name="mailparse" />
            <extension name="mbstring" />
            <extension name="mcrypt" />
            <extension name="mongodb" />
            <extension name="mysqli" />
            <extension name="mysqlnd" />
            <extension name="newrelic" />
            <extension name="openssl" />
            <extension name="pcntl" />
            <extension name="pcre" />
            <extension name="pdo_mysql" />
            <extension name="pdo_sqlite" />
            <extension name="posix" />
            <extension name="readline" />
            <extension name="session" />
            <extension name="shmop" />
            <extension name="soap" />
            <extension name="sockets" />
            <extension name="sqlite3" />
            <extension name="standard" />
            <extension name="sysvmsg" />
            <extension name="sysvsem" />
            <extension name="sysvshm" />
            <extension name="tokenizer" />
            <extension name="xdebug" />
            <extension name="xml" />
            <extension name="xmlreader" />
            <extension name="xmlwriter" />
            <extension name="xsl" />
            <extension name="zip" />
            <extension name="zlib" />
          </loaded_extensions>
        </phpinfo>
      </interpreter>
      <interpreter name="D02">
        <phpinfo binary_type="PHP" php_cgi="/usr/bin/php-cgi" php_cli="/usr/bin/php" path_separator=":" remote_host="***********" version="7.4.33">
          <additional_php_ini>/etc/php.d/15-xdebug.ini, /etc/php.d/20-bz2.ini, /etc/php.d/20-calendar.ini, /etc/php.d/20-ctype.ini, /etc/php.d/20-curl.ini, /etc/php.d/20-dom.ini, /etc/php.d/20-exif.ini, /etc/php.d/20-fileinfo.ini, /etc/php.d/20-ftp.ini, /etc/php.d/20-gd.ini, /etc/php.d/20-gettext.ini, /etc/php.d/20-iconv.ini, /etc/php.d/20-json.ini, /etc/php.d/20-mbstring.ini, /etc/php.d/20-mysqlnd.ini, /etc/php.d/20-pdo.ini, /etc/php.d/20-phar.ini, /etc/php.d/20-simplexml.ini, /etc/php.d/20-sockets.ini, /etc/php.d/20-sodium.ini, /etc/php.d/20-sqlite3.ini, /etc/php.d/20-tokenizer.ini, /etc/php.d/20-xml.ini, /etc/php.d/20-xmlwriter.ini, /etc/php.d/20-xsl.ini, /etc/php.d/30-mcrypt.ini, /etc/php.d/30-mysqli.ini, /etc/php.d/30-pdo_mysql.ini, /etc/php.d/30-pdo_sqlite.ini, /etc/php.d/30-xmlreader.ini, /etc/php.d/blitz.ini, /etc/php.d/imagick.ini</additional_php_ini>
          <configuration_file>/etc/php.ini</configuration_file>
          <configuration_options>
            <configuration_option name="include_path" value=".:/usr/share/pear:/usr/share/php:/usr/share/pear:/usr/share/php" />
          </configuration_options>
          <debuggers>
            <debugger_info debugger="xdebug" debugger_version="3.1.6">
              <debug_extensions />
            </debugger_info>
          </debuggers>
          <loaded_extensions>
            <extension name="Core" />
            <extension name="PDO" />
            <extension name="Phar" />
            <extension name="Reflection" />
            <extension name="SPL" />
            <extension name="SimpleXML" />
            <extension name="blitz" />
            <extension name="bz2" />
            <extension name="calendar" />
            <extension name="ctype" />
            <extension name="curl" />
            <extension name="date" />
            <extension name="dom" />
            <extension name="exif" />
            <extension name="fileinfo" />
            <extension name="filter" />
            <extension name="ftp" />
            <extension name="gd" />
            <extension name="gettext" />
            <extension name="hash" />
            <extension name="iconv" />
            <extension name="json" />
            <extension name="libxml" />
            <extension name="mbstring" />
            <extension name="mcrypt" />
            <extension name="mysqli" />
            <extension name="mysqlnd" />
            <extension name="openssl" />
            <extension name="pcntl" />
            <extension name="pcre" />
            <extension name="pdo_mysql" />
            <extension name="pdo_sqlite" />
            <extension name="readline" />
            <extension name="session" />
            <extension name="sockets" />
            <extension name="sodium" />
            <extension name="sqlite3" />
            <extension name="standard" />
            <extension name="tokenizer" />
            <extension name="xdebug" />
            <extension name="xml" />
            <extension name="xmlreader" />
            <extension name="xmlwriter" />
            <extension name="xsl" />
            <extension name="zlib" />
          </loaded_extensions>
        </phpinfo>
      </interpreter>
      <interpreter name="W27">
        <phpinfo binary_type="PHP" php_cgi="/usr/bin/php-cgi" php_cli="/usr/bin/php" path_separator=":" remote_host="*************" version="7.4.22">
          <additional_php_ini>/etc/php.d/10-opcache.ini, /etc/php.d/20-bcmath.ini, /etc/php.d/20-bz2.ini, /etc/php.d/20-calendar.ini, /etc/php.d/20-ctype.ini, /etc/php.d/20-curl.ini, /etc/php.d/20-dom.ini, /etc/php.d/20-enchant.ini, /etc/php.d/20-exif.ini, /etc/php.d/20-fileinfo.ini, /etc/php.d/20-ftp.ini, /etc/php.d/20-gd.ini, /etc/php.d/20-gettext.ini, /etc/php.d/20-iconv.ini, /etc/php.d/20-imap.ini, /etc/php.d/20-intl.ini, /etc/php.d/20-json.ini, /etc/php.d/20-ldap.ini, /etc/php.d/20-mbstring.ini, /etc/php.d/20-mysqlnd.ini, /etc/php.d/20-pdo.ini, /etc/php.d/20-phar.ini, /etc/php.d/20-posix.ini, /etc/php.d/20-shmop.ini, /etc/php.d/20-simplexml.ini, /etc/php.d/20-soap.ini, /etc/php.d/20-sockets.ini, /etc/php.d/20-sqlite3.ini, /etc/php.d/20-sysvmsg.ini, /etc/php.d/20-sysvsem.ini, /etc/php.d/20-sysvshm.ini, /etc/php.d/20-tokenizer.ini, /etc/php.d/20-xml.ini, /etc/php.d/20-xmlwriter.ini, /etc/php.d/20-xsl.ini, /etc/php.d/30-mcrypt.ini, /etc/php.d/30-mysqli.ini, /etc/php.d/30-pdo_mysql.ini, /etc/php.d/30-pdo_sqlite.ini, /etc/php.d/30-xmlreader.ini, /etc/php.d/40-apcu.ini, /etc/php.d/40-mailparse.ini, /etc/php.d/40-memcache.ini, /etc/php.d/40-zip.ini, /etc/php.d/50-mongodb.ini, /etc/php.d/blitz.ini, /etc/php.d/imagick.ini, /etc/php.d/xdebug.ini</additional_php_ini>
          <configuration_file>/etc/php.ini</configuration_file>
          <configuration_options>
            <configuration_option name="include_path" value=".:/usr/local/WWW/modules/ZendFramework:/usr/share/pear:/usr/local/WWW/modules" />
          </configuration_options>
          <debuggers>
            <debugger_info debugger="xdebug" debugger_version="3.0.4">
              <debug_extensions />
            </debugger_info>
          </debuggers>
          <loaded_extensions>
            <extension name="Core" />
            <extension name="PDO" />
            <extension name="Phar" />
            <extension name="Reflection" />
            <extension name="SPL" />
            <extension name="SimpleXML" />
            <extension name="Zend OPcache" />
            <extension name="apcu" />
            <extension name="bcmath" />
            <extension name="blitz" />
            <extension name="bz2" />
            <extension name="calendar" />
            <extension name="ctype" />
            <extension name="curl" />
            <extension name="date" />
            <extension name="dom" />
            <extension name="enchant" />
            <extension name="exif" />
            <extension name="fileinfo" />
            <extension name="filter" />
            <extension name="ftp" />
            <extension name="gd" />
            <extension name="gettext" />
            <extension name="hash" />
            <extension name="iconv" />
            <extension name="imagick" />
            <extension name="imap" />
            <extension name="intl" />
            <extension name="json" />
            <extension name="ldap" />
            <extension name="libxml" />
            <extension name="mailparse" />
            <extension name="mbstring" />
            <extension name="mcrypt" />
            <extension name="memcache" />
            <extension name="mongodb" />
            <extension name="mysqli" />
            <extension name="mysqlnd" />
            <extension name="openssl" />
            <extension name="pcntl" />
            <extension name="pcre" />
            <extension name="pdo_mysql" />
            <extension name="pdo_sqlite" />
            <extension name="posix" />
            <extension name="readline" />
            <extension name="session" />
            <extension name="shmop" />
            <extension name="soap" />
            <extension name="sockets" />
            <extension name="sqlite3" />
            <extension name="standard" />
            <extension name="sysvmsg" />
            <extension name="sysvsem" />
            <extension name="sysvshm" />
            <extension name="tokenizer" />
            <extension name="xdebug" />
            <extension name="xml" />
            <extension name="xmlreader" />
            <extension name="xmlwriter" />
            <extension name="xsl" />
            <extension name="zip" />
            <extension name="zlib" />
          </loaded_extensions>
        </phpinfo>
      </interpreter>
    </phpInfoCache>
  </component>
  <component name="PhpProjectSharedConfiguration" php_language_level="7.4">
    <option name="suggestChangeDefaultLanguageLevel" value="false" />
  </component>
  <component name="PhpStanOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PhpStepFilterConfiguration">
    <skipped_files>
      <skipped_file file="$PROJECT_DIR$/vendor/composer" />
    </skipped_files>
  </component>
  <component name="PhpUnit">
    <phpunit_settings>
      <phpunit_by_interpreter interpreter_id="22bae98a-7bde-457d-a741-b506bc897dd3" load_method="PHPUNIT_PHAR" custom_loader_path="/usr/local/WWW/W28/vendor/autoload.php" phpunit_phar_path="/usr/local/WWW/W28/vendor/phpunit/phpunit/phpunit" />
      <PhpUnitSettings configuration_file_path="$PROJECT_DIR$/phpunit.xml" custom_loader_path="$PROJECT_DIR$/vendor/autoload.php" use_configuration_file="true" />
    </phpunit_settings>
  </component>
  <component name="PsalmOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
</project>