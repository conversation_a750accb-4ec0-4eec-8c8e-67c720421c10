<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="DuplicatedCode" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <Languages>
        <language minSize="170" name="PHP" />
      </Languages>
    </inspection_tool>
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ForgottenDebugOutputInspection" enabled="true" level="ERROR" enabled_by_default="true">
      <option name="configuration">
        <list>
          <option value="\Codeception\Util\Debug::debug" />
          <option value="\Codeception\Util\Debug::pause" />
          <option value="\Doctrine\Common\Util\Debug::dump" />
          <option value="\Doctrine\Common\Util\Debug::export" />
          <option value="\Illuminate\Support\Debug\Dumper::dump" />
          <option value="\Symfony\Component\Debug\Debug::enable" />
          <option value="\Symfony\Component\Debug\DebugClassLoader::enable" />
          <option value="\Symfony\Component\Debug\ErrorHandler::register" />
          <option value="\Symfony\Component\Debug\ExceptionHandler::register" />
          <option value="\TYPO3\CMS\Core\Utility\DebugUtility::debug" />
          <option value="\Zend\Debug\Debug::dump" />
          <option value="\Zend\Di\Display\Console::export" />
          <option value="dd" />
          <option value="debug_print_backtrace" />
          <option value="debug_zval_dump" />
          <option value="dpm" />
          <option value="dpq" />
          <option value="dsm" />
          <option value="dump" />
          <option value="dvm" />
          <option value="error_log" />
          <option value="kpr" />
          <option value="phpinfo" />
          <option value="print_r" />
          <option value="var_dump" />
          <option value="var_export" />
          <option value="wp_die" />
          <option value="xdebug_break" />
          <option value="xdebug_call_class" />
          <option value="xdebug_call_file" />
          <option value="xdebug_call_function" />
          <option value="xdebug_call_line" />
          <option value="xdebug_code_coverage_started" />
          <option value="xdebug_debug_zval" />
          <option value="xdebug_debug_zval_stdout" />
          <option value="xdebug_dump_superglobals" />
          <option value="xdebug_enable" />
          <option value="xdebug_get_code_coverage" />
          <option value="xdebug_get_collected_errors" />
          <option value="xdebug_get_declared_vars" />
          <option value="xdebug_get_function_stack" />
          <option value="xdebug_get_headers" />
          <option value="xdebug_get_monitored_functions" />
          <option value="xdebug_get_profiler_filename" />
          <option value="xdebug_get_stack_depth" />
          <option value="xdebug_get_tracefile_name" />
          <option value="xdebug_is_enabled" />
          <option value="xdebug_memory_usage" />
          <option value="xdebug_peak_memory_usage" />
          <option value="xdebug_print_function_stack" />
          <option value="xdebug_start_code_coverage" />
          <option value="xdebug_start_error_collection" />
          <option value="xdebug_start_function_monitor" />
          <option value="xdebug_start_trace" />
          <option value="xdebug_stop_code_coverage" />
          <option value="xdebug_stop_error_collection" />
          <option value="xdebug_stop_function_monitor" />
          <option value="xdebug_stop_trace" />
          <option value="xdebug_time_index" />
          <option value="xdebug_var_dump" />
        </list>
      </option>
      <option name="migratedIntoUserSpace" value="true" />
    </inspection_tool>
    <inspection_tool class="HtmlUnknownAttribute" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="myValues">
        <value>
          <list size="2">
            <item index="0" class="java.lang.String" itemvalue="true-value" />
            <item index="1" class="java.lang.String" itemvalue="false-value" />
          </list>
        </value>
      </option>
      <option name="myCustomValuesEnabled" value="true" />
    </inspection_tool>
    <inspection_tool class="HtmlUnknownTag" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="myValues">
        <value>
          <list size="27">
            <item index="0" class="java.lang.String" itemvalue="nobr" />
            <item index="1" class="java.lang.String" itemvalue="noembed" />
            <item index="2" class="java.lang.String" itemvalue="comment" />
            <item index="3" class="java.lang.String" itemvalue="noscript" />
            <item index="4" class="java.lang.String" itemvalue="embed" />
            <item index="5" class="java.lang.String" itemvalue="script" />
            <item index="6" class="java.lang.String" itemvalue="validationprovider" />
            <item index="7" class="java.lang.String" itemvalue="container-dialog" />
            <item index="8" class="java.lang.String" itemvalue="x-block" />
            <item index="9" class="java.lang.String" itemvalue="x-blocks" />
            <item index="10" class="java.lang.String" itemvalue="x-grid" />
            <item index="11" class="java.lang.String" itemvalue="x-cell" />
            <item index="12" class="java.lang.String" itemvalue="x-grid-cell" />
            <item index="13" class="java.lang.String" itemvalue="x-page-layout" />
            <item index="14" class="java.lang.String" itemvalue="x-react-layout" />
            <item index="15" class="java.lang.String" itemvalue="x-react-layout-blocks" />
            <item index="16" class="java.lang.String" itemvalue="exo-layout-slot" />
            <item index="17" class="java.lang.String" itemvalue="x-layout" />
            <item index="18" class="java.lang.String" itemvalue="x-layout-slot" />
            <item index="19" class="java.lang.String" itemvalue="b-modal" />
            <item index="20" class="java.lang.String" itemvalue="b-form-input" />
            <item index="21" class="java.lang.String" itemvalue="dialog-modal" />
            <item index="22" class="java.lang.String" itemvalue="dialog-aside" />
            <item index="23" class="java.lang.String" itemvalue="b-form-group" />
            <item index="24" class="java.lang.String" itemvalue="b-form-radio" />
            <item index="25" class="java.lang.String" itemvalue="b-input-group" />
            <item index="26" class="java.lang.String" itemvalue="b-input-group-prepend" />
          </list>
        </value>
      </option>
      <option name="myCustomValuesEnabled" value="true" />
    </inspection_tool>
    <inspection_tool class="HttpUrlsUsage" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredUrls">
        <list>
          <option value="http://localhost" />
          <option value="http://127.0.0.1" />
          <option value="http://0.0.0.0" />
          <option value="http://www.w3.org/" />
          <option value="http://json-schema.org/draft" />
          <option value="http://java.sun.com/" />
          <option value="http://xmlns.jcp.org/" />
          <option value="http://javafx.com/javafx/" />
          <option value="http://javafx.com/fxml" />
          <option value="http://maven.apache.org/xsd/" />
          <option value="http://maven.apache.org/POM/" />
          <option value="http://www.springframework.org/schema/" />
          <option value="http://www.springframework.org/tags" />
          <option value="http://www.springframework.org/security/tags" />
          <option value="http://www.thymeleaf.org" />
          <option value="http://www.jboss.org/j2ee/schema/" />
          <option value="http://www.jboss.com/xml/ns/" />
          <option value="http://www.ibm.com/webservices/xsd" />
          <option value="http://activemq.apache.org/schema/" />
          <option value="http://schema.cloudfoundry.org/spring/" />
          <option value="http://schemas.xmlsoap.org/" />
          <option value="http://cxf.apache.org/schemas/" />
          <option value="http://primefaces.org/ui" />
          <option value="http://tiles.apache.org/" />
          <option value="http://" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="MagicMethodsValidityInspection" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="SecurityAdvisoriesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="optionConfiguration">
        <list>
          <option value="barryvdh/laravel-debugbar" />
          <option value="behat/behat" />
          <option value="brianium/paratest" />
          <option value="codeception/codeception" />
          <option value="codedungeon/phpunit-result-printer" />
          <option value="composer/composer" />
          <option value="doctrine/coding-standard" />
          <option value="filp/whoops" />
          <option value="friendsofphp/php-cs-fixer" />
          <option value="humbug/humbug" />
          <option value="infection/infection" />
          <option value="jakub-onderka/php-parallel-lint" />
          <option value="johnkary/phpunit-speedtrap" />
          <option value="kalessil/production-dependencies-guard" />
          <option value="mikey179/vfsStream" />
          <option value="mockery/mockery" />
          <option value="mybuilder/phpunit-accelerator" />
          <option value="orchestra/testbench" />
          <option value="pdepend/pdepend" />
          <option value="phan/phan" />
          <option value="phing/phing" />
          <option value="phpcompatibility/php-compatibility" />
          <option value="phpmd/phpmd" />
          <option value="phpro/grumphp" />
          <option value="phpspec/phpspec" />
          <option value="phpspec/prophecy" />
          <option value="phpstan/phpstan" />
          <option value="phpunit/phpunit" />
          <option value="povils/phpmnd" />
          <option value="roave/security-advisories" />
          <option value="satooshi/php-coveralls" />
          <option value="sebastian/phpcpd" />
          <option value="slevomat/coding-standard" />
          <option value="spatie/phpunit-watcher" />
          <option value="squizlabs/php_codesniffer" />
          <option value="sstalle/php7cc" />
          <option value="symfony/debug" />
          <option value="symfony/maker-bundle" />
          <option value="symfony/phpunit-bridge" />
          <option value="symfony/var-dumper" />
          <option value="vimeo/psalm" />
          <option value="wimg/php-compatibility" />
          <option value="wp-coding-standards/wpcs" />
          <option value="yiisoft/yii2-coding-standards" />
          <option value="yiisoft/yii2-debug" />
          <option value="yiisoft/yii2-gii" />
          <option value="zendframework/zend-coding-standard" />
          <option value="zendframework/zend-debug" />
          <option value="zendframework/zend-test" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="SlowArrayOperationsInLoopInspection" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="XmlUnboundNsPrefix" enabled="false" level="WARNING" enabled_by_default="false" />
  </profile>
</component>