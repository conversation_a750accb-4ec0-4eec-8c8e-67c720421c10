<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="PublishConfigData" autoUpload="Always" serverName="W30 - d02.fidela.net (DEV)" createEmptyFolders="true" uploadOnCheckin="ec01d733-5df9-403d-888b-db500960e9ea" confirmBeforeUploading="false" confirmBeforeDeletion="false" autoUploadExternalChanges="true" showAutoUploadSettingsWarning="false">
    <option name="confirmBeforeDeletion" value="false" />
    <option name="confirmBeforeUploading" value="false" />
    <serverData>
      <paths name="D01/W27 - d01.fidela.net">
        <serverdata>
          <mappings>
            <mapping deploy="/" local="$PROJECT_DIR$" web="/" />
          </mappings>
          <excludedPaths>
            <excludedPath path="/homepages.webkracht.nl/htdocs/sites/webkracht/vendor" />
            <excludedPath path="/homepages.webkracht.nl/htdocs/sites/webkracht/cache" />
            <excludedPath path="/homepages.webkracht.nl/htdocs/sites/webkracht/.vscode" />
            <excludedPath path="/homepages.webkracht.nl/htdocs/sites/webkracht/tmpl" />
            <excludedPath path="/homepages.webkracht.nl/htdocs/sites/webkracht/temp" />
            <excludedPath path="/homepages.webkracht.nl/htdocs/sites/webkracht/files" />
            <excludedPath path="/homepages.webkracht.nl/htdocs/sites/nem/cache" />
            <excludedPath path="/homepages.webkracht.nl/htdocs/sites/nem/media" />
            <excludedPath path="/homepages.webkracht.nl/htdocs/sites/nem/protected" />
          </excludedPaths>
        </serverdata>
      </paths>
      <paths name="D04/W29">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="D05">
        <serverdata>
          <mappings>
            <mapping deploy="/" local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="D06">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="W14 - VPS (test)">
        <serverdata>
          <mappings>
            <mapping deploy="/" local="$PROJECT_DIR$" web="" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="W18 - VPS d01.africanetic">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="W19 - VPS">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="W28 - dev">
        <serverdata>
          <mappings>
            <mapping deploy="/" local="$PROJECT_DIR$" web="" />
            <mapping deploy="/homepages.webkracht.nl/htdocs" local="$PROJECT_DIR$/homepages.webkracht.nl/htdocs" web="/" />
          </mappings>
          <excludedPaths>
            <excludedPath path="/vendor" />
            <excludedPath path="/storage" />
            <excludedPath path="/README-SEC.md" />
            <excludedPath path="/homepages.webkracht.nl/configs" />
            <excludedPath path="/resources/js" />
            <excludedPath path="/.fleet" />
            <excludedPath local="true" path="$PROJECT_DIR$/resources/js" />
            <excludedPath path="/homepages.webkracht.nl/htdocs/sites/route/cache" />
          </excludedPaths>
        </serverdata>
      </paths>
      <paths name="W30 - d02.fidela.net (DEV)">
        <serverdata>
          <mappings>
            <mapping deploy="/" local="$PROJECT_DIR$" web="/" />
          </mappings>
          <excludedPaths>
            <excludedPath path="/configs/server.inc.php" />
            <excludedPath path="/resources/js" />
            <excludedPath path="/storage" />
            <excludedPath path="/homepages.webkracht.nl/htdocs/sites/route/resources/node_modules" />
          </excludedPaths>
        </serverdata>
      </paths>
      <paths name="W31 - VPS (zonweringstunter)">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="W41 - d03">
        <serverdata>
          <mappings>
            <mapping deploy="/" local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="dev01.zonweringstunter.com">
        <serverdata>
          <mappings>
            <mapping deploy="/" local="$PROJECT_DIR$" web="" />
            <mapping deploy="/homepages.webkracht.nl/htdocs" local="$PROJECT_DIR$/homepages.webkracht.nl/htdocs" web="/" />
          </mappings>
          <excludedPaths>
            <excludedPath path="/vendor" />
            <excludedPath path="/storage" />
            <excludedPath path="/.gitlab" />
            <excludedPath path="/homepages.webkracht.nl/src" />
            <excludedPath path="/exologadmin.webkracht.nl/src" />
            <excludedPath path="/configs/server.inc.php" />
            <excludedPath path="/README-SEC.md" />
            <excludedPath path="/homepages.webkracht.nl/configs" />
          </excludedPaths>
        </serverdata>
      </paths>
    </serverData>
    <option name="myUploadOnCheckinName" value="W28 - PHP7" />
    <option name="myAutoUpload" value="ALWAYS" />
  </component>
</project>