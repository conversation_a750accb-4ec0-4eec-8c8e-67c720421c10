#!/bin/bash

# Exolog ReactJson - Local Development Setup Script
# This script automates the setup process for Apple Silicon Macs

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if running on macOS
    if [[ "$OSTYPE" != "darwin"* ]]; then
        print_error "This script is designed for macOS. Detected OS: $OSTYPE"
        exit 1
    fi
    
    # Check for Apple Silicon
    if [[ $(uname -m) != "arm64" ]]; then
        print_warning "This script is optimized for Apple Silicon. Detected architecture: $(uname -m)"
    fi
    
    # Check Docker
    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker Desktop from https://www.docker.com/products/docker-desktop"
        exit 1
    fi
    
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker Desktop."
        exit 1
    fi
    
    # Check Git
    if ! command_exists git; then
        print_error "Git is not installed. Please install Git."
        exit 1
    fi
    
    print_success "All prerequisites are met!"
}

# Function to setup environment
setup_environment() {
    print_status "Setting up environment configuration..."
    
    # Create .env.local if it doesn't exist
    if [[ ! -f .env.local ]]; then
        if [[ -f .env.example ]]; then
            cp .env.example .env.local
            print_success "Created .env.local from .env.example"
        else
            # Create basic .env.local
            cat > .env.local << EOF
APP_NAME="Exolog Local"
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost:8080

DB_CONNECTION=sqlite
DB_DATABASE=/app/database/database.sqlite

CLOUDFLARE_API_TOKEN=
CLOUDFLARE_ZONE_ID=

MAIL_MAILER=log
MAIL_HOST=localhost
MAIL_PORT=1025

SESSION_DRIVER=file
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
EOF
            print_success "Created basic .env.local file"
        fi
    else
        print_warning ".env.local already exists, skipping..."
    fi
}

# Function to setup database
setup_database() {
    print_status "Setting up database..."
    
    # Create database directory
    mkdir -p database
    
    # Create SQLite database file
    if [[ ! -f database/database.sqlite ]]; then
        touch database/database.sqlite
        print_success "Created SQLite database file"
    else
        print_warning "Database file already exists, skipping..."
    fi
    
    # Initialize database with SQL script
    if [[ -f setup-docs/init-database.sql ]]; then
        sqlite3 database/database.sqlite < setup-docs/init-database.sql
        print_success "Initialized database with default data"
    else
        print_warning "Database initialization script not found, skipping..."
    fi
}

# Function to build Docker containers
build_containers() {
    print_status "Building Docker containers..."
    
    if [[ -f docker-compose.local.yml ]]; then
        docker-compose -f docker-compose.local.yml build
        print_success "Docker containers built successfully"
    else
        print_error "docker-compose.local.yml not found!"
        exit 1
    fi
}

# Function to start services
start_services() {
    print_status "Starting Docker services..."
    
    docker-compose -f docker-compose.local.yml up -d
    
    # Wait for services to be ready
    print_status "Waiting for services to start..."
    sleep 10
    
    # Check if services are running
    if docker-compose -f docker-compose.local.yml ps | grep -q "Up"; then
        print_success "Docker services started successfully"
    else
        print_error "Failed to start Docker services"
        docker-compose -f docker-compose.local.yml logs
        exit 1
    fi
}

# Function to install dependencies
install_dependencies() {
    print_status "Installing PHP dependencies..."
    
    # Install Composer dependencies
    docker-compose -f docker-compose.local.yml exec -T app composer install
    
    # Generate application key
    docker-compose -f docker-compose.local.yml exec -T app php artisan key:generate
    
    print_status "Installing Node.js dependencies..."
    
    # Install npm dependencies and build frontend
    docker-compose -f docker-compose.local.yml exec -T node bash -c "
        cd resources/js/dealer && 
        npm install && 
        npm run build:direct:dealer
    "
    
    print_success "Dependencies installed successfully"
}

# Function to setup hosts file
setup_hosts() {
    print_status "Setting up hosts file..."
    
    # Check if entries already exist
    if grep -q "exolog.local" /etc/hosts; then
        print_warning "Hosts entries already exist, skipping..."
        return
    fi
    
    print_status "Adding entries to /etc/hosts (requires sudo)..."
    
    # Add hosts entries
    sudo bash -c 'cat >> /etc/hosts << EOF

# Exolog Local Development
127.0.0.1 exolog.local
127.0.0.1 dealer.exolog.local
127.0.0.1 admin.exolog.local
EOF'
    
    print_success "Hosts file updated"
}

# Function to display final information
display_info() {
    print_success "Setup completed successfully!"
    echo
    echo -e "${GREEN}🎉 Exolog ReactJson is now ready for local development!${NC}"
    echo
    echo -e "${BLUE}Access URLs:${NC}"
    echo "  • Main Application: http://exolog.local:8080"
    echo "  • Dealer Panel:     http://dealer.exolog.local:8080"
    echo "  • Admin Panel:      http://admin.exolog.local:8080"
    echo
    echo -e "${BLUE}Default Login:${NC}"
    echo "  • Username: admin"
    echo "  • Password: admin123"
    echo
    echo -e "${BLUE}Useful Commands:${NC}"
    echo "  • View logs:        docker-compose -f docker-compose.local.yml logs -f"
    echo "  • Stop services:    docker-compose -f docker-compose.local.yml down"
    echo "  • Restart services: docker-compose -f docker-compose.local.yml restart"
    echo "  • Build frontend:   docker-compose -f docker-compose.local.yml exec node bash"
    echo
    echo -e "${YELLOW}Next Steps:${NC}"
    echo "  1. Configure your Cloudflare API token in .env.local"
    echo "  2. Set up your IDE for PHP and Vue.js development"
    echo "  3. Review the documentation in setup-docs/README.md"
    echo
}

# Main execution
main() {
    echo -e "${GREEN}🚀 Exolog ReactJson - Local Development Setup${NC}"
    echo -e "${BLUE}This script will set up the project for local development on Apple Silicon${NC}"
    echo
    
    check_prerequisites
    setup_environment
    setup_database
    build_containers
    start_services
    install_dependencies
    setup_hosts
    display_info
}

# Run main function
main "$@"
