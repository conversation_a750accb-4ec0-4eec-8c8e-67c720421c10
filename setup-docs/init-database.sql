-- Exolog ReactJson - Database Initialization Script
-- This script creates the basic database structure for local development

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    email_verified_at TIMESTAMP NULL,
    password VARCHAR(255) NOT NULL,
    remember_token VARCHAR(100) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create sites table
CREATE TABLE IF NOT EXISTS sites (
    site_id INTEGER PRIMARY KEY AUTOINCREMENT,
    site_name VARCHAR(255) NOT NULL,
    site_alias VARCHAR(255) UNIQUE NOT NULL,
    site_status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create domains table
CREATE TABLE IF NOT EXISTS domain (
    domain_id INTEGER PRIMARY KEY AUTOINCREMENT,
    domain_name VARCHAR(255) <PERSON>IQUE NOT NULL,
    domain_site INTEGER NOT NULL,
    domain_ismain INTEGER DEFAULT 0,
    domain_isdefault INTEGER DEFAULT 0,
    domain_isssl INTEGER DEFAULT 0,
    domain_isletsencrypt INTEGER DEFAULT 1,
    domain_ismail INTEGER DEFAULT 0,
    domain_ssl_status VARCHAR(50) DEFAULT 'OK',
    domain_edition_id INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (domain_site) REFERENCES sites(site_id)
);

-- Create editions table
CREATE TABLE IF NOT EXISTS editions (
    e_id INTEGER PRIMARY KEY AUTOINCREMENT,
    e_title VARCHAR(255) NOT NULL,
    e_description TEXT,
    e_status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default data

-- Default user (admin/admin123)
INSERT OR IGNORE INTO users (id, name, email, password) VALUES 
(1, 'Administrator', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi');

-- Default edition
INSERT OR IGNORE INTO editions (e_id, e_title, e_description) VALUES 
(1, 'Default Edition', 'Default edition for local development');

-- Default site
INSERT OR IGNORE INTO sites (site_id, site_name, site_alias) VALUES 
(1, 'Local Development Site', 'demo');

-- Default domain
INSERT OR IGNORE INTO domain (
    domain_id, 
    domain_name, 
    domain_site, 
    domain_ismain, 
    domain_isdefault, 
    domain_isssl, 
    domain_isletsencrypt,
    domain_edition_id
) VALUES 
(1, 'demo.localhost', 1, 1, 1, 1, 1, 1);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_domain_site ON domain(domain_site);
CREATE INDEX IF NOT EXISTS idx_domain_name ON domain(domain_name);
CREATE INDEX IF NOT EXISTS idx_sites_alias ON sites(site_alias);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- Create configuration table for application settings
CREATE TABLE IF NOT EXISTS config (
    config_key VARCHAR(255) PRIMARY KEY,
    config_value TEXT,
    config_description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default configuration
INSERT OR IGNORE INTO config (config_key, config_value, config_description) VALUES 
('app_name', 'Exolog Local', 'Application name'),
('server_domain', 'localhost', 'Server domain for subdomains'),
('default_edition_id', '1', 'Default edition ID for new domains'),
('cloudflare_enabled', '1', 'Enable Cloudflare DNS management'),
('ssl_enabled', '1', 'Enable SSL certificate management');

-- Create migrations table to track database version
CREATE TABLE IF NOT EXISTS migrations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    migration VARCHAR(255) NOT NULL,
    batch INTEGER NOT NULL
);

-- Insert initial migration record
INSERT OR IGNORE INTO migrations (migration, batch) VALUES 
('2024_01_01_000000_create_initial_tables', 1);
