# Troubleshooting Guide

This guide covers common issues and solutions when setting up Exolog ReactJson on Apple Silicon Macs.

## Common Issues

### 1. Docker Issues

#### Docker Desktop Not Starting
**Problem**: Docker Desktop fails to start or shows errors.

**Solutions**:
```bash
# Reset Docker Desktop
# Go to Docker Desktop > Troubleshoot > Reset to factory defaults

# Or restart Docker from command line
killall Docker && open /Applications/Docker.app

# Check Docker version (should support Apple Silicon)
docker --version
```

#### Port Already in Use (8080)
**Problem**: Error message "Port 8080 is already in use"

**Solutions**:
```bash
# Find what's using port 8080
lsof -i :8080

# Kill the process
kill -9 <PID>

# Or use a different port in docker-compose.local.yml
# Change "8080:80" to "8081:80"
```

#### Container Build Failures
**Problem**: Docker containers fail to build

**Solutions**:
```bash
# Clean Docker cache
docker system prune -a

# Rebuild without cache
docker-compose -f docker-compose.local.yml build --no-cache

# Check Docker logs
docker-compose -f docker-compose.local.yml logs
```

### 2. Database Issues

#### SQLite Permission Errors
**Problem**: Cannot write to database file

**Solutions**:
```bash
# Fix file permissions
chmod 664 database/database.sqlite
chmod 755 database/

# Recreate database
rm database/database.sqlite
touch database/database.sqlite
sqlite3 database/database.sqlite < setup-docs/init-database.sql
```

#### Database Connection Errors
**Problem**: Application cannot connect to database

**Solutions**:
```bash
# Check database file exists
ls -la database/database.sqlite

# Verify database path in .env.local
grep DB_DATABASE .env.local

# Test database connection
docker-compose -f docker-compose.local.yml exec app php artisan tinker
# In tinker: DB::connection()->getPdo();
```

### 3. Frontend Build Issues

#### npm Install Failures
**Problem**: Node.js dependencies fail to install

**Solutions**:
```bash
# Clear npm cache
docker-compose -f docker-compose.local.yml exec node npm cache clean --force

# Remove node_modules and reinstall
docker-compose -f docker-compose.local.yml exec node bash
cd resources/js/dealer
rm -rf node_modules package-lock.json
npm install
```

#### Build Process Errors
**Problem**: Frontend build fails with errors

**Solutions**:
```bash
# Check Node.js version (should be 16+)
docker-compose -f docker-compose.local.yml exec node node --version

# Build with verbose output
docker-compose -f docker-compose.local.yml exec node bash
cd resources/js/dealer
npm run build:direct:dealer -- --verbose

# Check for syntax errors
npm run lint
```

### 4. PHP/Laravel Issues

#### Composer Install Failures
**Problem**: PHP dependencies fail to install

**Solutions**:
```bash
# Clear Composer cache
docker-compose -f docker-compose.local.yml exec app composer clear-cache

# Install with verbose output
docker-compose -f docker-compose.local.yml exec app composer install -v

# Check PHP version
docker-compose -f docker-compose.local.yml exec app php --version
```

#### Application Key Errors
**Problem**: "No application encryption key has been specified"

**Solutions**:
```bash
# Generate new application key
docker-compose -f docker-compose.local.yml exec app php artisan key:generate

# Check .env.local file
grep APP_KEY .env.local
```

#### Permission Errors
**Problem**: Laravel cannot write to storage directories

**Solutions**:
```bash
# Fix Laravel permissions
docker-compose -f docker-compose.local.yml exec app bash
chmod -R 775 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache
```

### 5. Network/Access Issues

#### Cannot Access Application URLs
**Problem**: Browser shows "This site can't be reached"

**Solutions**:
```bash
# Check hosts file
cat /etc/hosts | grep exolog

# Add missing entries
sudo bash -c 'echo "127.0.0.1 exolog.local" >> /etc/hosts'
sudo bash -c 'echo "127.0.0.1 dealer.exolog.local" >> /etc/hosts'
sudo bash -c 'echo "127.0.0.1 admin.exolog.local" >> /etc/hosts'

# Check if containers are running
docker-compose -f docker-compose.local.yml ps

# Test port connectivity
curl -I http://localhost:8080
```

#### SSL/HTTPS Issues
**Problem**: SSL certificate errors in development

**Solutions**:
```bash
# Disable SSL in development
# In .env.local, set:
APP_URL=http://localhost:8080

# Or generate self-signed certificates
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes
```

### 6. Performance Issues

#### Slow Container Startup
**Problem**: Containers take a long time to start

**Solutions**:
```bash
# Increase Docker Desktop resources
# Docker Desktop > Settings > Resources
# Increase CPU and Memory allocation

# Use Docker BuildKit for faster builds
export DOCKER_BUILDKIT=1
docker-compose -f docker-compose.local.yml build
```

#### High CPU/Memory Usage
**Problem**: Docker containers consume too many resources

**Solutions**:
```bash
# Monitor resource usage
docker stats

# Limit container resources in docker-compose.local.yml
services:
  app:
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
```

## Diagnostic Commands

### Check System Status
```bash
# Docker status
docker info
docker-compose -f docker-compose.local.yml ps

# Container logs
docker-compose -f docker-compose.local.yml logs app
docker-compose -f docker-compose.local.yml logs nginx
docker-compose -f docker-compose.local.yml logs node

# System resources
top -o cpu
df -h
```

### Test Application Components
```bash
# Test database connection
docker-compose -f docker-compose.local.yml exec app php artisan tinker
# In tinker: DB::connection()->getPdo();

# Test web server
curl -I http://localhost:8080

# Test PHP
docker-compose -f docker-compose.local.yml exec app php -v

# Test Node.js
docker-compose -f docker-compose.local.yml exec node node --version
```

## Getting Help

### Log Files to Check
- Docker container logs: `docker-compose logs`
- Laravel logs: `storage/logs/laravel.log`
- Nginx logs: Inside nginx container at `/var/log/nginx/`
- System logs: Console.app on macOS

### Information to Gather
When reporting issues, include:
1. macOS version and chip type (M1/M2/M3)
2. Docker Desktop version
3. Error messages (full stack traces)
4. Output of `docker-compose ps`
5. Relevant log files
6. Steps to reproduce the issue

### Reset Everything
If all else fails, complete reset:
```bash
# Stop and remove containers
docker-compose -f docker-compose.local.yml down -v

# Remove all Docker data
docker system prune -a --volumes

# Remove local files
rm -rf database/database.sqlite
rm .env.local

# Start fresh
./setup-docs/setup-local.sh
```

## Prevention Tips

1. **Regular Updates**: Keep Docker Desktop updated
2. **Resource Monitoring**: Monitor Docker resource usage
3. **Clean Builds**: Occasionally run `docker system prune`
4. **Backup**: Keep backups of working configurations
5. **Documentation**: Document any custom changes you make
