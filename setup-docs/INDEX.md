# Exolog ReactJson - Setup Documentation

Welcome to the Exolog ReactJson setup documentation. This folder contains comprehensive guides for setting up and developing with the project on Apple Silicon Macs.

## 📚 Documentation Files

### 🚀 [README.md](README.md)
**Main setup guide** - Complete step-by-step instructions for setting up the project locally.

**What's included:**
- Prerequisites and system requirements
- Environment configuration
- Docker setup and container management
- Database initialization
- Frontend and backend dependency installation
- Access URLs and default credentials

**Perfect for:** First-time setup, new developers joining the project

---

### 🔧 [setup-local.sh](setup-local.sh)
**Automated setup script** - One-command setup for the entire development environment.

**Features:**
- Automated prerequisite checking
- Environment file creation
- Database initialization
- Docker container building and startup
- Dependency installation
- Hosts file configuration

**Usage:**
```bash
chmod +x setup-docs/setup-local.sh
./setup-docs/setup-local.sh
```

---

### 🛠️ [DEVELOPMENT.md](DEVELOPMENT.md)
**Development workflow guide** - Daily development tasks, debugging, and best practices.

**What's covered:**
- Frontend development with Vue.js
- Backend development with Laravel
- API testing and debugging
- Database management
- Code quality tools
- Git workflow
- Performance optimization

**Perfect for:** Active development, learning the codebase structure

---

### 🚨 [TROUBLESHOOTING.md](TROUBLESHOOTING.md)
**Problem-solving guide** - Solutions for common issues and debugging techniques.

**Issue categories:**
- Docker and container problems
- Database connection issues
- Frontend build failures
- PHP/Laravel errors
- Network and access problems
- Performance issues

**Perfect for:** When things go wrong, debugging problems

---

### 🗄️ [init-database.sql](init-database.sql)
**Database initialization script** - SQL script to set up the initial database structure.

**What's included:**
- Core table creation (users, sites, domains, editions)
- Default data insertion
- Index creation for performance
- Configuration table setup
- Migration tracking

**Usage:**
```bash
sqlite3 database/database.sqlite < setup-docs/init-database.sql
```

---

## 🎯 Quick Start

### For Complete Beginners
1. Start with [README.md](README.md) for detailed setup instructions
2. Use [setup-local.sh](setup-local.sh) for automated setup
3. Refer to [TROUBLESHOOTING.md](TROUBLESHOOTING.md) if you encounter issues

### For Experienced Developers
1. Run `./setup-docs/setup-local.sh` for quick setup
2. Check [DEVELOPMENT.md](DEVELOPMENT.md) for workflow details
3. Use [TROUBLESHOOTING.md](TROUBLESHOOTING.md) as needed

### For Project Maintenance
1. Update [README.md](README.md) when setup process changes
2. Modify [setup-local.sh](setup-local.sh) for new automation needs
3. Add new issues to [TROUBLESHOOTING.md](TROUBLESHOOTING.md)
4. Update [init-database.sql](init-database.sql) for schema changes

## 🔄 Setup Process Overview

```mermaid
graph TD
    A[Clone Repository] --> B[Check Prerequisites]
    B --> C[Run setup-local.sh]
    C --> D[Environment Setup]
    D --> E[Database Init]
    E --> F[Docker Build]
    F --> G[Start Services]
    G --> H[Install Dependencies]
    H --> I[Configure Hosts]
    I --> J[Access Application]
    
    K[Manual Setup] --> L[Follow README.md]
    L --> M[Step-by-step process]
    M --> J
    
    N[Issues?] --> O[Check TROUBLESHOOTING.md]
    O --> P[Apply Solutions]
    P --> J
```

## 🎨 Project Features

### DNS Management
- **Cloudflare Integration**: Automatic DNS record management
- **A Record Setup**: Main domain pointing with proxy
- **WWW CNAME**: Subdomain handling with proxy
- **Page Rules**: www → non-www redirects (301)
- **Real-time Status**: Live DNS validation and status checking

### Frontend (Vue.js)
- **Modern Stack**: Vue.js 3 with Composition API
- **Component Library**: Bootstrap Vue for UI components
- **Development Tools**: Hot reload, linting, automated builds
- **Domain Management**: Interactive domain validation and setup

### Backend (Laravel)
- **Framework**: Laravel 9+ with modern PHP features
- **Database**: SQLite for local development
- **API**: RESTful endpoints for domain management
- **Services**: Modular DNS and domain handling services

## 🌟 Key Benefits

### For Developers
- **Fast Setup**: Automated script gets you running in minutes
- **Apple Silicon Optimized**: Specifically designed for M1/M2/M3 Macs
- **Docker-based**: Consistent environment across machines
- **Hot Reload**: Instant feedback during development

### For Teams
- **Standardized Environment**: Everyone runs the same setup
- **Comprehensive Documentation**: Detailed guides for all scenarios
- **Troubleshooting Support**: Solutions for common problems
- **Version Controlled**: Setup process is tracked in git

### For Maintenance
- **Modular Documentation**: Easy to update specific sections
- **Automated Testing**: Setup script validates prerequisites
- **Error Handling**: Graceful failure with helpful messages
- **Reset Capability**: Easy to start fresh when needed

## 📞 Support

### Getting Help
1. **Check Documentation**: Start with the relevant guide above
2. **Search Issues**: Look through troubleshooting guide
3. **Check Logs**: Use Docker logs for debugging
4. **Reset Environment**: Use clean setup when all else fails

### Contributing
1. **Update Documentation**: Keep guides current with changes
2. **Report Issues**: Add new problems to troubleshooting guide
3. **Improve Scripts**: Enhance automation and error handling
4. **Share Solutions**: Document fixes for future reference

---

**Happy coding! 🚀**

*Last updated: Based on conversation history through July 2025*
