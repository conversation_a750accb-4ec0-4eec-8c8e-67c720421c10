# Development Workflow Guide

This guide covers the development workflow for Exolog ReactJson, including common tasks, debugging, and best practices.

## Daily Development Workflow

### Starting Development
```bash
# Start all services
docker-compose -f docker-compose.local.yml up -d

# Check services are running
docker-compose -f docker-compose.local.yml ps

# Start frontend watch mode (in separate terminal)
docker-compose -f docker-compose.local.yml exec node bash
cd resources/js/dealer
npm run watch
```

### Stopping Development
```bash
# Stop all services
docker-compose -f docker-compose.local.yml down

# Or just stop without removing containers
docker-compose -f docker-compose.local.yml stop
```

## Frontend Development

### Vue.js Development
```bash
# Access Node container
docker-compose -f docker-compose.local.yml exec node bash
cd resources/js/dealer

# Install new package
npm install package-name

# Build for development
npm run dev

# Build for production
npm run build:direct:dealer

# Watch for changes (auto-rebuild)
npm run watch

# Lint code
npm run lint

# Fix linting issues
npm run lint:fix
```

### Frontend File Structure
```
resources/js/dealer/
├── src/
│   ├── components/          # Vue components
│   │   ├── dialogs/        # Modal dialogs
│   │   └── forms/          # Form components
│   ├── views/              # Page components
│   │   └── sites/          # Site management views
│   ├── services/           # API services
│   ├── store/              # Vuex store
│   └── router/             # Vue Router
├── package.json            # Dependencies
└── webpack.config.js       # Build configuration
```

### Key Frontend Components
- **DomainValidationDialog.vue** - Domain setup with DNS management
- **SiteDomains.vue** - Domain list and management
- **DomainEditDialog.vue** - Domain editing interface
- **DomainValidationService.js** - DNS validation logic

## Backend Development

### PHP/Laravel Development
```bash
# Access PHP container
docker-compose -f docker-compose.local.yml exec app bash

# Run artisan commands
php artisan make:controller ExampleController
php artisan make:model Example
php artisan make:migration create_examples_table

# Run migrations
php artisan migrate

# Seed database
php artisan db:seed

# Clear caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear

# Generate IDE helper files
php artisan ide-helper:generate
```

### Backend File Structure
```
lib/Exolog/
├── Dealer/
│   ├── Controllers/        # API controllers
│   └── Handler/           # Business logic handlers
├── Module/
│   ├── DNS/               # DNS management services
│   │   ├── CloudflareDnsSetup.php
│   │   └── CloudflarePageRules.php
│   └── Domains/           # Domain models and traits
└── Support/               # Utility classes
```

### Key Backend Components
- **DomainController.php** - Domain management API
- **CloudflareDnsSetup.php** - DNS record management
- **CloudflarePageRules.php** - Page rule management
- **HasDomainSaveFrom.php** - Domain saving logic

## Database Development

### Working with SQLite
```bash
# Access database directly
sqlite3 database/database.sqlite

# Common SQLite commands
.tables                    # List tables
.schema table_name         # Show table structure
.dump                      # Export database
.quit                      # Exit

# Run SQL queries
SELECT * FROM domain;
```

### Database Migrations
```bash
# Create new migration
php artisan make:migration add_column_to_table

# Run migrations
php artisan migrate

# Rollback migrations
php artisan migrate:rollback

# Reset database
php artisan migrate:fresh --seed
```

## API Development

### Testing API Endpoints
```bash
# Test domain validation
curl -X POST http://localhost:8080/api/domain/validate \
  -H "Content-Type: application/json" \
  -d '{"domain_name": "example.com"}'

# Test DNS setup
curl -X POST http://localhost:8080/api/domain/setupComplete \
  -H "Content-Type: application/json" \
  -d '{"domain_name": "example.com", "create_www_rule": true}'

# Check DNS status
curl -X POST http://localhost:8080/api/domain/checkDnsStatus \
  -H "Content-Type: application/json" \
  -d '{"domain_name": "example.com"}'
```

### API Endpoints
- `POST /api/domain/validate` - Validate domain
- `POST /api/domain/setupComplete` - Complete domain setup
- `POST /api/domain/checkDnsStatus` - Check DNS status
- `POST /api/domain/updateDnsRecords` - Update DNS records

## Debugging

### Frontend Debugging
```bash
# Browser console
# Open DevTools (F12) and check Console tab

# Vue DevTools
# Install Vue DevTools browser extension

# Check network requests
# DevTools > Network tab

# Component debugging
console.log('Debug info:', data);
```

### Backend Debugging
```bash
# Laravel logs
tail -f storage/logs/laravel.log

# Debug with dd() or dump()
dd($variable);
dump($variable);

# Log debugging
Log::info('Debug message', ['data' => $data]);

# Tinker (Laravel REPL)
php artisan tinker
```

### Docker Debugging
```bash
# Container logs
docker-compose -f docker-compose.local.yml logs app
docker-compose -f docker-compose.local.yml logs nginx
docker-compose -f docker-compose.local.yml logs node

# Follow logs in real-time
docker-compose -f docker-compose.local.yml logs -f

# Execute commands in containers
docker-compose -f docker-compose.local.yml exec app bash
docker-compose -f docker-compose.local.yml exec node bash

# Check container status
docker-compose -f docker-compose.local.yml ps
```

## Testing

### Frontend Testing
```bash
# Run tests
npm run test

# Run tests with coverage
npm run test:coverage

# Run specific test
npm run test -- --grep "component name"
```

### Backend Testing
```bash
# Run PHPUnit tests
php artisan test

# Run specific test
php artisan test --filter TestName

# Generate test coverage
php artisan test --coverage
```

## Code Quality

### Frontend Code Quality
```bash
# ESLint
npm run lint
npm run lint:fix

# Prettier
npm run format

# Type checking (if using TypeScript)
npm run type-check
```

### Backend Code Quality
```bash
# PHP CS Fixer
./vendor/bin/php-cs-fixer fix

# PHPStan
./vendor/bin/phpstan analyse

# PHPMD
./vendor/bin/phpmd lib text cleancode,codesize,controversial,design,naming,unusedcode
```

## Git Workflow

### Branch Management
```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes and commit
git add .
git commit -m "Add new feature"

# Push to remote
git push origin feature/new-feature

# Create merge request
# Use GitLab web interface
```

### Commit Message Format
```
type(scope): description

Examples:
feat(dns): add Cloudflare Page Rules support
fix(domain): resolve DNS validation issue
docs(setup): update installation guide
refactor(api): improve error handling
```

## Performance Optimization

### Frontend Performance
```bash
# Analyze bundle size
npm run build:analyze

# Optimize images
# Use appropriate formats (WebP, AVIF)
# Compress images before adding to project

# Code splitting
# Use dynamic imports for large components
const Component = () => import('./Component.vue');
```

### Backend Performance
```bash
# Database query optimization
# Use Laravel Debugbar to identify slow queries

# Cache optimization
php artisan cache:clear
php artisan config:cache
php artisan route:cache

# Optimize autoloader
composer dump-autoload --optimize
```

## Deployment Preparation

### Build for Production
```bash
# Frontend production build
docker-compose -f docker-compose.local.yml exec node bash
cd resources/js/dealer
npm run build:direct:dealer

# Backend optimization
docker-compose -f docker-compose.local.yml exec app bash
composer install --optimize-autoloader --no-dev
php artisan config:cache
php artisan route:cache
```

### Environment Configuration
```bash
# Production environment variables
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

# Database configuration
DB_CONNECTION=mysql
DB_HOST=your-db-host
DB_DATABASE=your-database

# Cloudflare configuration
CLOUDFLARE_API_TOKEN=your-production-token
```

## Best Practices

### Code Organization
- Keep components small and focused
- Use consistent naming conventions
- Separate business logic from presentation
- Write comprehensive comments
- Follow PSR standards for PHP

### Security
- Validate all user inputs
- Use CSRF protection
- Sanitize database queries
- Keep dependencies updated
- Use environment variables for secrets

### Performance
- Minimize database queries
- Use appropriate caching strategies
- Optimize frontend bundle size
- Compress assets
- Use CDN for static files
