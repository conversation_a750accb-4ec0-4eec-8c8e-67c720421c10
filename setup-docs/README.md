# Exolog ReactJson - Local Development Setup Guide

## Overview
This guide provides step-by-step instructions for setting up the Exolog ReactJson project on Apple Silicon (M1/M2/M3) Macs for local development.

## Prerequisites

### Required Software
- **Docker Desktop** (latest version with Apple Silicon support)
- **Git** (for cloning the repository)
- **Node.js** (v16+ recommended)
- **PHP** (v8.1+ recommended)
- **Composer** (PHP dependency manager)

### System Requirements
- macOS 12.0+ (Monterey or later)
- Apple Silicon chip (M1/M2/M3)
- At least 8GB RAM
- 10GB free disk space

## Project Structure
```
exolog-reactJson/
├── docker/                    # Docker configuration
├── lib/                      # PHP backend code
├── resources/js/dealer/      # Vue.js frontend
├── database/                 # Database files
├── setup-docs/              # This documentation
└── docker-compose.local.yml # Local development config
```

## Step 1: Clone the Repository

```bash
<NAME_EMAIL>:fidela/exolog-reactJson.git
cd exolog-reactJson
```

## Step 2: Environment Configuration

### Create Local Environment File
```bash
cp .env.example .env.local
```

### Configure .env.local
Edit `.env.local` with the following settings:

```env
# Application
APP_NAME="Exolog Local"
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost:8080

# Database
DB_CONNECTION=sqlite
DB_DATABASE=/app/database/database.sqlite

# Cloudflare API (for DNS management)
CLOUDFLARE_API_TOKEN=your_cloudflare_token_here
CLOUDFLARE_ZONE_ID=your_zone_id_here

# Mail Configuration
MAIL_MAILER=log
MAIL_HOST=localhost
MAIL_PORT=1025

# Session & Cache
SESSION_DRIVER=file
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
```

## Step 3: Database Setup

### Create SQLite Database
```bash
mkdir -p database
touch database/database.sqlite
```

### Initialize Database Schema
Run the database initialization script:

```bash
php artisan migrate:fresh --seed
```

## Step 4: Docker Configuration

### Build and Start Containers
```bash
# Build the containers
docker-compose -f docker-compose.local.yml build

# Start the services
docker-compose -f docker-compose.local.yml up -d
```

### Verify Containers are Running
```bash
docker-compose -f docker-compose.local.yml ps
```

You should see:
- `exolog-app` (PHP application)
- `exolog-nginx` (Web server)
- `exolog-node` (Node.js for frontend builds)

## Step 5: Install Dependencies

### PHP Dependencies
```bash
# Enter the app container
docker-compose -f docker-compose.local.yml exec app bash

# Install Composer dependencies
composer install

# Generate application key
php artisan key:generate

# Exit container
exit
```

### Frontend Dependencies
```bash
# Enter the node container
docker-compose -f docker-compose.local.yml exec node bash

# Navigate to dealer frontend
cd resources/js/dealer

# Install npm dependencies
npm install

# Build frontend assets
npm run build:direct:dealer

# Exit container
exit
```

## Step 6: Configure Hosts File

Add the following entries to your `/etc/hosts` file:

```bash
sudo nano /etc/hosts
```

Add these lines:
```
127.0.0.1 exolog.local
127.0.0.1 dealer.exolog.local
127.0.0.1 admin.exolog.local
```

## Step 7: Access the Application

### URLs
- **Main Application**: http://exolog.local:8080
- **Dealer Panel**: http://dealer.exolog.local:8080
- **Admin Panel**: http://admin.exolog.local:8080

### Default Login Credentials
- **Username**: admin
- **Password**: admin123

## Step 8: Development Workflow

### Frontend Development
```bash
# Watch for changes and auto-rebuild
docker-compose -f docker-compose.local.yml exec node bash
cd resources/js/dealer
npm run watch
```

### Backend Development
```bash
# Access PHP container for artisan commands
docker-compose -f docker-compose.local.yml exec app bash
php artisan make:controller ExampleController
php artisan migrate
```

### View Logs
```bash
# Application logs
docker-compose -f docker-compose.local.yml logs app

# Nginx logs
docker-compose -f docker-compose.local.yml logs nginx

# All logs
docker-compose -f docker-compose.local.yml logs -f
```

## Troubleshooting

### Common Issues

#### 1. Port Already in Use
If port 8080 is already in use:
```bash
# Find process using port 8080
lsof -i :8080

# Kill the process
kill -9 <PID>
```

#### 2. Permission Issues
```bash
# Fix file permissions
sudo chown -R $(whoami):$(whoami) .
chmod -R 755 storage bootstrap/cache
```

#### 3. Database Connection Issues
```bash
# Recreate database
rm database/database.sqlite
touch database/database.sqlite
php artisan migrate:fresh --seed
```

#### 4. Frontend Build Issues
```bash
# Clear npm cache and reinstall
docker-compose -f docker-compose.local.yml exec node bash
cd resources/js/dealer
rm -rf node_modules package-lock.json
npm install
npm run build:direct:dealer
```

### Useful Commands

```bash
# Restart all services
docker-compose -f docker-compose.local.yml restart

# Stop all services
docker-compose -f docker-compose.local.yml down

# Rebuild containers
docker-compose -f docker-compose.local.yml build --no-cache

# Clean up Docker
docker system prune -a
```

## Quick Setup Script

For automated setup, use the provided script:

```bash
# Make script executable
chmod +x setup-docs/setup-local.sh

# Run setup script
./setup-docs/setup-local.sh
```

This script will:
- Check prerequisites
- Set up environment files
- Initialize database
- Build and start Docker containers
- Install dependencies
- Configure hosts file

## Development Features

### DNS Management
The project includes complete Cloudflare DNS management:
- Automatic A record creation with proxy
- WWW CNAME record setup
- Page Rules for www → non-www redirects
- Real-time DNS status checking

### Frontend Development
- Vue.js 3 with Composition API
- Bootstrap Vue components
- Hot reload during development
- Automated build process

### Backend Features
- Laravel 9+ framework
- SQLite database for local development
- RESTful API endpoints
- Comprehensive logging

## File Structure

```
setup-docs/
├── README.md              # This setup guide
├── TROUBLESHOOTING.md     # Common issues and solutions
├── DEVELOPMENT.md         # Development workflow guide
├── init-database.sql      # Database initialization script
└── setup-local.sh         # Automated setup script
```

## Next Steps

1. **Configure Cloudflare API** - Add your Cloudflare API token for DNS management
2. **Set up SSL certificates** - Configure SSL for HTTPS development
3. **Configure mail testing** - Set up MailHog or similar for email testing
4. **IDE Setup** - Configure your IDE for PHP and Vue.js development

## Support

For issues or questions:
1. Check [TROUBLESHOOTING.md](TROUBLESHOOTING.md) for common solutions
2. Review Docker logs for error messages
3. Ensure all prerequisites are installed correctly
4. Verify environment configuration

## Additional Resources

- [Development Workflow Guide](DEVELOPMENT.md)
- [Troubleshooting Guide](TROUBLESHOOTING.md)
- [Database Schema](init-database.sql)

---

**Note**: This setup is for local development only. Production deployment requires additional security and performance configurations.
