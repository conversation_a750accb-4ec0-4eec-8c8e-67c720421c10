#!/bin/bash

# Quick Start Script for Exolog Local Development
# This script sets up a simplified local development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🚀 Exolog Quick Start - Local Development Setup"
echo "================================================"

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Setup environment
setup_environment() {
    print_status "Setting up environment..."
    
    if [ ! -f .env ]; then
        cp .env.local .env
        print_success "Environment file created from .env.local"
    else
        print_warning "Environment file already exists, skipping..."
    fi
}

# Start services
start_services() {
    print_status "Starting Docker services..."
    
    # Stop any existing containers
    docker compose -f docker-compose.local.yml down 2>/dev/null || true

    # Start services
    docker compose -f docker-compose.local.yml up -d mysql redis

    print_status "Waiting for database to be ready..."
    sleep 10

    # Start application containers
    docker compose -f docker-compose.local.yml up -d
    
    print_success "All services started"
}

# Install dependencies
install_dependencies() {
    print_status "Installing PHP dependencies..."
    
    if command -v composer &> /dev/null; then
        composer install
    else
        print_warning "Composer not found locally, using Docker..."
        docker compose -f docker-compose.local.yml exec dealer composer install
    fi
    
    print_success "PHP dependencies installed"
}

# Setup Laravel
setup_laravel() {
    print_status "Setting up Laravel application..."
    
    # Generate application key
    if command -v php &> /dev/null; then
        php artisan key:generate
        php artisan config:cache
    else
        docker compose -f docker-compose.local.yml exec dealer php artisan key:generate
        docker compose -f docker-compose.local.yml exec dealer php artisan config:cache
    fi
    
    print_success "Laravel application configured"
}

# Run migrations
run_migrations() {
    print_status "Running database migrations..."
    
    if command -v php &> /dev/null; then
        php artisan migrate
    else
        docker compose -f docker-compose.local.yml exec dealer php artisan migrate
    fi
    
    print_success "Database migrations completed"
}

# Install frontend dependencies
install_frontend() {
    print_status "Installing frontend dependencies..."
    
    if command -v npm &> /dev/null; then
        # Install dependencies for each frontend app
        cd resources/js/dealer && npm install && cd ../../..
        cd resources/js/exo_admin && npm install && cd ../../..
        cd resources/js/exo_editor && npm install && cd ../../..
    else
        print_warning "NPM not found locally, using Docker..."
        docker compose -f docker-compose.local.yml exec node-dev sh -c "
            cd resources/js/dealer && npm install &&
            cd ../exo_admin && npm install &&
            cd ../exo_editor && npm install
        "
    fi
    
    print_success "Frontend dependencies installed"
}

# Build frontend
build_frontend() {
    print_status "Building frontend applications..."
    
    if command -v npm &> /dev/null; then
        cd resources/js/dealer && npm run build && cd ../../..
        cd resources/js/exo_admin && npm run build && cd ../../..
        cd resources/js/exo_editor && npm run build && cd ../../..
    else
        docker compose -f docker-compose.local.yml exec node-dev sh -c "
            cd resources/js/dealer && npm run build &&
            cd ../exo_admin && npm run build &&
            cd ../exo_editor && npm run build
        "
    fi
    
    print_success "Frontend applications built"
}

# Display information
display_info() {
    echo ""
    print_success "🎉 Exolog local development environment is ready!"
    echo ""
    echo "📋 Access URLs:"
    echo "  • Main Application: http://localhost:8082"
    echo "  • Dealer Interface: http://localhost:8081"
    echo "  • Database Admin (Adminer): http://localhost:8080"
    echo "  • Mail Testing (MailHog): http://localhost:8025"
    echo ""
    echo "🔧 Useful Commands:"
    echo "  • View logs: docker compose -f docker-compose.local.yml logs -f"
    echo "  • Stop services: docker compose -f docker-compose.local.yml down"
    echo "  • Restart services: docker compose -f docker-compose.local.yml restart"
    echo "  • Access container: docker compose -f docker-compose.local.yml exec dealer bash"
    echo ""
    echo "🛠️ Development:"
    echo "  • Frontend dev servers: npm run serve:all"
    echo "  • Laravel artisan: php artisan [command] or docker compose -f docker-compose.local.yml exec dealer php artisan [command]"
    echo ""
    echo "📁 Database Connection (Adminer):"
    echo "  • Server: mysql"
    echo "  • Username: exolog"
    echo "  • Password: password"
    echo "  • Database: exolog"
}

# Main execution
main() {
    check_prerequisites
    setup_environment
    start_services
    install_dependencies
    setup_laravel
    run_migrations
    install_frontend
    build_frontend
    display_info
}

# Handle script arguments
case "${1:-}" in
    "clean")
        print_status "Cleaning up environment..."
        docker compose -f docker-compose.local.yml down -v
        docker system prune -f
        print_success "Environment cleaned"
        ;;
    "logs")
        docker compose -f docker-compose.local.yml logs -f
        ;;
    "stop")
        docker compose -f docker-compose.local.yml down
        print_success "Services stopped"
        ;;
    "restart")
        docker compose -f docker-compose.local.yml restart
        print_success "Services restarted"
        ;;
    *)
        main
        ;;
esac
