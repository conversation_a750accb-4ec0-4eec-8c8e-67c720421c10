# Local Development Environment Configuration
# Copy this to .env and modify as needed

APP_NAME=Exolog
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost:8082

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Database Configuration (Docker)
DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=exolog
DB_USERNAME=exolog
DB_PASSWORD=password

# Cache and Session
BROADCAST_DRIVER=log
CACHE_DRIVER=redis
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=database
SESSION_DRIVER=redis
SESSION_LIFETIME=120

# Redis Configuration (Docker)
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

# Mail Configuration (MailHog for testing)
MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# AWS Configuration (Optional)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=eu-central-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

# Exolog Specific Configuration
CYRUS_HOST=localhost
CYRUS_PORT=143
CYRUS_USERNAME=
CYRUS_PASSWORD=

# Poste API Configuration (for production)
POSTE_API_URL=
POSTE_API_USER=
POSTE_API_PASSWORD=

# Mail Server IP for DNS A records
MAIL_SERVER_IP=127.0.0.1

# Mail Database (Optional)
DB_MAIL_HOST=mysql
DB_MAIL_DATABASE=mail
DB_MAIL_USERNAME=exolog
DB_MAIL_PASSWORD=password

# Server Configuration
EXOLOG_SERVER_NAME=localhost
EXOLOG_SERVER_DOMAIN=localhost

# API Tokens (Optional)
GITLAB_PERSONAL_ACCESS_TOKEN=
OPENPROVIDER_USERNAME=
OPENPROVIDER_PASSWORD=
CLOUDFLARE_API_TOKEN=
LOG_DISCORD_WEBHOOK_URL=

# Docker Environment Variables
MYSQL_ROOT_PASSWORD=root
